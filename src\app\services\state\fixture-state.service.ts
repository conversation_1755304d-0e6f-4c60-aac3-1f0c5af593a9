import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { FixtureDTO, GameFixtureData, PaginatedFixtureDTO } from '../../shared/models/game.model';

export interface FixtureState {
  fixtures: FixtureDTO[];
  currentFixture: FixtureDTO | null;
  gamesByDate: { [date: string]: GameFixtureData[] };
  fixturesOptions: Array<{ value: string; displayText: string }>;
  currentFixtureNumber: number;
  currentPage: number;
  totalPages: number;
  totalFixtures: number;
  hasMoreFixtures: boolean;
  pageSize: number;
  lastFixturesUpdate: Date | null;
  isFixturesLoading: boolean;
  isPaginationLoading: boolean;
  gameUpdateStates: Map<string, 'updating' | 'success' | 'error'>;
  optimisticUpdates: Map<string, { homeGoals: number; awayGoals: number; timestamp: Date }>;
  viewMode: 'fixtures' | 'date' | 'playoffs';
  selectedStage: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class FixtureStateService {
  private readonly initialState: FixtureState = {
    fixtures: [],
    currentFixture: null,
    gamesByDate: {},
    fixturesOptions: [],
    currentFixtureNumber: 1,
    currentPage: 1,
    totalPages: 0,
    totalFixtures: 29,
    hasMoreFixtures: true,
    pageSize: 40,
    lastFixturesUpdate: null,
    isFixturesLoading: false,
    isPaginationLoading: false,
    gameUpdateStates: new Map(),
    optimisticUpdates: new Map(),
    viewMode: 'fixtures',
    selectedStage: null
  };

  private stateSubject = new BehaviorSubject<FixtureState>(this.initialState);
  public state$ = this.stateSubject.asObservable();

  // Cache duration in milliseconds (15 minutes for fixtures)
  private readonly FIXTURES_CACHE_DURATION = 15 * 60 * 1000;

  constructor() {
    this.loadStateFromStorage();
  }

  get currentState(): FixtureState {
    return this.stateSubject.value;
  }

  // Fixtures management
  updateFixtures(paginatedData: PaginatedFixtureDTO, append: boolean = false): void {
    const currentState = this.currentState;
    const newFixtures = append 
      ? [...currentState.fixtures, ...paginatedData.fixtures]
      : paginatedData.fixtures;

    const newState = {
      ...currentState,
      fixtures: newFixtures,
      currentPage: paginatedData.currentPage,
      totalPages: paginatedData.totalPages,
      totalFixtures: paginatedData.totalFixtures,
      hasMoreFixtures: paginatedData.currentPage < paginatedData.totalPages,
      lastFixturesUpdate: new Date(),
      isFixturesLoading: false,
      isPaginationLoading: false
    };

    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  setCurrentFixture(fixture: FixtureDTO | null): void {
    this.updateState({ currentFixture: fixture });
  }

  setCurrentFixtureNumber(fixtureNumber: number): void {
    this.updateState({ currentFixtureNumber: fixtureNumber });
  }

  updateGamesByDate(gamesByDate: { [date: string]: GameFixtureData[] }): void {
    this.updateState({ gamesByDate });
  }

  updateFixturesOptions(options: Array<{ value: string; displayText: string }>): void {
    this.updateState({ fixturesOptions: options });
  }

  // Loading states
  setFixturesLoading(isLoading: boolean): void {
    this.updateState({ isFixturesLoading: isLoading });
  }

  setPaginationLoading(isLoading: boolean): void {
    this.updateState({ isPaginationLoading: isLoading });
  }

  // Game update states for individual games
  setGameUpdateState(gameId: string, state: 'updating' | 'success' | 'error' | null): void {
    const currentState = this.currentState;
    const newGameUpdateStates = new Map(currentState.gameUpdateStates);
    
    if (state === null) {
      newGameUpdateStates.delete(gameId);
    } else {
      newGameUpdateStates.set(gameId, state);
    }

    this.updateState({ gameUpdateStates: newGameUpdateStates });
  }

  // Optimistic updates for immediate UI feedback
  addOptimisticUpdate(gameId: string, homeGoals: number, awayGoals: number): void {
    const currentState = this.currentState;
    const newOptimisticUpdates = new Map(currentState.optimisticUpdates);
    newOptimisticUpdates.set(gameId, { homeGoals, awayGoals, timestamp: new Date() });

    // Update the game in fixtures immediately
    const updatedFixtures = this.updateGameInFixtures(currentState.fixtures, gameId, homeGoals, awayGoals);
    const updatedGamesByDate = this.updateGameInGamesByDate(currentState.gamesByDate, gameId, homeGoals, awayGoals);

    this.updateState({ 
      optimisticUpdates: newOptimisticUpdates,
      fixtures: updatedFixtures,
      gamesByDate: updatedGamesByDate
    });
  }

  removeOptimisticUpdate(gameId: string): void {
    const currentState = this.currentState;
    const newOptimisticUpdates = new Map(currentState.optimisticUpdates);
    newOptimisticUpdates.delete(gameId);

    this.updateState({ optimisticUpdates: newOptimisticUpdates });
  }

  // Rollback optimistic update on error
  rollbackOptimisticUpdate(gameId: string, originalHomeGoals: number, originalAwayGoals: number): void {
    const currentState = this.currentState;
    const newOptimisticUpdates = new Map(currentState.optimisticUpdates);
    newOptimisticUpdates.delete(gameId);

    // Revert the game in fixtures
    const revertedFixtures = this.updateGameInFixtures(currentState.fixtures, gameId, originalHomeGoals, originalAwayGoals);
    const revertedGamesByDate = this.updateGameInGamesByDate(currentState.gamesByDate, gameId, originalHomeGoals, originalAwayGoals);

    this.updateState({ 
      optimisticUpdates: newOptimisticUpdates,
      fixtures: revertedFixtures,
      gamesByDate: revertedGamesByDate
    });
  }

  // View mode management
  setViewMode(viewMode: 'fixtures' | 'date' | 'playoffs'): void {
    this.updateState({ viewMode });
  }

  setSelectedStage(stage: string | null): void {
    this.updateState({ selectedStage: stage });
  }

  // Cache management
  isFixturesStale(): boolean {
    const lastUpdate = this.currentState.lastFixturesUpdate;
    if (!lastUpdate) return true;
    return Date.now() - lastUpdate.getTime() > this.FIXTURES_CACHE_DURATION;
  }

  forceRefresh(): void {
    const newState = {
      ...this.currentState,
      lastFixturesUpdate: null,
      fixtures: [],
      currentPage: 1,
      hasMoreFixtures: true
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  clearCache(): void {
    this.stateSubject.next(this.initialState);
    this.clearStateFromStorage();
  }

  // Helper methods
  private updateGameInFixtures(fixtures: FixtureDTO[], gameId: string, homeGoals: number, awayGoals: number): FixtureDTO[] {
    return fixtures.map(fixture => ({
      ...fixture,
      games: fixture.games?.map(game => 
        game.id === gameId 
          ? { ...game, result: { homeTeamGoals: homeGoals, awayTeamGoals: awayGoals }, status: 'PLAYED' as any }
          : game
      ) || []
    }));
  }

  private updateGameInGamesByDate(gamesByDate: { [date: string]: GameFixtureData[] }, gameId: string, homeGoals: number, awayGoals: number): { [date: string]: GameFixtureData[] } {
    const updated = { ...gamesByDate };
    Object.keys(updated).forEach(date => {
      updated[date] = updated[date].map(game => 
        game.id === gameId 
          ? { ...game, result: { homeTeamGoals: homeGoals, awayTeamGoals: awayGoals }, status: 'PLAYED' as any }
          : game
      );
    });
    return updated;
  }

  private updateState(partialState: Partial<FixtureState>): void {
    const newState = { ...this.currentState, ...partialState };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  private saveStateToStorage(state: FixtureState): void {
    try {
      const stateToSave = {
        ...state,
        // Convert Maps to arrays for JSON serialization
        gameUpdateStates: Array.from(state.gameUpdateStates.entries()),
        optimisticUpdates: Array.from(state.optimisticUpdates.entries())
      };
      localStorage.setItem('fixture-state', JSON.stringify(stateToSave));
    } catch (error) {
      console.warn('Failed to save fixture state to localStorage:', error);
    }
  }

  private loadStateFromStorage(): void {
    try {
      const savedState = localStorage.getItem('fixture-state');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        const restoredState: FixtureState = {
          ...this.initialState,
          ...parsedState,
          lastFixturesUpdate: parsedState.lastFixturesUpdate ? new Date(parsedState.lastFixturesUpdate) : null,
          // Convert arrays back to Maps
          gameUpdateStates: new Map(parsedState.gameUpdateStates || []),
          optimisticUpdates: new Map((parsedState.optimisticUpdates || []).map(([key, value]: [string, any]) => [
            key, 
            { ...value, timestamp: new Date(value.timestamp) }
          ])),
          // Reset loading states on app start
          isFixturesLoading: false,
          isPaginationLoading: false
        };
        this.stateSubject.next(restoredState);
      }
    } catch (error) {
      console.warn('Failed to load fixture state from localStorage:', error);
    }
  }

  private clearStateFromStorage(): void {
    try {
      localStorage.removeItem('fixture-state');
    } catch (error) {
      console.warn('Failed to clear fixture state from localStorage:', error);
    }
  }
}
