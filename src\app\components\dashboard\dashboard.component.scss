/* === MODERN DASHBOARD DESIGN === */

.dashboard-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) var(--spacing-lg);
    background: var(--bg-primary);
    min-height: 100vh;
    font-family: var(--font-sans);
    position: relative;

    .dashboard-content {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    &::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
        pointer-events: none;
        z-index: 0;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg) var(--spacing-md);
        gap: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md) var(--spacing-sm);
        gap: var(--spacing-md);
    }
}

/* === HEADER SECTION === */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    z-index: 1;
    animation: slideInDown 0.6s ease-out;
    flex-wrap: wrap;
    gap: var(--spacing-md);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
    }

    @media (max-width: 768px) {
        padding: var(--spacing-md) var(--spacing-lg);
        flex-direction: column;
        text-align: center;
    }
}

.header-content {
    flex: 1;
    min-width: 0;

    .header-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;

        i {
            color: var(--primary-500);
            font-size: var(--text-xl);
        }
    }

    .header-subtitle {
        color: var(--text-secondary);
        font-size: var(--text-base);
        margin: 0;
        font-weight: var(--font-weight-medium);
    }

    @media (max-width: 768px) {
        text-align: center;
    }
}

.header-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;

    @media (max-width: 768px) {
        gap: var(--spacing-sm);
        justify-content: center;
    }

    .cache-info {
        display: flex;
        align-items: center;
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-md);
        font-size: var(--text-xs);
        color: var(--text-secondary);

        @media (max-width: 768px) {
            display: none;
        }

        .cache-text {
            font-weight: var(--font-weight-medium);
        }
    }

    .force-refresh-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        background: linear-gradient(135deg, var(--success-500), var(--success-600));
        color: white;
        border: none;
        border-radius: var(--radius-lg);
        font-weight: var(--font-weight-semibold);
        font-size: var(--text-sm);
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
        white-space: nowrap;

        &:hover:not(.disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
            background: linear-gradient(135deg, var(--success-600), var(--success-700));
        }

        &:active:not(.disabled) {
            transform: translateY(0);
        }

        &.disabled {
            background: var(--surface-tertiary);
            color: var(--text-tertiary);
            cursor: not-allowed;
            opacity: 0.6;
            box-shadow: none;
        }

        i {
            font-size: var(--text-base);

            &.spinning {
                animation: spin 1s linear infinite;
            }
        }

        @media (max-width: 768px) {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: var(--text-xs);

            span {
                display: none;
            }
        }
    }

    .view-all-seasons-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        color: white;
        border: none;
        border-radius: var(--radius-lg);
        font-weight: var(--font-weight-semibold);
        font-size: var(--text-sm);
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        white-space: nowrap;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        }

        &:active {
            transform: translateY(0);
        }

        i {
            font-size: var(--text-base);
        }

        @media (max-width: 768px) {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: var(--text-xs);

            span {
                display: none;
            }
        }
    }
}



/* === STATS SECTION === */
.stats-section {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    position: relative;
    z-index: 1;
    animation: fadeInUp 0.6s ease-out 0.2s both;

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: var(--spacing-xl);

    @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-secondary);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
        transition: left 0.6s ease;
    }

    &:hover {
        transform: translateY(-6px) scale(1.02);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-300);

        &::before {
            left: 100%;
        }

        .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }
    }

    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }

    .stat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%);
        border-radius: var(--radius-xl);
        color: var(--primary-600);
        font-size: var(--text-xl);
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);

        @media (max-width: 480px) {
            width: 48px;
            height: 48px;
            font-size: var(--text-lg);
        }
    }

    .stat-content {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);

        .stat-value {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            line-height: 1;

            @media (max-width: 480px) {
                font-size: var(--text-lg);
            }
        }

        .stat-label {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
    }
}

/* === DASHBOARD LAYOUT === */
.top-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

.middle-row {
    display: flex;
    width: 100%;
}

/* === DASHBOARD CARDS === */
.dashboard-card {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    min-height: 400px;

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--border-hover);
    }

    &.league-table-card {
        width: 100%;
        min-height: 500px;

        @media (max-width: 768px) {
            min-height: 400px;
        }
    }

    &.scorers-card,
    &.assists-card {
        min-height: 400px;

        .card-content {
            padding: var(--spacing-lg);
        }

        @media (max-width: 768px) {
            min-height: 350px;
        }
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-secondary);
        background: var(--surface-secondary);

        .header-info {
            .card-title {
                display: flex;
                align-items: center;
                gap: var(--spacing-md);
                font-size: var(--text-lg);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
                margin: 0 0 var(--spacing-xs) 0;

                i {
                    color: var(--primary-500);
                }
            }

            .card-subtitle {
                font-size: var(--text-sm);
                color: var(--text-secondary);
                margin: 0;
                font-weight: var(--font-weight-medium);
            }
        }

        .header-action {
            color: var(--text-tertiary);
            font-size: var(--text-sm);
            transition: all 0.2s ease;
        }
    }

    &:hover .header-action {
        color: var(--primary-500);
        transform: translateX(2px);
    }

    .card-content {
        flex: 1;
        padding: var(--spacing-lg);
        overflow: hidden;
        display: flex;
        flex-direction: column;

        @media (max-width: 768px) {
            padding: var(--spacing-md);
        }
    }
}

/* === COMPONENT INTEGRATION === */
.dashboard-card {
    league-table {
        display: block;
        height: 100%;
        overflow-y: auto;

        /* Fix table text color and styling */
        ::ng-deep {
            // Force all text to be clearly visible
            * {
                color: var(--text-primary) !important;
            }

            .table-row {
                color: var(--text-primary) !important;

                &.header-row {
                    color: var(--text-primary) !important;
                    background: var(--surface-tertiary) !important;

                    * {
                        color: var(--text-primary) !important;
                    }
                }

                &.team-row {
                    color: var(--text-primary) !important;

                    * {
                        color: var(--text-primary) !important;
                    }

                    .team-name {
                        color: var(--text-primary) !important;
                    }

                    .rank-number,
                    .points-number,
                    .gd-number,
                    .games-number,
                    .wins-number,
                    .draws-number,
                    .losses-number,
                    .gf-number,
                    .ga-number {
                        color: var(--text-primary) !important;
                    }
                }
            }

            // Specific overrides for common elements
            .rank-cell,
            .team-cell,
            .games-cell,
            .wins-cell,
            .draws-cell,
            .losses-cell,
            .gf-cell,
            .ga-cell,
            .gd-cell,
            .points-cell {
                color: var(--text-primary) !important;

                * {
                    color: var(--text-primary) !important;
                }
            }

            // Ensure podium text is visible
            .podium-name,
            .points-count,
            .table-title {
                color: var(--text-primary) !important;
            }

            // Keep special colors for specific elements
            .gd-number {
                &.positive {
                    color: var(--success-600) !important;
                }

                &.negative {
                    color: var(--error-600) !important;
                }
            }

            .rank-number {
                &.gold {
                    color: var(--warning-500) !important;
                }

                &.silver {
                    color: #C0C0C0 !important; // Bright silver
                }

                &.bronze {
                    color: #CD7F32 !important; // Bright bronze
                }
            }
        }
    }

    app-dashboard-topscorers,
    app-dashboard-topassists {
        display: block;
        height: 100%;
        overflow: hidden;
    }
}

/* === LOADING AND ERROR STATES === */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    min-height: 400px;

    .loading-spinner {
        font-size: var(--text-3xl);
        color: var(--primary-500);
        margin-bottom: var(--spacing-md);

        i {
            animation: spin 1s linear infinite;
        }
    }

    p {
        color: var(--text-secondary);
        font-size: var(--text-lg);
    }
}

.error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    min-height: 400px;

    .error-content {
        text-align: center;
        max-width: 400px;

        i {
            font-size: var(--text-4xl);
            color: var(--error-500);
            margin-bottom: var(--spacing-md);
        }

        h3 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        p {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
        }

        .retry-btn {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-lg);
            background: var(--primary-500);
            color: var(--text-inverse);
            border: none;
            border-radius: var(--radius-md);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background: var(--primary-600);
                transform: translateY(-1px);
            }
        }
    }
}

/* === REFRESH BUTTON === */
.refresh-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
        background: var(--surface-tertiary);
        border-color: var(--primary-300);
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    i.fa-spin {
        animation: spin 1s linear infinite;
    }
}

/* === SEASON PROGRESS === */
.season-progress-section {
    margin-bottom: var(--spacing-lg);

    .progress-card {
        .progress-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);

            .progress-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;

                .progress-label {
                    font-size: var(--text-sm);
                    color: var(--text-secondary);
                    margin-bottom: var(--spacing-xs);
                }

                .progress-value {
                    font-size: var(--text-xl);
                    font-weight: var(--font-weight-bold);
                    color: var(--text-primary);
                }
            }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--surface-tertiary);
            border-radius: var(--radius-full);
            overflow: hidden;

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
                border-radius: var(--radius-full);
                transition: width 0.3s ease;
            }
        }
    }
}

/* === HIGHLIGHTS SECTION === */
.highlights-section {
    margin-top: var(--spacing-lg);

    .highlights-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }
    }

    .highlight-card {
        .highlight-info {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);

            .highlight-name {
                font-size: var(--text-lg);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
            }

            .highlight-team {
                font-size: var(--text-sm);
                color: var(--text-secondary);
            }

            .highlight-value {
                font-size: var(--text-xl);
                font-weight: var(--font-weight-bold);
                color: var(--primary-600);
            }
        }
    }
}

/* === ENHANCED HIGHLIGHTS SECTION === */
.highlights-section {
    .highlights-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .highlight-card {
        border: none;
        overflow: hidden;
        position: relative;
        min-height: auto;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            pointer-events: none;
        }

        .card-header {
            padding: 1rem 1.25rem 0.75rem;
        }

        .card-content {
            padding: 0.75rem 1.25rem 1.25rem;
        }
    }
}

/* Golden Boot Card Styles */
.golden-boot-card {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
    color: #1a1a1a;
    box-shadow: 0 8px 32px rgba(255, 215, 0, 0.3);

    .golden-boot-header {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        background: rgba(0, 0, 0, 0.05);

        .card-title {
            color: #1a1a1a;
            font-weight: 800;
            font-size: 0.95rem;
            letter-spacing: 0.5px;
            margin: 0;

            .golden-boot-icon {
                color: #B8860B;
                margin-right: 0.5rem;
                font-size: 1rem;
            }
        }
    }
}

.golden-boot-player {
    display: flex;
    align-items: center;
    gap: 1rem;

    .player-avatar-large {
        position: relative;
        width: 60px;
        height: 60px;
        flex-shrink: 0;

        .player-image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #B8860B;
        }

        .crown-overlay {
            position: absolute;
            top: -6px;
            right: -6px;
            background: #FFD700;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #B8860B;

            i {
                color: #B8860B;
                font-size: 0.7rem;
            }
        }
    }

    .player-details {
        flex: 1;
        min-width: 0;

        .player-name-large {
            display: block;
            font-size: 1.1rem;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 0.15rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .player-team-badge {
            display: inline-block;
            background: rgba(0, 0, 0, 0.1);
            padding: 0.15rem 0.5rem;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .goals-display {
            display: flex;
            align-items: baseline;
            gap: 0.4rem;

            .goals-number {
                font-size: 1.5rem;
                font-weight: 900;
                color: #B8860B;
                line-height: 1;
            }

            .goals-label {
                font-size: 0.8rem;
                font-weight: 600;
                color: #666;
                letter-spacing: 0.3px;
            }
        }
    }
}

/* League Leader Card Styles */
.league-leader-card {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 50%, #EC4899 100%);
    color: white;
    box-shadow: 0 8px 32px rgba(79, 70, 229, 0.3);

    .league-leader-header {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(255, 255, 255, 0.05);

        .card-title {
            color: white;
            font-weight: 800;
            font-size: 0.95rem;
            letter-spacing: 0.5px;
            margin: 0;

            .league-leader-icon {
                color: #FCD34D;
                margin-right: 0.5rem;
                font-size: 1rem;
            }
        }
    }
}

.league-leader-team {
    display: flex;
    align-items: center;
    gap: 1rem;

    .team-avatar-large {
        position: relative;
        width: 60px;
        height: 60px;
        flex-shrink: 0;

        .team-image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #FCD34D;
        }

        .leader-badge {
            position: absolute;
            top: -6px;
            right: -6px;
            background: #FCD34D;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;

            i {
                color: #7C3AED;
                font-size: 0.7rem;
            }
        }
    }

    .team-details {
        flex: 1;
        min-width: 0;

        .team-name-large {
            display: block;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.15rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .team-stats-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.15);
            padding: 0.15rem 0.5rem;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0.5rem;
        }

        .points-display {
            display: flex;
            align-items: baseline;
            gap: 0.4rem;

            .points-number {
                font-size: 1.5rem;
                font-weight: 900;
                color: #FCD34D;
                line-height: 1;
            }

            .points-label {
                font-size: 0.8rem;
                font-weight: 600;
                color: rgba(255, 255, 255, 0.8);
                letter-spacing: 0.3px;
            }
        }
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* === TRENDING PREDICTIONS SECTION === */
.trending-predictions-section {
    margin-top: var(--spacing-2xl);

    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-xl);

        .section-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-md);

            i {
                color: var(--primary);
                font-size: var(--text-xl);
            }
        }

        .section-subtitle {
            color: var(--text-secondary);
            font-size: var(--text-base);
            margin: 0;
        }
    }

    .predictions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(380px, 1fr)); // Increased min width to prevent 4 columns
        gap: var(--spacing-sm); // Reduced gap for tighter spacing
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 5px; // Added small padding

        // Ensure maximum of 3 columns on large screens
        @media (min-width: 1400px) {
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md); // Slightly larger gap on very large screens
        }

        @media (max-width: 1200px) {
            grid-template-columns: repeat(2, 1fr); // Force 2 columns on medium screens
            gap: var(--spacing-sm);
        }

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
            padding: 0;
        }

        .prediction-card {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-primary);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            transition: all 0.3s ease;
            min-height: 280px; // Set consistent height
            max-height: 320px; // Prevent excessive height

            &:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-lg);
                border-color: var(--primary-400);
            }
        }
    }
}