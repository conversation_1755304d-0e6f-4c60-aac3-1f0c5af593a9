
::ng-deep .cdk-overlay-container { z-index: 9999; }

.view-only-message {
    display: flex; align-items: center; justify-content: center; min-height: 60vh; padding: 2rem; background: var(--bg-primary);

    .message-content {
        text-align: center; padding: 2rem; background: var(--surface-primary); border-radius: 1rem; border: 1px solid var(--border-primary); box-shadow: var(--shadow-lg); max-width: 500px;

        i { font-size: 4rem; color: var(--warning); margin-bottom: 1rem; }
        h2 { font-size: 1.5rem; font-weight: bold; color: var(--text-primary); margin-bottom: 1rem; }
        p { font-size: 1rem; color: var(--text-secondary); margin-bottom: 0.5rem; line-height: 1.6; &:last-child { margin-bottom: 0; } }
    }
}

.modify-game-container {
    display: flex; flex-direction: column; gap: 2rem; padding: 2rem; background: var(--bg-primary); min-height: 100vh; font-family: var(--font-sans);
    @media (max-width: 768px) { padding: 1rem; gap: 1rem; }
}

.match-header {
    background: var(--surface-primary); border-radius: 1rem; padding: 1rem 2rem; border: 1px solid var(--border-primary); box-shadow: var(--shadow-lg);
    @media (max-width: 768px) { padding: 1rem; }
}

.match-title {
    display: flex; align-items: center; justify-content: center; gap: 1rem; margin-bottom: 2rem;
    i { color: var(--primary); font-size: 1.25rem; }
    h1 { font-size: 1.5rem; font-weight: bold; color: var(--text-primary); margin: 0; }
    @media (max-width: 768px) { margin-bottom: 1rem; h1 { font-size: 1.25rem; } }
}

.score-section {
    display: grid; grid-template-columns: 1fr auto 1fr; gap: 2rem; align-items: center;
    @media (max-width: 768px) { grid-template-columns: 1fr; gap: 1rem; text-align: center; }
}

.team-section {
    display: flex; align-items: center; gap: 1rem;
    &.home-team { justify-content: flex-end; @media (max-width: 768px) { justify-content: center; } }
    &.away-team { justify-content: flex-start; @media (max-width: 768px) { justify-content: center; order: 3; } }
}

.team-info { display: flex; align-items: center; gap: 1rem; }

.team-logo {
    width: 60px; height: 60px; border-radius: 50%; object-fit: cover; border: 3px solid var(--border-primary); box-shadow: var(--shadow-md);
    @media (max-width: 768px) { width: 50px; height: 50px; }
}

.team-details { display: flex; flex-direction: column; gap: 0.25rem; }

.team-name {
    font-size: 1.125rem; font-weight: bold; color: var(--text-primary); margin: 0; line-height: 1.2;
    @media (max-width: 768px) { font-size: 1rem; }
}

.team-label { font-size: 0.875rem; color: var(--text-secondary); font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em; }

.score-input-section {
    display: flex; justify-content: center; align-items: center;
    @media (max-width: 768px) { order: 2; }
}

.score-inputs {
    display: flex; align-items: center; gap: 1rem; background: var(--surface-secondary); padding: 1rem; border-radius: 1rem; border: 2px solid var(--border-primary); box-shadow: var(--shadow-md);
    @media (max-width: 768px) { gap: 0.75rem; padding: 0.75rem; }
}

.penalty-section {
    margin-top: 1rem; background: var(--surface-tertiary); border-radius: 1rem; padding: 1rem; border: 2px solid var(--warning-color); box-shadow: var(--shadow-md);
    @media (max-width: 768px) { padding: 0.75rem; }
}

.penalty-header {
    display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem; color: var(--warning-color);
    i { font-size: 1.25rem; }
    h4 { margin: 0; font-size: 1rem; font-weight: 600; }
    .penalty-info { font-size: 0.875rem; color: var(--text-secondary); font-style: italic; }
}

.penalty-inputs {
    display: flex; align-items: center; gap: 1rem; background: var(--surface-primary); padding: 1rem; border-radius: 0.75rem; border: 1px solid var(--border-secondary);
    @media (max-width: 768px) { gap: 0.75rem; padding: 0.75rem; }
}

.penalty-input-group { display: flex; flex-direction: column; align-items: center; gap: 0.25rem; }

.penalty-input {
    width: 70px; height: 50px; font-size: 1.25rem; font-weight: 700; text-align: center; background: var(--surface-secondary); border: 2px solid var(--warning-color); border-radius: 0.5rem; color: var(--text-primary); font-family: monospace; transition: all 0.2s ease;

    &:focus { outline: none; border-color: var(--warning-color); box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.2); transform: scale(1.05); }
    &:hover { border-color: var(--warning-color); }
    &.home-penalties:focus { border-color: var(--success); box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2); }
    &.away-penalties:focus { border-color: var(--error); box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2); }
    @media (max-width: 768px) { width: 55px; height: 40px; font-size: 1rem; }
}

.penalty-separator {
    display: flex; align-items: center; justify-content: center; width: 60px; height: 60px; background: var(--warning-color); border-radius: 50%; color: white; font-weight: 900; font-size: 0.75rem;
    @media (max-width: 768px) { width: 50px; height: 50px; font-size: 0.625rem; }
}

.penalty-label { font-size: 0.75rem; font-weight: 600; color: var(--text-secondary); text-align: center; }

.penalty-validation {
    margin-top: 0.75rem; padding: 0.5rem; background: var(--error-color-light); border: 1px solid var(--error-color); border-radius: 0.5rem; display: flex; align-items: center; gap: 0.5rem; color: var(--error-color);
    i { font-size: 1rem; }
    span { font-size: 0.875rem; font-weight: 500; }
}

.score-input-group { display: flex; flex-direction: column; align-items: center; gap: 0.25rem; }

.score-input {
    width: 80px; height: 60px; font-size: 1.5rem; font-weight: 900; text-align: center; background: var(--surface-primary); border: 2px solid var(--border-primary); border-radius: 0.5rem; color: var(--text-primary); font-family: monospace; transition: all 0.2s ease;

    &:focus { outline: none; border-color: var(--primary); box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1); transform: scale(1.05); }
    &:hover { border-color: var(--primary); }
    &.home-score:focus { border-color: var(--success); box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1); }
    &.away-score:focus { border-color: var(--warning); box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1); }
    @media (max-width: 768px) { width: 60px; height: 50px; font-size: 1.25rem; }
}

.score-label { font-size: 0.75rem; color: var(--text-secondary); font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em; }

.score-separator { display: flex; align-items: center; justify-content: center; margin: 0 0.75rem; }

.vs-text {
    font-size: 1.125rem; font-weight: bold; color: var(--text-secondary); background: var(--surface-primary); padding: 0.5rem 0.75rem; border-radius: 9999px; border: 1px solid var(--border-secondary);
}

.datetime-section {
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);

    .datetime-header {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-md);

        i {
            color: var(--primary);
            font-size: var(--text-lg);
        }

        h3 {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0;
        }
    }

    .datetime-inputs {
        display: flex;
        gap: var(--spacing-lg);
        align-items: end;

        @media (max-width: 768px) {
            flex-direction: column;
            gap: var(--spacing-md);
        }
    }

    .datetime-input-group {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
        flex: 1;
    }

    .datetime-label {
        font-size: var(--text-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
    }

    .datetime-input {
        padding: var(--spacing-sm) var(--spacing-md);
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-md);
        background: var(--surface-primary);
        color: var(--text-primary);
        font-size: var(--text-sm);
        transition: all 0.2s ease;

        &:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
        }

        &.modified {
            border-color: var(--warning-500);
            background: var(--surface-secondary);
        }
    }

    .datetime-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        margin-top: var(--spacing-md);
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--surface-tertiary);
        border: 1px solid var(--border-secondary);
        border-radius: var(--radius-md);
        color: var(--text-secondary);
        font-size: var(--text-sm);
        font-weight: var(--font-weight-medium);

        i {
            color: var(--text-tertiary);
        }
    }
}

// Compact styles for all remaining sections
.broadcast-section, .technical-result-section, .players-section, .ai-upload-section {
    background: var(--surface-secondary); border-radius: 0.5rem; padding: 1rem; margin-top: 1rem; border: 1px solid var(--border-secondary);
}

.broadcast-header, .technical-header, .section-header, .upload-header {
    display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem;
    i { color: var(--primary); font-size: 1.125rem; }
    h3 { font-size: 1.125rem; font-weight: 600; color: var(--text-primary); margin: 0; }
}

.broadcast-inputs, .datetime-inputs, .technical-controls {
    display: flex; gap: 1rem; align-items: end;
    @media (max-width: 768px) { flex-direction: column; gap: 0.75rem; }
}

.broadcast-input-group, .datetime-input-group { display: flex; flex-direction: column; gap: 0.25rem; flex: 1; }
.broadcast-label, .datetime-label, .technical-label { font-size: 0.875rem; font-weight: 600; color: var(--text-primary); }

.broadcast-input, .datetime-input, .reason-textarea {
    padding: 0.5rem 0.75rem; border: 2px solid var(--border-primary); border-radius: 0.375rem; background: var(--surface-primary); color: var(--text-primary); font-size: 0.875rem; transition: all 0.2s ease;
    &:focus { outline: none; border-color: var(--primary); box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1); }
    &.modified { border-color: var(--warning-500); background: var(--surface-secondary); }
    &.url-input { font-family: monospace; }
}

.broadcast-info, .datetime-info {
    display: flex; align-items: center; gap: 0.25rem; margin-top: 0.75rem; padding: 0.5rem 0.75rem; background: var(--surface-tertiary); border: 1px solid var(--border-secondary); border-radius: 0.375rem; color: var(--text-secondary); font-size: 0.875rem; font-weight: 500;
    i { color: var(--text-tertiary); }
}

.broadcast-help {
    display: flex; align-items: center; gap: 0.25rem; margin-top: 0.5rem; padding: 0.5rem 0.75rem; background: var(--surface-tertiary); border: 1px solid var(--border-secondary); border-radius: 0.375rem;
    i { color: var(--primary); }
    span { font-size: 0.75rem; color: var(--text-secondary); }
}

// Technical result section - compact
.technical-content { display: flex; flex-direction: column; gap: 1rem; }
.technical-info .technical-description { font-size: 0.875rem; color: var(--text-secondary); margin: 0; padding: 0.5rem 0.75rem; background: var(--warning-50); border: 1px solid var(--warning-200); border-radius: 0.375rem; }

.team-selection .team-buttons {
    display: flex; gap: 0.75rem;
    @media (max-width: 768px) { flex-direction: column; }

    .team-btn {
        display: flex; align-items: center; gap: 0.5rem; padding: 0.75rem; background: var(--surface-primary); border: 2px solid var(--border-primary); border-radius: 0.375rem; cursor: pointer; transition: all 0.2s ease; flex: 1;
        &:hover { border-color: var(--error-300); background: var(--error-50); }
        &.selected { border-color: var(--error-500); background: var(--error-100); box-shadow: 0 0 0 3px var(--error-100); }
        img { width: 32px; height: 32px; border-radius: 0.25rem; object-fit: cover; }
        span { font-size: 0.875rem; font-weight: 500; color: var(--text-primary); }
    }
}

.technical-actions {
    display: flex; gap: 0.75rem; align-items: center;
    @media (max-width: 768px) { flex-direction: column; align-items: stretch; }

    .technical-btn {
        display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 600; cursor: pointer; transition: all 0.2s ease;
        &.apply-btn { background: var(--error-500); color: white; &:hover:not(:disabled) { background: var(--error-600); transform: translateY(-1px); box-shadow: var(--shadow-md); } &:disabled { background: var(--surface-tertiary); color: var(--text-tertiary); cursor: not-allowed; } }
        &.clear-btn { background: var(--surface-tertiary); color: var(--text-secondary); &:hover { background: var(--surface-quaternary); color: var(--text-primary); } }
    }
}

.technical-preview {
    padding: 0.75rem; background: var(--info-50); border: 1px solid var(--info-200); border-radius: 0.375rem;
    .preview-header { display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem; i { color: var(--info-600); } span { font-size: 0.875rem; font-weight: 600; color: var(--info-700); } }
    .preview-score { display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: bold; .preview-team { color: var(--success-600); &.losing { color: var(--error-600); } } .preview-separator { color: var(--text-secondary); } }
    .preview-reason { font-size: 0.875rem; color: var(--text-secondary); strong { color: var(--text-primary); } }
}

// Team indicator for captains
.team-indicator {
    color: var(--primary);
    font-weight: 500;
    font-size: 0.9em;
}

// Score display section for non-admin users
.score-display-section {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.score-display {
    display: flex;
    align-items: center;
    gap: 2rem;

    .score-display-group {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;

        .score-display-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary);
            padding: 0.5rem 1rem;
            background: var(--surface-secondary);
            border: 2px solid var(--border-primary);
            border-radius: 0.5rem;
            min-width: 3rem;
            text-align: center;
        }

        .score-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }
    }

    .score-separator {
        .vs-text {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--text-secondary);
        }
    }
}

// Players section - compact
.section-header {
    text-align: center; margin-bottom: 2rem;
    @media (max-width: 768px) { margin-bottom: 1rem; }
}

.section-title {
    display: flex; align-items: center; justify-content: center; gap: 0.75rem; font-size: 1.25rem; font-weight: bold; color: var(--text-primary); margin: 0 0 0.5rem 0;
    i { color: var(--primary); font-size: 1.125rem; }
    @media (max-width: 768px) { font-size: 1.125rem; }
}

.section-subtitle { font-size: 0.875rem; color: var(--text-secondary); margin: 0; font-weight: 500; }

.players-form { display: flex; flex-direction: column; gap: 2rem; }
// Players cube styles
.players-container {
    background: var(--surface-secondary);
    border-radius: 1rem;
    border: 1px solid var(--border-primary);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);

    @media (max-width: 768px) {
        border-radius: 0.75rem;
        padding: var(--spacing-md);
    }
}

.players-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: var(--spacing-lg);

    @media (max-width: 1200px) {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-md);
    }

    @media (max-width: 768px) {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md);
    }

    @media (max-width: 600px) {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    @media (max-width: 400px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
}

.player-cube {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(99, 102, 241, 0.1),
            transparent);
        transition: left 0.5s ease;
    }

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary);

        &::before {
            left: 100%;
        }
    }

    @media (max-width: 768px) {
        min-height: 260px;
        padding: var(--spacing-sm);
    }
}

.cube-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);

    &.player-section, &.position-section {
        flex: 0 0 auto;
    }

    &.stats-section {
        flex: 1;
    }

    &.actions-section {
        flex: 0 0 auto;
        margin-top: auto;
    }
}

.section-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);

    .section-icon {
        color: var(--primary);
        font-size: 0.875rem;
        width: 16px;
        flex-shrink: 0;
    }

    .section-label {
        font-size: var(--text-xs);
        font-weight: var(--font-weight-semibold);
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
}

.cube-select {
    width: 100%;
    font-size: 0.875rem;

    @media (max-width: 768px) {
        font-size: 1rem;
    }
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xs);
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .stat-label {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 10px;
        font-weight: var(--font-weight-semibold);
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.05em;

        i {
            font-size: 10px;
            color: var(--primary);
        }
    }
}

.cube-input {
    width: 100%;
    height: 32px;
    padding: 0.25rem 0.5rem;
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 0.875rem;
    text-align: center;
    font-family: monospace;
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
    }

    &:hover {
        border-color: var(--primary);
    }

    &.rating-input:focus {
        border-color: var(--warning);
        box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.1);
    }

    &.goals-input:focus {
        border-color: var(--success);
        box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.1);
    }

    &.assists-input:focus {
        border-color: var(--info);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }

    @media (max-width: 768px) {
        height: 36px;
        font-size: 1rem;
        padding: 0.5rem;
    }
}

.actions-grid {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-xs);
}

.potm-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    flex: 1;

    .potm-checkbox {
        display: none;
    }

    .potm-custom {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-md);
        transition: all 0.2s ease;
        font-size: 10px;
        font-weight: var(--font-weight-semibold);
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.05em;

        i {
            font-size: 12px;
            color: var(--warning);
        }

        .potm-label {
            @media (max-width: 600px) {
                display: none;
            }
        }
    }

    .potm-checkbox:checked + .potm-custom {
        background: var(--warning-100);
        border-color: var(--warning);
        color: var(--warning-700);

        i {
            color: var(--warning-600);
        }
    }

    &:hover .potm-custom {
        border-color: var(--warning);
        background: var(--warning-50);
    }
}

.remove-cube-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;

    i {
        font-size: 12px;
    }

    &:hover {
        background: var(--danger-100);
        border-color: var(--danger);
        color: var(--danger-600);
    }

    &:active {
        transform: scale(0.95);
    }
}

// Legacy styles for cube select components
.cube-select {
    ::ng-deep .pro-clubs-auto-complete-select .select-container {
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-md);
        transition: all 0.2s ease;
        height: 32px;
        font-size: 0.875rem;

        &:hover {
            border-color: var(--primary);
        }

        &:focus-within {
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
        }

        @media (max-width: 768px) {
            height: 36px;
            font-size: 1rem;
        }
    }
}

// Controls, formation, save, and all remaining styles - ultra compact
.controls-section { margin-top: 2rem; padding-top: 2rem; border-top: 1px solid var(--border-secondary); }

.controls-container {
    display: flex; align-items: center; justify-content: space-between; gap: 2rem;
    @media (max-width: 768px) { flex-direction: column; gap: 1rem; }
}

.control-group {
    display: flex; gap: 0.75rem;
    @media (max-width: 768px) { width: 100%; justify-content: center; }
}

.control-button {
    display: flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1rem; background: var(--surface-secondary); border: 1px solid var(--border-primary); border-radius: 0.5rem; color: var(--text-primary); font-weight: 500; cursor: pointer; transition: all 0.2s ease; box-shadow: var(--shadow-sm);
    &:hover { transform: translateY(-2px); box-shadow: var(--shadow-md); border-color: var(--primary); }
    &:focus { outline: none; border-color: var(--primary); box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1); }
    &.add-player-btn:hover { background: var(--success); border-color: var(--success); color: white; }
    &.add-team-btn:hover { background: var(--info); border-color: var(--info); color: white; }
    &.remove-all-btn:hover { background: var(--danger); border-color: var(--danger); color: white; }
    i { font-size: 0.875rem; }
    @media (max-width: 768px) { flex: 1; justify-content: center; }
}

.formation-group {
    display: flex; flex-direction: column; gap: 0.25rem; min-width: 200px;
    @media (max-width: 768px) { width: 100%; min-width: unset; }
}

.formation-select ::ng-deep .pro-clubs-auto-complete-select .select-container {
    background: var(--surface-primary); border: 1px solid var(--border-primary); border-radius: 0.375rem; transition: all 0.2s ease; height: 45px;
    &:hover { border-color: var(--primary); }
    &:focus-within { border-color: var(--primary); box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1); }
}

.save-section { display: flex; justify-content: center; padding-top: 2rem; border-top: 1px solid var(--border-secondary); }

.save-button {
    display: flex; align-items: center; gap: 0.75rem; padding: 1rem 2rem; background: linear-gradient(135deg, var(--primary), var(--secondary)); border: none; border-radius: 1rem; color: white; font-size: 1.125rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease; box-shadow: var(--shadow-lg); position: relative; overflow: hidden;
    &::before { content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent); transition: left 0.5s ease; }
    &:hover:not(:disabled) { transform: translateY(-3px); box-shadow: var(--shadow-2xl); &::before { left: 100%; } }
    &:focus { outline: none; box-shadow: var(--shadow-2xl), 0 0 0 3px rgba(99, 102, 241, 0.3); }
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        background: var(--surface-tertiary);
        &::before { display: none; }
    }
    &:active { transform: translateY(-1px); }
    i { font-size: 1rem; }
    @media (max-width: 768px) { width: 100%; justify-content: center; padding: 0.75rem 1.5rem; font-size: 1rem; }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 480px) {
    .modify-game-container {
        padding: var(--spacing-sm);
        gap: var(--spacing-md);
    }

    .match-header,
    .players-section {
        padding: var(--spacing-md);
    }

    .score-inputs {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .score-input {
        width: 100%;
        max-width: 120px;
    }

    .team-logo {
        width: 40px;
        height: 40px;
    }

    .team-name {
        font-size: var(--text-sm);
    }



    .control-group {
        flex-direction: column;
        width: 100%;
    }
}

/* === LOADING STATE === */
.modify-game-container {
    &.loading {
        opacity: 0.6;
        pointer-events: none;
    }
}

/* === ACCESSIBILITY === */
.modify-game-container {
    * {
        &:focus {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
        }
    }

    .score-input,
    .stat-input,
    .control-button,
    .save-button {
        &:focus-visible {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
        }
    }

    .checkbox-input:focus-visible + .checkbox-custom {
        outline: 2px solid var(--primary);
        outline-offset: 2px;
    }
}

/* === ANIMATIONS === */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.player-cube {
    animation: slideIn 0.3s ease-out;
}

.match-header,
.players-section {
    animation: slideIn 0.5s ease-out;
}

/* === PRINT STYLES === */
@media print {
    .modify-game-container {
        background: white;
        color: black;
        box-shadow: none;
    }

    .control-button,
    .save-button,
    .remove-button {
        display: none;
    }

    .players-section {
        break-inside: avoid;
    }
}

/* === AI UPLOAD SECTION === */
.ai-upload-section {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-xl);

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
}

.upload-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.upload-header {
    text-align: center;
    margin-bottom: var(--spacing-md);

    i {
        font-size: 2rem;
        color: var(--primary);
        margin-bottom: var(--spacing-sm);
    }

    h3 {
        color: var(--text-primary);
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0 0 var(--spacing-xs) 0;
    }

    p {
        color: var(--text-secondary);
        margin: 0;
        font-size: 0.95rem;
    }
}

.upload-area {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;

    &.processing {
        pointer-events: none;
    }
}

.upload-zone {
    border: 2px dashed var(--border-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-secondary);

    &:hover {
        border-color: var(--primary);
        background: var(--surface-secondary);
        transform: translateY(-2px);
    }

    .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);

        .upload-icon {
            font-size: 3rem;
            color: var(--primary);
            opacity: 0.7;
        }

        h4 {
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        p {
            color: var(--text-secondary);
            margin: 0;
            font-size: 0.95rem;
        }

        .file-types {
            color: var(--text-tertiary);
            font-size: 0.85rem;
            font-style: italic;
        }
    }
}

.image-preview {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--bg-secondary);

    .preview-image {
        width: 100%;
        max-height: 400px;
        object-fit: contain;
        display: block;
    }

    .preview-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        &:hover {
            opacity: 1;
        }
    }

    .preview-actions {
        display: flex;
        gap: var(--spacing-md);

        .action-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;

            &.retry-btn {
                background: var(--primary);
                color: white;

                &:hover:not(:disabled) {
                    background: var(--primary-dark);
                    transform: translateY(-1px);
                }
            }

            &.clear-btn {
                background: var(--danger);
                color: white;

                &:hover:not(:disabled) {
                    background: var(--danger-dark);
                    transform: translateY(-1px);
                }
            }

            &:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }
        }
    }
}

.processing-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .processing-content {
        text-align: center;
        color: white;

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto var(--spacing-md) auto;
        }

        h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0 0 var(--spacing-xs) 0;
        }

        p {
            font-size: 0.95rem;
            opacity: 0.9;
            margin: 0;
        }
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.upload-tips {
    background: var(--surface-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    border-left: 4px solid var(--primary);

    h4 {
        color: var(--text-primary);
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 var(--spacing-md) 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        i {
            color: var(--primary);
        }
    }

    ul {
        margin: 0;
        padding-left: var(--spacing-lg);
        color: var(--text-secondary);

        li {
            margin-bottom: var(--spacing-xs);
            font-size: 0.9rem;
            line-height: 1.5;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

/* === TOAST DEMO SECTION === */
.toast-demo-section {
    background: var(--surface-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    border-left: 4px solid var(--accent-primary);
    margin-top: var(--spacing-lg);

    h4 {
        color: var(--text-primary);
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 var(--spacing-md) 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        i {
            color: var(--accent-primary);
        }
    }
}

.toast-demo-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);

    .demo-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
        border: none;
        border-radius: var(--radius-sm);
        font-size: 0.85rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        &.success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        &.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        &.info {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
        }

        &.ai {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
        }

        i {
            font-size: 0.8rem;
        }
    }

    @media (max-width: 768px) {
        flex-direction: column;

        .demo-btn {
            justify-content: center;
            padding: var(--spacing-sm) var(--spacing-md);
        }
    }
}

/* === AI ANALYSIS STYLES === */
.ai-confidence-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;

  &.confidence-high {
    background: rgba(var(--success-rgb), 0.1);
    color: var(--success);
    border: 1px solid rgba(var(--success-rgb), 0.2);
  }

  &.confidence-medium {
    background: rgba(var(--warning-rgb), 0.1);
    color: var(--warning);
    border: 1px solid rgba(var(--warning-rgb), 0.2);
  }

  &.confidence-low {
    background: rgba(var(--danger-rgb), 0.1);
    color: var(--danger);
    border: 1px solid rgba(var(--danger-rgb), 0.2);
  }
}

.ai-match-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xs);
  font-size: 0.8rem;
  color: var(--text-secondary);

  .extracted-name {
    font-style: italic;
    color: var(--text-tertiary);
  }

  .match-arrow {
    color: var(--primary);
  }
}

.player-of-match-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--warning);
  font-weight: 600;

  i {
    color: var(--warning);
    animation: sparkle 2s ease-in-out infinite;
  }
}