import { Request, Response, NextFunction } from "express";
import { inject, injectable } from "tsyringe";
import { ICommentService } from "../services/comment-service";

@injectable()
export class CommentController {
  
  constructor(
    @inject("ICommentService") private commentService: ICommentService
  ) {}

  async createComment(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { gameId } = req.params;
    const { content, parentCommentId } = req.body;
    const userId = req.user!.id;

    try {
      const comment = await this.commentService.createComment(
        gameId, 
        userId, 
        content, 
        parentCommentId
      );

      res.status(201).json({ 
        success: true, 
        data: comment 
      });
    } catch (error: any) {
      next(error);
    }
  }

  async updateComment(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { commentId } = req.params;
    const { content } = req.body;
    const userId = req.user!.id;

    try {
      const comment = await this.commentService.updateComment(commentId, userId, content);
      
      res.status(200).json({ 
        success: true, 
        data: comment 
      });
    } catch (error: any) {
      next(error);
    }
  }

  async deleteComment(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { commentId } = req.params;
    const userId = req.user!.id;

    try {
      await this.commentService.deleteComment(commentId, userId);
      
      res.status(200).json({ 
        success: true, 
        message: "Comment deleted successfully" 
      });
    } catch (error: any) {
      next(error);
    }
  }

  async getCommentsByGame(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { gameId } = req.params;
    const { page, limit } = req.query;
    const userId = req.user?.id; // Optional for non-authenticated users

    try {
      const comments = await this.commentService.getCommentsByGame(
        gameId,
        userId,
        page ? parseInt(page as string) : undefined,
        limit ? parseInt(limit as string) : undefined
      );
      
      res.status(200).json({ 
        success: true, 
        data: comments 
      });
    } catch (error: any) {
      next(error);
    }
  }

  async likeComment(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { commentId } = req.params;
    const userId = req.user!.id;

    try {
      const comment = await this.commentService.likeComment(commentId, userId);
      
      res.status(200).json({ 
        success: true, 
        data: comment 
      });
    } catch (error: any) {
      next(error);
    }
  }

  async unlikeComment(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { commentId } = req.params;
    const userId = req.user!.id;

    try {
      const comment = await this.commentService.unlikeComment(commentId, userId);
      
      res.status(200).json({ 
        success: true, 
        data: comment 
      });
    } catch (error: any) {
      next(error);
    }
  }

  async getUserComments(req: Request, res: Response, next: NextFunction): Promise<void> {
    const userId = req.user!.id;
    const { limit } = req.query;

    try {
      const comments = await this.commentService.getUserComments(
        userId, 
        limit ? parseInt(limit as string) : undefined
      );
      
      res.status(200).json({ 
        success: true, 
        data: comments 
      });
    } catch (error: any) {
      next(error);
    }
  }
}
