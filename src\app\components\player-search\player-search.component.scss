/* === MODERN PLAYER SEARCH DESIGN === */

.player-search-container {
    min-height: 100vh;
    background: var(--bg-primary);
    padding: var(--spacing-lg) 5px;
    font-family: var(--font-sans);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;

    @media (max-width: 768px) {
        padding: var(--spacing-md) 5px;
        gap: var(--spacing-md);
    }
}

.player-search-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    min-height: 60px;

    .back-button {
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-sm) var(--spacing-md);
        color: var(--text-primary);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--text-sm);
        font-weight: var(--font-weight-medium);

        &:hover {
            background: var(--surface-tertiary);
            border-color: var(--border-secondary);
            transform: translateY(-1px);
        }

        i {
            font-size: var(--text-xs);
        }
    }

    .search-title {
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        i {
            color: var(--primary);
            font-size: var(--text-base);
        }

        @media (max-width: 768px) {
            font-size: var(--text-lg);
        }
    }
}

.search-section {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);

    .search-input-group {
        display: flex;
        gap: var(--spacing-md);
        align-items: stretch;

        @media (max-width: 768px) {
            gap: var(--spacing-sm);
        }

        .search-input-wrapper {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;

            .search-icon {
                position: absolute;
                left: var(--spacing-md);
                color: var(--text-tertiary);
                font-size: var(--text-sm);
                z-index: 1;
            }

            .search-input {
                width: 100%;
                background: var(--surface-secondary);
                border: 2px solid var(--border-primary);
                border-radius: var(--radius-lg);
                padding: var(--spacing-md) var(--spacing-lg);
                padding-left: calc(var(--spacing-lg) + var(--spacing-xl));
                font-size: var(--text-base);
                color: var(--text-primary);
                transition: all 0.3s ease;
                font-family: var(--font-sans);

                &::placeholder {
                    color: var(--text-tertiary);
                }

                &:focus {
                    outline: none;
                    border-color: var(--primary);
                    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
                    background: var(--surface-primary);
                }

                &:hover:not(:focus) {
                    border-color: var(--border-secondary);
                }
            }
        }

        .search-button {
            background: linear-gradient(135deg, var(--primary), var(--accent-primary));
            border: none;
            border-radius: var(--radius-lg);
            padding: var(--spacing-md) var(--spacing-xl);
            color: var(--text-inverse);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            font-size: var(--text-base);
            font-weight: var(--font-weight-semibold);
            white-space: nowrap;
            min-height: 48px;
            box-shadow: var(--shadow-sm);

            &:hover:not(:disabled) {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            }

            &:disabled {
                background: var(--surface-tertiary);
                color: var(--text-tertiary);
                cursor: not-allowed;
                opacity: 0.6;
                transform: none;
                box-shadow: none;
            }

            @media (max-width: 768px) {
                padding: var(--spacing-md) var(--spacing-lg);
                font-size: var(--text-base);
                font-weight: var(--font-weight-bold);
                min-height: 48px;
                border-radius: var(--radius-lg);
                box-shadow: var(--shadow-md);
                flex-shrink: 0;

                &:hover:not(:disabled) {
                    transform: translateY(-1px);
                    box-shadow: var(--shadow-lg);
                }

                span {
                    display: none;
                }

                i {
                    margin: 0;
                }
            }
        }
    }
}

.results-section {
    flex: 1;
    display: flex;
    flex-direction: column;

    .results-content {
        background: var(--surface-primary);
        border-radius: var(--radius-xl);
        border: 1px solid var(--border-primary);
        box-shadow: var(--shadow-md);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: 100%;

        .results-header {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-primary);
            background: var(--surface-secondary);

            .results-title {
                font-size: var(--text-lg);
                font-weight: var(--font-weight-semibold);
                color: var(--text-primary);
                margin: 0;
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);

                i {
                    color: var(--primary);
                }

                .results-count {
                    color: var(--text-secondary);
                    font-weight: var(--font-weight-normal);
                    font-size: var(--text-base);
                }
            }
        }

        .results-table {
            flex: 1;
            overflow: auto;
            max-height: 500px;
        }
    }

    .no-results {
        background: var(--surface-primary);
        border-radius: var(--radius-xl);
        border: 1px solid var(--border-primary);
        box-shadow: var(--shadow-md);
        padding: var(--spacing-2xl);
        text-align: center;

        .no-results-content {
            .no-results-icon {
                font-size: var(--text-4xl);
                color: var(--text-tertiary);
                margin-bottom: var(--spacing-lg);
            }

            .no-results-title {
                font-size: var(--text-xl);
                font-weight: var(--font-weight-semibold);
                color: var(--text-primary);
                margin: 0 0 var(--spacing-sm) 0;
            }

            .no-results-text {
                font-size: var(--text-base);
                color: var(--text-secondary);
                margin: 0;
            }
        }
    }
}