import { AdvancedPlayersStats, TopAssister, TopAvgRating, TopScorer } from "@pro-clubs-manager/shared-dtos";
import { Types } from "mongoose";
import { AllTimeTopAvgRatingByPosition, MostHattricks, MostCleanSheets, MostWinningPercentageTeam, MostWinningPercentagePlayer } from "../../repositories/game-repository";

export interface ILeagueStatsService {
  getLeagueTopScorers(leagueId: string | Types.ObjectId, limit?: number): Promise<TopScorer[]>;
  getLeagueTopAssisters(leagueId: string | Types.ObjectId, limit?: number): Promise<TopAssister[]>;
  getAllTimeTopScorers(leagueId: string | Types.ObjectId, limit?: number): Promise<TopScorer[]>;
  getAllTimeTopAssisters(leagueId: string | Types.ObjectId, limit?: number): Promise<TopAssister[]>;
  getLeagueTopAvgRatingPlayers(leagueId: string | Types.ObjectId, limit?: number): Promise<TopAvgRating[]>;
  getAdvancedLeaguePlayersStats(leagueId: string | Types.ObjectId, limit?: number): Promise<AdvancedPlayersStats>;
  getAdvancedLeagueTeamStats(leagueId: string | Types.ObjectId): Promise<any>;

  // New all-time statistics methods
  getAllTimeTopAvgRatingByPosition(leagueId: string | Types.ObjectId, position: string, minimumGames: number, limit?: number): Promise<AllTimeTopAvgRatingByPosition[]>;
  getMostHattricks(leagueId: string | Types.ObjectId, limit?: number): Promise<MostHattricks[]>;
  getMostCleanSheets(leagueId: string | Types.ObjectId, limit?: number): Promise<MostCleanSheets[]>;
  getMostWinningPercentageTeams(leagueId: string | Types.ObjectId, minimumGames?: number, limit?: number): Promise<MostWinningPercentageTeam[]>;
  getMostWinningPercentagePlayers(leagueId: string | Types.ObjectId, minimumGames?: number, limit?: number): Promise<MostWinningPercentagePlayer[]>;
}
