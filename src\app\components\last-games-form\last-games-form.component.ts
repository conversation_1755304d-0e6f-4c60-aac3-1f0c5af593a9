import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { PlayerGameForm, PlayerLastGamesForm } from '@pro-clubs-manager/shared-dtos';
import { PlayerService } from '../../services/player.service';
import { AgChartOptions } from 'ag-charts-community';
import { PlayerDataStateService } from '../../services/state/player-data-state.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'last-games-form',
  templateUrl: './last-games-form.component.html',
  styleUrl: './last-games-form.component.scss'
})
export class LastGamesFormComponent implements OnInit, OnDestroy {

  lastGamesForm: PlayerLastGamesForm | null = null;
  ratingChartOptions: AgChartOptions = {};
  isLoading: boolean = false;
  @Input() playerId: string = '';
  @Input() teamId: string = '';

  private stateSubscription?: Subscription;

  constructor(
    private playerService: PlayerService,
    private playerDataState: PlayerDataStateService
  ) { }

  ngOnInit() {
    console.log('LastGamesFormComponent initialized with playerId:', this.playerId);

    // Subscribe to state changes
    this.stateSubscription = this.playerDataState.state$.subscribe(state => {
      console.log('State updated for player:', this.playerId);
      const form = state.playerForms.get(this.playerId);
      console.log('Player form from state:', form);

      if (form) {
        this.lastGamesForm = form;
        console.log('Setting lastGamesForm:', this.lastGamesForm);
        this.loadGraphData();
      }
      this.isLoading = state.loadingForms.has(this.playerId);
      console.log('Loading state:', this.isLoading);
    });

    this.loadPlayerLastGamesForm();
  }

  ngOnDestroy() {
    if (this.stateSubscription) {
      this.stateSubscription.unsubscribe();
    }
  }

  async loadPlayerLastGamesForm() {
    console.log('Loading player form for playerId:', this.playerId);

    if (!this.playerId) {
      console.error('No playerId provided to loadPlayerLastGamesForm');
      return;
    }

    try {
      console.log('Calling playerService.getPlayerForm...');
      const result = await this.playerService.getPlayerForm(this.playerId);
      console.log('Player form API result:', result);
      // The state is automatically updated by the service
    } catch (error: any) {
      console.error('Error loading player form:', error);
      console.error('Error details:', {
        status: error?.status,
        message: error?.message,
        error: error?.error
      });
    }
  }

  loadGraphData() {
    if (!this.lastGamesForm || !this.lastGamesForm.lastGames || this.lastGamesForm.lastGames.length === 0) {
      console.log('No game data available for chart');
      this.ratingChartOptions = {};
      return;
    }

    console.log('Loading chart data:', this.lastGamesForm.lastGames);

    // Validate data structure
    const validData = this.lastGamesForm.lastGames.filter(game =>
      game && typeof game.rating === 'number' && typeof game.round === 'number'
    );

    if (validData.length === 0) {
      console.error('No valid chart data found - all games missing rating or round data');
      this.ratingChartOptions = {};
      return;
    }

    console.log('Valid chart data:', validData);

    this.ratingChartOptions = {
      data: validData,
      background: {
        visible: true,
        fill: 'transparent'
      },
      padding: {
        top: 20,
        right: 20,
        bottom: 40,
        left: 50
      },
      height: 320,
      series: [{
        type: "line",
        xKey: "round",
        yKey: "rating",
        stroke: '#6366f1',
        strokeWidth: 3,
        marker: {
          enabled: true,
          shape: 'circle',
          size: 8,
          fill: '#6366f1',
          stroke: '#ffffff',
          strokeWidth: 2
        },
        highlightStyle: {
          item: {
            fill: '#4f46e5',
            stroke: '#ffffff',
            strokeWidth: 3
          }
        },
        tooltip: {
          renderer: (tooltipParams: any) => {
            const rating = tooltipParams.datum.rating;
            const ratingColor = rating >= 8 ? '#10b981' : rating >= 7 ? '#f59e0b' : rating >= 6 ? '#ef4444' : '#6b7280';
            return {
              content: `
                <div style="padding: 12px; font-family: 'Inter', sans-serif;">
                  <div style="font-weight: 600; color: #1f2937; margin-bottom: 8px;">
                    ${this.getTeams(tooltipParams.datum)}
                  </div>
                  <div style="color: #6b7280; font-size: 12px; margin-bottom: 8px;">
                    ${this.getScores(tooltipParams.datum)}
                  </div>
                  <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                    <span style="color: #374151;">Rating:</span>
                    <span style="font-weight: 600; color: ${ratingColor};">${rating}</span>
                  </div>
                  <div style="color: #6b7280; font-size: 12px;">
                    Position: ${tooltipParams.datum.positionPlayed} • Goals: ${tooltipParams.datum.goals} • Assists: ${tooltipParams.datum.assists}
                  </div>
                </div>
              `,
              title: `Fixture ${tooltipParams.datum.round}`
            };
          },
        }
      }],
      axes: [
        {
          type: 'category',
          position: 'bottom'
        },
        {
          type: 'number',
          position: 'left',
          min: 5,
          max: 10
        }
      ]
    } as any;
  };

  getTeams(playerGameForm: PlayerGameForm): string {
    return `${playerGameForm.homeTeam.name} vs ${playerGameForm.awayTeam.name}`;
  }

  getScores(playerGameForm: PlayerGameForm): string {
    return `${playerGameForm.result.homeTeamGoals}:${playerGameForm.result.awayTeamGoals}`;
  }

  getAverageRating(): string {
    if (!this.lastGamesForm || !this.lastGamesForm.lastGames.length) return '0.0';
    const total = this.lastGamesForm.lastGames.reduce((sum, game) => sum + game.rating, 0);
    return (total / this.lastGamesForm.lastGames.length).toFixed(1);
  }

  getTotalGoals(): number {
    if (!this.lastGamesForm || !this.lastGamesForm.lastGames.length) return 0;
    return this.lastGamesForm.lastGames.reduce((sum, game) => sum + game.goals, 0);
  }

  getTotalAssists(): number {
    if (!this.lastGamesForm || !this.lastGamesForm.lastGames.length) return 0;
    return this.lastGamesForm.lastGames.reduce((sum, game) => sum + game.assists, 0);
  }

  getDebugInfo(): string {
    return JSON.stringify({
      playerId: this.playerId,
      hasLastGamesForm: !!this.lastGamesForm,
      lastGamesLength: this.lastGamesForm?.lastGames?.length || 0,
      lastGames: this.lastGamesForm?.lastGames || [],
      hasChartOptions: !!this.ratingChartOptions,
      chartOptionsKeys: Object.keys(this.ratingChartOptions || {}),
      isLoading: this.isLoading
    }, null, 2);
  }
}
