<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h1 class="login-title">Login</h1>
            <p class="login-subtitle">Sign in to your Pro Clubs account</p>
        </div>

        <form [formGroup]="loginFormGroup" (ngSubmit)="onSubmit()" class="login-form">
            <div class="form-group" *ngFor="let control of formControls">
                <label class="form-label">
                    {{ control.displayText }}
                    <span class="required-indicator" *ngIf="isRequiredForm(control.control)">*</span>
                </label>
                <input
                    *ngIf="control.type === 'email'"
                    [formControlName]="control.field"
                    type="email"
                    [placeholder]="'Enter your ' + control.displayText.toLowerCase()"
                    class="form-input"
                    [class.error]="control.control.invalid && control.control.touched">
                <input
                    *ngIf="control.type === 'password'"
                    [formControlName]="control.field"
                    type="password"
                    [placeholder]="'Enter your ' + control.displayText.toLowerCase()"
                    class="form-input"
                    [class.error]="control.control.invalid && control.control.touched">

                <!-- Error messages -->
                <div *ngIf="control.control.invalid && control.control.touched" class="error-message">
                    <span *ngIf="isRequiredForm(control.control)">{{ control.displayText }} is required</span>
                    <span *ngIf="isEmailInvalid(control.control)">Please enter a valid email address</span>
                </div>
            </div>

            <button
                type="submit"
                class="submit-button"
                [disabled]="loginFormGroup.invalid || isLoading">
                <span *ngIf="!isLoading">Login</span>
                <span *ngIf="isLoading">Logging in...</span>
            </button>
        </form>

        <div class="divider">or</div>

        <!-- Google Sign-In Button Container -->
        <div id="google-signin-button" class="google-signin-container"></div>

        <div class="signup-link">
            <p>Don't have an account?
                <a (click)="navigateToSignUp()" class="link">Sign up here</a>
            </p>
        </div>

        <div class="browse-link">
            <p>Want to explore first?
                <a routerLink="/dashboard" class="link">Browse without signing up</a>
            </p>
        </div>
    </div>
</div>