import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ApiService } from './api.service';

export interface TransferRequest {
  id: string;
  playerId: string;
  fromTeamId: string;
  toTeamId: string;
  requestedBy: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  requestedAt: Date;
  processedAt?: Date;
  processedBy?: string;
  reason?: string;
  message?: string;
  expiresAt: Date;
  player?: {
    id: string;
    name: string;
    position: string;
    imgUrl?: string;
  };
  fromTeam?: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  toTeam?: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  requester?: {
    firstName: string;
    lastName: string;
    email: string;
  };
  processor?: {
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreateTransferRequestData {
  playerId: string;
  fromTeamId: string;
  toTeamId: string;
  message?: string;
}

export interface ProcessTransferRequestData {
  action: 'approve' | 'reject';
  reason?: string;
}

@Injectable({
  providedIn: 'root'
})
export class TransferRequestService {
  private transferRequestsSubject = new BehaviorSubject<TransferRequest[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);

  public transferRequests$ = this.transferRequestsSubject.asObservable();
  public loading$ = this.loadingSubject.asObservable();

  constructor(private apiService: ApiService) {}

  async createTransferRequest(data: CreateTransferRequestData): Promise<TransferRequest> {
    try {
      this.loadingSubject.next(true);
      const response = await this.apiService.post<TransferRequest>('transfer-requests', data);
      
      // Add to local state
      const currentRequests = this.transferRequestsSubject.value;
      this.transferRequestsSubject.next([response.data, ...currentRequests]);
      
      return response.data;
    } catch (error) {
      console.error('Error creating transfer request:', error);
      throw error;
    } finally {
      this.loadingSubject.next(false);
    }
  }

  async processTransferRequest(requestId: string, data: ProcessTransferRequestData): Promise<TransferRequest> {
    try {
      this.loadingSubject.next(true);
      const response = await this.apiService.put<TransferRequest>(`transfer-requests/${requestId}/process`, data);
      
      // Update local state
      const currentRequests = this.transferRequestsSubject.value;
      const updatedRequests = currentRequests.map(req => 
        req.id === requestId ? response.data : req
      );
      this.transferRequestsSubject.next(updatedRequests);
      
      return response.data;
    } catch (error) {
      console.error('Error processing transfer request:', error);
      throw error;
    } finally {
      this.loadingSubject.next(false);
    }
  }

  async getTransferRequestsByTeam(teamId: string, status?: string): Promise<TransferRequest[]> {
    try {
      this.loadingSubject.next(true);
      const params = status ? { status } : {};
      const response = await this.apiService.get<TransferRequest[]>(`transfer-requests/team/${teamId}`, { params });
      
      this.transferRequestsSubject.next(response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching transfer requests:', error);
      throw error;
    } finally {
      this.loadingSubject.next(false);
    }
  }

  async getTransferRequestById(requestId: string): Promise<TransferRequest> {
    try {
      const response = await this.apiService.get< TransferRequest>(`transfer-requests/${requestId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching transfer request:', error);
      throw error;
    }
  }

  async cancelTransferRequest(requestId: string): Promise<void> {
    try {
      this.loadingSubject.next(true);
      await this.apiService.delete(`transfer-requests/${requestId}`);
      
      // Remove from local state
      const currentRequests = this.transferRequestsSubject.value;
      const filteredRequests = currentRequests.filter(req => req.id !== requestId);
      this.transferRequestsSubject.next(filteredRequests);
    } catch (error) {
      console.error('Error cancelling transfer request:', error);
      throw error;
    } finally {
      this.loadingSubject.next(false);
    }
  }

  async cleanupExpiredRequests(): Promise<{ count: number }> {
    try {
      const response = await this.apiService.post< { count: number }>('transfer-requests/cleanup/expired', {});
      return response.data;
    } catch (error) {
      console.error('Error cleaning up expired requests:', error);
      throw error;
    }
  }

  // Helper methods
  getCurrentTransferRequests(): TransferRequest[] {
    return this.transferRequestsSubject.value;
  }

  clearTransferRequests(): void {
    this.transferRequestsSubject.next([]);
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'pending': return 'warning';
      case 'approved': return 'success';
      case 'rejected': return 'danger';
      case 'cancelled': return 'secondary';
      default: return 'primary';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'pending': return 'fas fa-clock';
      case 'approved': return 'fas fa-check-circle';
      case 'rejected': return 'fas fa-times-circle';
      case 'cancelled': return 'fas fa-ban';
      default: return 'fas fa-question-circle';
    }
  }

  isExpired(request: TransferRequest): boolean {
    return new Date() > new Date(request.expiresAt);
  }

  canProcess(request: TransferRequest): boolean {
    return request.status === 'pending' && !this.isExpired(request);
  }

  canCancel(request: TransferRequest): boolean {
    return request.status === 'pending' && !this.isExpired(request);
  }

  getTimeRemaining(request: TransferRequest): string {
    const now = new Date();
    const expires = new Date(request.expiresAt);
    const diff = expires.getTime() - now.getTime();

    if (diff <= 0) {
      return 'Expired';
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) {
      return `${days}d ${hours}h remaining`;
    } else if (hours > 0) {
      return `${hours}h remaining`;
    } else {
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      return `${minutes}m remaining`;
    }
  }
}
