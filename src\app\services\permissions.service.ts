import { Injectable } from '@angular/core';
import { AuthService, User } from './auth.service';
import { TeamService } from './team.service';
import { GameService } from './game.service';
import { PlayerService } from './player.service';
import { Observable, map, combineLatest, from, of } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class PermissionsService {

  constructor(
    private authService: AuthService,
    private teamService: TeamService,
    private gameService: GameService,
    private playerService: PlayerService
  ) {}

  /**
   * Check if current user can edit a specific player's profile
   */
  async canEditPlayer(playerId: string): Promise<boolean> {
    const user = this.authService.getCurrentUser();
    
    if (!user) {
      return false;
    }

    // Admins can edit any player
    if (user.role === 'admin') {
      return true;
    }

    // Check if user owns this player
    try {
      return await this.authService.checkPlayerOwnership(playerId);
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if current user can edit match stats (team captain only)
   */
  canEditMatchStats(): Observable<boolean> {
    return combineLatest([
      this.authService.isAuthenticated$,
      this.authService.currentUser$
    ]).pipe(
      map(([isAuth, user]) => {
        if (!isAuth || !user) {
          return false;
        }

        // Admins can edit match stats
        if (user.role === 'admin') {
          return true;
        }

        // Check if user has associated players (potential captains)
        return user.associatedPlayers && user.associatedPlayers.length > 0;
      })
    );
  }

  /**
   * Check if current user can edit match stats for a specific game
   */
  canEditMatchStatsForGame(gameId: string): Observable<boolean> {
    return this.canEditGame(gameId);
  }

  /**
   * Check if current user is admin
   */
  isAdmin(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => user?.role === 'admin' || false)
    );
  }

  /**
   * Check if current user is authenticated
   */
  isAuthenticated(): Observable<boolean> {
    return this.authService.isAuthenticated$;
  }

  /**
   * Check if current user can create/manage teams (admin only)
   */
  canManageTeams(): Observable<boolean> {
    return this.isAdmin();
  }

  /**
   * Check if current user can create/manage players (admin only for global management)
   */
  canManagePlayersGlobally(): Observable<boolean> {
    return this.isAdmin();
  }

  /**
   * Check if current user can create/manage fixtures (admin only)
   */
  canManageFixtures(): Observable<boolean> {
    return this.isAdmin();
  }

  /**
   * Check if current user can create/manage news (admin only)
   */
  canManageNews(): Observable<boolean> {
    return this.isAdmin();
  }

  /**
   * Check if current user can view admin-only content
   */
  canViewAdminContent(): Observable<boolean> {
    return this.isAdmin();
  }

  /**
   * Get user's associated player IDs
   */
  getUserPlayerIds(): string[] {
    const user = this.authService.getCurrentUser();
    return user?.associatedPlayers || [];
  }

  /**
   * Check if user has any associated players
   */
  hasAssociatedPlayers(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => (user?.associatedPlayers?.length || 0) > 0)
    );
  }

  /**
   * Check if current user can edit a specific team (admin or team captain)
   */
  canEditTeam(teamId: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      switchMap(user => {
        if (!user) return of(false);
        if (user.role === 'admin') return of(true);
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) return of(false);

        // Check if user is captain of this team
        return from(this.teamService.getTeamById(teamId)).pipe(
          map(team => !!(team.captain && user.associatedPlayers!.includes(team.captain.id))),
          catchError(() => of(false))
        );
      }),
      catchError(() => of(false))
    );
  }

  /**
   * Check if current user can edit game stats (admin or captain of one of the teams)
   */
  canEditGame(gameId: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      switchMap(user => {
        if (!user) return of(false);
        if (user.role === 'admin') return of(true);
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) return of(false);

        // Check if user is captain of one of the teams in the game
        return from(this.gameService.getGameById(gameId)).pipe(
          switchMap(game => {
            return from(Promise.all([
              this.teamService.getTeamById(game.homeTeam.id),
              this.teamService.getTeamById(game.awayTeam.id)
            ])).pipe(
              map(([homeTeam, awayTeam]) => {
                const isHomeCaptain = !!(homeTeam.captain && user.associatedPlayers!.includes(homeTeam.captain.id));
                const isAwayCaptain = !!(awayTeam.captain && user.associatedPlayers!.includes(awayTeam.captain.id));
                return isHomeCaptain || isAwayCaptain;
              })
            );
          }),
          catchError(() => of(false))
        );
      }),
      catchError(() => of(false))
    );
  }

  /**
   * Check if current user is captain of any team
   */
  isTeamCaptain(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      switchMap(user => {
        if (!user) return of(false);
        if (user.role === 'admin') return of(true);
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) return of(false);

        return from(this.teamService.getAllTeams()).pipe(
          map(allTeams => allTeams.some(team =>
            !!(team.captain && user.associatedPlayers!.includes(team.captain.id))
          )),
          catchError(() => of(false))
        );
      }),
      catchError(() => of(false))
    );
  }

  /**
   * Check if current user can manage players (admin or team captain)
   */
  canManagePlayers(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      switchMap(user => {
        if (!user) return of(false);
        if (user.role === 'admin') return of(true);
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) return of(false);

        // Team captains can manage players for their teams
        return from(this.teamService.getAllTeams()).pipe(
          map(allTeams => allTeams.some(team =>
            !!(team.captain && user.associatedPlayers!.includes(team.captain.id))
          )),
          catchError(() => of(false))
        );
      }),
      catchError(() => of(false))
    );
  }

  /**
   * Check if current user can set team captains (admin only)
   */
  canSetCaptains(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => user?.role === 'admin' || false)
    );
  }

  /**
   * Check if current user can remove a player from their team
   * Only admin or team captain of the player's team can remove players
   */
  canRemovePlayerFromTeam(playerId: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      switchMap(user => {
        if (!user) return of(false);
        if (user.role === 'admin') return of(true);
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) return of(false);

        // Get the player to find their team
        return from(this.playerService.getPlayerById(playerId)).pipe(
          switchMap(player => {
            if (!player.team) return of(false); // Player not in a team

            // Check if user is captain of the player's team
            return from(this.teamService.getTeamById(player.team.id)).pipe(
              map(team => !!(team.captain && user.associatedPlayers!.includes(team.captain.id))),
              catchError(() => of(false))
            );
          }),
          catchError(() => of(false))
        );
      }),
      catchError(() => of(false))
    );
  }
}
