/* === MODERN LAST GAMES FORM DESIGN === */

.last-games-form-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    font-family: var(--font-sans);
    position: relative;
    animation: fadeInUp 0.6s ease-out;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    }

    @media (max-width: 768px) {
        gap: var(--spacing-md);
    }
}

/* === HEADER SECTION === */
.form-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-bottom: 1px solid var(--border-primary);

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }
}

.header-content {
    flex: 1;

    .form-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;

        i {
            color: var(--primary);
            font-size: var(--text-base);
        }

        @media (max-width: 768px) {
            font-size: var(--text-base);
        }
    }

    .form-subtitle {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin: 0;
        font-weight: var(--font-weight-medium);
    }
}

.games-count {
    background: var(--surface-tertiary);
    color: var(--text-tertiary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-semibold);
    border: 1px solid var(--border-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;

    @media (max-width: 768px) {
        align-self: flex-start;
    }
}

/* === CHART SECTION === */
.chart-section {
    padding: var(--spacing-md);
    background: var(--surface-primary);

    @media (max-width: 768px) {
        padding: var(--spacing-sm);
    }
}

.chart-container {
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    overflow: hidden;
    min-height: 340px;
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: scaleIn 0.5s ease-out 0.2s both;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
        pointer-events: none;
    }

    /* Ensure chart takes full container size */
    ::ng-deep {
        ag-charts-angular {
            display: block;
            width: 100% !important;
            height: 300px !important;
        }

        .ag-chart-wrapper {
            background: var(--surface-secondary) !important;
            width: 100% !important;
            height: 100% !important;
        }

        .ag-chart {
            background: var(--surface-secondary) !important;
            width: 100% !important;
            height: 100% !important;
        }

        .ag-chart-canvas {
            width: 100% !important;
            height: 100% !important;
        }

        .ag-chart-title {
            color: var(--text-primary) !important;
            font-family: var(--font-sans) !important;
        }

        .ag-chart-axis-label {
            color: var(--text-secondary) !important;
            font-family: var(--font-sans) !important;
        }

        .ag-chart-axis-line {
            stroke: var(--border-primary) !important;
        }

        .ag-chart-axis-tick {
            stroke: var(--border-secondary) !important;
        }

        .ag-chart-series-line {
            stroke: var(--primary) !important;
            stroke-width: 3px !important;
        }

        .ag-chart-series-marker {
            fill: var(--primary) !important;
            stroke: var(--surface-primary) !important;
            stroke-width: 2px !important;
        }

        .ag-chart-tooltip {
            background: var(--surface-primary) !important;
            border: 1px solid var(--border-primary) !important;
            border-radius: var(--radius-md) !important;
            color: var(--text-primary) !important;
            font-family: var(--font-sans) !important;
            box-shadow: var(--shadow-lg) !important;
            padding: var(--spacing-sm) !important;
        }

        .ag-chart-tooltip-title {
            color: var(--text-primary) !important;
            font-weight: var(--font-weight-bold) !important;
            margin-bottom: var(--spacing-xs) !important;
        }

        .ag-chart-tooltip-content {
            color: var(--text-secondary) !important;
            line-height: 1.4 !important;
        }
    }

    @media (max-width: 768px) {
        min-height: 250px;

        ::ng-deep ag-charts-angular {
            height: 250px !important;
        }
    }

    @media (max-width: 480px) {
        min-height: 220px;

        ::ng-deep ag-charts-angular {
            height: 220px !important;
        }
    }
}

.no-chart-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 340px;
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);

    i {
        font-size: 3rem;
        margin-bottom: var(--spacing-md);
        opacity: 0.5;
    }

    p {
        font-size: var(--text-lg);
        margin: 0;
    }

    .debug-info {
        margin-top: var(--spacing-md);
        padding: var(--spacing-sm);
        background: var(--surface-tertiary);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-secondary);
        max-width: 90%;

        details {
            summary {
                cursor: pointer;
                font-size: var(--text-xs);
                color: var(--text-secondary);
                padding: var(--spacing-xs);
                background: var(--surface-primary);
                border-radius: var(--radius-sm);

                &:hover {
                    background: var(--surface-hover);
                }
            }

            pre {
                margin-top: var(--spacing-xs);
                padding: var(--spacing-sm);
                background: var(--surface-primary);
                border-radius: var(--radius-sm);
                font-size: var(--text-xs);
                color: var(--text-tertiary);
                font-family: monospace;
                overflow-x: auto;
                white-space: pre-wrap;
                max-height: 200px;
                overflow-y: auto;
                text-align: left;
            }
        }
    }

    @media (max-width: 768px) {
        min-height: 250px;

        i {
            font-size: 2.5rem;
        }

        p {
            font-size: var(--text-base);
        }
    }
}

/* === GAMES SUMMARY === */
.games-summary {
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-top: 1px solid var(--border-primary);

    @media (max-width: 768px) {
        padding: var(--spacing-md);
    }
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    @media (max-width: 480px) {
        gap: var(--spacing-sm);
    }
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: fadeInLeft 0.6s ease-out;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
        transition: left 0.6s ease;
    }

    &:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-300);

        &::before {
            left: 100%;
        }
    }

    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }

    .stat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: var(--primary-100);
        border-radius: var(--radius-lg);
        color: var(--primary);
        font-size: var(--text-base);
        flex-shrink: 0;

        @media (max-width: 480px) {
            width: 36px;
            height: 36px;
            font-size: var(--text-sm);
        }
    }

    .stat-content {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);

        .stat-value {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            line-height: 1;

            @media (max-width: 480px) {
                font-size: var(--text-base);
            }
        }

        .stat-label {
            font-size: var(--text-xs);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
    }
}

/* === LOADING STATE === */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    min-height: 300px;
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);

    p {
        color: var(--text-secondary);
        font-size: var(--text-base);
        margin: 0;
    }
}

/* === NO DATA STATE === */
.no-data-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);

    .no-data-content {
        text-align: center;
        color: var(--text-secondary);

        i {
            font-size: 4rem;
            margin-bottom: var(--spacing-lg);
            opacity: 0.5;
            color: var(--text-tertiary);
        }

        h3 {
            font-size: var(--text-xl);
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }

        p {
            font-size: var(--text-base);
            margin-bottom: var(--spacing-lg);
            max-width: 300px;
        }

        .debug-info {
            margin-top: var(--spacing-md);
            padding: var(--spacing-sm);
            background: var(--surface-secondary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-secondary);

            small {
                font-size: var(--text-xs);
                color: var(--text-tertiary);
                font-family: monospace;
            }

            details {
                margin-top: var(--spacing-sm);

                summary {
                    cursor: pointer;
                    font-size: var(--text-xs);
                    color: var(--text-secondary);
                    padding: var(--spacing-xs);
                    background: var(--surface-tertiary);
                    border-radius: var(--radius-sm);

                    &:hover {
                        background: var(--surface-hover);
                    }
                }

                pre {
                    margin-top: var(--spacing-xs);
                    padding: var(--spacing-sm);
                    background: var(--surface-tertiary);
                    border-radius: var(--radius-sm);
                    font-size: var(--text-xs);
                    color: var(--text-tertiary);
                    font-family: monospace;
                    overflow-x: auto;
                    white-space: pre-wrap;
                    max-height: 200px;
                    overflow-y: auto;
                }
            }
        }
    }
}