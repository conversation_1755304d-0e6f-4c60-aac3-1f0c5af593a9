<div class="user-profile-container">
  <!-- Hero <PERSON> -->
  <div class="hero-header">
    <div class="hero-content">
      <div class="user-avatar">
        <img [src]="user?.profilePicture || 'assets/Icons/User.jpg'" [alt]="user?.firstName + ' ' + user?.lastName" class="avatar-image">
        <div class="avatar-overlay">
          <i class="fas fa-camera"></i>
        </div>
      </div>
      <div class="user-info">
        <h1 class="user-name">{{ user?.firstName }} {{ user?.lastName }}</h1>
        <p class="user-email">{{ user?.email }}</p>
        <div class="user-stats">
          <div class="stat-item">
            <span class="stat-number">{{ associatedPlayers.length > 0 ? '1' : '0' }}</span>
            <span class="stat-label">Associated Player</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ associationRequests.length }}</span>
            <span class="stat-label">Pending Requests</span>
          </div>
        </div>
      </div>
    </div>
    <div class="hero-actions">
      <button class="btn btn-secondary" (click)="toggleEditProfile()">
        <i class="fas fa-edit"></i>
        <span>Edit Profile</span>
      </button>
      <button class="btn btn-danger" (click)="logout()">
        <i class="fas fa-sign-out-alt"></i>
        <span>Logout</span>
      </button>
    </div>
  </div>

  <!-- Edit Profile Modal/Section -->
  <div class="profile-edit-section" *ngIf="showEditProfile">
    <div class="section-card">
      <div class="card-header">
        <h2><i class="fas fa-user-edit"></i> Edit Profile Information</h2>
        <button class="btn-close" (click)="toggleEditProfile()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <form [formGroup]="profileForm" (ngSubmit)="updateProfile()" class="profile-form">
        <div class="form-grid">
          <div class="form-group">
            <label for="firstName">
              <i class="fas fa-user"></i>
              First Name
            </label>
            <input
              id="firstName"
              type="text"
              formControlName="firstName"
              class="form-input"
              placeholder="Enter your first name"
              [class.error]="profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched">
            <div *ngIf="profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched" class="error-message">
              <i class="fas fa-exclamation-circle"></i>
              First name is required
            </div>
          </div>

          <div class="form-group">
            <label for="lastName">
              <i class="fas fa-user"></i>
              Last Name
            </label>
            <input
              id="lastName"
              type="text"
              formControlName="lastName"
              class="form-input"
              placeholder="Enter your last name"
              [class.error]="profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched">
            <div *ngIf="profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched" class="error-message">
              <i class="fas fa-exclamation-circle"></i>
              Last name is required
            </div>
          </div>

          <div class="form-group full-width">
            <label for="email">
              <i class="fas fa-envelope"></i>
              Email Address
            </label>
            <input
              id="email"
              type="email"
              formControlName="email"
              class="form-input"
              placeholder="Enter your email address"
              [class.error]="profileForm.get('email')?.invalid && profileForm.get('email')?.touched">
            <div *ngIf="profileForm.get('email')?.invalid && profileForm.get('email')?.touched" class="error-message">
              <i class="fas fa-exclamation-circle"></i>
              <span *ngIf="profileForm.get('email')?.errors?.['required']">Email is required</span>
              <span *ngIf="profileForm.get('email')?.errors?.['email']">Please enter a valid email</span>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn btn-secondary" (click)="toggleEditProfile()">
            <i class="fas fa-times"></i>
            Cancel
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="profileForm.invalid || isLoading">
            <i class="fas fa-save" *ngIf="!isLoading"></i>
            <i class="fas fa-spinner fa-spin" *ngIf="isLoading"></i>
            <span *ngIf="!isLoading">Save Changes</span>
            <span *ngIf="isLoading">Saving...</span>
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Associated Player Section -->
  <div class="section-card">
    <div class="card-header">
      <h2><i class="fas fa-user"></i> Your Player</h2>
      <div class="header-actions">
        <button class="btn btn-secondary btn-sm" (click)="refreshAssociatedPlayers()" title="Refresh associated players">
          <i class="fas fa-sync-alt"></i>
          <span>Refresh</span>
        </button>
        <div class="player-limit-info" *ngIf="associatedPlayers.length > 0">
          <span class="limit-badge">
            <i class="fas fa-info-circle"></i>
            Limit: 1 Player per User
          </span>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="associatedPlayers.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-user-plus"></i>
      </div>
      <h3>No Associated Player</h3>
      <p>You haven't associated with a player yet. Associate with a player to edit their profile and manage their stats.</p>
      <div class="limit-notice">
        <i class="fas fa-info-circle"></i>
        <span>You can associate with only <strong>1 player</strong> per account</span>
      </div>
      <button class="btn btn-primary" (click)="togglePlayerSearch()">
        <i class="fas fa-plus"></i>
        Associate Your Player
      </button>
    </div>

    <!-- Player Card (Single) -->
    <div *ngIf="associatedPlayers.length > 0" class="single-player-container">
      <div class="player-card featured-player" *ngFor="let player of associatedPlayers">
        <div class="player-header">
          <img [src]="player.imgUrl || 'assets/Icons/User.jpg'" [alt]="player.name" class="player-avatar">
          <div class="player-badge" [ngClass]="'position-' + player.position.toLowerCase()">
            {{ player.position }}
          </div>
          <div class="featured-badge">
            <i class="fas fa-star"></i>
            <span>Your Player</span>
          </div>
        </div>

        <div class="player-details">
          <h3 class="player-name">{{ player.name }}</h3>
          <div class="player-meta">
            <div class="meta-item" *ngIf="player.team">
              <i class="fas fa-shield-alt"></i>
              <span>{{ player.team.name }}</span>
            </div>
            <div class="meta-item" *ngIf="!player.team">
              <i class="fas fa-user-slash"></i>
              <span>Free Agent</span>
            </div>
            <div class="meta-item" *ngIf="player.age">
              <i class="fas fa-birthday-cake"></i>
              <span>{{ player.age }} years</span>
            </div>
          </div>
        </div>

        <div class="player-actions">
          <button class="btn btn-primary" [routerLink]="['/player', player.id]">
            <i class="fas fa-edit"></i>
            <span>Edit Profile</span>
          </button>
          <button class="btn btn-danger" (click)="removePlayer(player)">
            <i class="fas fa-unlink"></i>
            <span>Remove Association</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Player Search Section -->
  <div *ngIf="showPlayerSearch" class="section-card search-section">
    <div class="card-header">
      <h2><i class="fas fa-search"></i> Associate New Player</h2>
      <p class="card-subtitle">Search for players by name or request association by email</p>
    </div>

    <form [formGroup]="searchForm" class="search-form">
      <div class="search-methods">
        <!-- Search by Name -->
        <div class="search-method">
          <div class="method-header">
            <i class="fas fa-search"></i>
            <h3>Search by Name</h3>
          </div>
          <div class="form-group">
            <div class="input-group">
              <div class="input-icon">
                <i class="fas fa-user"></i>
              </div>
              <input
                id="searchTerm"
                type="text"
                formControlName="searchTerm"
                placeholder="Enter player name..."
                class="form-input">
              <button
                type="button"
                class="btn btn-primary"
                (click)="searchPlayers()"
                [disabled]="isSearching || !searchForm.get('searchTerm')?.value">
                <i class="fas fa-search" *ngIf="!isSearching"></i>
                <i class="fas fa-spinner fa-spin" *ngIf="isSearching"></i>
                <span *ngIf="!isSearching">Search</span>
                <span *ngIf="isSearching">Searching...</span>
              </button>
            </div>
          </div>
        </div>

        <div class="divider">
          <span>OR</span>
        </div>

        <!-- Associate by Email -->
        <div class="search-method">
          <div class="method-header">
            <i class="fas fa-envelope"></i>
            <h3>Request Association by Email</h3>
          </div>
          <div class="form-group">
            <div class="input-group">
              <div class="input-icon">
                <i class="fas fa-envelope"></i>
              </div>
              <input
                id="playerEmail"
                type="email"
                formControlName="playerEmail"
                placeholder="Enter player's email address..."
                class="form-input"
                [class.error]="searchForm.get('playerEmail')?.invalid && searchForm.get('playerEmail')?.touched">
              <button
                type="button"
                class="btn btn-success"
                (click)="associatePlayerByEmail()"
                [disabled]="!searchForm.get('playerEmail')?.value || searchForm.get('playerEmail')?.invalid">
                <i class="fas fa-paper-plane"></i>
                <span>Send Request</span>
              </button>
            </div>
            <div *ngIf="searchForm.get('playerEmail')?.invalid && searchForm.get('playerEmail')?.touched" class="error-message">
              <i class="fas fa-exclamation-circle"></i>
              Please enter a valid email address
            </div>
          </div>
        </div>
      </div>
    </form>

    <!-- Search Results -->
    <div *ngIf="availablePlayers.length > 0" class="search-results">
      <div class="results-header">
        <h3><i class="fas fa-users"></i> Available Players ({{ availablePlayers.length }})</h3>
        <p>Click "Associate" to send a request to link with a player</p>
      </div>
      <div class="players-grid">
        <div *ngFor="let player of availablePlayers" class="player-card search-result">
          <div class="player-header">
            <img [src]="player.imgUrl || 'assets/Icons/User.jpg'" [alt]="player.name" class="player-avatar">
            <div class="player-badge" [ngClass]="'position-' + player.position.toLowerCase()">
              {{ player.position }}
            </div>
          </div>

          <div class="player-details">
            <h3 class="player-name">{{ player.name }}</h3>
            <div class="player-meta">
              <div class="meta-item" *ngIf="player.team">
                <i class="fas fa-shield-alt"></i>
                <span>{{ player.team.name }}</span>
              </div>
              <div class="meta-item" *ngIf="!player.team">
                <i class="fas fa-user-slash"></i>
                <span>Free Agent</span>
              </div>
              <div class="meta-item" *ngIf="player.age">
                <i class="fas fa-birthday-cake"></i>
                <span>{{ player.age }} years</span>
              </div>
            </div>
          </div>

          <div class="player-actions">
            <button class="btn btn-success btn-block" (click)="associatePlayer(player)">
              <i class="fas fa-link"></i>
              <span>Associate Player</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Association Requests Section -->
  <div class="section-card" *ngIf="associationRequests.length > 0">
    <div class="card-header">
      <h2><i class="fas fa-clock"></i> Player Association Requests</h2>
      <p class="card-subtitle">Track your pending and completed association requests</p>
    </div>

    <div class="requests-container">
      <div class="request-card" *ngFor="let request of associationRequests">
        <div class="request-header">
          <div class="player-info">
            <img [src]="request.playerId.imgUrl || 'assets/Icons/User.jpg'" [alt]="request.playerId.name" class="request-avatar">
            <div class="player-details">
              <h3>{{ request.playerId.name }}</h3>
              <div class="player-meta">
                <span class="position-badge">{{ request.playerId.position }}</span>
                <span class="team-name" *ngIf="request.playerId.team">{{ request.playerId.team.name }}</span>
                <span class="team-name free-agent" *ngIf="!request.playerId.team">Free Agent</span>
              </div>
            </div>
          </div>
          <div class="request-status">
            <span class="status-badge" [ngClass]="getRequestStatusClass(request.status)">
              <i class="fas fa-clock" *ngIf="request.status === 'pending'"></i>
              <i class="fas fa-check" *ngIf="request.status === 'approved'"></i>
              <i class="fas fa-times" *ngIf="request.status === 'rejected'"></i>
              {{ request.status | titlecase }}
            </span>
            <p class="request-date">{{ request.requestedAt | date:'dd/MM/yyyy HH:mm' }}</p>
          </div>
        </div>

        <div class="request-message" *ngIf="request.userMessage">
          <div class="message-header">
            <i class="fas fa-comment"></i>
            <span>Your message:</span>
          </div>
          <p>{{ request.userMessage }}</p>
        </div>

        <div class="request-reason" *ngIf="request.status === 'rejected' && request.reason">
          <div class="reason-header">
            <i class="fas fa-exclamation-triangle"></i>
            <span>Rejection reason:</span>
          </div>
          <p>{{ request.reason }}</p>
        </div>

        <div class="request-actions" *ngIf="request.status === 'pending'">
          <button class="btn btn-danger btn-sm" (click)="cancelRequest(request.id)">
            <i class="fas fa-times"></i>
            Cancel Request
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
