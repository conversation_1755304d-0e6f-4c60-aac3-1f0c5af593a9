import { Request, Response, NextFunction } from "express";
import { inject, injectable } from "tsyringe";
import { ITransferRequestService, CreateTransferRequestData, ProcessTransferRequestData } from "../services/transfer-request-service";
import logger from "../config/logger";

@injectable()
export default class TransferRequestController {
  constructor(
    @inject("ITransferRequestService") private transferRequestService: ITransferRequestService
  ) {}

  async createTransferRequest(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { playerId, fromTeamId, toTeamId, message } = req.body;

      if (!playerId || !fromTeamId || !toTeamId) {
        res.status(400).json({ message: "playerId, fromTeamId, and toTeamId are required" });
        return;
      }

      const data: CreateTransferRequestData = {
        playerId,
        fromTeamId,
        toTeamId,
        message
      };

      const transferRequest = await this.transferRequestService.createTransferRequest(req.user.id, data);

      res.status(201).json({
        success: true,
        message: "Transfer request created successfully",
        data: transferRequest
      });
    } catch (error: any) {
      logger.error("Error in createTransferRequest:", error);
      next(error);
    }
  }

  async processTransferRequest(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { requestId } = req.params;
      const { action, reason } = req.body;

      if (!action || !['approve', 'reject'].includes(action)) {
        res.status(400).json({ message: "action must be 'approve' or 'reject'" });
        return;
      }

      const data: ProcessTransferRequestData = {
        requestId,
        action,
        reason
      };

      const transferRequest = await this.transferRequestService.processTransferRequest(req.user.id, data);

      res.json({
        success: true,
        message: `Transfer request ${action}ed successfully`,
        data: transferRequest
      });
    } catch (error: any) {
      logger.error("Error in processTransferRequest:", error);
      next(error);
    }
  }

  async getTransferRequestsByTeam(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { teamId } = req.params;
      const { status } = req.query;

      const transferRequests = await this.transferRequestService.getTransferRequestsByTeam(
        teamId,
        status as string
      );

      res.json({
        success: true,
        data: transferRequests
      });
    } catch (error: any) {
      logger.error("Error in getTransferRequestsByTeam:", error);
      next(error);
    }
  }

  async getTransferRequestById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { requestId } = req.params;

      const transferRequest = await this.transferRequestService.getTransferRequestById(requestId);

      res.json({
        success: true,
        data: transferRequest
      });
    } catch (error: any) {
      logger.error("Error in getTransferRequestById:", error);
      next(error);
    }
  }

  async cancelTransferRequest(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { requestId } = req.params;

      await this.transferRequestService.cancelTransferRequest(requestId, req.user.id);

      res.json({
        success: true,
        message: "Transfer request cancelled successfully"
      });
    } catch (error: any) {
      logger.error("Error in cancelTransferRequest:", error);
      next(error);
    }
  }

  async cleanupExpiredRequests(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user || req.user.role !== 'admin') {
        res.status(403).json({ message: "Admin access required" });
        return;
      }

      const count = await this.transferRequestService.cleanupExpiredRequests();

      res.json({
        success: true,
        message: `${count} expired transfer requests cleaned up`,
        data: { count }
      });
    } catch (error: any) {
      logger.error("Error in cleanupExpiredRequests:", error);
      next(error);
    }
  }
}
