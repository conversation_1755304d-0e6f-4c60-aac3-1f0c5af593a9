import { Types } from "mongoose";
import { ILeagueRepository } from "../../interfaces/league";
import { ILeague } from "../../models/league";
import { TopScorer, TopAssister } from "@pro-clubs-manager/shared-dtos";
import { AllTimeTopAvgRatingByPosition, MostHattricks, MostCleanSheets, MostWinningPercentageTeam, MostWinningPercentagePlayer } from "../../repositories/game-repository";

export class MockLeagueRepository implements ILeagueRepository {
  getAllLeagues = jest.fn<Promise<ILeague[]>, []>();
  getLeagueById = jest.fn<Promise<ILeague>, [string | Types.ObjectId]>();
  isLeagueNameExists = jest.fn<Promise<boolean>, [string]>();
  createLeague = jest.fn<Promise<ILeague>, [string, string?]>();
  deleteLeague = jest.fn<Promise<void>, [string | Types.ObjectId]>();
  removeTeamFromLeague = jest.fn<Promise<void>, [Types.ObjectId, Types.ObjectId]>();
  calculateLeagueTopScorers = jest.fn<Promise<TopScorer[]>, [string | Types.ObjectId, number]>();
  calculateLeagueTopAssisters = jest.fn<Promise<TopAssister[]>, [string | Types.ObjectId, number]>();
  calculateAllTimeTopScorers = jest.fn<Promise<TopScorer[]>, [string | Types.ObjectId, number]>();
  calculateAllTimeTopAssisters = jest.fn<Promise<TopAssister[]>, [string | Types.ObjectId, number]>();
  calculateAllTimeTopAvgRatingByPosition = jest.fn<Promise<AllTimeTopAvgRatingByPosition[]>, [string, string, number, number?]>();
  calculateMostHattricks = jest.fn<Promise<MostHattricks[]>, [string, number?]>();
  calculateMostCleanSheets = jest.fn<Promise<MostCleanSheets[]>, [string, number?]>();
  calculateMostWinningPercentageTeams = jest.fn<Promise<MostWinningPercentageTeam[]>, [string, number?, number?]>();
  calculateMostWinningPercentagePlayers = jest.fn<Promise<MostWinningPercentagePlayer[]>, [string, number?, number?]>();
  syncPlayerStatsWithGameData = jest.fn<Promise<{ updated: number; errors: string[] }>, [string]>();
}
