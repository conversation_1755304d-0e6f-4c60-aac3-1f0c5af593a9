<nav class="navbar">
  <div class="nav-container">
    <!-- Left: Brand -->
    <div class="brand" routerLink="/dashboard" (click)="closeMobileMenu()">
      <img src="assets/Icons/IPL.png" alt="IPL Logo">
      <span>IPL</span>
    </div>

    <!-- Center: Navigation -->
    <div class="nav-menu" [class.active]="isMobileMenuOpen">
      <ul class="nav-links">
        <li *ngFor="let item of navbarItems; let i = index"
            [hidden]="item.toHide && !isAdmin()"
            class="nav-item"
            [class.has-dropdown]="item.isDropdown">

          <!-- Regular Link -->
          <a *ngIf="!item.isDropdown"
             [routerLink]="item.link"
             class="nav-link"
             (click)="closeMobileMenu()">
            <i *ngIf="item.icon" [class]="item.icon"></i>
            <span>{{item.displayText}}</span>
          </a>

          <!-- Dropdown Link -->
          <div *ngIf="item.isDropdown" class="nav-dropdown">
            <button class="nav-dropdown-toggle"
                    (click)="toggleNavDropdown(i)"
                    [class.active]="isDropdownOpen(i)">
              <i *ngIf="item.icon" [class]="item.icon"></i>
              <span>{{item.displayText}}</span>
              <i class="fas fa-chevron-down dropdown-arrow"
                 [class.rotated]="isDropdownOpen(i)"></i>
            </button>

            <ul class="nav-dropdown-menu"
                [class.show]="isDropdownOpen(i)">
              <li *ngFor="let dropdownItem of item.dropdownItems">
                <a [routerLink]="dropdownItem.link"
                   class="nav-dropdown-link"
                   (click)="closeMobileMenu()">
                  <i *ngIf="dropdownItem.icon" [class]="dropdownItem.icon"></i>
                  <span>{{dropdownItem.displayText}}</span>
                </a>
              </li>
            </ul>
          </div>
        </li>
      </ul>

      <!-- Mobile: Auth Links (only for non-authenticated users) -->
      <div class="nav-actions-mobile" *ngIf="!isAuthenticated">
        <div class="auth-links">
          <a routerLink="/login" (click)="closeMobileMenu()">Login</a>
          <a routerLink="/sign-up" (click)="closeMobileMenu()">Sign Up</a>
        </div>
      </div>
    </div>

    <!-- Right: User Actions, Theme Toggle & Mobile Toggle -->
    <div class="nav-right">
      <!-- Auth Links (Desktop only) -->
      <div class="auth-links" *ngIf="!isAuthenticated">
        <a routerLink="/login">Login</a>
        <a routerLink="/sign-up">Sign Up</a>
      </div>

      <!-- User Profile (Always visible) -->
      <div class="user-profile" *ngIf="isAuthenticated && currentUser">
        <div class="dropdown">
          <button class="user-btn" (click)="toggleUserDropdown()">
            <img [src]="currentUser.profilePicture || 'assets/default-avatar.png'"
                 [alt]="currentUser.fullName"
                 (error)="onImageError($event)">
            <span>{{ currentUser.firstName }}</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <ul class="dropdown-menu" [class.show]="isUserDropdownOpen">
            <li class="dropdown-header">{{ currentUser.fullName }}</li>
            <li><a routerLink="/profile" (click)="closeMobileMenu()">
                <i class="fas fa-user"></i> Profile
              </a></li>

            <!-- Linked Player Shortcut -->
            <li *ngIf="linkedPlayer" class="linked-player-item">
              <a (click)="navigateToLinkedPlayer()" class="linked-player-link">
                <div class="linked-player-info">
                  <img [src]="linkedPlayer.imgUrl || 'assets/Icons/User.jpg'"
                       [alt]="linkedPlayer.name"
                       class="linked-player-avatar"
                       (error)="onImageError($event)">
                  <div class="linked-player-details">
                    <span class="linked-player-name">{{ linkedPlayer.name }}</span>
                    <span class="linked-player-position">{{ linkedPlayer.position }}</span>
                  </div>
                </div>
                <i class="fas fa-external-link-alt"></i>
              </a>
            </li>

            <li *ngIf="linkedPlayer" class="dropdown-divider"></li>
            <li *ngIf="isAdmin()"><a routerLink="/add-news" (click)="closeMobileMenu()">
                <i class="fas fa-plus"></i> Add News
              </a></li>
            <li *ngIf="isAdmin()"><a routerLink="/create-team" (click)="closeMobileMenu()">
                <i class="fas fa-users"></i> Create Team
              </a></li>
            <li *ngIf="isAdmin()"><a routerLink="/create-player" (click)="closeMobileMenu()">
                <i class="fas fa-user-plus"></i> Create Player
              </a></li>
            <li *ngIf="isAdmin()"><a routerLink="/add-fixture" (click)="closeMobileMenu()">
                <i class="fas fa-calendar-plus"></i> Add Fixture
              </a></li>
            <li *ngIf="isAdmin()"><a routerLink="/ai-import" (click)="closeMobileMenu()">
                <i class="fas fa-robot"></i> AI Import
              </a></li>
            <li><a (click)="logout()">
                <i class="fas fa-sign-out-alt"></i> Logout
              </a></li>
          </ul>
        </div>
      </div>

      <!-- Theme Toggle (Always visible) -->
      <button class="theme-toggle" (click)="toggleDarkMode()">
        <i class="fas" [ngClass]="isDarkMode ? 'fa-sun' : 'fa-moon'"></i>
        <span>{{isDarkMode ? 'Light' : 'Dark'}}</span>
      </button>

      <!-- Mobile Toggle -->
      <button class="mobile-toggle" (click)="toggleMobileMenu()">
        <span></span>
        <span></span>
        <span></span>
      </button>
    </div>
  </div>
</nav>