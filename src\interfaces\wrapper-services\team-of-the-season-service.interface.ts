import { ClientSession } from "mongoose";
import { ITeamOfTheSeason } from "../../models/team-of-the-season";

export interface ITeamOfTheSeasonService {
  generateTeamOfTheSeason(
    leagueId: string,
    seasonNumber: number,
    formationName?: string,
    session?: ClientSession
  ): Promise<ITeamOfTheSeason>;

  getTeamOfTheSeason(
    leagueId: string,
    seasonNumber: number,
    formationName?: string
  ): Promise<ITeamOfTheSeason | null>;

  getAllTeamsOfTheSeason(leagueId: string): Promise<ITeamOfTheSeason[]>;

  getAvailableSeasons(leagueId: string): Promise<number[]>;

  getAvailableFormations(leagueId: string, seasonNumber: number): Promise<string[]>;

  deleteTeamOfTheSeason(
    leagueId: string,
    seasonNumber: number,
    formationName: string
  ): Promise<boolean>;

  regenerateTeamOfTheSeason(
    leagueId: string,
    seasonNumber: number,
    formationName?: string,
    session?: ClientSession
  ): Promise<ITeamOfTheSeason>;

  getFormationPositions(formationName: string): { [position: string]: number } | null;

  getSupportedFormations(): string[];
}
