<div class="dashboard-topscorers-container" *ngIf="topScorers; else noData">
    <div class="scorers-list">
        <div class="scorer-item"
             *ngFor="let scorer of topScorers.slice(0, 5); let i = index"
             (click)="onPlayerClick(scorer)"
             [class.first-place]="i === 0"
             [class.second-place]="i === 1"
             [class.third-place]="i === 2">

            <div class="rank-badge">
                <span class="rank-number">{{i + 1}}</span>
                <i class="fas fa-crown" *ngIf="i === 0"></i>
            </div>

            <div class="player-avatar">
                <img [src]="scorer.playerImgUrl || 'assets/Icons/User.jpg'"
                     [alt]="scorer.playerName">
            </div>

            <div class="player-details">
                <div class="player-name">{{scorer.playerName}}</div>
                <div class="player-team">{{scorer.teamName}}</div>
            </div>

            <div class="goals-stat">
                <span class="goals-number">{{scorer.goals}}</span>
                <span class="goals-label">Goals</span>
            </div>
        </div>
    </div>
</div>

<ng-template #noData>
    <div class="loading-container">
        <pro-clubs-spinner></pro-clubs-spinner>
    </div>
</ng-template>