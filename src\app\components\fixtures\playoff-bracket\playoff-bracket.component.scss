.playoffs-view {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.playoffs-header {
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);

    .title-section {
      .playoffs-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;

        i {
          color: var(--warning);
        }
      }

      .playoffs-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin: 0;
      }
    }

    .controls-section {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);



      .view-mode-toggle {
        display: flex;
        gap: var(--spacing-xs);
        background: var(--surface-primary);
        border-radius: var(--radius-md);
        padding: var(--spacing-xs);

        .toggle-btn {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          padding: var(--spacing-sm);
          border: none;
          border-radius: var(--radius-sm);
          background: transparent;
          color: var(--text-secondary);
          font-size: var(--font-size-sm);
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: var(--surface-hover);
            color: var(--text-primary);
          }

          &.active {
            background: var(--primary);
            color: var(--text-on-primary);
          }
        }
      }

      .refresh-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--surface-primary);
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          background: var(--primary);
          color: var(--text-on-primary);
          border-color: var(--primary);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }

  .progress-section {
    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-xs);

      .progress-label {
        font-size: var(--font-size-sm);
        font-weight: 600;
        color: var(--text-primary);
      }

      .progress-percentage {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
      }
    }

    .progress-bar {
      height: 8px;
      background: var(--surface-tertiary);
      border-radius: var(--radius-full);
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--success), var(--primary));
        border-radius: var(--radius-full);
        transition: width 0.3s ease;
      }
    }
  }
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);

  .loading-spinner {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);

    i {
      color: var(--primary);
      font-size: var(--font-size-lg);
    }
  }
}

.bracket-content {
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);

  .placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;

    i {
      font-size: 3rem;
      color: var(--warning);
      margin-bottom: var(--spacing-md);
    }

    h3 {
      font-size: var(--font-size-lg);
      font-weight: 700;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-sm) 0;
    }

    p {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      margin: 0 0 var(--spacing-xs) 0;
      max-width: 400px;
    }

    .bracket-info {
      background: var(--surface-primary);
      border-radius: var(--radius-md);
      padding: var(--spacing-md);
      margin-top: var(--spacing-lg);
      border: 1px solid var(--border-color);
      max-width: 300px;

      p {
        margin: var(--spacing-xs) 0;
        font-size: var(--font-size-sm);
        color: var(--text-primary);

        strong {
          color: var(--primary);
        }
      }
    }
  }

  .bracket-stages {
    padding: var(--spacing-lg);

    .stage {
      background: var(--surface-primary);
      border-radius: var(--radius-md);
      padding: var(--spacing-md);
      margin-bottom: var(--spacing-md);
      border: 1px solid var(--border-color);

      h4 {
        margin: 0 0 var(--spacing-sm) 0;
        color: var(--primary);
        font-size: var(--font-size-lg);
      }

      p {
        margin: 0;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
      }
    }
  }
}

.championship-results {
  margin-bottom: var(--spacing-xl);
  text-align: center;

  .results-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
  }

  .podium {
    display: flex;
    justify-content: center;
    align-items: end;
    gap: var(--spacing-lg);

    .podium-position {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-lg);
      border-radius: var(--radius-lg);
      background: var(--surface-primary);
      box-shadow: var(--shadow-md);
      min-width: 150px;

      &.champion {
        background: linear-gradient(135deg, var(--warning), var(--warning-dark));
        color: var(--text-on-warning);
        transform: scale(1.1);

        .position-medal i {
          color: var(--warning-light);
        }
      }

      &.runner-up {
        background: linear-gradient(135deg, var(--text-secondary), var(--surface-tertiary));
        color: var(--text-on-secondary);

        .position-medal i {
          color: var(--text-secondary);
        }
      }

      &.third-place {
        background: linear-gradient(135deg, var(--warning-dark), var(--surface-tertiary));
        color: var(--text-primary);

        .position-medal i {
          color: var(--warning);
        }
      }

      .position-medal {
        font-size: 2rem;
        margin-bottom: var(--spacing-sm);
      }

      .team-image {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 3px solid currentColor;
      }

      .team-name {
        font-size: var(--font-size-md);
        font-weight: 700;
        margin: 0;
        text-align: center;
      }

      .position-label {
        font-size: var(--font-size-sm);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        opacity: 0.8;
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--border-color);

  i {
    font-size: 3rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    opacity: 0.6;
  }

  h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
  }

  p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-md) 0;
    max-width: 300px;
  }

  .retry-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--primary);
    border-radius: var(--radius-md);
    background: var(--primary);
    color: var(--text-on-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--primary-hover);
    }
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .playoffs-header {
    padding: var(--spacing-md);

    .header-content {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: stretch;

      .controls-section {
        flex-direction: column;
        gap: var(--spacing-sm);
      }
    }
  }

  .championship-results {
    .podium {
      flex-direction: column;
      gap: var(--spacing-md);

      .podium-position {
        min-width: auto;
        width: 100%;
        max-width: 200px;

        &.champion {
          transform: none;
        }
      }
    }
  }
}
