import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface ApiResponse<T> {
data: T;
}

@Injectable({
    providedIn: 'root'
})
export class ApiService {
    SERVER_URL = "https://proclubs-stats-server.duckdns.org/";
    // SERVER_URL = "http://localhost:443/";

    constructor(private http: HttpClient) { }

    async get<T>(path: string, options: { params?: any } = {}): Promise<ApiResponse<T>> {
        let httpParams = new HttpParams();
        if (options.params) {
            Object.keys(options.params).forEach(key => {
                if (options.params[key] !== undefined && options.params[key] !== null) {
                    httpParams = httpParams.set(key, options.params[key]);
                }
            });
        }

        const response = await this.http.get<any>(this.SERVER_URL + path, { params: httpParams }).toPromise();
        // Handle server response format: { success: true, data: actualData }
        if (response && response.data) {
            return { data: response.data as T };
        }
        return { data: response as T };
    }

    async patch<T>(path: string, body: any = {}): Promise<ApiResponse<T>> {
        const response = await this.http.patch<T>(this.SERVER_URL + path, body).toPromise();
        return { data: response as T };
    }

    async post<T>(path: string, body: any = {}): Promise<ApiResponse<T>> {
        const response = await this.http.post<any>(this.SERVER_URL + path, body).toPromise();
        // Handle server response format: { success: true, data: actualData }
        if (response && response.data) {
            return { data: response.data as T };
        }
        return { data: response as T };
    }

    async delete<T>(path: string, options: { params?: any } = {}): Promise<ApiResponse<T>> {
        let httpParams = new HttpParams();
        if (options.params) {
            Object.keys(options.params).forEach(key => {
                if (options.params[key] !== undefined && options.params[key] !== null) {
                    httpParams = httpParams.set(key, options.params[key]);
                }
            });
        }

        const response = await this.http.delete<any>(this.SERVER_URL + path, { params: httpParams }).toPromise();
        // Handle server response format: { success: true, data: actualData }
        if (response && response.data) {
            return { data: response.data as T };
        }
        return { data: response as T };
    }

    async put<T>(path: string, body: any = {}): Promise<ApiResponse<T>> {
        const response = await this.http.put<T>(this.SERVER_URL + path, body).toPromise();
        return { data: response as T };
    }
}