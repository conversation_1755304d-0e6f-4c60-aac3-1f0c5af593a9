import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { FixtureDTO, GameFixtureData } from '../../shared/models/game.model';
import { LeagueService } from '../../services/league.service';
import { LEAGUE_ID } from '../../constants/constants';
import { FixtureStateService } from '../../services/state/fixture-state.service';
import { BackgroundSyncService } from '../../services/background-sync.service';
import { AuthService } from '../../services/auth.service';
import { ViewMode } from './view-mode-toggle/view-mode-toggle.component';
import { FixtureNavigationEvent } from './fixture-navigation/fixture-navigation.component';
import { GameEditEvent } from './game-editing/game-editing.component';
import { Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { ListOption } from '../../shared/models/list-option.model';

@Component({
  selector: 'fixtures',
  templateUrl: './fixtures.component.html',
  styleUrls: ['./fixtures.component.scss']
})
export class FixturesComponent implements OnInit, OnDestroy {
  @Input() hideTitle: boolean = false;

  // Loading states
  isInitialLoad: boolean = true;
  loadingState: 'loading' | 'loaded' | 'error' = 'loading';

  // State observables
  fixtureState$ = this.fixtureState.state$;
  fixtures$ = this.fixtureState$.pipe(map(state => state.fixtures));
  currentFixture$ = this.fixtureState$.pipe(map(state => state.currentFixture));
  gamesByDate$ = this.fixtureState$.pipe(map(state => state.gamesByDate));
  fixturesOptions$ = this.fixtureState$.pipe(map(state => state.fixturesOptions));
  isLoading$ = this.fixtureState$.pipe(map(state => state.isFixturesLoading));
  viewMode$ = this.fixtureState$.pipe(map(state => state.viewMode));
  currentFixtureNumber$ = this.fixtureState$.pipe(map(state => state.currentFixtureNumber));
  totalFixtures$ = this.fixtureState$.pipe(map(state => state.totalFixtures));

  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private leagueService: LeagueService,
    private fixtureState: FixtureStateService,
    private backgroundSync: BackgroundSyncService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    this.loadFixtures();
    
    // Initialize background sync for periodic updates
    this.backgroundSync.schedulePeriodicSync();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  async loadFixtures(): Promise<void> {
    // Check if we have cached data and it's not stale
    if (!this.fixtureState.isFixturesStale() && this.fixtureState.currentState.fixtures.length > 0) {
      this.loadingState = 'loaded';
      this.isInitialLoad = false;
      this.setupViewMode();
      return;
    }

    this.fixtureState.setFixturesLoading(true);
    this.loadingState = 'loading';

    try {
      // Load ALL fixtures, not paginated - use a large page size to get all fixtures
      const serverResponse = await this.leagueService.getPaginatedLeagueFixturesGames(
        LEAGUE_ID,
        1,
        1000 // Large page size to get all fixtures
      );

      // Update state with new fixtures
      this.fixtureState.updateFixtures(serverResponse, false);

      // Load fixture options after fixtures are loaded
      this.loadFixturesOptions();

      // Set current fixture to the latest one by default
      if (serverResponse.fixtures && serverResponse.fixtures.length > 0) {
        const latestFixture = serverResponse.fixtures[serverResponse.fixtures.length - 1];
        this.fixtureState.setCurrentFixture(latestFixture);
        this.fixtureState.setCurrentFixtureNumber(latestFixture.round);
      }

      this.setupViewMode();
      this.loadingState = 'loaded';
      this.isInitialLoad = false;
    } catch (error) {
      console.error('Error loading fixtures:', error);
      this.loadingState = 'error';
      this.fixtureState.setFixturesLoading(false);
    }
  }

  private setupViewMode(): void {
    // Organize games by date for date view
    this.organizeGamesByDate();
  }

  private loadFixturesOptions(): void {
    const currentState = this.fixtureState.currentState;
    const fixtures = currentState.fixtures;
    
    let fixturesOptions: Array<{ value: string; displayText: string }> = [];

    if (fixtures && fixtures.length > 0) {
      fixturesOptions = fixtures.map((fixture, index) => {
        let displayText: string;

        // Check if this is a playoff fixture with new playoff details
        if (fixture.isPlayoff && fixture.playoffDetails) {
          const { stage, format } = fixture.playoffDetails;
          displayText = `${stage} (${format})`;
        }
        // Fallback: Check if any game in the fixture is a playoff game (for older data)
        else {
          const playoffGame = fixture.games?.find((game: any) => game.isPlayoff);
          if (playoffGame && playoffGame.playoffStage) {
            displayText = playoffGame.playoffStage;
          } else {
            displayText = `Fixture ${fixture.round || (index + 1)}`;
          }
        }

        return {
          value: (fixture.round || (index + 1)).toString(),
          displayText: displayText
        };
      });
    } else {
      // Fallback for when fixtures are not loaded yet
      fixturesOptions = Array.from({ length: currentState.totalFixtures }, (_, i) => ({
        value: (i + 1).toString(),
        displayText: `Fixture ${i + 1}`
      }));
    }

    // Update state with new options
    this.fixtureState.updateFixturesOptions(fixturesOptions);
  }

  private organizeGamesByDate(): void {
    const currentState = this.fixtureState.currentState;
    const fixtures = currentState.fixtures;
    
    let gamesByDate: { [date: string]: GameFixtureData[] } = {};
    
    if (!fixtures) {
      this.fixtureState.updateGamesByDate(gamesByDate);
      return;
    }

    fixtures.forEach((fixture: FixtureDTO) => {
      fixture.games?.forEach((game: GameFixtureData) => {
        if (game.date) {
          const dateKey = new Date(game.date).toDateString();
          if (!gamesByDate[dateKey]) {
            gamesByDate[dateKey] = [];
          }
          gamesByDate[dateKey].push(game);
        }
      });
    });

    // Sort games within each date by time
    Object.keys(gamesByDate).forEach(date => {
      gamesByDate[date].sort((a: GameFixtureData, b: GameFixtureData) => {
        if (!a.date || !b.date) return 0;
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      });
    });

    // Update state with organized games
    this.fixtureState.updateGamesByDate(gamesByDate);
  }

  // Event handlers for child components
  onViewModeChanged(viewMode: ViewMode): void {
    this.fixtureState.setViewMode(viewMode);
    this.setupViewMode();
  }

  onNavigationEvent(event: FixtureNavigationEvent): void {
    const currentState = this.fixtureState.currentState;
    const fixtures = currentState.fixtures;

    switch (event.type) {
      case 'selection':
        const selectedOption = event.data as ListOption;

        // Check if selectedOption exists and has a valid value
        if (!selectedOption || !selectedOption.value) {
          console.warn('Invalid selection option:', selectedOption);
          return;
        }

        const selectedValue = parseInt(selectedOption.value);
        const selectedFixture = fixtures.find(fixture => fixture.round === selectedValue);

        if (selectedFixture) {
          this.fixtureState.setCurrentFixture(selectedFixture);
          this.fixtureState.setCurrentFixtureNumber(selectedValue);
        }
        break;

      case 'page':
        const pageEvent = event.data;
        if (fixtures && pageEvent.pageIndex >= 0 && pageEvent.pageIndex < fixtures.length) {
          const selectedFixture = fixtures[pageEvent.pageIndex];
          this.fixtureState.setCurrentFixture(selectedFixture);
          this.fixtureState.setCurrentFixtureNumber(pageEvent.pageIndex + 1);
        }
        break;

      case 'first':
        if (fixtures && fixtures.length > 0) {
          this.fixtureState.setCurrentFixtureNumber(1);
          this.fixtureState.setCurrentFixture(fixtures[0]);
        }
        break;

      case 'last':
        if (fixtures && fixtures.length > 0) {
          this.fixtureState.setCurrentFixtureNumber(currentState.totalFixtures);
          this.fixtureState.setCurrentFixture(fixtures[currentState.totalFixtures - 1]);
        }
        break;

      case 'previous':
        if (currentState.currentFixtureNumber > 1 && fixtures && fixtures.length > 0) {
          const newFixtureNumber = currentState.currentFixtureNumber - 1;
          const newFixture = fixtures[newFixtureNumber - 1];
          this.fixtureState.setCurrentFixtureNumber(newFixtureNumber);
          this.fixtureState.setCurrentFixture(newFixture);
        }
        break;

      case 'next':
        if (currentState.currentFixtureNumber < currentState.totalFixtures && fixtures && fixtures.length > 0) {
          const newFixtureNumber = currentState.currentFixtureNumber + 1;
          const newFixture = fixtures[newFixtureNumber - 1];
          this.fixtureState.setCurrentFixtureNumber(newFixtureNumber);
          this.fixtureState.setCurrentFixture(newFixture);
        }
        break;
    }
  }

  onGameEditEvent(event: GameEditEvent): void {
    // Handle game editing events if needed
    console.log('Game edit event:', event);
  }

  onGameClicked(game: GameFixtureData): void {
    // Navigate to game details
    this.router.navigate(['/game-details', game.id]);
  }



  // Helper methods for template
  getFixtureTitle(): string {
    const currentFixture = this.fixtureState.currentState.currentFixture;
    if (!currentFixture) return 'N/A';

    // Check if any game in the fixture is a playoff/play-in game
    const playoffGame = currentFixture.games?.find((game: any) => game.isPlayoff);

    if (playoffGame && playoffGame.playoffStage) {
      return playoffGame.playoffStage;
    }

    // Default to fixture number for regular season games
    return `Fixture ${currentFixture.round || 'N/A'}`;
  }

  getCurrentFixtureDisplayText(): string {
    const currentState = this.fixtureState.currentState;
    const currentFixture = currentState.currentFixture;
    const currentFixtureNumber = currentState.currentFixtureNumber;
    
    if (!currentFixture) return currentFixtureNumber.toString();

    // Check if this is a playoff fixture
    if (currentFixture.isPlayoff && currentFixture.playoffDetails) {
      const { stage, format } = currentFixture.playoffDetails;
      return `${stage} (${format})`;
    }

    // Check if any game in the current fixture is a playoff game (fallback for older data)
    const playoffGame = currentFixture.games?.find((game: any) => game.isPlayoff);
    if (playoffGame && playoffGame.playoffStage) {
      return playoffGame.playoffStage;
    }

    // Default to fixture number for regular season games
    return `Fixture ${currentFixture.round || currentFixtureNumber}`;
  }

  isAdmin(): boolean {
    // Simple admin check - can be enhanced based on actual auth service
    return this.authService.isAdmin();
  }

  trackByGameId(_index: number, game: GameFixtureData): string {
    return game.id;
  }
}
