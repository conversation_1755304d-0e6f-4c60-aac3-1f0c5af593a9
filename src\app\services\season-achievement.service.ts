import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

export enum ACHIEVEMENT_TYPE {
  CHAMPIONSHIP_WINNER = "Championship Winner",
  FINALIST = "Finalist",
  THIRD_PLACE = "Third Place",
  TOP_SCORER = "Top Scorer",
  TOP_ASSIST_PROVIDER = "Top Assist Provider",
  BEST_GOALKEEPER = "Best Goalkeeper",
  BEST_CENTER_BACK = "Best Center Back",
  BEST_DEFENSIVE_MIDFIELDER = "Best Defensive Midfielder",
  BEST_MIDFIELDER = "Best Midfielder",
  BEST_ATTACKING_MIDFIELDER = "Best Attacking Midfielder",
  BEST_WINGER = "Best Winger",
  BEST_STRIKER = "Best Striker",
  MOST_CLEAN_SHEETS = "Most Clean Sheets",
  PLAYER_OF_THE_SEASON = "Player of the Season",
  TEAM_OF_THE_SEASON = "Team of the Season"
}

export interface PlayerAchievement {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  achievementType: ACHIEVEMENT_TYPE;
  rank?: number;
  stats: {
    goals?: number;
    assists?: number;
    cleanSheets?: number;
    avgRating?: number;
    games?: number;
    playerOfTheMatch?: number;
  };
  description?: string;
}

export interface TeamAchievement {
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  achievementType: ACHIEVEMENT_TYPE;
  rank?: number;
  stats: {
    wins?: number;
    losses?: number;
    draws?: number;
    goalsScored?: number;
    goalsConceded?: number;
    points?: number;
    goalDifference?: number;
  };
  description?: string;
}

export interface SeasonAchievements {
  id: string;
  seasonNumber: number;
  league: string;
  leagueName: string;
  startDate: Date;
  endDate: Date;
  playerAchievements: PlayerAchievement[];
  teamAchievements: TeamAchievement[];
  champion?: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
  };
  finalist?: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
  };
  thirdPlace?: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
  };
  createdAt: Date;
  createdBy: string;
}

export interface SeasonEndPreview {
  seasonNumber: number;
  leagueId: string;
  leagueName: string;
  isSeasonComplete: boolean;
  totalGames: number;
  completedGames: number;
  remainingGames: number;
  champion?: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
  };
  finalist?: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
  };
  thirdPlace?: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
  };
  topScorers: Array<{
    playerId: string;
    playerName: string;
    playerImgUrl?: string;
    teamName: string;
    goals: number;
    games: number;
    rank: number;
  }>;
  topAssists: Array<{
    playerId: string;
    playerName: string;
    playerImgUrl?: string;
    teamName: string;
    assists: number;
    games: number;
    rank: number;
  }>;
  bestByPosition: Array<{
    position: string;
    playerId: string;
    playerName: string;
    playerImgUrl?: string;
    teamName: string;
    avgRating: number;
    games: number;
  }>;
  warnings: string[];
}

export interface SeasonEndRequest {
  leagueId: string;
  seasonNumber: number;
  endDate?: Date;
  forceEnd?: boolean;
}

export interface SeasonEndResult {
  success: boolean;
  seasonAchievements: SeasonAchievements;
  playersUpdated: number;
  teamsUpdated: number;
  message: string;
}

export interface PlayerAchievementHistory {
  playerId: string;
  achievements: Array<{
    seasonNumber: number;
    leagueName: string;
    achievementType: string;
    rank?: number;
    description?: string;
    year: number;
  }>;
}

export interface TeamAchievementHistory {
  teamId: string;
  achievements: Array<{
    seasonNumber: number;
    leagueName: string;
    achievementType: string;
    rank?: number;
    description?: string;
    year: number;
  }>;
}

@Injectable({
  providedIn: 'root'
})
export class SeasonAchievementService {
  private readonly SEASON_ACHIEVEMENTS_URL = 'season-achievements';

  constructor(private apiService: ApiService) { }

  /**
   * Preview what achievements would be recorded if season ended now
   */
  async previewSeasonEnd(leagueId: string, seasonNumber?: number): Promise<SeasonEndPreview> {
    const url = seasonNumber
      ? `${this.SEASON_ACHIEVEMENTS_URL}/preview/${leagueId}/${seasonNumber}`
      : `${this.SEASON_ACHIEVEMENTS_URL}/preview/${leagueId}`;

    const response = await this.apiService.get<SeasonEndPreview>(url);
    return response.data;
  }

  /**
   * Manually end a season and record all achievements
   */
  async endSeason(request: SeasonEndRequest): Promise<SeasonEndResult> {
    const response = await this.apiService.post<SeasonEndResult>(
      `${this.SEASON_ACHIEVEMENTS_URL}/end-season`,
      request
    );
    return response.data;
  }

  /**
   * Get all season achievements for a league
   */
  async getLeagueSeasonAchievements(leagueId: string): Promise<SeasonAchievements[]> {
    const response = await this.apiService.get<SeasonAchievements[]>(
      `${this.SEASON_ACHIEVEMENTS_URL}/league/${leagueId}`
    );
    return response.data;
  }

  /**
   * Get season achievements for a specific season
   */
  async getSeasonAchievements(leagueId: string, seasonNumber: number): Promise<SeasonAchievements | null> {
    try {
      const response = await this.apiService.get<SeasonAchievements>(
        `${this.SEASON_ACHIEVEMENTS_URL}/league/${leagueId}/season/${seasonNumber}`
      );
      return response.data;
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Get achievement history for a player
   */
  async getPlayerAchievementHistory(playerId: string): Promise<PlayerAchievementHistory> {
    const response = await this.apiService.get<PlayerAchievementHistory>(
      `${this.SEASON_ACHIEVEMENTS_URL}/player/${playerId}/history`
    );
    return response.data;
  }

  /**
   * Get achievement history for a team
   */
  async getTeamAchievementHistory(teamId: string): Promise<TeamAchievementHistory> {
    const response = await this.apiService.get<TeamAchievementHistory>(
      `${this.SEASON_ACHIEVEMENTS_URL}/team/${teamId}/history`
    );
    return response.data;
  }

  /**
   * Manually add an achievement to a player
   */
  async addManualPlayerAchievement(achievementData: {
    playerId: string;
    seasonNumber: number;
    achievementType: ACHIEVEMENT_TYPE;
    rank?: number;
    stats?: {
      goals?: number;
      assists?: number;
      cleanSheets?: number;
      avgRating?: number;
      games?: number;
      playerOfTheMatch?: number;
    };
    description?: string;
  }): Promise<{ success: boolean; message: string }> {
    const response = await this.apiService.post<{ success: boolean; message: string }>(
      `${this.SEASON_ACHIEVEMENTS_URL}/manual-achievement`,
      achievementData
    );
    return response.data;
  }

  /**
   * Check if a season can be ended
   */
  async canEndSeason(leagueId: string, seasonNumber: number): Promise<{ canEnd: boolean; reason?: string }> {
    const response = await this.apiService.get<{ canEnd: boolean; reason?: string }>(
      `${this.SEASON_ACHIEVEMENTS_URL}/can-end/${leagueId}/${seasonNumber}`
    );
    return response.data;
  }

  /**
   * Get top performers for a season
   */
  async getSeasonTopPerformers(leagueId: string, seasonNumber: number): Promise<{
    topScorers: PlayerAchievement[];
    topAssists: PlayerAchievement[];
    bestByPosition: PlayerAchievement[];
  }> {
    const response = await this.apiService.get<any>(
      `${this.SEASON_ACHIEVEMENTS_URL}/top-performers/${leagueId}/${seasonNumber}`
    );
    return response.data;
  }

  /**
   * Get championship results for a season
   */
  async getChampionshipResults(leagueId: string, seasonNumber: number): Promise<{
    champion?: TeamAchievement;
    finalist?: TeamAchievement;
    thirdPlace?: TeamAchievement;
  }> {
    const response = await this.apiService.get<any>(
      `${this.SEASON_ACHIEVEMENTS_URL}/championship/${leagueId}/${seasonNumber}`
    );
    return response.data;
  }
}
