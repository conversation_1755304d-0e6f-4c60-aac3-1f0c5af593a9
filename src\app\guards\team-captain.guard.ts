import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError, take } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { TeamService } from '../services/team.service';
import { NotificationService } from '../services/notification.service';

@Injectable({
  providedIn: 'root'
})
export class TeamCaptainGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private teamService: TeamService,
    private router: Router,
    private notificationService: NotificationService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    return this.authService.currentUser$.pipe(
      take(1),
      map(user => {
        if (!user) {
          this.router.navigate(['/login'], { 
            queryParams: { returnUrl: state.url } 
          });
          return false;
        }

        // Admin can always access
        if (user.role === 'admin') {
          return true;
        }

        // Check if user has associated players
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) {
          this.notificationService.error('You must be associated with a player to access this feature');
          this.router.navigate(['/profile']);
          return false;
        }

        // For team-specific routes, check if user is captain of that team
        const teamId = route.params['id'] || route.params['teamId'];
        if (teamId) {
          return this.checkTeamCaptainStatus(teamId, user.associatedPlayers);
        }

        // For general team captain routes, just check if user has any captain role
        return this.checkAnyCaptainStatus(user.associatedPlayers);
      }),
      catchError(() => {
        this.notificationService.error('Error checking permissions');
        this.router.navigate(['/']);
        return of(false);
      })
    );
  }

  private async checkTeamCaptainStatus(teamId: string, associatedPlayers: string[]): Promise<boolean> {
    try {
      const team = await this.teamService.getTeamById(teamId);
      
      if (!team.captain) {
        this.notificationService.error('This team has no captain assigned');
        return false;
      }

      const isCaptain = associatedPlayers.includes(team.captain.id);
      
      if (!isCaptain) {
        this.notificationService.error('You must be the captain of this team to access this feature');
        this.router.navigate(['/teams']);
        return false;
      }

      return true;
    } catch (error) {
      this.notificationService.error('Error checking team captain status');
      this.router.navigate(['/teams']);
      return false;
    }
  }

  private async checkAnyCaptainStatus(associatedPlayers: string[]): Promise<boolean> {
    try {
      const allTeams = await this.teamService.getAllTeams();
      
      const isCaptainOfAnyTeam = allTeams.some(team => 
        team.captain && associatedPlayers.includes(team.captain.id)
      );

      if (!isCaptainOfAnyTeam) {
        this.notificationService.error('You must be a team captain to access this feature');
        this.router.navigate(['/teams']);
        return false;
      }

      return true;
    } catch (error) {
      this.notificationService.error('Error checking captain status');
      this.router.navigate(['/teams']);
      return false;
    }
  }
}
