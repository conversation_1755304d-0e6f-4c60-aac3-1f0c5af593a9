import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

export interface CommentDTO {
  id: string;
  gameId: string;
  userId: string;
  content: string;
  parentCommentId?: string;
  likesCount: number;
  isLikedByUser: boolean;
  isEdited: boolean;
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: string;
    name: string;
    profileImage?: string;
  };
  replies?: CommentDTO[];
}

export interface CommentsResponse {
  comments: CommentDTO[];
  totalCount: number;
  hasMore: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CommentService {
  private readonly COMMENTS_CONTROLLER_URL = 'comments';

  constructor(private apiService: ApiService) {}

  async createComment(
    gameId: string,
    content: string,
    parentCommentId?: string
  ): Promise<CommentDTO> {
    const endpoint = `${this.COMMENTS_CONTROLLER_URL}/game/${gameId}`;

    try {
      const response = await this.apiService.post<CommentDTO>(
        endpoint,
        { content, parentCommentId }
      );
      return this.parseCommentDates(response.data);
    } catch (error) {
      console.error('Comment creation failed:', error);
      throw error;
    }
  }

  async updateComment(commentId: string, content: string): Promise<CommentDTO> {
    const response = await this.apiService.put<CommentDTO>(
      `${this.COMMENTS_CONTROLLER_URL}/${commentId}`,
      { content }
    );
    return response.data;
  }

  async deleteComment(commentId: string): Promise<void> {
    await this.apiService.delete(`${this.COMMENTS_CONTROLLER_URL}/${commentId}`);
  }

  async getCommentsByGame(
    gameId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<CommentsResponse> {
    const response = await this.apiService.get<CommentsResponse>(
      `${this.COMMENTS_CONTROLLER_URL}/game/${gameId}?page=${page}&limit=${limit}`
    );
    return this.parseCommentsResponse(response.data);
  }

  async likeComment(commentId: string): Promise<CommentDTO> {
    const response = await this.apiService.post<CommentDTO>(
      `${this.COMMENTS_CONTROLLER_URL}/${commentId}/like`,
      {}
    );
    return this.parseCommentDates(response.data);
  }

  async unlikeComment(commentId: string): Promise<CommentDTO> {
    const response = await this.apiService.delete<CommentDTO>(
      `${this.COMMENTS_CONTROLLER_URL}/${commentId}/like`
    );
    return this.parseCommentDates(response.data);
  }

  async getUserComments(limit?: number): Promise<CommentDTO[]> {
    const params = limit ? `?limit=${limit}` : '';
    const response = await this.apiService.get<CommentDTO[]>(
      `${this.COMMENTS_CONTROLLER_URL}/user/my-comments${params}`
    );
    return response.data.map(comment => this.parseCommentDates(comment));
  }

  private parseCommentDates(comment: CommentDTO): CommentDTO {
    return {
      ...comment,
      createdAt: new Date(comment.createdAt),
      updatedAt: new Date(comment.updatedAt),
      replies: comment.replies?.map(reply => this.parseCommentDates(reply))
    };
  }

  private parseCommentsResponse(response: CommentsResponse): CommentsResponse {
    return {
      ...response,
      comments: response.comments.map(comment => this.parseCommentDates(comment))
    };
  }

  formatTimeAgo(date: Date): string {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - new Date(date).getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}m ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}h ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}d ago`;
    } else {
      return new Date(date).toLocaleDateString();
    }
  }

  validateCommentContent(content: string): { isValid: boolean; error?: string } {
    if (!content || content.trim().length === 0) {
      return { isValid: false, error: 'Comment cannot be empty' };
    }

    if (content.length > 1000) {
      return { isValid: false, error: 'Comment cannot exceed 1000 characters' };
    }

    return { isValid: true };
  }

  getDefaultProfileImage(): string {
    return 'assets/icons/default-profile.png';
  }
}
