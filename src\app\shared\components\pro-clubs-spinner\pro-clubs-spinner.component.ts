import { Component, OnInit } from '@angular/core';

interface Particle {
  x: number;
  y: number;
  delay: number;
}

@Component({
  selector: 'pro-clubs-spinner',
  templateUrl: './pro-clubs-spinner.component.html',
  styleUrl: './pro-clubs-spinner.component.scss'
})
export class ProClubsSpinnerComponent implements OnInit {

  particles: Particle[] = [];
  loadingLetters: string[] = [];

  ngOnInit() {
    this.generateParticles();
    this.setupLoadingText();
  }

  private generateParticles() {
    this.particles = [];
    for (let i = 0; i < 20; i++) {
      this.particles.push({
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 2
      });
    }
  }

  private setupLoadingText() {
    this.loadingLetters = 'LOADING'.split('');
  }
}
