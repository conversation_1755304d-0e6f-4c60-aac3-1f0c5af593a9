.highlight-card {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
    animation: fadeInUp 0.6s ease-out both;
    transition: all 0.3s ease;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    }

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-md);
        background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);

        .header-content {
            .card-title {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                font-size: var(--text-base);
                font-weight: var(--font-weight-semibold);
                color: var(--text-primary);
                margin: 0;

                i {
                    color: var(--primary-500);
                    font-size: var(--text-sm);
                }
            }
        }

        .card-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: var(--surface-primary);
            border-radius: var(--radius-full);
            box-shadow: var(--shadow-sm);

            i {
                font-size: var(--text-sm);
            }
        }
    }

    .card-content {
        padding: var(--spacing-md) var(--spacing-xl) var(--spacing-xl);

        .highlight-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);

            .avatar-container {
                position: relative;
                flex-shrink: 0;

                .avatar-image {
                    width: 60px;
                    height: 60px;
                    border-radius: var(--radius-full);
                    object-fit: cover;
                    border: 2px solid var(--border-primary);
                    transition: all 0.3s ease;

                    &.player-avatar {
                        border-color: #6366f1;
                    }

                    &.team-avatar {
                        border-color: #ec4899;
                    }
                }

                .avatar-overlay {
                    position: absolute;
                    top: -6px;
                    right: -6px;
                    width: 24px;
                    height: 24px;
                    background: #fcd34d;
                    border-radius: var(--radius-full);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 2px solid white;
                    box-shadow: var(--shadow-sm);

                    i {
                        font-size: var(--text-xs);
                        color: #7c3aed;
                    }
                }
            }

            .info-details {
                flex: 1;
                min-width: 0;

                .highlight-name {
                    font-size: var(--text-lg);
                    font-weight: var(--font-weight-bold);
                    color: var(--text-primary);
                    margin: 0 0 var(--spacing-xs) 0;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .stats-badge {
                    display: inline-block;
                    background: rgba(99, 102, 241, 0.1);
                    color: var(--primary-600);
                    padding: var(--spacing-xs) var(--spacing-sm);
                    border-radius: var(--radius-md);
                    font-size: var(--text-xs);
                    font-weight: var(--font-weight-medium);
                    margin-bottom: var(--spacing-sm);
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .primary-stat {
                    display: flex;
                    align-items: baseline;
                    gap: var(--spacing-sm);

                    .stat-value {
                        font-size: var(--text-xl);
                        font-weight: var(--font-weight-bold);
                        color: var(--primary-600);
                        line-height: 1;
                    }

                    .stat-label {
                        font-size: var(--text-sm);
                        font-weight: var(--font-weight-medium);
                        color: var(--text-secondary);
                        text-transform: lowercase;
                    }
                }
            }
        }
    }

    @media (max-width: 768px) {
        .card-header {
            padding: var(--spacing-md) var(--spacing-lg) var(--spacing-sm);
        }

        .card-content {
            padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-lg);

            .highlight-info {
                gap: var(--spacing-md);

                .avatar-container .avatar-image {
                    width: 50px;
                    height: 50px;
                }

                .info-details {
                    .highlight-name {
                        font-size: var(--text-base);
                    }

                    .primary-stat .stat-value {
                        font-size: var(--text-lg);
                    }
                }
            }
        }
    }
}
