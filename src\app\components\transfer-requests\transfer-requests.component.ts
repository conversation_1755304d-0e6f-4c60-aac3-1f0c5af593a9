import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { TransferRequestService, TransferRequest, CreateTransferRequestData, ProcessTransferRequestData } from '../../services/transfer-request.service';
import { TeamService } from '../../services/team.service';
import { PlayerService } from '../../services/player.service';
import { AuthService } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';
import { TeamDTO } from '@pro-clubs-manager/shared-dtos';

@Component({
  selector: 'app-transfer-requests',
  templateUrl: './transfer-requests.component.html',
  styleUrls: ['./transfer-requests.component.scss']
})
export class TransferRequestsComponent implements OnInit, OnDestroy {
  transferRequests: TransferRequest[] = [];
  teams: TeamDTO[] = [];
  loading = false;
  showCreateForm = false;
  selectedTeamId: string | null = null;
  currentUser: any = null;

  createForm: FormGroup;
  processForm: FormGroup;
  selectedRequest: TransferRequest | null = null;
  showProcessModal = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private transferRequestService: TransferRequestService,
    private teamService: TeamService,
    private playerService: PlayerService,
    private authService: AuthService,
    private notificationService: NotificationService,
    private fb: FormBuilder
  ) {
    this.createForm = this.fb.group({
      playerId: ['', Validators.required],
      fromTeamId: ['', Validators.required],
      toTeamId: ['', Validators.required],
      message: ['', Validators.maxLength(500)]
    });

    this.processForm = this.fb.group({
      action: ['', Validators.required],
      reason: ['', Validators.maxLength(500)]
    });
  }

  ngOnInit(): void {
    this.subscribeToServices();
    this.loadInitialData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private subscribeToServices(): void {
    const transferRequestsSubscription = this.transferRequestService.transferRequests$.subscribe(requests => {
      this.transferRequests = requests;
    });

    const loadingSubscription = this.transferRequestService.loading$.subscribe(loading => {
      this.loading = loading;
    });

    const userSubscription = this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });

    this.subscriptions.push(transferRequestsSubscription, loadingSubscription, userSubscription);
  }

  private async loadInitialData(): Promise<void> {
    try {
      // Load teams
      this.teams = await this.teamService.getAllTeams();
      
      // If user has associated player, find their team and load transfer requests
      if (this.currentUser?.associatedPlayers?.length > 0) {
        const playerId = this.currentUser.associatedPlayers[0];
        const player = await this.playerService.getPlayerById(playerId);
        if (player.team) {
          this.selectedTeamId = player.team.id;
          await this.loadTransferRequests(player.team.id);
        }
      }
    } catch (error) {
      this.notificationService.error('Failed to load initial data');
    }
  }

  async loadTransferRequests(teamId: string): Promise<void> {
    try {
      await this.transferRequestService.getTransferRequestsByTeam(teamId);
    } catch (error) {
      this.notificationService.error('Failed to load transfer requests');
    }
  }

  async onTeamChange(teamId: string): Promise<void> {
    this.selectedTeamId = teamId;
    if (teamId) {
      await this.loadTransferRequests(teamId);
    } else {
      this.transferRequestService.clearTransferRequests();
    }
  }

  async createTransferRequest(): Promise<void> {
    if (this.createForm.invalid) {
      return;
    }

    try {
      const data: CreateTransferRequestData = this.createForm.value;
      await this.transferRequestService.createTransferRequest(data);
      
      this.notificationService.success('Transfer request created successfully');
      this.createForm.reset();
      this.showCreateForm = false;
    } catch (error: any) {
      this.notificationService.error(error.message || 'Failed to create transfer request');
    }
  }

  openProcessModal(request: TransferRequest): void {
    this.selectedRequest = request;
    this.processForm.reset();
    this.showProcessModal = true;
  }

  closeProcessModal(): void {
    this.selectedRequest = null;
    this.showProcessModal = false;
    this.processForm.reset();
  }

  async processTransferRequest(): Promise<void> {
    if (!this.selectedRequest || this.processForm.invalid) {
      return;
    }

    try {
      const data: ProcessTransferRequestData = this.processForm.value;
      await this.transferRequestService.processTransferRequest(this.selectedRequest.id, data);
      
      const action = data.action === 'approve' ? 'approved' : 'rejected';
      this.notificationService.success(`Transfer request ${action} successfully`);
      this.closeProcessModal();
    } catch (error: any) {
      this.notificationService.error(error.message || 'Failed to process transfer request');
    }
  }

  async cancelTransferRequest(request: TransferRequest): Promise<void> {
    if (!confirm('Are you sure you want to cancel this transfer request?')) {
      return;
    }

    try {
      await this.transferRequestService.cancelTransferRequest(request.id);
      this.notificationService.success('Transfer request cancelled successfully');
    } catch (error: any) {
      this.notificationService.error(error.message || 'Failed to cancel transfer request');
    }
  }

  async refreshTransferRequests(): Promise<void> {
    if (this.selectedTeamId) {
      await this.loadTransferRequests(this.selectedTeamId);
      this.notificationService.success('Transfer requests refreshed');
    }
  }

  // Helper methods
  getStatusColor(status: string): string {
    return this.transferRequestService.getStatusColor(status);
  }

  getStatusIcon(status: string): string {
    return this.transferRequestService.getStatusIcon(status);
  }

  isExpired(request: TransferRequest): boolean {
    return this.transferRequestService.isExpired(request);
  }

  canProcess(request: TransferRequest): boolean {
    return this.transferRequestService.canProcess(request);
  }

  canCancel(request: TransferRequest): boolean {
    return this.transferRequestService.canCancel(request);
  }

  getTimeRemaining(request: TransferRequest): string {
    return this.transferRequestService.getTimeRemaining(request);
  }

  isCurrentUserCaptain(teamId: string): boolean {
    if (!this.currentUser?.associatedPlayers?.length) {
      return false;
    }

    const team = this.teams.find(t => t.id === teamId);
    return team?.captain?.id === this.currentUser.associatedPlayers[0];
  }

  isAdmin(): boolean {
    return this.currentUser?.role === 'admin';
  }

  getPlayerImage(request: TransferRequest): string {
    return request.player?.imgUrl || 'assets/icons/default-profile.png';
  }

  getTeamImage(team: any): string {
    return team?.imgUrl || 'assets/icons/default-team.png';
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getAvailablePlayers(fromTeamId: string): any[] {
    const team = this.teams.find(t => t.id === fromTeamId);
    return team?.players || [];
  }

  getAvailableTeams(excludeTeamId?: string): TeamDTO[] {
    return this.teams.filter(team => team.id !== excludeTeamId);
  }

  onFromTeamChange(): void {
    // Reset player selection when from team changes
    this.createForm.patchValue({ playerId: '' });
  }

  trackByRequestId(index: number, request: TransferRequest): string {
    return request.id;
  }
}
