import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TopScorer } from '@pro-clubs-manager/shared-dtos';
import { DEFENSIVE_POSITIONS } from '../../shared/models/player.model';

@Component({
  selector: 'totw-card',
  templateUrl: './totw-card.component.html',
  styleUrl: './totw-card.component.scss'
})
export class TotwCardComponent {
  @Input() totwPlayer: any | null = null;
  @Output() onCardClickEvent: EventEmitter<string> = new EventEmitter<string>();

  constructor() { }

  ngOnInit() {
    console.log(this.totwPlayer);
  }

  onCardClick(): void {
    const playerId = this.totwPlayer?.player?.playerId ||
                    this.totwPlayer?.player?.id ||
                    this.totwPlayer?.playerId;

    if (!playerId) {
      console.error('Player ID is undefined in TOTW card:', this.totwPlayer);
      return;
    }

    console.log('TOTW Card click - Player ID:', playerId);
    this.onCardClickEvent.emit(playerId);
  }

  isDefender(): boolean {
    return DEFENSIVE_POSITIONS.includes(this.totwPlayer.position) && this.totwPlayer.position !== 'GK';
  }

  isGoalkeeper(): boolean {
    return this.totwPlayer.position === 'GK';
  }
}
