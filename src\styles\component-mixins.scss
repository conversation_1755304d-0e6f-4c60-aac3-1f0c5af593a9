/* === COMPONENT MIXINS FOR CSS OPTIMIZATION === */

/* Card Mixins */
@mixin card-base {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

@mixin card-hover {
    &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-300);
    }
}

@mixin card-interactive {
    @include card-base;
    @include card-hover;
    cursor: pointer;
}

/* Header Mixins */
@mixin section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-bottom: 1px solid var(--border-primary);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

@mixin page-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    i {
        color: var(--accent-primary);
        font-size: var(--text-2xl);
    }
}

/* Button Mixins */
@mixin btn-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    font-size: var(--text-sm);
    line-height: 1;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

@mixin btn-primary {
    @include btn-base;
    background: var(--primary-500);
    color: var(--text-inverse);
    border-color: var(--primary-500);

    &:hover:not(:disabled) {
        background: var(--primary-600);
        border-color: var(--primary-600);
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
    }
}

@mixin btn-secondary {
    @include btn-base;
    background: var(--surface-secondary);
    color: var(--text-primary);
    border-color: var(--border-primary);

    &:hover:not(:disabled) {
        background: var(--surface-hover);
        border-color: var(--primary-300);
    }
}

/* Layout Mixins */
@mixin flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

@mixin flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@mixin flex-column {
    display: flex;
    flex-direction: column;
}

@mixin grid-responsive($min-width: 300px) {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax($min-width, 1fr));
    gap: var(--spacing-lg);
}

/* Container Mixins */
@mixin container-responsive {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);

    @media (max-width: 768px) {
        padding: 0 var(--spacing-md);
    }

    @media (max-width: 480px) {
        padding: 0 var(--spacing-sm);
    }
}

@mixin page-container {
    @include container-responsive;
    min-height: 100vh;
    padding-top: var(--spacing-xl);
    padding-bottom: var(--spacing-xl);
}

/* Text Mixins */
@mixin text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@mixin text-clamp($lines: 2) {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Animation Mixins */
@mixin hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }
}

@mixin fade-in($duration: 0.3s) {
    animation: fadeIn $duration ease-out;
}

@mixin slide-up($duration: 0.3s) {
    animation: slideUp $duration ease-out;
}

/* Form Mixins */
@mixin input-base {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-sm);
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    &::placeholder {
        color: var(--text-tertiary);
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: var(--surface-secondary);
    }
}

/* Badge Mixins */
@mixin badge-base {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
    line-height: 1;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

@mixin badge-success {
    @include badge-base;
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-600);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

@mixin badge-warning {
    @include badge-base;
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-600);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

@mixin badge-error {
    @include badge-base;
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-600);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Avatar Mixins */
@mixin avatar-base($size: 40px) {
    width: $size;
    height: $size;
    border-radius: var(--radius-full);
    object-fit: cover;
    border: 2px solid var(--border-primary);
}

/* Loading Mixins */
@mixin loading-spinner($size: 20px) {
    display: inline-block;
    width: $size;
    height: $size;
    border: 2px solid var(--border-primary);
    border-radius: 50%;
    border-top-color: var(--primary-500);
    animation: spin 1s ease-in-out infinite;
}

/* Glass Effect Mixins */
@mixin glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@mixin glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Mixins */
@mixin gradient-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

@mixin gradient-accent {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-600));
}

@mixin gradient-text {
    background: linear-gradient(135deg, var(--primary-500), var(--accent-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive Mixins */
@mixin mobile-only {
    @media (max-width: 767px) {
        @content;
    }
}

@mixin tablet-up {
    @media (min-width: 768px) {
        @content;
    }
}

@mixin desktop-up {
    @media (min-width: 1024px) {
        @content;
    }
}

@mixin large-desktop-up {
    @media (min-width: 1200px) {
        @content;
    }
}

/* Animation Keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}
