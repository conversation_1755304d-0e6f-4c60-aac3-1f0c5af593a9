.transfer-requests-container {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;

    h2 {
      margin: 0;
      color: var(--text-color);
      font-size: 1.75rem;
      font-weight: 600;

      i {
        margin-right: 0.5rem;
        color: var(--primary-color);
      }
    }

    .header-actions {
      display: flex;
      gap: 0.75rem;
    }
  }
}

.filters {
  background: var(--background-secondary);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);

  .filter-group {
    display: flex;
    align-items: center;
    gap: 1rem;

    label {
      font-weight: 600;
      color: var(--text-color);
      min-width: 120px;
    }

    .form-select {
      min-width: 200px;
    }
  }
}

.create-form {
  margin-bottom: 2rem;

  .form-card {
    background: var(--background-secondary);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);

    h3 {
      margin: 0 0 1.5rem 0;
      color: var(--text-color);
      font-size: 1.25rem;
      font-weight: 600;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1rem;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    .form-group {
      margin-bottom: 1rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--text-color);
      }

      .form-select,
      .form-textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        background: var(--background-color);
        color: var(--text-color);
        font-size: 0.875rem;
        transition: border-color 0.2s ease;

        &:focus {
          outline: none;
          border-color: var(--primary-color);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }

      .form-textarea {
        resize: vertical;
        min-height: 80px;
      }

      .character-count {
        display: block;
        text-align: right;
        margin-top: 0.25rem;
        font-size: 0.75rem;
        color: var(--text-secondary);
      }
    }

    .form-actions {
      display: flex;
      gap: 0.75rem;
      margin-top: 1.5rem;
    }
  }
}

.requests-list {
  .loading-indicator {
    text-align: center;
    padding: 3rem;
    color: var(--text-primary);

    i {
      font-size: 2rem;
      margin-bottom: 1rem;
      color: var(--primary-color);
    }
  }

  .empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-primary);

    i {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: var(--primary-color);
    }

    h3 {
      margin: 0 0 0.5rem 0;
      color: var(--text-color);
    }
  }
}

.request-card {
  background: var(--background-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .request-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);

    .request-status {
      display: flex;
      align-items: center;
      gap: 1rem;

      .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;

        &.status-pending {
          background: var(--warning-color-light);
          color: var(--warning-color);
        }

        &.status-approved {
          background: var(--success-color-light);
          color: var(--success-color);
        }

        &.status-rejected {
          background: var(--danger-color-light);
          color: var(--danger-color);
        }

        &.status-cancelled {
          background: var(--text-secondary-light);
          color: var(--text-secondary);
        }
      }

      .request-date {
        font-size: 0.875rem;
        color: var(--text-secondary);
      }
    }

    .request-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .request-content {
    padding: 1.5rem;

    .player-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .player-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid var(--border-color);
      }

      .player-details {
        h4 {
          margin: 0 0 0.25rem 0;
          color: var(--text-color);
          font-size: 1.125rem;
          font-weight: 600;
        }

        .player-position {
          color: var(--text-secondary);
          font-size: 0.875rem;
          font-weight: 500;
        }
      }
    }

    .transfer-direction {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 2rem;
      margin-bottom: 1.5rem;
      padding: 1rem;
      background: var(--background-color);
      border-radius: 8px;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;

        .transfer-arrow {
          transform: rotate(90deg);
        }
      }

      .team-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        text-align: center;

        .team-logo {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
          border: 2px solid var(--border-color);
        }

        .team-name {
          font-weight: 600;
          color: var(--text-color);
          font-size: 0.875rem;
        }
      }

      .transfer-arrow {
        color: var(--primary-color);
        font-size: 1.5rem;
        transition: transform 0.2s ease;
      }
    }

    .request-message {
      margin-bottom: 1.5rem;
      padding: 1rem;
      background: var(--background-color);
      border-radius: 8px;
      border-left: 4px solid var(--primary-color);

      p {
        margin: 0;
        color: var(--text-color);
        font-size: 0.875rem;
        line-height: 1.5;
      }
    }

    .request-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.75rem;

      .detail-item {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;

        .label {
          font-size: 0.75rem;
          font-weight: 600;
          color: var(--text-primary);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .value {
          font-size: 0.875rem;
          color: var(--text-color);
          font-weight: 500;

          &.text-danger {
            color: var(--danger-color);
          }
        }
      }
    }
  }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;

  .modal-content {
    background: var(--background-color);
    border-radius: 12px;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      border-bottom: 1px solid var(--border-color);

      h3 {
        margin: 0;
        color: var(--text-color);
        font-size: 1.25rem;
        font-weight: 600;
      }

      .close-btn {
        background: transparent;
        border: none;
        color: var(--text-secondary);
        font-size: 1.25rem;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background: var(--background-secondary);
          color: var(--text-color);
        }
      }
    }

    .modal-body {
      padding: 1.5rem;

      .request-summary {
        background: var(--background-secondary);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;

        p {
          margin: 0 0 0.5rem 0;
          font-size: 0.875rem;
          color: var(--text-color);

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .radio-group {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;

        .radio-option {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem;
          border: 1px solid var(--border-color);
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            border-color: var(--primary-color);
            background: var(--background-secondary);
          }

          input[type="radio"] {
            margin: 0;
          }

          .radio-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            color: var(--text-color);
          }
        }
      }

      .modal-actions {
        display: flex;
        gap: 0.75rem;
        margin-top: 1.5rem;
        justify-content: flex-end;
      }
    }
  }
}

// Button styles
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }

  &.btn-primary {
    background: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
      background: var(--primary-color-dark);
    }
  }

  &.btn-secondary {
    background: var(--background-secondary);
    color: var(--text-color);
    border: 1px solid var(--border-color);

    &:hover:not(:disabled) {
      background: var(--background-color);
    }
  }

  &.btn-danger {
    background: var(--danger-color);
    color: white;

    &:hover:not(:disabled) {
      background: var(--danger-color-dark);
    }
  }

  .spinning {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
