import { Injectable } from '@angular/core';
import Tesseract from 'tesseract.js';

@Injectable({
  providedIn: 'root'
})
export class ImageToTextService {

  constructor() { }

  public extractText(image: File): Promise<string> {
    return new Promise((resolve, reject) => {
      Tesseract.recognize(
        image,
        'eng', // Language of text
        {
          logger: (info) => console.log(info), // Optional logging
        }
      )
        .then(({ data: { text } }) => {
          resolve(text); // Extracted text
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
}