import mongoose, { Schema, Document } from "mongoose";

export interface IUser extends Document {
  id: string;
  email: string;
  password?: string; // Optional for Google OAuth users
  firstName: string;
  lastName: string;
  googleId?: string; // For Google OAuth
  profilePicture?: string;
  isEmailVerified: boolean;
  associatedPlayers: mongoose.Types.ObjectId[]; // References to Player documents
  role: 'user' | 'admin';
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
}

const userSchema: Schema<IUser> = new Schema<IUser>(
  {
    email: { 
      type: String, 
      required: true, 
      unique: true,
      lowercase: true,
      trim: true
    },
    password: { 
      type: String, 
      required: function(this: IUser) {
        // Password is required only if googleId is not present
        return !this.googleId;
      }
    },
    firstName: { 
      type: String, 
      required: true,
      trim: true
    },
    lastName: { 
      type: String, 
      required: true,
      trim: true
    },
    googleId: { 
      type: String, 
      unique: true, 
      sparse: true // Allows multiple null values
    },
    profilePicture: { 
      type: String 
    },
    isEmailVerified: { 
      type: Boolean, 
      default: false 
    },
    associatedPlayers: [{ 
      type: mongoose.Schema.Types.ObjectId, 
      ref: "Player" 
    }],
    role: { 
      type: String, 
      enum: ['user', 'admin'], 
      default: 'user' 
    },
    lastLogin: { 
      type: Date 
    }
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt
    toJSON: { 
      virtuals: true,
      transform: function(doc, ret) {
        // Remove password from JSON output
        delete ret.password;
        return ret;
      }
    },
    id: true, // Use 'id' instead of '_id'
  }
);

// Virtual for full name
userSchema.virtual('fullName').get(function(this: IUser) {
  return `${this.firstName} ${this.lastName}`;
});

// Index for performance
userSchema.index({ email: 1 });
userSchema.index({ googleId: 1 });

const User = mongoose.model<IUser>("User", userSchema);

export default User;
