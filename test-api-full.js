﻿const https = require('https');

async function testItamarStats() {
  const itamarId = '67f2bd3317ec7db8bcd4f087';
  const apiUrl = 'proclubs-stats-server.duckdns.org';
  const path = `/player/${itamarId}`;
  
  console.log('Testing Itamar stats from deployed API...');
  console.log(`Calling: https://${apiUrl}${path}`);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: apiUrl,
      port: 443,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      rejectUnauthorized: false
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          
          console.log(`Status: ${res.statusCode}`);
          console.log('Full Response:', JSON.stringify(jsonData, null, 2));
          
          if (jsonData.currentSeason) {
            console.log('\n=== Current Season Stats ===');
            console.log('Games:', jsonData.currentSeason.games);
            console.log('Goals:', jsonData.currentSeason.goals);
            console.log('Assists:', jsonData.currentSeason.assists);
            console.log('Clean Sheets:', jsonData.currentSeason.cleanSheets);
            console.log('Player of the Match:', jsonData.currentSeason.playerOfTheMatch);
            console.log('Avg Rating:', jsonData.currentSeason.avgRating);
            
            console.log('\n=== Expected vs Actual ===');
            console.log('Expected: 25 games, 23 goals');
            console.log(`Actual: ${jsonData.currentSeason.games} games, ${jsonData.currentSeason.goals} goals`);
            
            if (jsonData.currentSeason.goals !== 23 || jsonData.currentSeason.games !== 25) {
              console.log('\n❌ MISMATCH DETECTED!');
              console.log('The backend fixes are not working as expected.');
            } else {
              console.log('\n✅ Stats match expected values!');
            }
          } else {
            console.log('\n❌ No currentSeason data found in response');
          }
          
          resolve(jsonData);
        } catch (error) {
          console.log('JSON Parse Error');
          console.log('Raw response:', data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('Request Error');
      console.error(error.message);
      reject(error);
    });

    req.end();
  });
}

testItamarStats()
  .then(() => {
    console.log('\nTest completed!');
  })
  .catch((error) => {
    console.error('Test failed:', error.message);
  });
