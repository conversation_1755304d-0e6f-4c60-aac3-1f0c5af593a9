import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { Column } from '../../shared/models/column.model';
import { Router } from '@angular/router';
import { LeagueService } from '../../services/league.service';
import { LEAGUE_ID } from '../../constants/constants';
import { TopAssister } from '../../shared/models/topassister.model';
import { SHORTENED_TOP_ASSISTS_COLUMNS, TOP_ASSISTS_COLUMNS } from './top-assists.definitions';
import { LeagueDataStateService } from '../../services/state/league-data-state.service';
import { Subscription } from 'rxjs';

const LIMIT = 10;

@Component({
  selector: 'top-assists',
  templateUrl: './top-assists.component.html',
  styleUrl: './top-assists.component.scss'
})
export class TopAssistsComponent implements OnInit, OnDestroy {
  displayedColumns: Column[] = [];
  topAssistsData: TopAssister[] = [];
  isLoading: boolean = false;

  @Input() hideTitle: boolean = false;

  private stateSubscription?: Subscription;

  constructor(
    private router: Router,
    private leagueService: LeagueService,
    private leagueDataState: LeagueDataStateService
  ) { }

  ngOnInit() {
    // Subscribe to state changes
    this.stateSubscription = this.leagueDataState.state$.subscribe(state => {
      this.topAssistsData = state.topAssisters.slice(0, LIMIT);
      this.isLoading = state.isTopAssistersLoading;
    });

    this.loadTopAssistsData();
  }

  ngOnDestroy() {
    if (this.stateSubscription) {
      this.stateSubscription.unsubscribe();
    }
  }

  private async loadTopAssistsData() {
    this.hideTitle ? (this.displayedColumns = SHORTENED_TOP_ASSISTS_COLUMNS) : (this.displayedColumns = TOP_ASSISTS_COLUMNS);

    try {
      const topAssistsResponse = await this.leagueService.getTopAssists(LEAGUE_ID, LIMIT);

      if (topAssistsResponse && topAssistsResponse.length > 0) {
        topAssistsResponse.map(topAssist => {
          topAssist.tableIcon = { name: topAssist.playerName, imgUrl: topAssist.playerImgUrl || '', isTeam: false };
          if (topAssist.assistsPerGame) {
            topAssist.assistsPerGame = parseFloat(topAssist.assistsPerGame.toFixed(2));
          }
        });
      }

      // The state is automatically updated by the service
    } catch (error) {
      console.error('Error loading top assists data:', error);
    }
  }

  refreshData(): void {
    this.leagueDataState.forceRefreshAll();
    this.loadTopAssistsData();
  }

  onPlayerClick($playerDetails: TopAssister): void {
    this.router.navigate(['/player-details', { id: $playerDetails.playerId }])
  }

  trackByPlayerId(_index: number, assister: TopAssister): string {
    return assister.playerId;
  }
}