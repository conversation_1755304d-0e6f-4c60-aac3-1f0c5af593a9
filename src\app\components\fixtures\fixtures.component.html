<!-- Loading State -->
<div class="loading-state" *ngIf="(isLoading$ | async) && isInitialLoad">
    <pro-clubs-spinner></pro-clubs-spinner>
    <h2>Loading Fixtures</h2>
    <p>Fetching the latest match schedule...</p>
</div>

<!-- Error State -->
<div class="error-state" *ngIf="loadingState === 'error'">
    <i class="fas fa-exclamation-triangle"></i>
    <h2>Failed to Load Fixtures</h2>
    <p>We couldn't load the fixtures. Please try again.</p>
    <button class="btn-primary" (click)="loadFixtures()">
        <i class="fas fa-redo"></i>
        Retry
    </button>
</div>

<!-- Main Content -->
<div class="fixtures-container" *ngIf="(fixtures$ | async) && loadingState === 'loaded'">
    <!-- View Mode Toggle -->
    <app-view-mode-toggle 
        [currentViewMode]="(viewMode$ | async) || 'fixtures'"
        [hideTitle]="hideTitle"
        (viewModeChanged)="onViewModeChanged($event)">
    </app-view-mode-toggle>

    <!-- Fixtures View -->
    <div class="fixtures-view" *ngIf="(viewMode$ | async) === 'fixtures'">
        <!-- Fixture Navigation -->
        <app-fixture-navigation
            [fixturesOptions]="(fixturesOptions$ | async) || []"
            [currentFixtureDisplayText]="getCurrentFixtureDisplayText()"
            [currentFixtureNumber]="(currentFixtureNumber$ | async) || 1"
            [totalFixtures]="(totalFixtures$ | async) || 0"
            (navigationEvent)="onNavigationEvent($event)">
        </app-fixture-navigation>

        <!-- Current Fixture Title -->
        <div class="fixture-title-section" *ngIf="currentFixture$ | async as currentFixture">
            <h2 class="fixture-title">{{ getFixtureTitle() }}</h2>
        </div>

        <!-- Games List -->
        <app-game-editing
            [games]="(currentFixture$ | async)?.games || []"
            [canEdit]="true"
            [isAdmin]="isAdmin()"
            (gameEditEvent)="onGameEditEvent($event)"
            (gameClicked)="onGameClicked($event)">
        </app-game-editing>
    </div>

    <!-- Date View -->
    <div class="date-view" *ngIf="(viewMode$ | async) === 'date'">
        <app-date-view
            [fixtures]="(fixtures$ | async) || []"
            [gamesByDate]="(gamesByDate$ | async) || {}"
            (gameClicked)="onGameClicked($event)">
        </app-date-view>
    </div>

    <!-- Playoffs View -->
    <div class="playoffs-view" *ngIf="(viewMode$ | async) === 'playoffs'">
        <app-brackets></app-brackets>
    </div>
</div>

<!-- No Fixtures State -->
<div class="no-fixtures" *ngIf="!(isLoading$ | async) && (!(fixtures$ | async) || ((fixtures$ | async)?.length || 0) === 0)">
    <i class="fas fa-calendar-times"></i>
    <h2>No Fixtures Available</h2>
    <p>There are no fixture rounds available at the moment.</p>
</div>
