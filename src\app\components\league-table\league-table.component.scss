/* === MODERN LEAGUE TABLE DESIGN === */

.league-table-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    min-height: 100vh;
    font-family: var(--font-sans);

    @media (max-width: 768px) {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }
}

/* === HEADER SECTION === */
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--warning-400), var(--warning-600));
    }

    @media (max-width: 768px) {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
}

.header-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.refresh-btn, .force-refresh-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
        background: var(--primary-hover);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    i.spinning {
        animation: spin 1s linear infinite;
    }
}

.force-refresh-btn {
    background: var(--warning-500);

    &:hover:not(:disabled) {
        background: var(--warning-600);
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.header-content {
    .header-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;

        i {
            color: var(--warning-500);
            font-size: var(--text-base);
        }
    }
}



/* === PODIUM SECTION === */
.podium-section {
    display: flex;
    justify-content: center;
    padding: var(--spacing-xl);
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }
}

.podium-container {
    display: flex;
    align-items: end;
    gap: var(--spacing-lg);
    max-width: 600px;
    width: 100%;
    min-height: 220px;

    @media (max-width: 768px) {
        gap: var(--spacing-md);
        min-height: 200px;
    }

    @media (max-width: 480px) {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);
        min-height: auto;
    }
}

.podium-team {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    flex: 1;
    justify-content: flex-end;

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--border-primary);
    }

    &.first-place {
        background: linear-gradient(135deg, var(--warning-200), var(--warning-300));
        border-color: var(--warning-500);
        transform: scale(1.2);
        min-height: 220px;
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);

        @media (max-width: 768px) {
            transform: scale(1.15);
            min-height: 200px;
        }

        @media (max-width: 480px) {
            transform: scale(1.1);
            min-height: 180px;
            width: 90%;
            max-width: 300px;
        }

        .podium-rank {
            background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
            color: var(--text-inverse);
            box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
        }

        .crown-icon {
            position: absolute;
            top: -10px;
            right: var(--spacing-md);
            color: var(--warning-600);
            font-size: var(--text-lg);
            animation: bounce 2s infinite;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .points-count {
            color: var(--warning-700) !important;
            font-weight: var(--font-weight-bold);
        }

        &:hover {
            transform: scale(1.1) translateY(-4px);
        }
    }

    &.second-place {
        min-height: 180px;
        background: linear-gradient(135deg, var(--neutral-100), var(--neutral-200));
        border-color: #C0C0C0;
        box-shadow: 0 6px 20px rgba(192, 192, 192, 0.2);

        .podium-rank {
            background: linear-gradient(135deg, #C0C0C0, #A8A8A8);
            color: var(--text-inverse);
            box-shadow: 0 4px 8px rgba(192, 192, 192, 0.3);
        }

        .points-count {
            color: var(--neutral-700) !important;
        }
    }

    &.third-place {
        min-height: 160px;
        background: linear-gradient(135deg, #F4A460, #DEB887);
        border-color: #CD7F32;
        box-shadow: 0 4px 15px rgba(205, 127, 50, 0.2);

        .podium-rank {
            background: linear-gradient(135deg, #CD7F32, #B8860B);
            color: var(--text-inverse);
            box-shadow: 0 4px 8px rgba(205, 127, 50, 0.3);
        }

        .points-count {
            color: #8B4513 !important;
        }
    }

    @media (max-width: 768px) {
        &.first-place {
            min-height: 180px;
        }

        &.second-place {
            min-height: 160px;
        }

        &.third-place {
            min-height: 140px;
        }
    }

    @media (max-width: 480px) {
        &.first-place {
            transform: none;
            order: -1;
            min-height: auto;
        }

        &.second-place {
            min-height: auto;
        }

        &.third-place {
            min-height: auto;
        }
    }
}

.podium-rank {
    position: absolute;
    top: -12px;
    left: var(--spacing-md);
    width: 32px;
    height: 32px;
    background: var(--slate-500);
    color: var(--text-inverse);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    font-size: var(--text-sm);
    border: 3px solid var(--surface-primary);
    z-index: 1;
}

.podium-logo {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    object-fit: cover;
    border: 2px solid var(--border-secondary);
    margin-bottom: var(--spacing-md);

    @media (max-width: 480px) {
        width: 50px;
        height: 50px;
    }
}

.podium-info {
    text-align: center;
    width: 100%;

    .podium-name {
        font-size: var(--text-base);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;
        line-height: 1.2;

        @media (max-width: 480px) {
            font-size: var(--text-sm);
        }
    }

    .podium-stats {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs);

        .points-count {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            color: var(--warning-600);
        }

        .points-label {
            font-size: var(--text-xs);
            color: var(--text-primary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-weight: var(--font-weight-medium);
        }
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* === THEME-SPECIFIC PODIUM STYLING === */
[data-theme="light"] {
    .podium-team {
        &.first-place {
            background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 20%, #f59e0b 100%);
            border-color: #d97706;
            box-shadow: 0 8px 25px rgba(217, 119, 6, 0.4);

            .podium-rank {
                background: linear-gradient(135deg, #d97706, #b45309);
                box-shadow: 0 4px 8px rgba(217, 119, 6, 0.4);
            }

            .crown-icon {
                color: #b45309;
                text-shadow: 0 2px 4px rgba(180, 83, 9, 0.5);
            }

            .points-count {
                color: #92400e !important;
                text-shadow: 0 1px 2px rgba(146, 64, 14, 0.3);
            }

            .podium-name {
                color: #78350f !important;
                font-weight: var(--font-weight-bold);
            }
        }

        &.second-place {
            background: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 20%, #94a3b8 100%);
            border-color: #64748b;
            box-shadow: 0 6px 20px rgba(100, 116, 139, 0.3);

            .podium-rank {
                background: linear-gradient(135deg, #64748b, #475569);
                box-shadow: 0 4px 8px rgba(100, 116, 139, 0.4);
            }

            .points-count {
                color: #334155 !important;
                text-shadow: 0 1px 2px rgba(51, 65, 85, 0.3);
            }

            .podium-name {
                color: #1e293b !important;
                font-weight: var(--font-weight-bold);
            }
        }

        &.third-place {
            background: linear-gradient(135deg, #fef3c7 0%, #f4a460 20%, #cd7f32 100%);
            border-color: #b8860b;
            box-shadow: 0 4px 15px rgba(184, 134, 11, 0.3);

            .podium-rank {
                background: linear-gradient(135deg, #b8860b, #8b4513);
                box-shadow: 0 4px 8px rgba(184, 134, 11, 0.4);
            }

            .points-count {
                color: #8b4513 !important;
                text-shadow: 0 1px 2px rgba(139, 69, 19, 0.3);
            }

            .podium-name {
                color: #654321 !important;
                font-weight: var(--font-weight-bold);
            }
        }
    }
}

[data-theme="dark"] {
    .podium-team {
        .podium-info {
            .podium-name {
                color: #000000 !important;
            }

            .podium-stats {
                .points-count {
                    color: #000000 !important;
                }

                .points-label {
                    color: #333333 !important;
                }
            }
        }
    }
}

/* === LEADERBOARD SECTION === */
.leaderboard-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    flex: 1;
    min-height: 0;
}

.leaderboard-header {
    .leaderboard-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;

        i {
            color: var(--primary);
        }
    }
}

.leaderboard-table {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.table-header {
    background: var(--surface-secondary);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);

    .table-title {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;
    }
}

.table-content {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.table-row {
    display: grid;
    grid-template-columns: 60px 1fr 80px 60px 60px 60px 80px 80px 80px 80px;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-secondary);
    transition: all 0.2s ease;

    &.header-row {
        background: var(--surface-tertiary);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        font-size: var(--text-sm);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border-bottom: 2px solid var(--border-primary);
    }

    &.team-row {
        cursor: pointer;
        color: var(--text-primary);

        &:hover {
            background: var(--surface-hover);
            transform: translateX(4px);
        }

        &:last-child {
            border-bottom: none;
        }

        &.top-three {
            background: var(--surface-secondary);
            border-left: 4px solid var(--info-400);
        }

        &.playoff-zone {
            border-left: 4px solid var(--success-400);
        }

        &.playin-zone {
            border-left: 4px solid var(--warning-400);
        }
    }

    @media (max-width: 768px) {
        grid-template-columns: 45px 1fr 45px 50px 50px;
        padding: var(--spacing-sm) var(--spacing-md);

        .hide-mobile {
            display: none;
        }
    }

    @media (max-width: 480px) {
        grid-template-columns: 40px 1fr 35px 45px 45px;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

/* === TABLE CELLS === */
.rank-cell, .team-cell, .games-cell, .wins-cell, .draws-cell,
.losses-cell, .gf-cell, .ga-cell, .gd-cell, .points-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);

    &.team-cell {
        justify-content: flex-start;
        gap: var(--spacing-md);
        min-width: 0; // Allow shrinking
        overflow: hidden;

        @media (max-width: 768px) {
            gap: var(--spacing-sm);
        }

        @media (max-width: 480px) {
            gap: var(--spacing-xs);
        }
    }
}

.rank-number {
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);

    &.gold {
        color: var(--warning-500);
        font-size: var(--text-base);
    }

    &.silver {
        color: #C0C0C0; // Bright silver
        font-size: var(--text-base);
    }

    &.bronze {
        color: #CD7F32; // Bright bronze
        font-size: var(--text-base);
    }
}

.team-logo {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
    object-fit: cover;
    border: 1px solid var(--border-secondary);
    flex-shrink: 0;

    @media (max-width: 768px) {
        width: 28px;
        height: 28px;
    }

    @media (max-width: 480px) {
        width: 24px;
        height: 24px;
    }
}

.team-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    min-width: 0; // Allow shrinking
    flex: 1;

    @media (max-width: 768px) {
        gap: 2px;
    }

    .team-name {
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        font-size: var(--text-sm);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        @media (max-width: 768px) {
            font-size: var(--text-xs);
        }

        @media (max-width: 480px) {
            font-size: 11px;
        }
    }

    .team-form {
        display: flex;
        gap: 2px;
        align-items: center;
        flex-wrap: nowrap;

        @media (max-width: 768px) {
            gap: 1px;
        }

        .form-indicator {
            font-size: 10px;
            font-weight: var(--font-weight-bold);
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
            transition: transform 0.2s ease;
            flex-shrink: 0;

            &.win {
                background-color: var(--success-500);
                color: white;
            }

            &.draw {
                background-color: var(--warning-500);
                color: white;
            }

            &.loss {
                background-color: var(--error-500);
                color: white;
            }

            &:hover {
                transform: scale(1.1);
            }

            @media (max-width: 768px) {
                width: 14px;
                height: 14px;
                font-size: 8px;
            }

            @media (max-width: 480px) {
                width: 12px;
                height: 12px;
                font-size: 7px;
            }
        }
    }
}

.gd-number {
    font-weight: var(--font-weight-semibold);

    &.positive {
        color: var(--success-600);
    }

    &.negative {
        color: var(--error-600);
    }
}

.points-number {
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    font-size: var(--text-base);
}

/* === LOADING AND NO DATA STATES === */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
}

.no-data-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
}

.no-data-card {
    text-align: center;
    padding: var(--spacing-xl);
    max-width: 400px;

    .no-data-icon {
        font-size: var(--text-4xl);
        color: var(--primary);
        margin-bottom: var(--spacing-lg);
    }

    .no-data-title {
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-md) 0;
    }

    .no-data-message {
        font-size: var(--text-base);
        color: var(--text-primary);
        margin: 0;
        line-height: 1.5;
    }
}
