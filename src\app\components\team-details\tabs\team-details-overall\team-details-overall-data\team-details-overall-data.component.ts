import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Router } from '@angular/router';
import { ListOption } from '../../../../../shared/models/list-option.model';
import { TeamService } from '../../../../../services/team.service';
import { NotificationService } from '../../../../../services/notification.service';
import { TeamDTO } from '@pro-clubs-manager/shared-dtos';
import { AuthService } from '../../../../../services/auth.service';
import { PermissionsService } from '../../../../../services/permissions.service';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Component({
  selector: 'team-details-overall-data',
  templateUrl: './team-details-overall-data.component.html',
  styleUrl: './team-details-overall-data.component.scss'
})
export class TeamDetailsOverallDataComponent {
  @Input() chosenTeam: TeamDTO | null = null;
  playersOptions: ListOption[] | null = null;
  editCaptainMode: boolean = false;
  editTeamMode : boolean = false;
  editedTeamName: string | null = null;
  editTeamPhotoModel: FormData | null = null;
  selectedCaptain: ListOption | null = null;

  @Output() onTeamUpdateEvent: EventEmitter<void> = new EventEmitter();

  // Permission observables
  canEditTeam$: Observable<boolean> = new Observable();
  isAdmin$: Observable<boolean> = new Observable();

  constructor(
    private router: Router,
    private teamService: TeamService,
    private notificationService: NotificationService,
    private authService: AuthService,
    private permissionsService: PermissionsService
  ) { }

  ngOnInit() {
    this.loadPlayersOptions();
    this.initializePermissions();
  }

  initializePermissions() {
    if (this.chosenTeam) {
      this.canEditTeam$ = this.permissionsService.canEditTeam(this.chosenTeam.id);
      this.isAdmin$ = this.authService.currentUser$.pipe(
        map(user => user?.role === 'admin' || false)
      );
    }
  }

  loadPlayersOptions() {
    this.playersOptions = this.chosenTeam!.players.map(player => { return { value: player.id, displayText: player.name } as ListOption });
    this.selectedCaptain = new ListOption();
    if (this.chosenTeam!.captain) {
      this.selectedCaptain.value = this.chosenTeam!.captain!.id;
      this.selectedCaptain.displayText = this.chosenTeam!.captain!.name;
    }
  }

  // when the user clicks on captain's name or photo
  navigateToPlayerDetails(playerId: string): void {
    this.router.navigate(['/player-details', { id: playerId }]);
  }

  getTeamCaptainName() {
    if (!this.chosenTeam?.captain) {
      return '';
    }
    return this.chosenTeam!.captain!.name;
  }

  onEditTeamClick() {
    this.editTeamMode = true;
    this.editedTeamName = this.chosenTeam?.name!;
  }

  onCancelEditTeamClick() {
    this.editTeamMode = false;
  }

  changeEditCaptainModeStatus(status: boolean) {
    this.editCaptainMode = status;
  }

  onCaptainSelect($selectedCaptain: ListOption) {
    if (!$selectedCaptain) {
      return;
    }
    this.selectedCaptain = $selectedCaptain;
  }

  onFileSelected($event: any) {
    if (!$event.target.files)
      return;

    const file: File = $event.target.files[0];
    this.setTeamImage(file);
  }

  async setTeamImage(file: File) {
    this.editTeamPhotoModel = new FormData();
    this.editTeamPhotoModel.append('file', file);
    const response = await this.teamService.setTeamImage(this.editTeamPhotoModel, this.chosenTeam!.id);
    this.editTeamPhotoModel = null;
    this.editTeamMode = false;
    this.chosenTeam!.imgUrl = response;
  }

  async saveNewCaptain() {
    await this.teamService.setTeamCaptain(this.chosenTeam!.id!, this.selectedCaptain!.value);

    this.notificationService.success(`${this.chosenTeam!.name}'s new captain: ${this.selectedCaptain?.displayText} `);
    this.onTeamUpdateEvent.emit();
    this.editCaptainMode = false;
  }

  async onSaveClick(){
    if (this.editTeamPhotoModel) {
      await this.teamService.setTeamImage(this.editTeamPhotoModel!, this.chosenTeam!.id);
      this.notificationService.success(`${this.chosenTeam!.name}'s image changed successfuly`);
      this.editTeamPhotoModel = null;
      this.editTeamMode = false;
    }

    if (this.chosenTeam!.name !== this.editedTeamName) {
      await this.teamService.renameTeam(this.chosenTeam!.id, this.editedTeamName!);
      this.notificationService.success(`Team renamed to ${this.editedTeamName} successfuly`);
      this.chosenTeam!.name = this.editedTeamName!;
      this.editedTeamName = null;
      this.editTeamMode = false;
    };

  }

  isAdmin() {
    return this.authService.isAdmin();
  }

  // New permission methods for template
  canEditThisTeam(): Observable<boolean> {
    return this.canEditTeam$;
  }

  canSetCaptain(): Observable<boolean> {
    return this.permissionsService.canSetCaptains();
  }

  onAddPlayerClick(): void {
    // TODO: Implement add player functionality
    console.log('Add player clicked');
  }

  onDeleteTeamClick(): void {
    // TODO: Implement delete team functionality
    console.log('Delete team clicked');
  }
}