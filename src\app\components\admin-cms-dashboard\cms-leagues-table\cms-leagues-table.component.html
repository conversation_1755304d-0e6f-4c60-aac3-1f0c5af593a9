<div class="cms-table-container">
    <div class="table-header">
        <h3>
            <i class="fas fa-trophy"></i>
            Leagues Management
        </h3>
        <div class="table-actions">
            <span class="coming-soon">League creation coming soon</span>
        </div>
    </div>

    <div class="table-wrapper">
        <table class="cms-table">
            <thead>
                <tr>
                    <th>League</th>
                    <th>Teams</th>
                    <th>Current Season</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let league of leagues" class="table-row">
                    <td class="league-cell">
                        <div class="league-info">
                            <img [src]="league.imgUrl || 'assets/Icons/League.png'" 
                                 [alt]="league.name"
                                 (error)="onImageError($event)"
                                 class="league-logo">
                            <div class="league-details">
                                <span class="league-name">{{ league.name }}</span>
                                <span class="league-id">ID: {{ league.id }}</span>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="team-count">
                            <i class="fas fa-shield-alt"></i>
                            <span>N/A</span>
                        </div>
                    </td>
                    <td>
                        <span class="season-text">{{ getCurrentSeason(league) }}</span>
                    </td>
                    <td class="actions-cell">
                        <div class="action-buttons">
                            <button class="action-btn view-btn" 
                                    (click)="viewLeagueDetails(league.id)"
                                    title="View League Table">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn edit-btn" 
                                    (click)="editLeague(league.id)"
                                    title="Edit League">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-btn" 
                                    (click)="deleteLeague(league)"
                                    title="Delete League">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="leagues.length === 0">
            <div class="empty-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <h4>No Leagues Found</h4>
            <p>No leagues match your current search criteria.</p>
        </div>
    </div>
</div>
