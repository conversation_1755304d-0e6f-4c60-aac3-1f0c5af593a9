﻿const https = require('https');

async function testItamarStats() {
  const itamarId = '67f2bd3317ec7db8bcd4f087';
  const apiUrl = 'proclubs-stats-server.duckdns.org';
  const path = `/player/${itamarId}`;
  
  console.log('Testing Itamar stats from deployed API...');
  console.log(`Calling: https://${apiUrl}${path}`);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: apiUrl,
      port: 443,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      rejectUnauthorized: false
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          
          console.log(`Status: ${res.statusCode}`);
          
          if (jsonData.currentSeason) {
            console.log('Current Season Stats:');
            console.log('Games:', jsonData.currentSeason.games);
            console.log('Goals:', jsonData.currentSeason.goals);
            console.log('Assists:', jsonData.currentSeason.assists);
            
            console.log('Expected: 25 games, 23 goals');
            console.log(`Actual: ${jsonData.currentSeason.games} games, ${jsonData.currentSeason.goals} goals`);
            
            if (jsonData.currentSeason.goals !== 23 || jsonData.currentSeason.games !== 25) {
              console.log('MISMATCH DETECTED!');
            } else {
              console.log('Stats match expected values!');
            }
          }
          
          resolve(jsonData);
        } catch (error) {
          console.log('JSON Parse Error');
          console.log('Raw response:', data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('Request Error');
      console.error(error.message);
      reject(error);
    });

    req.end();
  });
}

testItamarStats()
  .then(() => {
    console.log('Test completed!');
  })
  .catch((error) => {
    console.error('Test failed:', error.message);
  });
