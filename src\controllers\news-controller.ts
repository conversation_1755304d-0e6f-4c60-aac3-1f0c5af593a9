import { NextFunction, Request, Response } from "express";
import { inject, injectable } from "tsyringe";
import { INewsController } from "../interfaces/news/news-controller.interface";
import { INewsService } from "../interfaces/news/news-service.interface";
import { ITransferData, IFreeAgentData } from "../models/news";
// AuthenticatedRequest is defined globally by auth-middleware

export type AddNewsRequestModel = {
  title: string;
  content: string;
  createdBy: string;
  date: Date;
  type: string;
  transferData?: ITransferData;
  freeAgentData?: IFreeAgentData;
}

@injectable()
export default class NewsController implements INewsController {
  private newsService: INewsService;

  constructor(@inject("INewsService") newsService: INewsService) {
    this.newsService = newsService;
  }

  async getAllNews(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const allNews = await this.newsService.getAllNews();
      res.json(allNews);
    } catch (error: any) {
      next(error);
    }
  }

  async addNews(req: Request, res: Response, next: NextFunction): Promise<void> {
    // TODO: add validation
    const newsData = req.body as AddNewsRequestModel;

    try {
      const news = await this.newsService.addNews(newsData);

      res.status(201).json(news);
    } catch (error: any) {
      next(error);
    }
  }

  async deleteNews(req: Request, res: Response, next: NextFunction): Promise<void> {
    // TODO: add validation
    const { id: newsId } = req.params;

    if (!newsId) {
      res.status(400).send({ message: "No news id provided" });
      return;
    }

    try {
      const isDeleted = await this.newsService.deleteNews(newsId);

      if (isDeleted) {
        res.status(204).send(isDeleted);
      }
      else {
        next(`error when deleting news id: ${newsId}`)
      }
    } catch (error: any) {
      next(error);
    }
  }

  async likeNews(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: newsId } = req.params;
    const userId = req.user?.id;

    if (!newsId) {
      res.status(400).send({ message: "No news id provided" });
      return;
    }

    if (!userId) {
      res.status(401).send({ message: "User not authenticated" });
      return;
    }

    try {
      const updatedNews = await this.newsService.likeNews(newsId, userId);
      res.status(200).json({ success: true, data: updatedNews });
    } catch (error: any) {
      next(error);
    }
  }

  async unlikeNews(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: newsId } = req.params;
    const userId = req.user?.id;

    if (!newsId) {
      res.status(400).send({ message: "No news id provided" });
      return;
    }

    if (!userId) {
      res.status(401).send({ message: "User not authenticated" });
      return;
    }

    try {
      const updatedNews = await this.newsService.unlikeNews(newsId, userId);
      res.status(200).json({ success: true, data: updatedNews });
    } catch (error: any) {
      next(error);
    }
  }

  async updateNews(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: newsId } = req.params;
    const userId = req.user?.id;
    const newsData = req.body;

    if (!newsId) {
      res.status(400).send({ message: "No news id provided" });
      return;
    }

    if (!userId) {
      res.status(401).send({ message: "User not authenticated" });
      return;
    }

    try {
      // Get the existing news to check authorization
      const existingNews = await this.newsService.getAllNews();
      const newsToUpdate = existingNews.find(n => (n._id as any).toString() === newsId);

      if (!newsToUpdate) {
        res.status(404).send({ message: "News not found" });
        return;
      }

      // Check if user is admin or the author of the news
      const isAdmin = req.user?.role === 'admin';
      const isAuthor = newsToUpdate.createdBy === userId;

      if (!isAdmin && !isAuthor) {
        res.status(403).send({ message: "Not authorized to edit this news" });
        return;
      }

      const updatedNews = await this.newsService.updateNews(newsId, newsData);
      res.status(200).json({ success: true, data: updatedNews });
    } catch (error: any) {
      next(error);
    }
  }
}