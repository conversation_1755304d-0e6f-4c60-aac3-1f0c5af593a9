import mongoose, { Document, Schema } from 'mongoose';

export interface ITeamOfTheSeasonPlayer {
  playerId: mongoose.Types.ObjectId;
  playerName: string;
  playerImgUrl?: string;
  position: string;
  teamId: mongoose.Types.ObjectId;
  teamName: string;
  teamImgUrl?: string;
  stats: {
    games: number;
    goals: number;
    assists: number;
    cleanSheets: number;
    avgRating: number;
    playerOfTheMatch: number;
    score: number; // Calculated TOTS score
  };
}

export interface ITeamOfTheSeason extends Document {
  id: string;
  seasonNumber: number;
  league: mongoose.Types.ObjectId;
  formation: string; // e.g., "3-5-2", "4-3-3"
  players: ITeamOfTheSeasonPlayer[];
  createdAt: Date;
  generatedBy: string;
}

const teamOfTheSeasonPlayerSchema = new Schema<ITeamOfTheSeasonPlayer>(
  {
    playerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Player', required: true },
    playerName: { type: String, required: true },
    playerImgUrl: { type: String, required: false },
    position: { type: String, required: true },
    teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'Team', required: true },
    teamName: { type: String, required: true },
    teamImgUrl: { type: String, required: false },
    stats: {
      games: { type: Number, required: true },
      goals: { type: Number, required: true },
      assists: { type: Number, required: true },
      cleanSheets: { type: Number, required: true },
      avgRating: { type: Number, required: true },
      playerOfTheMatch: { type: Number, required: true },
      score: { type: Number, required: true },
    },
  },
  { _id: false }
);

const teamOfTheSeasonSchema = new Schema<ITeamOfTheSeason>(
  {
    seasonNumber: { type: Number, required: true },
    league: { type: mongoose.Schema.Types.ObjectId, ref: 'League', required: true },
    formation: { type: String, required: true },
    players: [teamOfTheSeasonPlayerSchema],
    createdAt: { type: Date, default: Date.now },
    generatedBy: { type: String, required: true },
  },
  {
    toJSON: { virtuals: true },
    id: true,
  }
);

// Create compound index for unique TOTS per season/league/formation
teamOfTheSeasonSchema.index({ seasonNumber: 1, league: 1, formation: 1 }, { unique: true });

const TeamOfTheSeason = mongoose.model<ITeamOfTheSeason>('TeamOfTheSeason', teamOfTheSeasonSchema);

export default TeamOfTheSeason;
