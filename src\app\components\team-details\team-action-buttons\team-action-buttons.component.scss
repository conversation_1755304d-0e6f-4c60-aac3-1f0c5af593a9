.team-actions-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

.edit-actions,
.normal-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    align-items: center;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    white-space: nowrap;

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    &:not(:disabled):hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    i {
        font-size: var(--text-sm);
        
        &.spinning {
            animation: spin 1s linear infinite;
        }
    }

    // Button variants
    &.edit-btn {
        background: var(--primary-100);
        color: var(--primary-700);
        border: 1px solid var(--primary-200);

        &:hover:not(:disabled) {
            background: var(--primary-200);
            border-color: var(--primary-300);
        }
    }

    &.save-btn {
        background: var(--success-100);
        color: var(--success-700);
        border: 1px solid var(--success-200);

        &:hover:not(:disabled) {
            background: var(--success-200);
            border-color: var(--success-300);
        }
    }

    &.cancel-btn {
        background: var(--surface-tertiary);
        color: var(--text-secondary);
        border: 1px solid var(--border-secondary);

        &:hover:not(:disabled) {
            background: var(--surface-quaternary);
            color: var(--text-primary);
            border-color: var(--border-primary);
        }
    }

    &.add-btn {
        background: var(--info-100);
        color: var(--info-700);
        border: 1px solid var(--info-200);

        &:hover:not(:disabled) {
            background: var(--info-200);
            border-color: var(--info-300);
        }
    }

    &.remove-all-btn {
        background: var(--warning-100);
        color: var(--warning-700);
        border: 1px solid var(--warning-200);

        &:hover:not(:disabled) {
            background: var(--warning-200);
            border-color: var(--warning-300);
        }
    }

    &.delete-btn {
        background: var(--danger-100);
        color: var(--danger-700);
        border: 1px solid var(--danger-200);

        &:hover:not(:disabled) {
            background: var(--danger-200);
            border-color: var(--danger-300);
        }
    }

    @media (max-width: 768px) {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--text-xs);
        
        span {
            display: none;
        }
        
        i {
            margin: 0;
        }
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@media (max-width: 480px) {
    .team-actions-container {
        padding: var(--spacing-md);
    }

    .edit-actions,
    .normal-actions {
        justify-content: center;
    }
}
