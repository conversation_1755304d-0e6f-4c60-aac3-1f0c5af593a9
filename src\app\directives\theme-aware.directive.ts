import { Directive, ElementRef, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, Renderer2 } from '@angular/core';
import { ThemeService, Theme } from '../services/theme.service';
import { Subscription } from 'rxjs';

@Directive({
  selector: '[appThemeAware]'
})
export class ThemeAwareDirective implements <PERSON>I<PERSON><PERSON>, OnDestroy {
  private themeSubscription?: Subscription;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private themeService: ThemeService
  ) {}

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeSubscription = this.themeService.currentTheme$.subscribe(theme => {
      this.updateThemeClass(theme);
    });
  }

  ngOnDestroy(): void {
    this.themeSubscription?.unsubscribe();
  }

  private updateThemeClass(theme: Theme): void {
    // Remove existing theme classes
    this.renderer.removeClass(this.el.nativeElement, 'theme-light');
    this.renderer.removeClass(this.el.nativeElement, 'theme-dark');
    
    // Add current theme class
    this.renderer.addClass(this.el.nativeElement, `theme-${theme}`);
  }
}
