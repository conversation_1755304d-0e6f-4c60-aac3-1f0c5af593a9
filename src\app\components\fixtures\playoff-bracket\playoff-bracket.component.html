<!-- Playoffs View -->
<div class="playoffs-view">
    <!-- Playoffs Header -->
    <div class="playoffs-header">
        <div class="header-content">
            <div class="title-section">
                <h2 class="playoffs-title">
                    <i class="fas fa-trophy"></i>
                    Playoff Brackets
                </h2>
                <p class="playoffs-subtitle">Tournament bracket and championship results</p>
            </div>

            <div class="controls-section">
                <!-- View Mode Toggle -->
                <div class="view-mode-toggle">
                    <button class="toggle-btn" 
                            [class.active]="bracketViewMode === 'bracket'"
                            (click)="onBracketViewModeChange('bracket')">
                        <i class="fas fa-sitemap"></i>
                        Bracket
                    </button>
                    <button class="toggle-btn" 
                            [class.active]="bracketViewMode === 'list'"
                            (click)="onBracketViewModeChange('list')">
                        <i class="fas fa-list"></i>
                        List
                    </button>
                </div>

                <!-- Refresh Button -->
                <button class="refresh-btn" (click)="refreshBracket()" [disabled]="bracketLoading">
                    <i class="fas fa-sync-alt" [class.fa-spin]="bracketLoading"></i>
                    Refresh
                </button>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="progress-section" *ngIf="hasPlayoffData()">
            <div class="progress-info">
                <span class="progress-label">Tournament Progress</span>
                <span class="progress-percentage">{{ getCompletionPercentage() }}% Complete</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" [style.width.%]="getCompletionPercentage()"></div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="bracketLoading">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Loading playoff brackets...</span>
        </div>
    </div>

    <!-- Bracket Content - Show when we have bracket data (even if stages are empty) -->
    <div class="bracket-content" *ngIf="!bracketLoading && bracketData">
        <div class="placeholder-content" *ngIf="!bracketData.stages || bracketData.stages.length === 0">
            <i class="fas fa-trophy"></i>
            <h3>Playoff Brackets</h3>
            <p>Playoff bracket functionality will be available when the tournament begins.</p>
            <div class="bracket-info">
                <p><strong>Season:</strong> {{ bracketData.seasonNumber }}</p>
                <p><strong>Status:</strong> {{ bracketData.isComplete ? 'Complete' : 'In Progress' }}</p>
            </div>
        </div>

        <!-- Actual bracket content when stages exist -->
        <div class="bracket-stages" *ngIf="bracketData.stages && bracketData.stages.length > 0">
            <div class="stage" *ngFor="let stage of bracketData.stages">
                <h4>{{ stage.displayName }}</h4>
                <p>{{ stage.matches.length }} matches in this stage</p>
                <!-- Stage content will be implemented here -->
            </div>
        </div>
    </div>

    <!-- Empty State - Show only when we have no bracket data at all -->
    <div class="empty-state" *ngIf="!bracketLoading && !bracketData">
        <i class="fas fa-trophy"></i>
        <h3>No Playoff Data Available</h3>
        <p>Unable to load playoff bracket data. Please try again.</p>
        <button class="retry-btn" (click)="refreshBracket()">
            <i class="fas fa-sync-alt"></i>
            Try Again
        </button>
    </div>
</div>
