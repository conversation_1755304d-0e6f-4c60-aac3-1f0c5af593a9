import { Component, Input } from '@angular/core';

export interface TeamStat {
  label: string;
  value: string | number;
  icon?: string;
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
}

@Component({
  selector: 'app-team-stats-card',
  templateUrl: './team-stats-card.component.html',
  styleUrl: './team-stats-card.component.scss'
})
export class TeamStatsCardComponent {
  @Input() title!: string;
  @Input() stats!: TeamStat[];
  @Input() columns: number = 3;
  @Input() compact: boolean = false;

  getStatColorClass(color?: string): string {
    if (!color) return 'stat-item--primary';
    return `stat-item--${color}`;
  }

  getTrendIcon(trend?: string): string {
    switch (trend) {
      case 'up':
        return 'fas fa-arrow-up';
      case 'down':
        return 'fas fa-arrow-down';
      case 'neutral':
        return 'fas fa-minus';
      default:
        return '';
    }
  }

  getGridColumns(): string {
    return `repeat(${this.columns}, 1fr)`;
  }

  trackByStat(index: number, stat: TeamStat): string {
    return stat.label;
  }
}
