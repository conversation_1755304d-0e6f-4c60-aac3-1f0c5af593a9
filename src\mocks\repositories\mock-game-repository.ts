import { Types, ClientSession } from "mongoose";
import { IGameRepository } from "../../interfaces/game";
import { AddGameData, IGame, PopulatedPlayerGameData } from "../../models/game/game";
import { TopAvgRatingByPosition } from "../../repositories/game-repository";

export class MockGameRepository implements IGameRepository {
  createGame = jest.fn<Promise<IGame>, [string | Types.ObjectId, Types.ObjectId, number, AddGameData, ClientSession?]>();
  createGames = jest.fn<Promise<IGame[]>, [string | Types.ObjectId, Types.ObjectId, number, AddGameData[], ClientSession?]>();
  deleteGameById = jest.fn<Promise<void>, [string | Types.ObjectId, ClientSession?]>();
  getGameById = jest.fn<Promise<IGame>, [string | Types.ObjectId, ClientSession?]>();
  getGamesByIds = jest.fn<Promise<IGame[]>, [string | Types.ObjectId[], ClientSession?]>();
  getAllGames = jest.fn<Promise<IGame[]>, [ClientSession?]>();
  getTopAvgRatingByPosition = jest.fn<Promise<TopAvgRatingByPosition[]>, [string, number, ClientSession?]>();
  updateGameResult = jest.fn<Promise<void>, [string | Types.ObjectId, number, number, ClientSession?]>();
  updateGamePlayerPerformance = jest.fn<Promise<void>, [string | Types.ObjectId, any, ClientSession?]>();
  updateGameBroadcast = jest.fn<Promise<void>, [string | Types.ObjectId, any, ClientSession?]>();
  updateGameDate = jest.fn<Promise<void>, [string | Types.ObjectId, Date, ClientSession?]>();
  updateGameTechnicalResult = jest.fn<Promise<void>, [string | Types.ObjectId, any, ClientSession?]>();
  getLeagueSeasonTeamGames = jest.fn<Promise<IGame[]>, [string, string, number, number?]>();
  getPlayedLeagueSeasonTeamGames = jest.fn<Promise<IGame[]>, [string, string, number, number?]>();
  getTeamVsTeamHistory = jest.fn<Promise<IGame[]>, [string | Types.ObjectId, string | Types.ObjectId, number?, ClientSession?]>();
  getPlayerPlayedSeasonGames = jest.fn<Promise<IGame[]>, [string, string | Types.ObjectId, number, ClientSession?]>();
  getPlayerLastGames = jest.fn<Promise<PopulatedPlayerGameData[]>, [string | Types.ObjectId, string | Types.ObjectId, number, number]>();
  getLeaguePlayedGamesByDate = jest.fn<Promise<IGame[]>, [{ leagueId: Types.ObjectId; seasonNumber: number }, Date, Date]>();
  getLeagueGamesBySeason = jest.fn<Promise<IGame[]>, [{ leagueId: string | Types.ObjectId; seasonNumber: number }]>();
  getGamesByFixtureId = jest.fn<Promise<IGame[]>, [string | Types.ObjectId]>();
  getMaxSeasonNumberForLeague = jest.fn<Promise<number | null>, [string | Types.ObjectId]>();
  aggregatePlayerStatsForSeason = jest.fn<Promise<{
    _id: Types.ObjectId;
    totalGoals: number;
    totalAssists: number;
    totalGames: number;
  }[]>, [string | Types.ObjectId, number]>();
}
