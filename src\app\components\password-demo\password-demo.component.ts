import { Component } from '@angular/core';

@Component({
  selector: 'app-password-demo',
  template: `
    <div class="password-demo-container">
      <div class="demo-header">
        <h2>Password Requirements Demo</h2>
        <p>See how our password validation works in real-time!</p>
      </div>
      
      <div class="demo-examples">
        <div class="example-card">
          <h3>❌ Weak Password</h3>
          <div class="example-password">password</div>
          <div class="requirements-demo">
            <div class="requirement-item">
              <i class="fas fa-times-circle error"></i>
              <span>At least 8 characters long</span>
            </div>
            <div class="requirement-item valid">
              <i class="fas fa-check-circle success"></i>
              <span>At least one lowercase letter (a-z)</span>
            </div>
            <div class="requirement-item">
              <i class="fas fa-times-circle error"></i>
              <span>At least one uppercase letter (A-Z)</span>
            </div>
            <div class="requirement-item">
              <i class="fas fa-times-circle error"></i>
              <span>At least one number (0-9)</span>
            </div>
          </div>
        </div>

        <div class="example-card">
          <h3>✅ Strong Password</h3>
          <div class="example-password">MyPassword123</div>
          <div class="requirements-demo">
            <div class="requirement-item valid">
              <i class="fas fa-check-circle success"></i>
              <span>At least 8 characters long</span>
            </div>
            <div class="requirement-item valid">
              <i class="fas fa-check-circle success"></i>
              <span>At least one lowercase letter (a-z)</span>
            </div>
            <div class="requirement-item valid">
              <i class="fas fa-check-circle success"></i>
              <span>At least one uppercase letter (A-Z)</span>
            </div>
            <div class="requirement-item valid">
              <i class="fas fa-check-circle success"></i>
              <span>At least one number (0-9)</span>
            </div>
          </div>
        </div>
      </div>

      <div class="tips-section">
        <h3>💡 Password Tips</h3>
        <ul>
          <li>Use a mix of uppercase and lowercase letters</li>
          <li>Include at least one number</li>
          <li>Make it at least 8 characters long</li>
          <li>Consider using a passphrase like "Coffee2Morning!"</li>
          <li>Avoid common words like "password" or "123456"</li>
        </ul>
      </div>
    </div>
  `,
  styles: [`
    .password-demo-container {
      max-width: 800px;
      margin: 2rem auto;
      padding: 2rem;
      background: var(--surface-primary);
      border-radius: var(--radius-lg);
      border: 1px solid var(--border-primary);
    }

    .demo-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .demo-header h2 {
      color: var(--text-primary);
      margin-bottom: 0.5rem;
    }

    .demo-header p {
      color: var(--text-secondary);
    }

    .demo-examples {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .example-card {
      padding: 1.5rem;
      background: var(--surface-secondary);
      border-radius: var(--radius-lg);
      border: 1px solid var(--border-primary);
    }

    .example-card h3 {
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .example-password {
      font-family: monospace;
      font-size: 1.1rem;
      padding: 0.75rem;
      background: var(--surface-tertiary);
      border-radius: var(--radius-md);
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .requirements-demo {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .requirement-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;
      padding: 0.5rem;
      border-radius: var(--radius-md);
      transition: all 0.3s ease;
    }

    .requirement-item.valid {
      background: rgba(var(--success-rgb), 0.1);
      border: 1px solid rgba(var(--success-rgb), 0.2);
    }

    .requirement-item i {
      width: 16px;
      text-align: center;
    }

    .error {
      color: var(--error);
    }

    .success {
      color: var(--success);
    }

    .requirement-item.valid span {
      color: var(--success);
      font-weight: 500;
    }

    .tips-section {
      background: linear-gradient(135deg, 
        rgba(var(--info-rgb), 0.1) 0%, 
        rgba(var(--primary-rgb), 0.05) 100%);
      padding: 1.5rem;
      border-radius: var(--radius-lg);
      border: 1px solid rgba(var(--info-rgb), 0.2);
    }

    .tips-section h3 {
      color: var(--text-primary);
      margin-bottom: 1rem;
    }

    .tips-section ul {
      list-style: none;
      padding: 0;
    }

    .tips-section li {
      padding: 0.5rem 0;
      color: var(--text-secondary);
      position: relative;
      padding-left: 1.5rem;
    }

    .tips-section li::before {
      content: '✓';
      position: absolute;
      left: 0;
      color: var(--success);
      font-weight: bold;
    }

    @media (max-width: 768px) {
      .demo-examples {
        grid-template-columns: 1fr;
      }
      
      .password-demo-container {
        padding: 1rem;
        margin: 1rem;
      }
    }
  `]
})
export class PasswordDemoComponent { }
