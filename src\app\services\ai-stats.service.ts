import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { NotificationService } from './notification.service';
import { AuthService } from './auth.service';

export interface ExtractedPlayerData {
  name: string;
  position: string;
  goals: number;
  assists: number;
  rating: number;
  isPlayerOfMatch?: boolean;
}

export interface PlayerMatchResult {
  extractedName: string;
  matchedPlayer?: {
    id: string;
    name: string;
    confidence: number;
  };
  position: string;
  goals: number;
  assists: number;
  rating: number;
  isPlayerOfMatch?: boolean;
}

export interface AIAnalysisResponse {
  success: boolean;
  extractedCount: number;
  matchedCount: number;
  players: PlayerMatchResult[];
  message: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AiStatsService {

  constructor(
    private apiService: ApiService,
    private notificationService: NotificationService,
    private authService: AuthService
  ) { }

  /**
   * Upload image and get AI analysis of player stats
   */
  async analyzePlayerStatsImage(imageFile: File, teamId?: string): Promise<AIAnalysisResponse> {
    try {
      // Validate file
      if (!imageFile) {
        throw new Error('No image file provided');
      }

      // Check file type
      if (!imageFile.type.startsWith('image/')) {
        throw new Error('File must be an image');
      }

      // Check file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (imageFile.size > maxSize) {
        throw new Error('Image file must be smaller than 10MB');
      }

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('image', imageFile);

      // Add teamId if provided to filter players for matching
      if (teamId) {
        formData.append('teamId', teamId);
      }

      // Make API call
      const token = this.authService.getToken();
      const response = await fetch(`${this.apiService.SERVER_URL}ai/analyze-player-stats`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result: AIAnalysisResponse = await response.json();
      
      if (result.success) {
        this.notificationService.success(
          `🤖 AI Analysis Complete! Extracted ${result.extractedCount} players, matched ${result.matchedCount} with database.`
        );
      } else {
        this.notificationService.error(result.error || 'AI analysis failed');
      }

      return result;

    } catch (error: any) {
      console.error('Error in AI stats analysis:', error);
      this.notificationService.error(`AI Analysis Failed: ${error.message}`);
      
      return {
        success: false,
        extractedCount: 0,
        matchedCount: 0,
        players: [],
        message: 'Analysis failed',
        error: error.message
      };
    }
  }

  /**
   * Get AI analysis capabilities
   */
  async getAnalysisCapabilities(): Promise<any> {
    try {
      const response = await this.apiService.get('ai/capabilities');
      return response.data;
    } catch (error: any) {
      console.error('Error getting AI capabilities:', error);
      return null;
    }
  }

  /**
   * Convert AI analysis results to form data structure
   */
  convertToFormData(analysisResults: PlayerMatchResult[]): any[] {
    return analysisResults.map((result, index) => {
      return {
        playerId: result.matchedPlayer?.id || '',
        playerName: result.matchedPlayer?.name || result.extractedName,
        position: this.normalizePosition(result.position),
        goals: result.goals,
        assists: result.assists,
        rating: result.rating,
        isPlayerOfMatch: result.isPlayerOfMatch || false,
        confidence: result.matchedPlayer?.confidence || 0,
        extractedName: result.extractedName,
        isMatched: !!result.matchedPlayer,
        index: index
      };
    });
  }

  /**
   * Normalize position codes to match your app's position system
   */
  private normalizePosition(position: string): string {
    const normalizedPos = position.toUpperCase().trim();

    // Position mappings as requested
    const positionMap: { [key: string]: string } = {
      // Defensive midfield variations -> CDM
      'RDM': 'CDM',
      'LDM': 'CDM',

      // Central midfield variations -> CM
      'LCM': 'CM',
      'RCM': 'CM',

      // Attacking midfield variations -> CAM
      'LAM': 'CAM',
      'RAM': 'CAM',

      // Center back variations -> CB
      'RCB': 'CB',
      'LCB': 'CB',

      // Keep other positions as they are
      'GK': 'GK',
      'GOALKEEPER': 'GK',
      'DEF': 'DEF',
      'DEFENDER': 'DEF',
      'CB': 'CB',
      'LB': 'LB',
      'RB': 'RB',
      'LWB': 'LWB',
      'RWB': 'RWB',
      'MID': 'MID',
      'MIDFIELDER': 'MID',
      'CM': 'CM',
      'CDM': 'CDM',
      'CAM': 'CAM',
      'LM': 'LM',
      'RM': 'RM',
      'ATT': 'ATT',
      'ATTACKER': 'ATT',
      'FORWARD': 'ATT',
      'ST': 'ST',
      'CF': 'CF',
      'LW': 'LW',
      'RW': 'RW',
      'LS': 'ST',
      'RS': 'ST'
    };

    return positionMap[normalizedPos] || normalizedPos; // Return original if no mapping found
  }

  /**
   * Validate analysis results
   */
  validateAnalysisResults(results: PlayerMatchResult[]): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!results || results.length === 0) {
      issues.push('No players extracted from image');
      return { isValid: false, issues };
    }

    if (results.length > 11) {
      issues.push(`Too many players detected (${results.length}). Expected maximum 11.`);
    }

    // Check for duplicate matched players
    const matchedPlayerIds = results
      .filter(r => r.matchedPlayer)
      .map(r => r.matchedPlayer!.id);
    
    const duplicates = matchedPlayerIds.filter((id, index) => matchedPlayerIds.indexOf(id) !== index);
    if (duplicates.length > 0) {
      issues.push('Duplicate players detected in matches');
    }

    // Check for missing essential data
    const playersWithMissingData = results.filter(r => 
      !r.position || 
      r.goals === undefined || 
      r.assists === undefined
    );

    if (playersWithMissingData.length > 0) {
      issues.push(`${playersWithMissingData.length} players have missing essential data`);
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Get confidence level description
   */
  getConfidenceDescription(confidence: number): string {
    if (confidence >= 0.9) return 'Excellent match';
    if (confidence >= 0.8) return 'Very good match';
    if (confidence >= 0.7) return 'Good match';
    if (confidence >= 0.6) return 'Fair match';
    return 'Poor match';
  }

  /**
   * Get confidence level color class
   */
  getConfidenceColorClass(confidence: number): string {
    if (confidence >= 0.8) return 'confidence-high';
    if (confidence >= 0.6) return 'confidence-medium';
    return 'confidence-low';
  }
}
