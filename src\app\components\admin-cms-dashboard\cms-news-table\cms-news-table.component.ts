import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { News } from '../../news/news.model';
import { NewsService } from '../../../services/news.service';
import { NotificationService } from '../../../services/notification.service';

@Component({
  selector: 'app-cms-news-table',
  templateUrl: './cms-news-table.component.html',
  styleUrl: './cms-news-table.component.scss'
})
export class CmsNewsTableComponent {
  @Input() news: News[] = [];
  @Output() newsDeleted = new EventEmitter<void>();
  @Output() newsUpdated = new EventEmitter<void>();

  deletingNewsIds = new Set<string>();

  constructor(
    private newsService: NewsService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  async deleteNews(newsItem: News): Promise<void> {
    const confirmDelete = confirm(
      `⚠️ Are you sure you want to delete "${newsItem.title}"?\n\n` +
      'This action cannot be undone and will permanently remove:\n' +
      '• News article content\n' +
      '• All likes and interactions\n' +
      '• Associated images'
    );

    if (!confirmDelete) {
      return;
    }

    try {
      this.deletingNewsIds.add(newsItem._id);
      await this.newsService.deleteNews(newsItem._id);
      this.notificationService.success('News deleted successfully');
      this.newsDeleted.emit();
    } catch (error: any) {
      console.error('Error deleting news:', error);
      this.notificationService.error(error.message || 'Failed to delete news');
    } finally {
      this.deletingNewsIds.delete(newsItem._id);
    }
  }

  viewNews(newsId: string): void {
    this.router.navigate(['/news'], { fragment: newsId });
  }

  editNews(newsId: string): void {
    // For now, navigate to news page where they can edit
    this.viewNews(newsId);
  }

  getNewsDate(newsItem: News): string {
    if (!newsItem.createdAt) {
      return 'No date';
    }
    
    return new Date(newsItem.createdAt).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  getNewsType(newsItem: News): string {
    if (newsItem.type === 'Transfer') return 'Transfer News';
    if (newsItem.type === 'FreeAgent') return 'Free Agent News';
    return 'General News';
  }

  getNewsTypeClass(newsItem: News): string {
    if (newsItem.type === 'Transfer') return 'type-transfer';
    if (newsItem.type === 'FreeAgent') return 'type-freeagent';
    return 'type-general';
  }

  truncateContent(content: string, maxLength: number = 100): string {
    if (content.length <= maxLength) {
      return content;
    }
    return content.substring(0, maxLength) + '...';
  }

  isNewsDeleting(newsId: string): boolean {
    return this.deletingNewsIds.has(newsId);
  }
}
