/* === MODERN SHARE MODAL STYLES === */

.share-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(12px);
  animation: fadeIn 0.3s ease-out;

  /* Subtle pattern overlay */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }
}

.share-modal-content {
  background: var(--surface-primary);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
  box-shadow:
    var(--shadow-2xl),
    0 0 0 1px rgba(99, 102, 241, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  width: 90%;
  max-width: 650px;
  max-height: 90vh;
  overflow-y: auto;
  font-family: var(--font-sans);
  backdrop-filter: blur(20px);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  /* Subtle background gradient */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(99, 102, 241, 0.02) 0%,
      transparent 50%,
      rgba(255, 215, 0, 0.01) 100%
    );
    border-radius: var(--radius-2xl);
    pointer-events: none;
  }
}

/* === MODAL HEADER === */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);
  background: var(--surface-secondary);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);

  i {
    color: var(--primary-500);
  }
}

.close-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--text-lg);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;

  &:hover {
    background: var(--surface-tertiary);
    color: var(--text-primary);
  }
}

/* === MODAL BODY === */
.modal-body {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* === NEWS PREVIEW === */
.news-preview {
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-primary);
}

.news-type-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: var(--spacing-sm);

  &.general {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
  }

  &.transfer {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
  }

  &.freeagent {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
  }
}

.news-title {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
}

.news-content {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: var(--text-sm);
}

/* === SHARE OPTIONS === */
.share-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.share-section-title {
  margin: 0;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  &::before {
    content: '';
    width: 3px;
    height: 16px;
    background: var(--primary-500);
    border-radius: var(--radius-full);
  }
}

.share-buttons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.share-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  background: var(--surface-secondary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-sm);
  font-weight: 600;
  position: relative;
  overflow: hidden;

  /* Subtle inner highlight */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  /* Shimmer effect */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    transition: left 0.5s ease;
  }

  i {
    font-size: var(--text-xl);
    transition: transform 0.3s ease;
  }

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);

    &::before {
      opacity: 1;
    }

    &::after {
      left: 100%;
    }

    i {
      transform: scale(1.2);
    }
  }

  &:active {
    transform: translateY(-2px) scale(1);
    transition: transform 0.1s ease;
  }

  &.whatsapp {
    &:hover {
      background: rgba(37, 211, 102, 0.15);
      border-color: #25d366;
      color: #25d366;
      box-shadow:
        var(--shadow-xl),
        0 0 30px rgba(37, 211, 102, 0.2);
    }
  }

  &.twitter {
    &:hover {
      background: rgba(29, 161, 242, 0.15);
      border-color: #1da1f2;
      color: #1da1f2;
      box-shadow:
        var(--shadow-xl),
        0 0 30px rgba(29, 161, 242, 0.2);
    }
  }

  &.facebook {
    &:hover {
      background: rgba(24, 119, 242, 0.15);
      border-color: #1877f2;
      color: #1877f2;
      box-shadow:
        var(--shadow-xl),
        0 0 30px rgba(24, 119, 242, 0.2);
    }
  }

  &.telegram {
    &:hover {
      background: rgba(0, 136, 204, 0.15);
      border-color: #0088cc;
      color: #0088cc;
      box-shadow:
        var(--shadow-xl),
        0 0 30px rgba(0, 136, 204, 0.2);
    }
  }

  &.copy {
    &:hover {
      background: rgba(99, 102, 241, 0.15);
      border-color: var(--primary-500);
      color: var(--primary-500);
      box-shadow:
        var(--shadow-xl),
        0 0 30px rgba(99, 102, 241, 0.2);
    }
  }
}

/* === IMAGE GENERATION === */
.image-generation-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.image-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.generate-image-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: var(--primary-600);
    transform: translateY(-1px);
  }

  &:disabled {
    background: var(--surface-tertiary);
    color: var(--text-secondary);
    cursor: not-allowed;
  }
}

.image-download-actions {
  display: flex;
  gap: var(--spacing-sm);

  @media (max-width: 480px) {
    flex-direction: column;
  }
}

.download-button,
.share-image-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--surface-secondary);
  color: var(--text-primary);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  flex: 1;

  &:hover {
    background: var(--surface-tertiary);
    transform: translateY(-1px);
  }
}

.image-preview {
  margin-top: var(--spacing-md);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--border-primary);
}

.generated-image {
  width: 100%;
  height: auto;
  display: block;
}

/* === MODAL FOOTER === */
.modal-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-primary);
  display: flex;
  justify-content: flex-end;
  background: var(--surface-secondary);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.cancel-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--surface-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background: var(--surface-primary);
    color: var(--text-primary);
  }
}

/* === MODERN ANIMATIONS === */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Staggered animation for share buttons */
.share-buttons-grid .share-button {
  animation: modalSlideIn 0.4s ease-out;
  animation-fill-mode: both;
}

.share-buttons-grid .share-button:nth-child(1) { animation-delay: 0.1s; }
.share-buttons-grid .share-button:nth-child(2) { animation-delay: 0.2s; }
.share-buttons-grid .share-button:nth-child(3) { animation-delay: 0.3s; }
.share-buttons-grid .share-button:nth-child(4) { animation-delay: 0.4s; }
.share-buttons-grid .share-button:nth-child(5) { animation-delay: 0.5s; }
