import { ClientSession, Types } from "mongoose";
import { AddGameData, IGame } from "../../models/game/game";
import { GameDTO, UpdatePlayerPerformanceDataRequest } from "@pro-clubs-manager/shared-dtos";
import { TopAvgRatingByPosition } from "../../repositories/game-repository";

export interface IGameService {
  getGameById(id: string | Types.ObjectId, session?: ClientSession): Promise<GameDTO>;
  getGamesByIds(gamesIds: string[] | Types.ObjectId[]): Promise<GameDTO[]>;
  getAllGames(): Promise<GameDTO[]>;

  getTopAvgRatingByPosition(position: string, minimumGames?:number, session?: ClientSession): Promise<TopAvgRatingByPosition[]>;

  getCurrentSeasonTeamGames(teamId: string | Types.ObjectId, limit?: number, session?: ClientSession): Promise<GameDTO[]>;
  getTeamVsTeamHistory(team1Id: string, team2Id: string, limit?: number, session?: ClientSession): Promise<GameDTO[]>;

  createGame(fixtureId: Types.ObjectId, leagueId: Types.ObjectId, seasonNumber: number, gameData: AddGameData, session: ClientSession): Promise<GameDTO>;
  createFixtureGames(
    fixtureId: Types.ObjectId,
    leagueId: Types.ObjectId,
    seasonNumber: number,
    gamesData: AddGameData[],
    session: ClientSession
  ): Promise<IGame[]>;

  updateGameResult(gameId: string, homeTeamGoals: number, awayTeamGoals: number, date: Date, isPlayoffGame: boolean, penalties?: { homeTeamPenalties: number; awayTeamPenalties: number }): Promise<void>;
  setTechnicalResult(gameId: string, losingTeamId: string, reason: string, date: Date): Promise<void>;
  updateGameDate(gameId: string, date: Date): Promise<void>;
  updateGameBroadcast(gameId: string, streamUrl: string, broadcastingTeam: string): Promise<void>;

  updateTeamPlayersPerformance(gameId: string, isHomeTeam: boolean, playersPerformance: UpdatePlayerPerformanceDataRequest[]): Promise<void>;

  deleteGame(id: string): Promise<void>;
}
