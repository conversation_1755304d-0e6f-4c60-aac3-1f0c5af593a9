import { Injectable } from '@angular/core';

declare global {
  interface Window {
    google: any;
  }
}

export interface GoogleUser {
  credential: string;
  select_by: string;
}

@Injectable({
  providedIn: 'root'
})
export class GoogleAuthService {
  private readonly clientId = '************-p141710mc4km6ap5bkm3ujp1ftfog8bb.apps.googleusercontent.com';
  private isInitialized = false;

  constructor() {
    this.loadGoogleScript();
  }

  private loadGoogleScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof window.google !== 'undefined') {
        this.isInitialized = true;
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        this.isInitialized = true;
        resolve();
      };
      script.onerror = () => reject(new Error('Failed to load Google Identity Services'));
      document.head.appendChild(script);
    });
  }

  async initializeGoogleSignIn(callback: (response: GoogleUser) => void): Promise<void> {
    if (!this.isInitialized) {
      await this.loadGoogleScript();
    }

    if (typeof window.google === 'undefined') {
      throw new Error('Google Identity Services not loaded');
    }

    window.google.accounts.id.initialize({
      client_id: this.clientId,
      callback: callback,
      auto_select: false,
      cancel_on_tap_outside: true,
      use_fedcm_for_prompt: false
    });
  }

  renderSignInButton(elementId: string): void {
    if (typeof window.google === 'undefined') {
      console.error('Google Identity Services not loaded');
      return;
    }

    window.google.accounts.id.renderButton(
      document.getElementById(elementId),
      {
        theme: 'outline',
        size: 'large',
        type: 'standard',
        shape: 'rectangular',
        text: 'signin_with',
        logo_alignment: 'left',
        width: '100%'
      }
    );
  }

  prompt(): void {
    if (typeof window.google !== 'undefined') {
      window.google.accounts.id.prompt();
    }
  }

  parseJWT(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error parsing JWT:', error);
      return null;
    }
  }
}
