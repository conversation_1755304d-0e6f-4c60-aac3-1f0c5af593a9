<!-- Main News Container -->
<div class="news-container">
    <!-- News List Component -->
    <app-news-list
        [allNews]="allNews"
        [filteredNews]="filteredNews"
        [selectedIndex]="selectedIndex"
        [isLoading]="isLoading"
        [isAdmin]="isAdmin()"
        (tabChange)="onTabChange($event)"
        (deleteNews)="onDeleteNewsClick($event)"
        (shareNews)="onShareNewsClick($event)"
        (teamClick)="onTeamClick($event)"
        (playerClick)="onPlayerClick($event)"
        (refreshNews)="refreshNews()"
        (likeNews)="onLikeNewsClick($event)"
        (unlikeNews)="onUnlikeNewsClick($event)"
        (editNews)="onEditNewsClick($event)">
    </app-news-list>

    <!-- Share Modal Component -->
    <app-news-share-modal
        [show]="showShareModal"
        [selectedNews]="selectedNewsForShare"
        (close)="closeShareModal()">
    </app-news-share-modal>

    <!-- Edit Modal Component -->
    <app-news-edit-modal
        [show]="showEditModal"
        [news]="selectedNewsForEdit"
        (close)="closeEditModal()"
        (save)="onSaveEditedNews($event)">
    </app-news-edit-modal>
</div>
