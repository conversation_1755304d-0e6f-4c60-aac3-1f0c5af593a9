import { Router } from "express";
import PlayerController from "../controllers/player-controller";
import upload from "../config/multer-config";
import { container } from "../config/container.config";
import { authenticateToken, requireAdmin, requirePlayerOwnership } from "../middlewares/auth-middleware";

const router = Router();

const playerController = container.resolve(PlayerController);

// Admin only routes
router.post("/", authenticateToken, requireAdmin, upload.single("file"), (req, res, next) => playerController.createPlayer(req, res, next));
router.delete("/:id", authenticateToken, requireAdmin, (req, res, next) => playerController.deletePlayer(req, res, next));

// Player owner or admin routes
router.patch("/:playerId/setImage", authenticateToken, requirePlayerOwnership, upload.single("file"), (req, res, next) => playerController.setPlayerImage(req, res, next));
router.put("/:playerId/rename", authenticateToken, requirePlayerOwnership, (req, res, next) => playerController.renamePlayer(req, res, next));
router.put("/:playerId/editPlayerAge", authenticateToken, requirePlayerOwnership, (req, res, next) => playerController.editPlayerAge(req, res, next));
router.put("/:playerId/editPlayerPosition", authenticateToken, requirePlayerOwnership, (req, res, next) => playerController.editPlayerPosition(req, res, next));
router.put("/:playerId/editPlayerPlayablePositions", authenticateToken, requirePlayerOwnership, (req, res, next) => playerController.editPlayerPlayablePositions(req, res, next));

router.get("/freeAgents", (req, res, next) => playerController.getFreeAgents(req, res, next));
router.get("/:id/statsByPosition", (req, res, next) => playerController.getPlayerStatsByPosition(req, res, next));
router.get("/:id/form", (req, res, next) => playerController.getPlayerForm(req, res, next));
router.get("/:id/transferHistory", (req, res, next) => playerController.getTransferHistoryByPlayerId(req, res, next));
router.get("/:id/seasonHistory", (req, res, next) => playerController.getPlayerSeasonHistory(req, res, next));
router.delete("/:playerId/seasonHistory/:seasonNumber", authenticateToken, requirePlayerOwnership, (req, res, next) => playerController.deletePlayerSeasonHistory(req, res, next));
router.get("/compare/:player1Id/:player2Id", (req, res, next) => playerController.comparePlayersById(req, res, next));
router.get("/playerSearch", (req, res, next) => playerController.playerSearchByText(req, res, next));
router.get("/:id", (req, res, next) => playerController.getPlayerById(req, res, next));
router.get("/", (req, res, next) => playerController.getAllPlayers(req, res, next));



export default router;
