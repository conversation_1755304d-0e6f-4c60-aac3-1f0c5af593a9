<div class="totw-card-container" (click)="onCardClick()" *ngIf="totwPlayer">
    <div class="card-main">
        <!-- Player Header -->
        <div class="player-header">
            <div class="player-name">{{totwPlayer.player.name}}</div>
            <div class="rating-badge">{{totwPlayer.stats.avgRating.toFixed(1)}}</div>
        </div>

        <!-- Player Image Section -->
        <div class="player-section">
            <div class="player-image-container">
                <img [src]="totwPlayer.player.imgUrl ? totwPlayer.player.imgUrl : 'assets/Icons/User.jpg'"
                     class="player-image"
                     [alt]="totwPlayer.player.name">
                <img [src]="totwPlayer.player.team.imgUrl ? totwPlayer.player.team.imgUrl : 'assets/Icons/TeamLogo.jpg'"
                     class="team-badge"
                     [alt]="totwPlayer.player.team.name">
            </div>
        </div>

        <!-- Stats Section -->
        <div class="stats-section">
            <div class="stat-item">
                <span class="stat-value">{{totwPlayer.stats.games}}</span>
                <span class="stat-label">Games</span>
            </div>
            <div class="stat-item" *ngIf="!isDefender() && !isGoalkeeper()">
                <span class="stat-value">{{totwPlayer.stats.goals}}</span>
                <span class="stat-label">Goals</span>
            </div>
            <div class="stat-item" *ngIf="isDefender() || isGoalkeeper()">
                <span class="stat-value">{{totwPlayer.stats.cleanSheets}}</span>
                <span class="stat-label">CS</span>
            </div>
            <div class="stat-item" *ngIf="!isGoalkeeper()">
                <span class="stat-value">{{totwPlayer.stats.assists}}</span>
                <span class="stat-label">Assists</span>
            </div>
        </div>
    </div>
</div>