<!-- Team vs Team History Component -->
<div class="team-vs-team-history">
    <!-- Header -->
    <div class="history-header" *ngIf="showHeader && team1 && team2">
        <div class="teams-matchup">
            <div class="team-info">
                <img [src]="team1.imgUrl || 'assets/icons/default-team.png'" 
                     [alt]="team1.name" 
                     class="team-logo">
                <span class="team-name">{{ team1.name }}</span>
            </div>
            
            <div class="vs-divider">
                <span class="vs-text">VS</span>
            </div>
            
            <div class="team-info">
                <img [src]="team2.imgUrl || 'assets/icons/default-team.png'" 
                     [alt]="team2.name" 
                     class="team-logo">
                <span class="team-name">{{ team2.name }}</span>
            </div>
        </div>

        <!-- Overall Stats -->
        <div class="overall-stats" *ngIf="stats && stats.totalMatches > 0">
            <div class="stat-card">
                <div class="stat-value">{{ stats.totalMatches }}</div>
                <div class="stat-label">Total Matches</div>
            </div>
            <div class="stat-card team1-stat">
                <div class="stat-value">{{ stats.team1Wins }}</div>
                <div class="stat-label">{{ team1.name }} Wins</div>
            </div>
            <div class="stat-card draw-stat">
                <div class="stat-value">{{ stats.draws }}</div>
                <div class="stat-label">Draws</div>
            </div>
            <div class="stat-card team2-stat">
                <div class="stat-value">{{ stats.team2Wins }}</div>
                <div class="stat-label">{{ team2.name }} Wins</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ stats.team1Goals }}:{{ stats.team2Goals }}</div>
                <div class="stat-label">Goals</div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="isLoading">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>Loading match history...</p>
    </div>

    <!-- Error State -->
    <div class="error-state" *ngIf="error && !isLoading">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <p>{{ error }}</p>
        <button class="retry-btn" (click)="refresh()">
            <i class="fas fa-redo"></i>
            Try Again
        </button>
    </div>

    <!-- Match History -->
    <div class="match-history" *ngIf="!isLoading && !error">
        <!-- No matches found -->
        <div class="no-matches" *ngIf="matchHistory.length === 0">
            <div class="no-matches-icon">
                <i class="fas fa-calendar-times"></i>
            </div>
            <h3>No Match History</h3>
            <p>These teams haven't played against each other yet.</p>
        </div>

        <!-- Match List -->
        <div class="matches-list" *ngIf="matchHistory.length > 0">
            <div class="matches-header">
                <h3>
                    <i class="fas fa-history"></i>
                    Recent Matches ({{ matchHistory.length }})
                </h3>
            </div>

            <div class="match-item" 
                 *ngFor="let match of matchHistory; trackBy: trackByMatchId"
                 [class.latest-match]="match === stats?.lastMeeting">
                
                <div class="match-date">
                    <i class="fas fa-calendar-alt"></i>
                    {{ formatDate(match.date!) }}
                </div>

                <div class="match-teams">
                    <div class="home-team" 
                         [class.winner]="getMatchResult(match, match.homeTeam.id) === 'win'"
                         [class.loser]="getMatchResult(match, match.homeTeam.id) === 'loss'">
                        <img [src]="match.homeTeam.imgUrl || 'assets/icons/default-team.png'" 
                             [alt]="match.homeTeam.name" 
                             class="team-logo-small">
                        <span class="team-name-small">{{ match.homeTeam.name }}</span>
                    </div>

                    <div class="match-score">
                        <span class="score-display" *ngIf="match.result">
                            {{ match.result.homeTeamGoals }} - {{ match.result.awayTeamGoals }}
                        </span>
                        <span class="no-result" *ngIf="!match.result">
                            No Result
                        </span>
                    </div>

                    <div class="away-team" 
                         [class.winner]="getMatchResult(match, match.awayTeam.id) === 'win'"
                         [class.loser]="getMatchResult(match, match.awayTeam.id) === 'loss'">
                        <span class="team-name-small">{{ match.awayTeam.name }}</span>
                        <img [src]="match.awayTeam.imgUrl || 'assets/icons/default-team.png'" 
                             [alt]="match.awayTeam.name" 
                             class="team-logo-small">
                    </div>
                </div>

                <!-- Match Details -->
                <div class="match-details" *ngIf="match.isPlayoff">
                    <div class="playoff-badge">
                        <i class="fas fa-trophy"></i>
                        {{ match.playoffStage }}
                    </div>
                </div>

                <!-- Result Indicators -->
                <div class="result-indicators" *ngIf="team1 && team2">
                    <div class="team-result team1-result" 
                         [class]="getMatchResult(match, team1.id)">
                        <span class="result-text">
                            {{ getMatchResult(match, team1.id) | titlecase }}
                        </span>
                    </div>
                    <div class="team-result team2-result" 
                         [class]="getMatchResult(match, team2.id)">
                        <span class="result-text">
                            {{ getMatchResult(match, team2.id) | titlecase }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
