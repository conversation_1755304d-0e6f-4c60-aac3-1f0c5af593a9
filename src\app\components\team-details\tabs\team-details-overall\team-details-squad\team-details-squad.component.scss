/* === MODERN SQUAD DESIGN === */

.modern-squad-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* === SQUAD HEADER === */
.squad-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-primary);

    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .squad-title-section {
        .squad-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            font-family: var(--font-sans);
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0;

            i {
                color: var(--primary);
                font-size: var(--text-lg);
            }

            .player-count {
                background: var(--primary);
                color: var(--text-inverse);
                font-size: var(--text-sm);
                font-weight: var(--font-weight-semibold);
                padding: var(--spacing-xs) var(--spacing-sm);
                border-radius: var(--radius-full);
                margin-left: var(--spacing-sm);
            }

            @media (max-width: 768px) {
                font-size: var(--text-lg);
                justify-content: center;
            }
        }
    }

    .squad-actions {
        display: flex;
        gap: var(--spacing-md);
        flex-wrap: wrap;
        .add-player-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            background: linear-gradient(135deg, var(--primary), var(--primary-400));
            border: none;
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-lg);
            color: var(--text-inverse);
            font-family: var(--font-sans);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            box-shadow: var(--shadow-md);

            &:hover {
                background: linear-gradient(135deg, var(--primary-400), var(--primary-300));
                transform: translateY(-1px);
                box-shadow: var(--shadow-lg);
            }

            &:active {
                transform: translateY(0);
            }

            i {
                font-size: var(--text-base);
            }

            @media (max-width: 768px) {
                width: 100%;
                justify-content: center;
                padding: var(--spacing-md);
            }
        }

        .remove-all-players-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            background: linear-gradient(135deg, var(--error-500), var(--error-600));
            border: none;
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-lg);
            color: var(--text-inverse);
            font-family: var(--font-sans);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            box-shadow: var(--shadow-md);

            &:hover:not(:disabled) {
                background: linear-gradient(135deg, var(--error-600), var(--error-700));
                transform: translateY(-1px);
                box-shadow: var(--shadow-lg);
            }

            &:active:not(:disabled) {
                transform: translateY(0);
            }

            &:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }

            i {
                font-size: var(--text-base);
            }

            @media (max-width: 768px) {
                width: 100%;
                justify-content: center;
                padding: var(--spacing-md);
            }
        }

        .jiggle-mode-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
            border: none;
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-lg);
            color: var(--text-inverse);
            font-family: var(--font-sans);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            box-shadow: var(--shadow-md);

            &:hover {
                background: linear-gradient(135deg, var(--warning-600), var(--warning-700));
                transform: translateY(-1px);
                box-shadow: var(--shadow-lg);
            }

            &:active {
                transform: translateY(0);
            }

            &.active {
                background: linear-gradient(135deg, var(--success-500), var(--success-600));

                &:hover {
                    background: linear-gradient(135deg, var(--success-600), var(--success-700));
                }
            }

            i {
                font-size: var(--text-base);
            }

            @media (max-width: 768px) {
                width: 100%;
                justify-content: center;
                padding: var(--spacing-md);
            }
        }
    }
}

/* === SQUAD CONTENT === */
.squad-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    overflow-y: auto;
    padding-right: var(--spacing-xs);

    /* Custom scrollbar */
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: var(--surface-secondary);
        border-radius: var(--radius-sm);
    }

    &::-webkit-scrollbar-thumb {
        background: var(--border-primary);
        border-radius: var(--radius-sm);

        &:hover {
            background: var(--border-secondary);
        }
    }

    @media (max-width: 768px) {
        gap: var(--spacing-lg);
        padding-right: 0;
    }
}

/* === POSITION SECTIONS === */
.position-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);

    .position-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-sm);

        .position-badge {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-family: var(--font-sans);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-bold);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: var(--radius-full);
            border: 2px solid;

            i {
                font-size: var(--text-base);
            }

            &.goalkeeper {
                background: rgba(255, 193, 7, 0.1);
                border-color: #ffc107;
                color: #ffc107;
            }

            &.defender {
                background: rgba(40, 167, 69, 0.1);
                border-color: #28a745;
                color: #28a745;
            }

            &.midfielder {
                background: rgba(23, 162, 184, 0.1);
                border-color: #17a2b8;
                color: #17a2b8;
            }

            &.attacker {
                background: rgba(220, 53, 69, 0.1);
                border-color: #dc3545;
                color: #dc3545;
            }
        }

        .position-count {
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-full);
            padding: var(--spacing-xs) var(--spacing-sm);
            font-family: var(--font-sans);
            font-size: var(--text-xs);
            font-weight: var(--font-weight-semibold);
            color: var(--text-secondary);
            min-width: 32px;
            text-align: center;
        }
    }

    .players-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: var(--spacing-md);

        @media (max-width: 768px) {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: var(--spacing-sm);
        }

        @media (max-width: 480px) {
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: var(--spacing-xs);
        }
    }
}

/* === PLAYER CARDS === */
.player-card {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary);
    }

    &:active {
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        padding: var(--spacing-sm);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-xs);
    }

    .player-avatar {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs);

        .player-photo {
            width: 80px;
            height: 80px;
            border-radius: var(--radius-full);
            object-fit: cover;
            border: 3px solid var(--border-primary);
            transition: all 0.3s ease-in-out;

            @media (max-width: 768px) {
                width: 70px;
                height: 70px;
            }

            @media (max-width: 480px) {
                width: 60px;
                height: 60px;
            }
        }

        .position-indicator {
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
            font-family: var(--font-sans);
            font-size: var(--text-xs);
            font-weight: var(--font-weight-bold);
            text-transform: uppercase;
            letter-spacing: 0.05em;

            &.goalkeeper {
                background: rgba(255, 193, 7, 0.1);
                border-color: #ffc107;
                color: #ffc107;
            }

            &.defender {
                background: rgba(40, 167, 69, 0.1);
                border-color: #28a745;
                color: #28a745;
            }

            &.midfielder {
                background: rgba(23, 162, 184, 0.1);
                border-color: #17a2b8;
                color: #17a2b8;
            }

            &.attacker {
                background: rgba(220, 53, 69, 0.1);
                border-color: #dc3545;
                color: #dc3545;
            }

            @media (max-width: 480px) {
                padding: 2px var(--spacing-xs);
                font-size: 10px;
            }
        }
    }

    .player-info {
        text-align: center;
        width: 100%;

        .player-name {
            font-family: var(--font-sans);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            line-height: 1.3;
            word-break: break-word;

            @media (max-width: 768px) {
                font-size: var(--text-xs);
            }

            @media (max-width: 480px) {
                font-size: 11px;
            }
        }
    }

    /* Position-specific card styling */
    &.goalkeeper-card:hover {
        border-color: #ffc107;
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2);

        .player-photo {
            border-color: #ffc107;
        }
    }

    &.defender-card:hover {
        border-color: #28a745;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);

        .player-photo {
            border-color: #28a745;
        }
    }

    &.midfielder-card:hover {
        border-color: #17a2b8;
        box-shadow: 0 8px 25px rgba(23, 162, 184, 0.2);

        .player-photo {
            border-color: #17a2b8;
        }
    }

    &.attacker-card:hover {
        border-color: #dc3545;
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.2);

        .player-photo {
            border-color: #dc3545;
        }
    }
}

/* === EMPTY STATE === */
.empty-squad {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: var(--spacing-2xl);
    background: var(--surface-secondary);
    border: 2px dashed var(--border-primary);
    border-radius: var(--radius-2xl);
    min-height: 300px;

    @media (max-width: 768px) {
        padding: var(--spacing-xl);
        min-height: 250px;
    }

    .empty-icon {
        margin-bottom: var(--spacing-lg);

        i {
            font-size: 4rem;
            color: var(--text-tertiary);

            @media (max-width: 768px) {
                font-size: 3rem;
            }
        }
    }

    .empty-title {
        font-family: var(--font-sans);
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;

        @media (max-width: 768px) {
            font-size: var(--text-lg);
        }
    }

    .empty-description {
        font-family: var(--font-sans);
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0 0 var(--spacing-lg) 0;
        max-width: 300px;
        line-height: 1.5;

        @media (max-width: 768px) {
            font-size: var(--text-sm);
        }
    }

    .add-first-player-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: linear-gradient(135deg, var(--primary), var(--primary-400));
        border: none;
        border-radius: var(--radius-lg);
        padding: var(--spacing-md) var(--spacing-xl);
        color: var(--text-inverse);
        font-family: var(--font-sans);
        font-size: var(--text-base);
        font-weight: var(--font-weight-semibold);
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        box-shadow: var(--shadow-lg);

        &:hover {
            background: linear-gradient(135deg, var(--primary-400), var(--primary-300));
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        &:active {
            transform: translateY(0);
        }

        i {
            font-size: var(--text-lg);
        }

        @media (max-width: 768px) {
            padding: var(--spacing-sm) var(--spacing-lg);
            font-size: var(--text-sm);
        }
    }
}

/* === iOS-STYLE JIGGLE MODE === */
.player-card {
    position: relative;

    &.jiggle {
        animation: jiggle 0.5s ease-in-out infinite alternate;
    }

    &.removing {
        opacity: 0.5;
        pointer-events: none;
    }
}

.remove-player-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--danger-500);
    border: 2px solid var(--surface-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-md);

    &:hover:not(:disabled) {
        background: var(--danger-600);
        transform: scale(1.1);
        box-shadow: var(--shadow-lg);
    }

    &:active:not(:disabled) {
        transform: scale(0.95);
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    i {
        font-size: 12px;
        font-weight: bold;
    }
}

@keyframes jiggle {
    0% {
        transform: rotate(-1deg);
    }
    100% {
        transform: rotate(1deg);
    }
}