import { Router } from "express";
import Chat<PERSON><PERSON>roller from "../controllers/chat-controller";
import { container } from "../config/container.config";
import { authenticateToken } from "../middlewares/auth-middleware";

const router = Router();
const chatController = container.resolve(ChatController);

// All chat routes require authentication
router.use(authenticateToken);

// Get chat messages with pagination
router.get("/messages", (req, res, next) => chatController.getMessages(req, res, next));

// Create a new message
router.post("/messages", (req, res, next) => chatController.createMessage(req, res, next));

// Edit a message
router.put("/messages/:messageId", (req, res, next) => chatController.editMessage(req, res, next));

// Delete a message
router.delete("/messages/:messageId", (req, res, next) => chatController.deleteMessage(req, res, next));

// Add/remove reaction to a message
router.post("/messages/:messageId/reactions", (req, res, next) => chatController.addReaction(req, res, next));

export { router as chatRoutes };
