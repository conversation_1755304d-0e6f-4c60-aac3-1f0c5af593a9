<!-- === BEAUTIFUL PLAYER COMPARISON COMPONENT === -->
<div class="player-comparison-container">

  <!-- Header -->
  <div class="comparison-header">
    <button class="back-button" (click)="goBack()">
      <i class="fas fa-arrow-left"></i>
      <span>Back</span>
    </button>
    <h1 class="page-title">
      <i class="fas fa-balance-scale"></i>
      <span>Player Comparison</span>
    </h1>
  </div>

  <!-- Loading State -->
  <div class="loading-state" *ngIf="isLoading">
    <div class="loading-spinner">
      <i class="fas fa-spinner fa-spin"></i>
    </div>
    <p class="loading-text">Loading...</p>
  </div>

  <!-- Error State -->
  <div class="error-state" *ngIf="hasError && !isLoading">
    <div class="error-icon">
      <i class="fas fa-exclamation-triangle"></i>
    </div>
    <p class="error-text">{{errorMessage}}</p>
    <button class="retry-btn" (click)="loadTeams()" *ngIf="currentStep === 'selectTeam'">
      <i class="fas fa-redo"></i>
      <span>Try Again</span>
    </button>
  </div>

  <!-- Step 1: Team Selection -->
  <div class="team-selection-step" *ngIf="currentStep === 'selectTeam' && !isLoading && !hasError">
    <div class="step-header">
      <h2 class="step-title">Select Team</h2>
      <p class="step-subtitle">Choose a team to compare players from</p>
    </div>

    <div class="teams-grid">
      <div class="team-card" 
           *ngFor="let team of teams" 
           (click)="onTeamSelect(team)">
        <div class="team-logo">
          <img [src]="team.imgUrl || getDefaultTeamImage()" 
               [alt]="team.name + ' logo'">
        </div>
        <div class="team-info">
          <h3 class="team-name">{{team.name}}</h3>
          <span class="team-players">{{team.players ? team.players.length : 0}} players</span>
        </div>
        <div class="team-arrow">
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Step 2: Player Selection -->
  <div class="player-selection-step" *ngIf="currentStep === 'selectPlayer' && !isLoading && !hasError">
    <div class="step-header">
      <h2 class="step-title">Select Player</h2>
      <p class="step-subtitle">Choose a player from {{selectedTeam?.name}} to compare</p>
    </div>

    <div class="players-grid" *ngIf="teamPlayers.length > 0">
      <div class="player-card" 
           *ngFor="let player of teamPlayers" 
           (click)="onPlayerSelect(player)">
        <div class="player-avatar">
          <img [src]="player.imgUrl || getDefaultPlayerImage()" 
               [alt]="player.name + ' avatar'">
        </div>
        <div class="player-info">
          <h3 class="player-name">{{player.name}}</h3>
          <span class="player-position">{{player.position}}</span>
          <div class="player-stats">
            <span class="stat">{{player.stats.games}} games</span>
            <span class="stat">{{player.stats.avgRating.toFixed(1)}} rating</span>
          </div>
        </div>
        <div class="player-arrow">
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
    </div>

    <div class="empty-state" *ngIf="teamPlayers.length === 0">
      <div class="empty-icon">
        <i class="fas fa-users-slash"></i>
      </div>
      <p class="empty-text">No other players available in this team</p>
      <button class="secondary-btn" (click)="goBack()">
        <i class="fas fa-arrow-left"></i>
        <span>Choose Different Team</span>
      </button>
    </div>
  </div>

  <!-- Step 3: Comparison Results -->
  <div class="comparison-results" *ngIf="currentStep === 'comparison' && comparisonData && !isLoading && !hasError">
    
    <!-- Players Header -->
    <div class="players-header">
      <div class="player-summary">
        <div class="player-avatar">
          <img [src]="comparisonData.player1.imgUrl || getDefaultPlayerImage()" 
               [alt]="comparisonData.player1.name">
        </div>
        <div class="player-details">
          <h2 class="player-name">{{comparisonData.player1.name}}</h2>
          <span class="player-position">{{comparisonData.player1.position}}</span>
          <span class="player-team" *ngIf="comparisonData.player1.team">{{comparisonData.player1.team.name}}</span>
        </div>
      </div>

      <div class="vs-divider">
        <span class="vs-text">VS</span>
      </div>

      <div class="player-summary">
        <div class="player-avatar">
          <img [src]="comparisonData.player2.imgUrl || getDefaultPlayerImage()" 
               [alt]="comparisonData.player2.name">
        </div>
        <div class="player-details">
          <h2 class="player-name">{{comparisonData.player2.name}}</h2>
          <span class="player-position">{{comparisonData.player2.position}}</span>
          <span class="player-team" *ngIf="comparisonData.player2.team">{{comparisonData.player2.team.name}}</span>
        </div>
      </div>
    </div>

    <!-- Comparison Stats -->
    <div class="comparison-stats">
      
      <!-- Basic Stats -->
      <div class="stats-section">
        <h3 class="section-title">
          <i class="fas fa-chart-bar"></i>
          <span>Basic Statistics</span>
        </h3>
        
        <div class="stats-grid">
          <!-- Games -->
          <div class="stat-comparison">
            <div class="stat-label">Games Played</div>
            <div class="stat-values">
              <span class="player1-value">{{comparisonData.player1.stats.games}}</span>
              <div class="difference" [ngClass]="getDifferenceClass(comparisonData.comparison.gamesDifference)">
                <i [class]="getDifferenceIcon(comparisonData.comparison.gamesDifference)"></i>
                <span>{{formatDifference(comparisonData.comparison.gamesDifference)}}</span>
              </div>
              <span class="player2-value">{{comparisonData.player2.stats.games}}</span>
            </div>
          </div>

          <!-- Goals -->
          <div class="stat-comparison">
            <div class="stat-label">Goals</div>
            <div class="stat-values">
              <span class="player1-value">{{comparisonData.player1.stats.goals}}</span>
              <div class="difference" [ngClass]="getDifferenceClass(comparisonData.comparison.goalsDifference)">
                <i [class]="getDifferenceIcon(comparisonData.comparison.goalsDifference)"></i>
                <span>{{formatDifference(comparisonData.comparison.goalsDifference)}}</span>
              </div>
              <span class="player2-value">{{comparisonData.player2.stats.goals}}</span>
            </div>
          </div>

          <!-- Assists -->
          <div class="stat-comparison">
            <div class="stat-label">Assists</div>
            <div class="stat-values">
              <span class="player1-value">{{comparisonData.player1.stats.assists}}</span>
              <div class="difference" [ngClass]="getDifferenceClass(comparisonData.comparison.assistsDifference)">
                <i [class]="getDifferenceIcon(comparisonData.comparison.assistsDifference)"></i>
                <span>{{formatDifference(comparisonData.comparison.assistsDifference)}}</span>
              </div>
              <span class="player2-value">{{comparisonData.player2.stats.assists}}</span>
            </div>
          </div>

          <!-- Average Rating -->
          <div class="stat-comparison">
            <div class="stat-label">Average Rating</div>
            <div class="stat-values">
              <span class="player1-value">{{comparisonData.player1.stats.avgRating.toFixed(2)}}</span>
              <div class="difference" [ngClass]="getDifferenceClass(comparisonData.comparison.avgRatingDifference)">
                <i [class]="getDifferenceIcon(comparisonData.comparison.avgRatingDifference)"></i>
                <span>{{formatDifference(comparisonData.comparison.avgRatingDifference, 2)}}</span>
              </div>
              <span class="player2-value">{{comparisonData.player2.stats.avgRating.toFixed(2)}}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Per Game Stats -->
      <div class="stats-section">
        <h3 class="section-title">
          <i class="fas fa-calculator"></i>
          <span>Per Game Statistics</span>
        </h3>
        
        <div class="stats-grid">
          <!-- Goals per Game -->
          <div class="stat-comparison">
            <div class="stat-label">Goals per Game</div>
            <div class="stat-values">
              <span class="player1-value">{{comparisonData.comparison.goalsPerGame.player1.toFixed(2)}}</span>
              <div class="difference" [ngClass]="getDifferenceClass(comparisonData.comparison.goalsPerGame.difference)">
                <i [class]="getDifferenceIcon(comparisonData.comparison.goalsPerGame.difference)"></i>
                <span>{{formatDifference(comparisonData.comparison.goalsPerGame.difference, 2)}}</span>
              </div>
              <span class="player2-value">{{comparisonData.comparison.goalsPerGame.player2.toFixed(2)}}</span>
            </div>
          </div>

          <!-- Assists per Game -->
          <div class="stat-comparison">
            <div class="stat-label">Assists per Game</div>
            <div class="stat-values">
              <span class="player1-value">{{comparisonData.comparison.assistsPerGame.player1.toFixed(2)}}</span>
              <div class="difference" [ngClass]="getDifferenceClass(comparisonData.comparison.assistsPerGame.difference)">
                <i [class]="getDifferenceIcon(comparisonData.comparison.assistsPerGame.difference)"></i>
                <span>{{formatDifference(comparisonData.comparison.assistsPerGame.difference, 2)}}</span>
              </div>
              <span class="player2-value">{{comparisonData.comparison.assistsPerGame.player2.toFixed(2)}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="comparison-actions">
      <button class="secondary-btn" (click)="startNewComparison()">
        <i class="fas fa-plus"></i>
        <span>New Comparison</span>
      </button>
      <button class="primary-btn" (click)="goBack()">
        <i class="fas fa-user"></i>
        <span>Back to Player</span>
      </button>
    </div>
  </div>

</div>
