import { Router } from "express";
import { PredictionController } from "../controllers/prediction-controller";
import { container } from "../config/container.config";
import { authenticateToken, optionalAuth } from "../middlewares/auth-middleware";

const router = Router();
const predictionController = container.resolve(PredictionController);

// Create or update prediction for a game (requires auth)
router.post("/game/:gameId", authenticateToken, (req, res, next) =>
  predictionController.createOrUpdatePrediction(req, res, next)
);

// Delete a prediction (requires auth)
router.delete("/:predictionId", authenticateToken, (req, res, next) =>
  predictionController.deletePrediction(req, res, next)
);

// Get prediction distribution for a game (optional auth for user's prediction)
router.get("/game/:gameId/distribution", optionalAuth, (req, res, next) =>
  predictionController.getPredictionDistribution(req, res, next)
);

// Get user's predictions (requires auth)
router.get("/user/my-predictions", authenticateToken, (req, res, next) =>
  predictionController.getUserPredictions(req, res, next)
);

// Get prediction by ID
router.get("/:predictionId", (req, res, next) => 
  predictionController.getPredictionById(req, res, next)
);

export default router;
