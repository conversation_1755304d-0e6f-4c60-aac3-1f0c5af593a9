<div class="pro-clubs-bg-primary h-100 popup-area">
    <div class="d-flex w-100 justify-content-between px-3 h-5 align-items-center">
        <div *ngIf="data.componentSwitchMode && isAdmin()">
            <div class="d-flex cursor-pointer" *ngIf="!displayFirstComponent" (click)="switchBetweenComponents(true)">
                CANCEL EDIT MODE
            </div>
        </div>
        <div class="d-flex">
            <i class="fa-solid fa-xmark cursor-pointer" (click)="closeModal()"></i>
        </div>
    </div>
    <!-- Game Details / Modify Game Tabs -->
    <div class="d-flex justify-content-center h-5" *ngIf="isGameModifyMode()">
        <div class="tab-container d-flex">
            <div class="tab-button"
                 [class.active]="displayFirstComponent"
                 (click)="switchBetweenComponents(true)">
                <i class="fas fa-info-circle"></i>
                Game Details
            </div>
            <div class="tab-button"
                 [class.active]="!displayFirstComponent"
                 (click)="switchBetweenComponents(false)">
                <i class="fas fa-edit"></i>
                Modify Game
            </div>
        </div>
    </div>

    <!-- Team Stats Editing Tabs -->
    <div class="d-flex justify-content-evenly h-5" *ngIf="isAdmin() && isGameModifyMode()">
        <div class="text-uppercase cursor-pointer" *ngIf="!displayFirstComponent"
            (click)="switchBetweenComponents(false, 'home')">
            edit {{homeTeamName}}'s stats <i class="fas fa-edit text-white"></i>
        </div>
        <div class="text-uppercase cursor-pointer" *ngIf="!displayFirstComponent"
            (click)="switchBetweenComponents(false, 'away')">
            edit {{awayTeamName}}'s stats <i class="fas fa-edit text-white"></i>
        </div>
    </div>
    <div class="h-90">
        <ng-template #componentContainer></ng-template>
    </div>
</div>