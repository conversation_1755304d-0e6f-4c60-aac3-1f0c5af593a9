/* === BEAUTIFUL PLAYER SEASON HISTORY STYLES === */

.season-history-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  min-height: 100vh;
  background: var(--background-primary);

  @media (max-width: 768px) {
    padding: var(--spacing-lg);
  }
}

/* Ensure full width coverage */
:host {
  display: block;
  width: 100%;
  background: var(--background-primary);
}

/* === HEADER === */
.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-xl);
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .back-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--surface-hover);
      transform: translateX(-2px);
      border-color: var(--primary-300);
    }
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex: 1;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      text-align: center;
    }

    .page-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      font-size: var(--text-2xl);
      font-weight: 700;
      color: var(--text-primary);
      margin: 0;

      i {
        color: var(--primary-500);
      }

      @media (max-width: 768px) {
        font-size: var(--text-xl);
      }
    }

    .player-info {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);

      .player-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        border: 2px solid var(--border-primary);
        object-fit: cover;
      }

      .player-name {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
      }
    }
  }

  .header-actions {
    .view-toggle-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      background: var(--primary-500);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      padding: var(--spacing-sm) var(--spacing-md);
      font-size: var(--text-sm);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--primary-600);
        transform: translateY(-1px);
      }
    }
  }
}

/* === LOADING & ERROR STATES === */
.loading-state, .error-state {
  text-align: center;
  padding: var(--spacing-3xl);
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);

  .loading-spinner, .error-icon {
    font-size: var(--text-3xl);
    margin-bottom: var(--spacing-lg);
  }

  .loading-spinner {
    color: var(--primary-500);
    
    i {
      animation: spin 1s linear infinite;
    }
  }

  .error-icon {
    color: var(--error-500);
  }

  .loading-text, .error-text {
    font-size: var(--text-lg);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
  }

  .retry-btn {
    background: var(--primary-500);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all 0.2s ease;

    &:hover {
      background: var(--primary-600);
      transform: translateY(-1px);
    }
  }
}

/* === CAREER SUMMARY === */
.career-summary {
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);

  .section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xl) 0;
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);

    i {
      color: var(--primary-500);
    }
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: var(--spacing-md);
    }

    .summary-card {
      background: var(--surface-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-xl);
      padding: var(--spacing-lg);
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      animation: slideInUp 0.6s ease-out;

      &:hover {
        background: var(--surface-hover);
        transform: translateY(-8px) scale(1.02);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-400);
      }

      &:hover .summary-icon {
        transform: scale(1.1) rotate(5deg);
      }

      &.best-season {
        background: linear-gradient(135deg, var(--primary-900), var(--primary-800));
        border-color: var(--primary-400);

        .summary-icon {
          background: var(--primary-500);
          color: white;
        }
      }

      .summary-icon {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-lg);
        flex-shrink: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &.seasons {
          background: var(--blue-800);
          color: var(--blue-200);
        }

        &.games {
          background: var(--green-800);
          color: var(--green-200);
        }

        &.goals {
          background: var(--red-800);
          color: var(--red-200);
        }

        &.assists {
          background: var(--purple-800);
          color: var(--purple-200);
        }

        &.rating {
          background: var(--yellow-800);
          color: var(--yellow-200);
        }

        &.best {
          background: var(--primary-500);
          color: white;
        }
      }

      .summary-content {
        display: flex;
        flex-direction: column;
        min-width: 0;

        .summary-value {
          font-size: var(--text-xl);
          font-weight: 700;
          color: var(--text-primary);
          line-height: 1.2;
        }

        .summary-label {
          font-size: var(--text-sm);
          color: var(--text-secondary);
          margin-top: var(--spacing-xs);
        }
      }
    }
  }
}

/* === RATING CLASSES === */
.rating-excellent {
  color: var(--success-600) !important;
  font-weight: 700;
}

.rating-good {
  color: var(--primary-600) !important;
  font-weight: 600;
}

.rating-average {
  color: var(--warning-600) !important;
  font-weight: 500;
}

.rating-poor {
  color: var(--error-600) !important;
  font-weight: 500;
}

/* === SEASON DETAILS === */
.season-details {
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);

  .details-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: flex-start;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      font-size: var(--text-xl);
      font-weight: 700;
      color: var(--text-primary);
      margin: 0;

      i {
        color: var(--primary-500);
      }
    }

    .sort-controls {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      @media (max-width: 768px) {
        flex-wrap: wrap;
      }

      .sort-label {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin-right: var(--spacing-sm);
      }

      .sort-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-md);
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--text-xs);
        color: var(--text-primary);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--surface-hover);
          border-color: var(--primary-300);
        }

        &.active {
          background: var(--primary-500);
          color: white;
          border-color: var(--primary-500);
        }

        i {
          font-size: var(--text-xs);
        }
      }
    }
  }
}

/* === TABLE VIEW === */
.table-view {
  width: 100%;
  overflow: hidden; /* Prevent parent overflow issues */

  .table-container {
    width: 100%;
    overflow-x: auto;
    overflow-y: visible;
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    /* Ensure scrollbar is visible on all browsers */
    scrollbar-width: thin;
    scrollbar-color: var(--primary-500) var(--surface-secondary);

    /* Webkit scrollbar styling */
    &::-webkit-scrollbar {
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: var(--surface-secondary);
      border-radius: var(--radius-sm);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--primary-500);
      border-radius: var(--radius-sm);

      &:hover {
        background: var(--primary-600);
      }
    }

    .seasons-table {
      width: 100%;
      min-width: 800px; /* Ensure minimum width to trigger horizontal scroll */
      border-collapse: collapse;
      background: var(--surface-secondary);

      thead {
        background: var(--surface-tertiary);

        th {
          padding: var(--spacing-md);
          text-align: left;
          font-size: var(--text-sm);
          font-weight: 600;
          color: var(--text-secondary);
          border-bottom: 1px solid var(--border-primary);
          white-space: nowrap;
          min-width: 80px; /* Ensure minimum column width */

          /* Specific column widths for better layout */
          &:nth-child(1) { min-width: 120px; } /* Season */
          &:nth-child(2) { min-width: 140px; } /* Team */
          &:nth-child(3) { min-width: 70px; }  /* Games */
          &:nth-child(4) { min-width: 70px; }  /* Goals */
          &:nth-child(5) { min-width: 80px; }  /* Assists */
          &:nth-child(6) { min-width: 100px; } /* Clean Sheets */
          &:nth-child(7) { min-width: 70px; }  /* POTM */
          &:nth-child(8) { min-width: 90px; }  /* Avg Rating */
          &:nth-child(9) { min-width: 90px; }  /* Goals/Game */
          &:nth-child(10) { min-width: 100px; } /* Assists/Game */
          &:nth-child(11) { min-width: 80px; }  /* Actions */
        }
      }

      tbody {
        tr {
          transition: all 0.2s ease;

          &:hover {
            background: var(--surface-hover);
          }

          &.current-season {
            background: var(--primary-900);
            border-left: 4px solid var(--primary-500);
          }

          &:not(:last-child) {
            border-bottom: 1px solid var(--border-primary);
          }
        }

        td {
          padding: var(--spacing-md);
          font-size: var(--text-sm);
          color: var(--text-primary);
          vertical-align: middle;
          white-space: nowrap; /* Prevent text wrapping */
          min-width: 80px; /* Ensure minimum cell width */

          &.season-cell {
            .season-info {
              display: flex;
              align-items: center;
              gap: var(--spacing-sm);

              .season-name {
                font-weight: 600;
              }

              .current-badge {
                background: var(--primary-500);
                color: white;
                padding: 2px var(--spacing-xs);
                border-radius: var(--radius-sm);
                font-size: var(--text-xs);
                font-weight: 600;
              }

              .best-badge {
                background: var(--warning-500);
                color: white;
                padding: 2px var(--spacing-xs);
                border-radius: var(--radius-sm);
                font-size: var(--text-xs);
                font-weight: 600;
              }
            }
          }

          &.team-cell {
            .team-info {
              display: flex;
              align-items: center;
              gap: var(--spacing-sm);

              .team-logo {
                width: 30px;
                height: 30px;
                border-radius: var(--radius-md);
                object-fit: cover;
                border: 1px solid var(--border-primary);
              }

              .team-name {
                font-weight: 500;
              }
            }

            .no-team {
              color: var(--text-tertiary);
              font-style: italic;
            }
          }

          &.stat-cell {
            text-align: center;
            font-weight: 500;
          }

          &.action-cell {
            text-align: center;
            padding: var(--spacing-sm);

            .delete-btn {
              background: var(--error-500);
              color: white;
              border: none;
              border-radius: var(--radius-md);
              padding: var(--spacing-xs);
              width: 32px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.2s ease;
              font-size: var(--text-sm);

              &:hover {
                background: var(--error-600);
                transform: scale(1.1);
                box-shadow: var(--shadow-md);
              }
            }
          }
        }
      }
    }
  }
}

/* === CARDS VIEW === */
.cards-view {
  .season-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-md);
    }

    .season-card {
      background: var(--surface-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-xl);
      padding: var(--spacing-lg);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      animation: fadeInScale 0.6s ease-out;

      &:hover {
        background: var(--surface-hover);
        transform: translateY(-8px) scale(1.03);
        box-shadow: var(--shadow-2xl);
        border-color: var(--primary-400);
      }

      &:hover .team-logo {
        transform: scale(1.1) rotate(3deg);
      }

      &.current-season {
        background: var(--primary-900);
        border-color: var(--primary-400);
        border-left: 4px solid var(--primary-500);
      }

      .card-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-primary);

        .season-info {
          .season-name {
            font-size: var(--text-lg);
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-sm) 0;
          }

          .season-badges {
            display: flex;
            gap: var(--spacing-xs);

            .current-badge {
              background: var(--primary-500);
              color: white;
              padding: 2px var(--spacing-xs);
              border-radius: var(--radius-sm);
              font-size: var(--text-xs);
              font-weight: 600;
            }

            .best-badge {
              background: var(--warning-500);
              color: white;
              padding: 2px var(--spacing-xs);
              border-radius: var(--radius-sm);
              font-size: var(--text-xs);
              font-weight: 600;
            }
          }
        }

        .team-info {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);

          .team-logo {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-lg);
            object-fit: cover;
            border: 2px solid var(--border-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          }

          .team-name {
            font-size: var(--text-sm);
            font-weight: 600;
            color: var(--text-primary);
          }
        }

        .no-team {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          color: var(--text-tertiary);
          font-style: italic;
          font-size: var(--text-sm);
        }

        .card-actions {
          .delete-btn {
            background: var(--error-500);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            padding: var(--spacing-xs);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: var(--text-sm);

            &:hover {
              background: var(--error-600);
              transform: scale(1.1) rotate(5deg);
              box-shadow: var(--shadow-lg);
            }
          }
        }
      }

      .card-stats {
        .stat-row {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: var(--spacing-md);
          margin-bottom: var(--spacing-md);

          .stat-item {
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--surface-primary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-primary);

            .stat-label {
              display: block;
              font-size: var(--text-xs);
              color: var(--text-secondary);
              margin-bottom: var(--spacing-xs);
              text-transform: uppercase;
              letter-spacing: 0.05em;
            }

            .stat-value {
              display: block;
              font-size: var(--text-lg);
              font-weight: 700;
              color: var(--text-primary);
            }
          }
        }

        .per-game-stats {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: var(--spacing-md);
          margin-top: var(--spacing-lg);
          padding-top: var(--spacing-md);
          border-top: 1px solid var(--border-primary);

          .per-game-item {
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--surface-tertiary);
            border-radius: var(--radius-md);

            .per-game-label {
              display: block;
              font-size: var(--text-xs);
              color: var(--text-secondary);
              margin-bottom: var(--spacing-xs);
              text-transform: uppercase;
              letter-spacing: 0.05em;
            }

            .per-game-value {
              display: block;
              font-size: var(--text-base);
              font-weight: 600;
              color: var(--text-primary);
            }
          }
        }
      }
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
