import "reflect-metadata";
import { json } from "body-parser";
import cors from "cors";
import fs from 'fs';
import dotenv from "dotenv";
import express, { Request, Response } from "express";
import https from 'https';
import morgan from "morgan";
import { connectToDatabase } from "./config/database";
import logger from "./config/logger";
import errorHandlerMiddleware from "./middlewares/error-handler";
import { requestLoggerMiddleware } from "./middlewares/request-logger";
import { fixtureRoutes, gameRoutes, leagueRoutes, playerRoutes, teamRoutes, newsRoutes, dashboardRoutes, userRoutes, playerAssociationRequestRoutes, predictionRoutes, commentRoutes } from "./routes";
import seasonAchievementRoutes from "./routes/season-achievement-routes";
import { aiRoutes } from "./routes/ai-routes";
import { chatRoutes } from "./routes/chat-routes";
import { transferRequestRoutes } from "./routes/transfer-request-routes";
import { bracketsRoutes } from "./routes/brackets-routes";
import "./config/container.config"; // runs the container configuration
import { container } from "./config/container.config";
import { ICacheWarmingService } from "./services/cache-warming-service";
import { LoggerUtils } from "./utils/logger-utils";

// HTTPS options for production
let options: any = null;
if (process.env.RUN_MODE === 'prod') {
  try {
    const path = require('path');
    let keyPath: string;
    let certFilePath: string;

    // Check if we're on Linux (EC2) or Windows (local)
    const linuxCertPath = '/etc/letsencrypt/live/proclubs-stats-server.duckdns.org';
    const windowsCertPath = path.join(__dirname, '..', 'ssh', 'cert');

    if (fs.existsSync(linuxCertPath)) {
      // Linux/EC2 environment
      keyPath = path.join(linuxCertPath, 'privkey.pem');
      certFilePath = path.join(linuxCertPath, 'fullchain.pem');
      logger.info('Using Linux certificate path');
    } else if (fs.existsSync(windowsCertPath)) {
      // Windows/local environment
      keyPath = path.join(windowsCertPath, 'privkey.pem');
      certFilePath = path.join(windowsCertPath, 'fullchain.pem');
      logger.info('Using Windows certificate path');
    } else {
      logger.warn('No certificate directory found, will run HTTP on specified port');
      throw new Error('No certificate directory found');
    }

    options = {
      key: fs.readFileSync(keyPath),
      cert: fs.readFileSync(certFilePath)
    };
    logger.info('HTTPS certificates loaded successfully');
  } catch (error) {
    logger.warn('HTTPS certificates not found, falling back to HTTP', error);
    options = null;
  }
}


dotenv.config(); // set env variables

const app = express();

// Enhanced CORS configuration for Safari compatibility
app.use(cors({
  origin: [
    'http://localhost:4200',
    'https://d2pql7k53cjta2.cloudfront.net',
    'https://proclubs-stats-server.duckdns.org'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Content-Range', 'X-Content-Range'],
  maxAge: 86400 // 24 hours preflight cache
}));
app.use(json()); // format
app.use(morgan("dev")); // HTTP request logger
app.use(requestLoggerMiddleware); // Custom request logger with user info

app.use("/league", leagueRoutes);
app.use("/team", teamRoutes);
app.use("/fixture", fixtureRoutes);
app.use("/game", gameRoutes);
app.use("/player", playerRoutes);
app.use("/news", newsRoutes);
app.use("/dashboard", dashboardRoutes);
app.use("/user", userRoutes);
app.use("/player-association-requests", playerAssociationRequestRoutes);
app.use("/ai", aiRoutes);
app.use("/chat", chatRoutes);
app.use("/transfer-requests", transferRequestRoutes);
app.use("/brackets", bracketsRoutes);
app.use("/predictions", predictionRoutes);
app.use("/comments", commentRoutes);
app.use("/season-achievements", seasonAchievementRoutes);

app.use("*", (_req: Request, res: Response) => {
  res.status(404).json({
    message: "Route Not Found",
  });
});

app.use(errorHandlerMiddleware);

const port = process.env.PORT || (options ? 3010 : 3000); // Changed to 3010

connectToDatabase()
  .then(() => {
    // Initialize cache warming service
    const cacheWarmingService = container.resolve<ICacheWarmingService>("ICacheWarmingService");
    cacheWarmingService.start();
    LoggerUtils.info("Cache warming service initialized");

    if (options) {
      // Production: Use HTTPS
      https.createServer(options, app).listen(port, () => {
        LoggerUtils.info(`HTTPS Server running on port ${port}`, undefined, { port, mode: 'production' });
      });
    } else {
      // Development: Use HTTP
      app.listen(port, () => {
        LoggerUtils.info(`HTTP Server running on port ${port}`, undefined, { port, mode: 'development' });
      });
    }
  })
  .catch((e) => {
    LoggerUtils.error("Failed to start server", undefined, e);
    process.exit(1);
  });
