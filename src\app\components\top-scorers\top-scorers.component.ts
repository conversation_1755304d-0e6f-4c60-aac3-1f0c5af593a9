import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { Column } from '../../shared/models/column.model';
import { Router } from '@angular/router';
import { LeagueService } from '../../services/league.service';
import { LEAGUE_ID } from '../../constants/constants';
import { TopScorer } from '../../shared/models/topscorer.model';
import { SHORTENED_TOP_SCORERS_COLUMNS, TOP_SCORERS_COLUMNS } from './top-scorers.definitions';
import { LeagueDataStateService } from '../../services/state/league-data-state.service';
import { Subscription } from 'rxjs';

const LIMIT = 10;

@Component({
  selector: 'top-scorers',
  templateUrl: './top-scorers.component.html',
  styleUrl: './top-scorers.component.scss'
})
export class TopScorersComponent implements OnInit, OnDestroy {
  displayedColumns: Column[] = [];
  topScorers: TopScorer[] = [];
  isLoading: boolean = false;

  @Input() hideTitle: boolean = false;

  private stateSubscription?: Subscription;

  constructor(
    private router: Router,
    private leagueService: LeagueService,
    private leagueDataState: LeagueDataStateService
  ) { }

  ngOnInit() {
    // Subscribe to state changes
    this.stateSubscription = this.leagueDataState.state$.subscribe(state => {
      this.topScorers = state.topScorers.slice(0, LIMIT);
      this.isLoading = state.isTopScorersLoading;
    });

    this.loadTopScorersData();
  }

  ngOnDestroy() {
    if (this.stateSubscription) {
      this.stateSubscription.unsubscribe();
    }
  }

  private async loadTopScorersData() {
    this.hideTitle ? (this.displayedColumns = SHORTENED_TOP_SCORERS_COLUMNS) : (this.displayedColumns = TOP_SCORERS_COLUMNS);

    try {
      const topScorersResponse = await this.leagueService.getTopScorers(LEAGUE_ID, LIMIT);

      if(topScorersResponse?.length > 0) {
        topScorersResponse.map(topScorer => {
          topScorer.tableIcon = { name: topScorer.playerName, imgUrl: topScorer.playerImgUrl || '', isTeam: false };
          if (topScorer.goalsPerGame) {
            topScorer.goalsPerGame = parseFloat(topScorer.goalsPerGame.toFixed(2));
          }
        });
      }

      // The state is automatically updated by the service
    } catch (error) {
      console.error('Error loading top scorers data:', error);
    }
  }

  refreshData(): void {
    this.leagueDataState.forceRefreshAll();
    this.loadTopScorersData();
  }

  onPlayerClick($playerDetails: TopScorer): void {
    this.router.navigate(['/player-details', { id: $playerDetails.playerId }])
  }

  trackByPlayerId(_index: number, scorer: TopScorer): string {
    return scorer.playerId;
  }
}