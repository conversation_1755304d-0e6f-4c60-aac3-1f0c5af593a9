export interface ITransferData {
    playerDetails: {
      id: string;
      name: string;
      imgUrl?: string;
    };
    fromTeam: {
      id: string;
      name: string;
      imgUrl?: string;
    };
    toTeam: {
      id: string;
      name: string;
      imgUrl?: string;
    };
  }

export interface IFreeAgentData {
    playerDetails: {
      id: string;
      name: string;
      imgUrl?: string;
      position: string;
      age: number;
    };
    previousTeams: {
      id: string;
      name: string;
      imgUrl?: string;
      seasonNumber: number;
    }[];
  }

export type News = {
    _id: string;
    title: string;
    content: string;
    type: string,
    createdAt: Date,
    createdBy: string,
    likes: string[];
    likeCount: number;
    transferData?: ITransferData;
    freeAgentData?: IFreeAgentData;
}


// change to enum
export enum NEWS_TYPE {
    LEAGUE_MANAGEMENT = 0,
    TRANSFER = 1,
    FREE_AGENT = 2
}