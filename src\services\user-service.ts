import { inject, injectable } from "tsyringe";
import { IUserService, IUserRepository, CreateUserRequest, LoginRequest, GoogleAuthRequest, AuthResponse } from "../interfaces/user";
import { IPlayerService } from "../interfaces/player";
import { IUser } from "../models/user";
import { AuthUtils } from "../utils/auth";
import logger from "../config/logger";
import { LoggerUtils } from "../utils/logger-utils";
import CustomError from "../errors/custom-error";

@injectable()
export default class UserService implements IUserService {
  constructor(
    @inject("IUserRepository") private userRepository: IUserRepository,
    @inject("IPlayerService") private playerService: IPlayerService
  ) {}

  async createUser(userData: CreateUserRequest): Promise<AuthResponse> {
    try {
      // Validate email format
      if (!AuthUtils.isValidEmail(userData.email)) {
        throw new CustomError("Invalid email format", 400);
      }

      // Validate password strength
      const passwordValidation = AuthUtils.isValidPassword(userData.password);
      if (!passwordValidation.isValid) {
        throw new CustomError(passwordValidation.message!, 400);
      }

      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(userData.email);
      if (existingUser) {
        throw new CustomError("User with this email already exists", 409);
      }

      // Hash password
      const hashedPassword = await AuthUtils.hashPassword(userData.password);

      // Create user
      const user = await this.userRepository.create({
        email: userData.email.toLowerCase(),
        password: hashedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        isEmailVerified: false,
        associatedPlayers: []
      });

      // Generate token
      const token = AuthUtils.generateToken(user);

      logger.info(`New user registered: ${user.email}`);

      return { user, token };
    } catch (error: any) {
      logger.error(`Error creating user: ${error.message}`);
      throw error;
    }
  }

  async loginUser(loginData: LoginRequest): Promise<AuthResponse> {
    try {
      // Find user by email
      const user = await this.userRepository.findByEmail(loginData.email);
      if (!user) {
        throw new CustomError("Invalid email or password", 401);
      }

      // Check if user has a password (not OAuth-only user)
      if (!user.password) {
        throw new CustomError("Please login with Google", 401);
      }

      // Verify password
      const isPasswordValid = await AuthUtils.comparePassword(loginData.password, user.password);
      if (!isPasswordValid) {
        throw new CustomError("Invalid email or password", 401);
      }

      // Update last login
      await this.userRepository.update(user.id, { lastLogin: new Date() });

      // Generate token
      const token = AuthUtils.generateToken(user);

      LoggerUtils.logAuth("User Login", user, { email: user.email });

      return { user, token };
    } catch (error: any) {
      LoggerUtils.error(`Error logging in user: ${error.message}`, undefined, error);
      throw error;
    }
  }

  async googleAuth(googleData: GoogleAuthRequest): Promise<AuthResponse> {
    try {
      // Check if user exists by Google ID
      let user = await this.userRepository.findByGoogleId(googleData.googleId);

      if (!user) {
        // Check if user exists by email
        user = await this.userRepository.findByEmail(googleData.email);
        
        if (user) {
          // Link Google account to existing user
          user = await this.userRepository.update(user.id, {
            googleId: googleData.googleId,
            profilePicture: googleData.profilePicture,
            isEmailVerified: true,
            lastLogin: new Date()
          });
        } else {
          // Create new user
          user = await this.userRepository.create({
            email: googleData.email.toLowerCase(),
            googleId: googleData.googleId,
            firstName: googleData.firstName,
            lastName: googleData.lastName,
            profilePicture: googleData.profilePicture,
            isEmailVerified: true,
            associatedPlayers: [],
            lastLogin: new Date()
          });
        }
      } else {
        // Update last login for existing Google user
        user = await this.userRepository.update(user.id, { lastLogin: new Date() });
      }

      if (!user) {
        throw new CustomError("Failed to authenticate with Google", 500);
      }

      // Generate token
      const token = AuthUtils.generateToken(user);

      LoggerUtils.logAuth("Google Authentication", user, { email: user.email });

      return { user, token };
    } catch (error: any) {
      LoggerUtils.error(`Error with Google authentication: ${error.message}`, undefined, error);
      throw error;
    }
  }

  async getUserById(userId: string): Promise<IUser | null> {
    try {
      return await this.userRepository.findById(userId);
    } catch (error: any) {
      logger.error(`Error getting user by ID: ${error.message}`);
      throw error;
    }
  }

  async getUserByEmail(email: string): Promise<IUser | null> {
    try {
      return await this.userRepository.findByEmail(email);
    } catch (error: any) {
      logger.error(`Error getting user by email: ${error.message}`);
      throw error;
    }
  }

  async updateUser(userId: string, updateData: Partial<IUser>): Promise<IUser | null> {
    try {
      // Remove sensitive fields that shouldn't be updated directly
      const { password, googleId, role, ...safeUpdateData } = updateData;

      return await this.userRepository.update(userId, safeUpdateData);
    } catch (error: any) {
      logger.error(`Error updating user: ${error.message}`);
      throw error;
    }
  }

  async deleteUser(userId: string): Promise<boolean> {
    try {
      return await this.userRepository.delete(userId);
    } catch (error: any) {
      logger.error(`Error deleting user: ${error.message}`);
      throw error;
    }
  }

  async associatePlayerWithUser(userId: string, playerId?: string, playerEmail?: string): Promise<IUser | null> {
    // This method is now deprecated - users must request association through the request system
    throw new CustomError("Direct player association is no longer allowed. Please submit a player association request for admin approval.", 403);
  }

  async removePlayerFromUser(userId: string, playerId: string): Promise<IUser | null> {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new CustomError("User not found", 404);
      }

      // Remove player from user's associated players
      user.associatedPlayers = user.associatedPlayers.filter(
        (id) => id.toString() !== playerId
      );
      
      return await this.userRepository.update(userId, { associatedPlayers: user.associatedPlayers });
    } catch (error: any) {
      logger.error(`Error removing player from user: ${error.message}`);
      throw error;
    }
  }

  async verifyEmail(userId: string): Promise<IUser | null> {
    try {
      return await this.userRepository.update(userId, { isEmailVerified: true });
    } catch (error: any) {
      logger.error(`Error verifying email: ${error.message}`);
      throw error;
    }
  }

  async searchAvailablePlayers(search?: string, email?: string): Promise<any[]> {
    try {
      // Get all users to check which players are already associated
      const allUsers = await this.userRepository.findAll();
      const associatedPlayerIds = new Set(
        allUsers.flatMap(user => user.associatedPlayers.map(id => id.toString()))
      );

      // Search for players
      let players;
      if (email) {
        const player = await this.playerService.getPlayerByEmail(email);
        players = player ? [player] : [];
      } else if (search) {
        players = await this.playerService.playerSearchByText(search);
      } else {
        players = await this.playerService.getAllPlayers();
      }

      // Filter out already associated players
      const availablePlayers = players.filter(player =>
        !associatedPlayerIds.has(player.id)
      );

      return availablePlayers.map(player => ({
        id: player.id,
        name: player.name,
        position: player.position,
        team: player.team,
        imgUrl: player.imgUrl
      }));
    } catch (error: any) {
      logger.error(`Error searching available players: ${error.message}`);
      throw error;
    }
  }

  async getUserAssociatedPlayers(userId: string): Promise<any[]> {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new CustomError("User not found", 404);
      }

      logger.info(`User ${userId} has ${user.associatedPlayers.length} associated players`);

      const players = [];
      for (const playerObjectId of user.associatedPlayers) {
        try {
          // Convert ObjectId to string
          const playerIdString = playerObjectId.toString();
          logger.info(`Fetching player data for ID: ${playerIdString}`);

          const player = await this.playerService.getPlayerById(playerIdString);
          if (player) {
            logger.info(`Successfully fetched player: ${player.name}`);
            players.push({
              id: player.id,
              name: player.name,
              position: player.position,
              team: player.team,
              imgUrl: player.imgUrl,
              stats: player.stats
            });
          } else {
            logger.warn(`Player with ID ${playerIdString} not found`);
          }
        } catch (error: any) {
          logger.warn(`Failed to get player with ID ${playerObjectId}: ${error.message}`);
          // Continue with other players
        }
      }

      logger.info(`Returning ${players.length} players for user ${userId}`);
      return players;
    } catch (error: any) {
      logger.error(`Error getting user associated players: ${error.message}`);
      throw error;
    }
  }

  async checkPlayerOwnership(userId: string, playerId: string): Promise<boolean> {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        return false;
      }

      return user.associatedPlayers.some(id => id.toString() === playerId);
    } catch (error: any) {
      logger.error(`Error checking player ownership: ${error.message}`);
      throw error;
    }
  }

  async isPlayerAssociated(playerId: string): Promise<boolean> {
    try {
      const allUsers = await this.userRepository.findAll();
      return allUsers.some(user =>
        user.associatedPlayers.some(id => id.toString() === playerId)
      );
    } catch (error: any) {
      logger.error(`Error checking player association: ${error.message}`);
      throw error;
    }
  }
}
