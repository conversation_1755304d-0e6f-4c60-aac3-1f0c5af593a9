/* === MODERN BACKGROUND CLASSES === */
.pro-clubs-bg-primary {
    background: var(--bg-primary);
}

.pro-clubs-bg-secondary {
    background: var(--bg-secondary);
}

.pro-clubs-bg-tertiary {
    background: var(--bg-tertiary);
}

.pro-clubs-surface-primary {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.pro-clubs-surface-secondary {
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.pro-clubs-surface-elevated {
    background: var(--bg-elevated);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

/* === MODERN TYPOGRAPHY CLASSES === */
.pro-clubs-header-1 {
    @extend .text-h1;
    color: var(--text-primary);
}

.pro-clubs-header-2-regular {
    @extend .text-h2;
    font-weight: var(--font-weight-normal);
    color: var(--text-primary);
}

.pro-clubs-header-2-medium {
    @extend .text-h2;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.pro-clubs-header-3 {
    @extend .text-h3;
    color: var(--text-primary);
}

.pro-clubs-text-regular {
    @extend .text-body;
    color: var(--text-primary);
}

.pro-clubs-text-large {
    @extend .text-body-lg;
    color: var(--text-primary);
}

.pro-clubs-text-small {
    @extend .text-body-sm;
    color: var(--text-primary);
}

.pro-clubs-label {
    @extend .text-label;
    color: var(--text-secondary);
}

/* === MODERN INPUT COMPONENTS === */
.pro-clubs-input {
    background: var(--surface-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    height: 44px;
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    transition: all 0.2s ease;
    outline: none;
    box-sizing: border-box;

    &:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        transform: translateY(-1px);
    }

    &:hover:not(:focus):not(:disabled) {
        border-color: var(--border-secondary);
        box-shadow: var(--shadow-sm);
    }

    &::placeholder {
        color: var(--text-muted);
        font-weight: var(--font-weight-normal);
    }

    &:disabled {
        background: var(--surface-tertiary);
        color: var(--text-muted);
        cursor: not-allowed;
        opacity: 0.6;
        transform: none;

        &:hover {
            border-color: var(--border-primary);
            box-shadow: none;
        }
    }

    /* Input variants */
    &.input-sm {
        height: 36px;
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--text-sm);
    }

    &.input-lg {
        height: 52px;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-lg);
    }
}

.pro-clubs-input-error {
    border-color: var(--danger);

    &:focus {
        border-color: var(--danger);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    &:hover:not(:focus) {
        border-color: var(--danger);
    }
}

.pro-clubs-input-success {
    border-color: var(--success);

    &:focus {
        border-color: var(--success);
        box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
    }
}

.pro-clubs-short-input {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    width: 4rem;
    height: 44px;
    text-align: center;
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
    transition: all 0.2s ease;
    outline: none;
    box-sizing: border-box;

    &:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        transform: scale(1.05);
    }

    &:hover:not(:focus):not(:disabled) {
        border-color: var(--border-secondary);
        box-shadow: var(--shadow-sm);
    }

    &:disabled {
        background: var(--surface-tertiary);
        color: var(--text-muted);
        cursor: not-allowed;
        opacity: 0.6;
        transform: none;
    }
}

/* === INPUT GROUPS === */
.pro-clubs-input-group {
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;

    .pro-clubs-input {
        border-radius: 0;

        &:first-child {
            border-top-left-radius: var(--radius-lg);
            border-bottom-left-radius: var(--radius-lg);
        }

        &:last-child {
            border-top-right-radius: var(--radius-lg);
            border-bottom-right-radius: var(--radius-lg);
        }

        &:not(:last-child) {
            border-right: none;
        }

        &:focus {
            z-index: 2;
            border-color: var(--primary);
        }
    }
}

.input-group-addon {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
    height: 44px;
    box-sizing: border-box;

    &:first-child {
        border-top-left-radius: var(--radius-lg);
        border-bottom-left-radius: var(--radius-lg);
        border-right: none;
    }

    &:last-child {
        border-top-right-radius: var(--radius-lg);
        border-bottom-right-radius: var(--radius-lg);
        border-left: none;
    }
}

/* === TEXTAREA === */
.pro-clubs-textarea {
    background: var(--surface-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    width: 100%;
    padding: var(--spacing-md);
    min-height: 100px;
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
    transition: all 0.2s ease;
    outline: none;
    resize: vertical;
    box-sizing: border-box;

    &:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    &:hover:not(:focus):not(:disabled) {
        border-color: var(--border-secondary);
        box-shadow: var(--shadow-sm);
    }

    &::placeholder {
        color: var(--text-muted);
        font-weight: var(--font-weight-normal);
    }

    &:disabled {
        background: var(--surface-tertiary);
        color: var(--text-muted);
        cursor: not-allowed;
        opacity: 0.6;
        resize: none;
    }
}

/* === RESPONSIVE INPUT DESIGN === */
@media (max-width: 768px) {
    .pro-clubs-input,
    .pro-clubs-short-input,
    .input-group-addon {
        height: 40px;
    }

    .pro-clubs-input {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--text-sm);

        &.input-sm {
            height: 32px;
            font-size: var(--text-xs);
        }

        &.input-lg {
            height: 48px;
            font-size: var(--text-base);
        }
    }

    .pro-clubs-short-input {
        width: 3.5rem;
        font-size: var(--text-xs);
    }

    .pro-clubs-textarea {
        padding: var(--spacing-sm);
        font-size: var(--text-sm);
        min-height: 80px;
    }

    .input-group-addon {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--text-xs);
    }
}

@media (max-width: 480px) {
    .pro-clubs-input,
    .pro-clubs-short-input,
    .input-group-addon {
        height: 36px;
    }

    .pro-clubs-input {
        padding: var(--spacing-xs);
        font-size: var(--text-sm);
    }

    .pro-clubs-short-input {
        width: 3rem;
        font-size: 10px;
    }

    .pro-clubs-textarea {
        min-height: 60px;
        font-size: var(--text-sm);
    }
}

/* === ACCESSIBILITY === */
.pro-clubs-input,
.pro-clubs-short-input,
.pro-clubs-textarea {
    &:focus-visible {
        outline: 2px solid var(--primary);
        outline-offset: 2px;
    }
}

/* === DARK MODE SUPPORT === */
@media (prefers-color-scheme: dark) {
    .pro-clubs-input,
    .pro-clubs-short-input,
    .pro-clubs-textarea {
        &::placeholder {
            color: var(--text-muted);
            opacity: 0.7;
        }
    }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
    .pro-clubs-input,
    .pro-clubs-short-input,
    .pro-clubs-textarea {
        border-width: 2px;

        &:focus {
            border-width: 3px;
        }
    }
}

/* === MODERN BUTTON COMPONENTS === */
.pro-clubs-button-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-hover) 100%);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    height: 2.75rem;
    padding: 0 var(--spacing-lg);
    font-family: var(--font-sans);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    position: relative;
    overflow: hidden;

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-active) 100%);
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
    }

    &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: var(--shadow-md);
    }

    &:focus-visible {
        outline: 2px solid var(--primary);
        outline-offset: 2px;
    }

    &:disabled {
        background: var(--surface-tertiary);
        color: var(--text-muted);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
}

.pro-clubs-button-secondary {
    background: var(--surface-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    height: 2.75rem;
    padding: 0 var(--spacing-lg);
    font-family: var(--font-sans);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);

    &:hover:not(:disabled) {
        background: var(--surface-hover);
        border-color: var(--border-secondary);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    &:active:not(:disabled) {
        transform: translateY(0);
        background: var(--surface-active);
    }

    &:focus-visible {
        outline: 2px solid var(--primary);
        outline-offset: 2px;
    }

    &:disabled {
        background: var(--surface-tertiary);
        color: var(--text-muted);
        border-color: var(--border-primary);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
}

.pro-clubs-button-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: none;
    border-radius: var(--radius-lg);
    height: 2.75rem;
    padding: 0 var(--spacing-lg);
    font-family: var(--font-sans);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);

    &:hover:not(:disabled) {
        background: var(--surface-hover);
        color: var(--text-primary);
    }

    &:active:not(:disabled) {
        background: var(--surface-active);
    }

    &:focus-visible {
        outline: 2px solid var(--primary);
        outline-offset: 2px;
    }

    &:disabled {
        color: var(--text-muted);
        cursor: not-allowed;
    }
}



/* === MODERN BORDER UTILITIES === */
.primary-border-bottom {
    border-bottom: 1px solid var(--primary);
}

.primary-border {
    border: 1px solid var(--primary);
}

.pro-clubs-border-radius {
    border-radius: var(--radius-lg);
}

.border-radius-sm {
    border-radius: var(--radius-sm);
}

.border-radius-md {
    border-radius: var(--radius-md);
}

.border-radius-lg {
    border-radius: var(--radius-lg);
}

.border-radius-xl {
    border-radius: var(--radius-xl);
}

.border-radius-2xl {
    border-radius: var(--radius-2xl);
}

.border-radius-full {
    border-radius: var(--radius-full);
}

/* === MODERN TEXT UTILITIES === */
.pro-clubs-text-primary {
    color: var(--primary);
}

.text-success {
    color: var(--success-500);
}

.text-warning {
    color: var(--warning-500);
}

.text-error {
    color: var(--error-500);
}

.text-green {
    color: var(--success-500);
}

.text-yellow {
    color: var(--warning-500);
}

.text-red {
    color: var(--error-500);
}

/* === MODERN STATE UTILITIES === */
.disabled-button {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* === MODERN SHADOW UTILITIES === */
.shadow-sm {
    box-shadow: var(--shadow-sm);
}

.shadow-md {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

.shadow-xl {
    box-shadow: var(--shadow-xl);
}

.shadow-2xl {
    box-shadow: var(--shadow-2xl);
}

/* === MODERN GRID SYSTEM === */

/* Container System */
.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);

    @media (max-width: 768px) {
        padding: 0 var(--spacing-md);
    }
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-lg);

    @media (max-width: 768px) {
        padding: 0 var(--spacing-md);
    }
}

/* Consistent 5px margin container for all app components */
.app-container {
    width: 100%;
    padding: 0 5px;
    margin: 0 auto;
    box-sizing: border-box;
}

.app-page-container {
    width: 100%;
    padding: var(--spacing-xl) 5px;
    margin: 0 auto;
    box-sizing: border-box;
    min-height: 100vh;
}

/* Modern Grid System */
.grid {
    display: grid;
    gap: var(--spacing-lg);

    &.gap-sm { gap: var(--spacing-sm); }
    &.gap-md { gap: var(--spacing-md); }
    &.gap-lg { gap: var(--spacing-lg); }
    &.gap-xl { gap: var(--spacing-xl); }
    &.gap-2xl { gap: var(--spacing-2xl); }
}

/* Grid Templates */
.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }
.grid-5 { grid-template-columns: repeat(5, 1fr); }
.grid-6 { grid-template-columns: repeat(6, 1fr); }

/* Auto-fit grids */
.grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* Responsive Grid Classes */
.grid-responsive {
    display: grid;
    gap: var(--spacing-lg);
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: var(--spacing-xl);
    min-height: calc(100vh - 120px);

    @media (max-width: 1200px) {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
    }

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        min-height: auto;
    }
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    @media (max-width: 480px) {
        grid-template-columns: 1fr;
    }
}

/* Player Grid */
.player-grid {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: var(--spacing-2xl);
    align-items: start;

    @media (max-width: 1200px) {
        grid-template-columns: 350px 1fr;
        gap: var(--spacing-xl);
    }

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}

/* Table Grid */
.table-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 768px) {
        gap: var(--spacing-md);
    }
}

/* === MODERN LAYOUT UTILITIES === */
.card {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, transparent, var(--primary), transparent);
        transition: left 0.6s ease-in-out;
    }

    &:hover::before {
        left: 100%;
    }
}

.card-hover {
    &:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
        border-color: var(--primary);
    }
}

.card-interactive {
    cursor: pointer;

    &:hover {
        box-shadow: var(--shadow-xl);
        transform: translateY(-4px);
        border-color: var(--primary);
    }

    &:active {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
}

.glass-effect {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.1);

    &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }
}

[data-theme="light"] .glass-effect {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);

    &::before {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, transparent 50%);
    }
}

/* === ADVANCED GRID UTILITIES === */

/* Grid Areas */
.grid-area-1 { grid-area: 1; }
.grid-area-2 { grid-area: 2; }
.grid-area-3 { grid-area: 3; }
.grid-area-4 { grid-area: 4; }

/* Grid Spans */
.col-span-1 { grid-column: span 1; }
.col-span-2 { grid-column: span 2; }
.col-span-3 { grid-column: span 3; }
.col-span-4 { grid-column: span 4; }
.col-span-5 { grid-column: span 5; }
.col-span-6 { grid-column: span 6; }
.col-span-full { grid-column: 1 / -1; }

.row-span-1 { grid-row: span 1; }
.row-span-2 { grid-row: span 2; }
.row-span-3 { grid-row: span 3; }
.row-span-4 { grid-row: span 4; }
.row-span-full { grid-row: 1 / -1; }

/* Flexbox Utilities */
.flex {
    display: flex;

    &.flex-col { flex-direction: column; }
    &.flex-row { flex-direction: row; }
    &.flex-wrap { flex-wrap: wrap; }
    &.flex-nowrap { flex-wrap: nowrap; }

    &.justify-start { justify-content: flex-start; }
    &.justify-center { justify-content: center; }
    &.justify-end { justify-content: flex-end; }
    &.justify-between { justify-content: space-between; }
    &.justify-around { justify-content: space-around; }
    &.justify-evenly { justify-content: space-evenly; }

    &.items-start { align-items: flex-start; }
    &.items-center { align-items: center; }
    &.items-end { align-items: flex-end; }
    &.items-stretch { align-items: stretch; }
    &.items-baseline { align-items: baseline; }

    &.gap-xs { gap: var(--spacing-xs); }
    &.gap-sm { gap: var(--spacing-sm); }
    &.gap-md { gap: var(--spacing-md); }
    &.gap-lg { gap: var(--spacing-lg); }
    &.gap-xl { gap: var(--spacing-xl); }
    &.gap-2xl { gap: var(--spacing-2xl); }
}

/* Layout Patterns */
.sidebar-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--spacing-xl);
    min-height: 100vh;

    @media (max-width: 1024px) {
        grid-template-columns: 240px 1fr;
        gap: var(--spacing-lg);
    }

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        min-height: auto;
    }
}

.hero-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    min-height: 60vh;

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        min-height: auto;
    }
}

.masonry-layout {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    grid-auto-rows: masonry;

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

/* Card Layouts */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);

    @media (max-width: 1024px) {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    @media (max-width: 640px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

/* === MODERN BADGE COMPONENTS === */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
    line-height: 1;

    &.badge-primary {
        background: var(--primary);
        color: var(--text-inverse);
    }

    &.badge-success {
        background: var(--success-500);
        color: var(--text-inverse);
    }

    &.badge-warning {
        background: var(--warning-500);
        color: var(--text-inverse);
    }

    &.badge-error {
        background: var(--error-500);
        color: var(--text-inverse);
    }

    &.badge-outline {
        background: transparent;
        border: 1px solid var(--border-primary);
        color: var(--text-primary);
    }
}

/* === MODERN AVATAR COMPONENT === */
.avatar {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-full);
    overflow: hidden;
    background: var(--surface-secondary);
    border: 2px solid var(--border-primary);

    &.avatar-sm {
        width: 2rem;
        height: 2rem;
    }

    &.avatar-md {
        width: 2.5rem;
        height: 2.5rem;
    }

    &.avatar-lg {
        width: 3rem;
        height: 3rem;
    }

    &.avatar-xl {
        width: 4rem;
        height: 4rem;
    }

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    &.avatar-online {
        position: relative;

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 25%;
            height: 25%;
            background: var(--success-500);
            border: 2px solid var(--surface-primary);
            border-radius: var(--radius-full);
        }
    }
}

/* === MODERN DIVIDER === */
.divider {
    height: 1px;
    background: var(--border-primary);
    margin: var(--spacing-lg) 0;

    &.divider-vertical {
        width: 1px;
        height: auto;
        margin: 0 var(--spacing-lg);
    }

    &.divider-dashed {
        background: none;
        border-top: 1px dashed var(--border-secondary);
    }
}

/* === MODERN TOOLTIP (CSS-only) === */
.tooltip {
    position: relative;
    cursor: help;

    &::before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: var(--neutral-900);
        color: var(--text-inverse);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-md);
        font-size: var(--text-xs);
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease-in-out;
        z-index: 1000;
    }

    &::after {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: var(--neutral-900);
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease-in-out;
    }

    &:hover::before,
    &:hover::after {
        opacity: 1;
    }
}