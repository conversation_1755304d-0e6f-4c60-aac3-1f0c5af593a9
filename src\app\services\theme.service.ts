import { Injectable, Inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';

export type Theme = 'light' | 'dark';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'theme';
  private readonly DEFAULT_THEME: Theme = 'dark';
  
  private currentThemeSubject = new BehaviorSubject<Theme>(this.DEFAULT_THEME);
  public currentTheme$ = this.currentThemeSubject.asObservable();

  constructor(@Inject(DOCUMENT) private document: Document) {
    this.initializeTheme();
  }

  /**
   * Initialize theme from localStorage or system preference
   */
  private initializeTheme(): void {
    const savedTheme = this.getSavedTheme();
    const systemTheme = this.getSystemTheme();
    const initialTheme = savedTheme || systemTheme || this.DEFAULT_THEME;
    
    this.setTheme(initialTheme, false); // Don't save to localStorage on init
  }

  /**
   * Get saved theme from localStorage
   */
  private getSavedTheme(): Theme | null {
    try {
      const saved = localStorage.getItem(this.THEME_KEY);
      return saved === 'light' || saved === 'dark' ? saved : null;
    } catch {
      return null;
    }
  }

  /**
   * Get system theme preference
   */
  private getSystemTheme(): Theme {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'dark';
    }
    return this.DEFAULT_THEME;
  }

  /**
   * Get current theme
   */
  getCurrentTheme(): Theme {
    return this.currentThemeSubject.value;
  }

  /**
   * Check if current theme is dark
   */
  isDarkMode(): boolean {
    return this.getCurrentTheme() === 'dark';
  }

  /**
   * Check if current theme is light
   */
  isLightMode(): boolean {
    return this.getCurrentTheme() === 'light';
  }

  /**
   * Set theme
   */
  setTheme(theme: Theme, saveToStorage: boolean = true): void {
    // Update document attribute
    this.document.documentElement.setAttribute('data-theme', theme);
    
    // Update color-scheme for better browser integration
    this.document.documentElement.style.colorScheme = theme;
    
    // Update current theme
    this.currentThemeSubject.next(theme);
    
    // Save to localStorage
    if (saveToStorage) {
      this.saveTheme(theme);
    }
  }

  /**
   * Toggle between light and dark theme
   */
  toggleTheme(): void {
    const newTheme: Theme = this.isDarkMode() ? 'light' : 'dark';
    this.setTheme(newTheme);
  }

  /**
   * Set light theme
   */
  setLightTheme(): void {
    this.setTheme('light');
  }

  /**
   * Set dark theme
   */
  setDarkTheme(): void {
    this.setTheme('dark');
  }

  /**
   * Save theme to localStorage
   */
  private saveTheme(theme: Theme): void {
    try {
      localStorage.setItem(this.THEME_KEY, theme);
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  }

  /**
   * Listen to system theme changes
   */
  listenToSystemThemeChanges(): void {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: light)');
      
      mediaQuery.addEventListener('change', (e) => {
        // Only update if no saved preference exists
        if (!this.getSavedTheme()) {
          const systemTheme: Theme = e.matches ? 'light' : 'dark';
          this.setTheme(systemTheme, false);
        }
      });
    }
  }

  /**
   * Reset to system theme
   */
  resetToSystemTheme(): void {
    // Remove saved preference
    try {
      localStorage.removeItem(this.THEME_KEY);
    } catch (error) {
      console.warn('Failed to remove theme preference:', error);
    }
    
    // Set to system theme
    const systemTheme = this.getSystemTheme();
    this.setTheme(systemTheme, false);
  }

  /**
   * Get theme for specific component (useful for conditional styling)
   */
  getThemeClass(): string {
    return `theme-${this.getCurrentTheme()}`;
  }

  /**
   * Get opposite theme
   */
  getOppositeTheme(): Theme {
    return this.isDarkMode() ? 'light' : 'dark';
  }
}
