/* === MODERN PLAYER STATS BY POSITION DESIGN === */

.player-stats-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    font-family: var(--font-sans);

    @media (max-width: 768px) {
        gap: var(--spacing-md);
    }
}

/* === HEADER SECTION === */
.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-bottom: 1px solid var(--border-primary);

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }
}

.header-content {
    flex: 1;

    .stats-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;

        i {
            color: var(--primary);
            font-size: var(--text-base);
        }

        @media (max-width: 768px) {
            font-size: var(--text-base);
        }
    }

    .stats-subtitle {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin: 0;
        font-weight: var(--font-weight-medium);
    }
}

.positions-count {
    background: var(--surface-tertiary);
    color: var(--text-tertiary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-semibold);
    border: 1px solid var(--border-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;

    @media (max-width: 768px) {
        align-self: flex-start;
    }
}

/* === CHART SECTION === */
.chart-section {
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-bottom: 1px solid var(--border-primary);

    @media (max-width: 768px) {
        padding: var(--spacing-md);
    }
}

.chart-header {
    margin-bottom: var(--spacing-lg);

    .chart-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0;

        i {
            color: var(--primary);
            font-size: var(--text-base);
        }

        @media (max-width: 480px) {
            font-size: var(--text-base);
        }
    }
}

.chart-container {
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    padding: var(--spacing-md);
    overflow: hidden;

    .position-chart {
        width: 100%;
        height: 240px;
        min-height: 240px;

        @media (max-width: 768px) {
            height: 200px;
            min-height: 200px;
        }

        @media (max-width: 480px) {
            height: 180px;
            min-height: 180px;
        }
    }

    // Ensure chart is visible and properly sized
    ::ng-deep {
        .ag-chart-wrapper {
            width: 100% !important;
            height: 100% !important;
        }

        .ag-chart {
            width: 100% !important;
            height: 100% !important;
        }

        // Dark theme adjustments
        .ag-chart-title {
            color: var(--text-primary) !important;
        }

        .ag-chart-axis-label {
            color: var(--text-secondary) !important;
        }

        .ag-chart-legend-item-text {
            color: var(--text-primary) !important;
        }
    }
}

/* === STATS GRID === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }

    @media (max-width: 480px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
    }
}

/* === POSITION CARD === */
.position-card {
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    padding: var(--spacing-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--border-secondary);
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary));
    }

    @media (max-width: 768px) {
        padding: var(--spacing-md);
    }
}

/* === POSITION HEADER === */
.position-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);

    .position-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: var(--primary-100);
        border-radius: var(--radius-lg);
        color: var(--primary);
        font-size: var(--text-lg);
        flex-shrink: 0;

        @media (max-width: 480px) {
            width: 40px;
            height: 40px;
            font-size: var(--text-base);
        }
    }

    .position-info {
        flex: 1;

        .position-name {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-xs) 0;
            line-height: 1.2;

            @media (max-width: 480px) {
                font-size: var(--text-base);
            }
        }

        .games-count {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
        }
    }
}

/* === POSITION STATS === */
.position-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);

    @media (max-width: 480px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--surface-primary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
        border-color: var(--border-secondary);
    }

    .stat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: var(--radius-md);
        font-size: var(--text-sm);
        flex-shrink: 0;
        transition: all 0.3s ease;

        @media (max-width: 480px) {
            width: 28px;
            height: 28px;
            font-size: var(--text-xs);
        }

        // Default background
        background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
        color: var(--primary-600);
    }

    // Icon color variants based on stat type
    .stat-item:nth-child(1) .stat-icon {
        background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
        color: var(--primary-600);
    }

    .stat-item:nth-child(2) .stat-icon {
        background: linear-gradient(135deg, var(--success-100), var(--success-200));
        color: var(--success-600);
    }

    .stat-item:nth-child(3) .stat-icon {
        background: linear-gradient(135deg, var(--info-100), var(--info-200));
        color: var(--info-600);
    }

    .stat-item:nth-child(4) .stat-icon {
        background: linear-gradient(135deg, var(--warning-100), var(--warning-200));
        color: var(--warning-600);
    }

    .stat-item:nth-child(5) .stat-icon {
        background: linear-gradient(135deg, var(--error-100), var(--error-200));
        color: var(--error-600);
    }

    .stat-content {
        display: flex;
        flex-direction: column;
        gap: 2px;

        .stat-value {
            font-size: var(--text-base);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            line-height: 1;

            @media (max-width: 480px) {
                font-size: var(--text-sm);
            }
        }

        .stat-label {
            font-size: var(--text-xs);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
    }
}

/* === PERFORMANCE BAR === */
.performance-bar {
    width: 100%;
    height: 6px;
    background: var(--surface-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;

    .performance-fill {
        height: 100%;
        border-radius: var(--radius-full);
        transition: width 0.8s ease;
        position: relative;

        &.excellent {
            background: linear-gradient(90deg, var(--success-500), var(--success-400));
        }

        &.good {
            background: linear-gradient(90deg, var(--warning-500), var(--warning-400));
        }

        &.average {
            background: linear-gradient(90deg, var(--info-500), var(--info-400));
        }

        &.poor {
            background: linear-gradient(90deg, var(--error-500), var(--error-400));
        }
    }
}

/* === LOADING STATE === */
.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
}

/* === RESPONSIVE ADJUSTMENTS === */
@media (max-width: 480px) {
    .player-stats-container {
        border-radius: var(--radius-lg);
    }

    .position-card {
        border-radius: var(--radius-md);
        padding: var(--spacing-sm);
    }

    .position-header {
        margin-bottom: var(--spacing-md);
    }

    .position-stats {
        margin-bottom: var(--spacing-md);
    }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
    .position-card,
    .stat-item {
        transition: none;

        &:hover {
            transform: none;
        }
    }

    .performance-fill {
        transition: none;
    }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
    .player-stats-container,
    .position-card,
    .stat-item {
        border-width: 2px;
    }

    .stats-header {
        border-bottom-width: 2px;
    }

    .performance-bar {
        border: 1px solid var(--border-primary);
    }
}