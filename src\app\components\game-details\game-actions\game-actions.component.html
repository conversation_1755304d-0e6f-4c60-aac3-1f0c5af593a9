<!-- Action Buttons -->
<div class="action-buttons">
    <!-- Edit Game Button
    <button class="action-btn edit-btn" 
            *ngIf="canEdit$ | async"
            (click)="onEditGameClick()">
        <i class="fas fa-edit"></i>
        <span>Edit Game</span>
    </button> -->

    <!-- Live Stream Button -->
    <button class="action-btn stream-btn" 
            *ngIf="hasLiveStream"
            (click)="onLiveStreamClick()">
        <i class="fas fa-external-link-alt"></i>
        <span *ngIf="isGameTime">Watch Live</span>
        <span *ngIf="!isGameTime">Available at Game Time</span>
    </button>
</div>
