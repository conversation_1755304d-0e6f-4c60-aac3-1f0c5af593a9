<div class="team-actions-container" *ngIf="canEdit">
    <!-- Edit Mode Actions -->
    <div class="edit-actions" *ngIf="isEditMode">
        <button class="action-btn cancel-btn" 
                (click)="onCancelClick()"
                [disabled]="isLoading">
            <i class="fas fa-times"></i>
            <span>Cancel</span>
        </button>
        <button class="action-btn save-btn" 
                (click)="onSaveClick()"
                [disabled]="isLoading">
            <i class="fas fa-save" [class.spinning]="isLoading"></i>
            <span>{{ isLoading ? 'Saving...' : 'Save' }}</span>
        </button>
    </div>

    <!-- Normal Mode Actions -->
    <div class="normal-actions" *ngIf="!isEditMode">
        <button class="action-btn edit-btn" 
                (click)="onEditClick()"
                title="Edit Team">
            <i class="fas fa-edit"></i>
            <span>Edit Team</span>
        </button>
        
        <button class="action-btn add-btn" 
                (click)="onAddPlayerClick()"
                title="Add Player">
            <i class="fas fa-user-plus"></i>
            <span>Add Player</span>
        </button>
        
        <button class="action-btn remove-all-btn" 
                *ngIf="team.players && team.players.length > 0"
                (click)="onRemoveAllPlayersClick()"
                title="Remove All Players">
            <i class="fas fa-users-slash"></i>
            <span>Remove All</span>
        </button>
        
        <button class="action-btn delete-btn" 
                (click)="onDeleteClick()"
                title="Delete Team">
            <i class="fas fa-trash-alt"></i>
            <span>Delete Team</span>
        </button>
    </div>
</div>
