import { Component } from '@angular/core';
import { PlayerService } from '../../services/player.service';
import { ListOption } from '../../shared/models/list-option.model';
import { ActivatedRoute } from '@angular/router';
import { TeamService } from '../../services/team.service';
import { PlayerDTO, TeamDTO } from '@pro-clubs-manager/shared-dtos';
import { LEAGUE_ID } from '../../constants/constants';

@Component({
  selector: 'assign-player-to-team',
  templateUrl: './assign-player-to-team.component.html',
  styleUrl: './assign-player-to-team.component.scss'
})
export class AssignPlayerToTeamComponent {
  teamsOptions: ListOption[] | null = null;
  playersOptions: ListOption[] | null = null;
  selectedPlayer: PlayerDTO | null = null;
  allPlayers: PlayerDTO[] | null = null;
  allTeams: TeamDTO[] | null = null;
  teamID: string = '';
  teamName: string = '';
  isLoading: boolean = false;

  constructor(private route: ActivatedRoute, private playerService: PlayerService, private teamService: TeamService) { }

  ngOnInit() {
    this.loadSelectedTeam();
    this.loadTeams();
  };

  private loadSelectedTeam(): void {
    this.teamID = this.route.snapshot.paramMap.get('id') || this.teamID;
    this.teamName = this.route.snapshot.paramMap.get('name') || this.teamName;

    if (!this.teamID || !this.teamName) { return; }
  }


  async loadTeams() {
    this.isLoading = true;
    this.allTeams = await this.teamService.getTeamsByLeagueId(LEAGUE_ID);

    this.teamsOptions = this.allTeams.map(team => { return { value: team.id, displayText: team.name } });
    this.teamsOptions.unshift({ value: '0', displayText: 'Free Agent' });

    this.isLoading = false;
  };

  async loadPlayersOptions(teamId: string) {
    this.isLoading = true;
    if (teamId === '0') {
      this.allPlayers = await this.playerService.getFreeAgents();
    }
    else {
      this.allPlayers = await this.teamService.getPlayersByTeam(teamId);
    }

    this.playersOptions = this.allPlayers.map(player => { return { value: player.id, displayText: `${player.name}, ${player.age}, ${player.position}` } });
    this.isLoading = false;
  }

  onTeamSelect(selectedTeam: ListOption) {
    if (!selectedTeam) {
      return;
    }

    this.selectedPlayer = null;

    this.loadPlayersOptions(selectedTeam.value);
  };

  onPlayerSelect(selectedPlayer: ListOption) {
    if (!selectedPlayer) {
      return;
    }

    this.selectedPlayer = this.allPlayers!.find(player => player.id === selectedPlayer.value)!;
  };

  async onAssignClick() {
    await this.teamService.addPlayerToTeam(this.teamID, this.selectedPlayer!.id);

    history.back();
  }

  onArrowBackClick() {
    history.back();
  }
}
