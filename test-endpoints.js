const http = require('http');

// Test function to make HTTP requests
function testEndpoint(path, description) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`\n${description}:`);
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response: ${data.substring(0, 200)}${data.length > 200 ? '...' : ''}`);
        resolve({ status: res.statusCode, data });
      });
    });

    req.on('error', (error) => {
      console.log(`\n${description}:`);
      console.log(`Error: ${error.message}`);
      reject(error);
    });

    req.setTimeout(10000, () => {
      console.log(`\n${description}:`);
      console.log('Request timed out');
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// Test the endpoints
async function runTests() {
  console.log('Testing Pro Clubs Stats Server Endpoints...');
  
  try {
    // Test dashboard endpoint
    await testEndpoint('/api/dashboard', 'Dashboard Endpoint');
    
    // Test news endpoint
    await testEndpoint('/api/news', 'News Endpoint');
    
    // Test health check if it exists
    await testEndpoint('/api/health', 'Health Check');
    
    console.log('\nAll tests completed!');
  } catch (error) {
    console.log('\nTest failed:', error.message);
  }
}

// Wait a bit for server to start, then run tests
setTimeout(runTests, 3000);
