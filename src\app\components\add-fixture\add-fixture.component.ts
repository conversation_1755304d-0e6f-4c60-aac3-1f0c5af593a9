import { Component, Input } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ListOption } from '../../shared/models/list-option.model';
import { LeagueService } from '../../services/league.service';
import { LEAGUE_ID } from '../../constants/constants';
import { AddFixtureDataRequest } from '../../shared/models/addFixtureDataRequest';
import { NotificationService } from '../../services/notification.service';
import { PLAYOFF_STAGE, SERIES_FORMAT } from '../../shared/models/game.model';

@Component({
  selector: 'add-fixture',
  templateUrl: './add-fixture.component.html',
  styleUrl: './add-fixture.component.scss'
})

export class AddFixtureComponent {
  @Input() initialNumberOfGames: number = 1; // Default to 5 games, but can be overridden from template
  @Input() preventSameTeamMatchup: boolean = true; // Flag to prevent team vs itself
  @Input() removeSelectedTeamsFromOtherDropdowns: boolean = true; // Flag to remove selected teams from other dropdowns

  addFixtureFormGroup: FormGroup | null = null;
  addPlayerFile: FormData = new FormData();
  isLoading: boolean = false;
  sourceTeamsOptions: ListOption[] | null = null;
  teamsOptions: ListOption[] | null = null;

  // Store filtered options for each dropdown
  homeTeamOptions: ListOption[][] = [];
  awayTeamOptions: ListOption[][] = [];

  // Playoff options
  playoffStageOptions: ListOption[] = [
    { value: PLAYOFF_STAGE.PLAY_IN_ROUND, displayText: 'Play-in Round' },
    { value: PLAYOFF_STAGE.QUARTER_FINAL, displayText: 'Quarter-Final' },
    { value: PLAYOFF_STAGE.SEMI_FINAL, displayText: 'Semi-Final' },
    { value: PLAYOFF_STAGE.FINAL, displayText: 'Final' },
    { value: PLAYOFF_STAGE.THIRD_PLACE, displayText: '3rd Place Match' },
    { value: PLAYOFF_STAGE.PROMOTION_PLAYOFF, displayText: 'Promotion Playoff' },
    { value: PLAYOFF_STAGE.RELEGATION_PLAYOFF, displayText: 'Relegation Playoff' }
  ];

  seriesFormatOptions: ListOption[] = [
    { value: SERIES_FORMAT.BEST_OF_1, displayText: 'Best of 1' },
    { value: SERIES_FORMAT.BEST_OF_3, displayText: 'Best of 3' },
    { value: SERIES_FORMAT.BEST_OF_5, displayText: 'Best of 5' },
    { value: SERIES_FORMAT.BEST_OF_7, displayText: 'Best of 7' }
  ];

  constructor(private notificationService: NotificationService, private leagueService: LeagueService, private formBuilder: FormBuilder) {

  };

  ngOnInit() {
    this.isLoading = true;
    this.initForms();
    this.loadTeams();
  }

  initForms() {
    this.addFixtureFormGroup = this.formBuilder.group({
      startDate: ['', Validators.required], // Date
      endDate: ['', Validators.required], // Date
      round: ['', Validators.required],
      isPlayoff: [false],
      playoffStage: [''],
      seriesFormat: [SERIES_FORMAT.BEST_OF_1],
      games: this.formBuilder.array([])
    });

    // Add initial games based on input property
    for (let i = 0; i < this.initialNumberOfGames; i++) {
      this.games.push(this.createGameFormGroup());
      // Initialize empty arrays - will be populated when teams are loaded
      this.homeTeamOptions[i] = [];
      this.awayTeamOptions[i] = [];
    }
  }

  get games() {
    return this.addFixtureFormGroup!.get('games') as FormArray;
  }

  createGameFormGroup(): FormGroup {
    return this.formBuilder.group({
      homeTeamId: ['', Validators.required],
      awayTeamId: ['', Validators.required]
    });
  }

  addGame() {
    const newIndex = this.games.length;
    this.games.push(this.createGameFormGroup());

    // Initialize filtered options for the new game
    this.homeTeamOptions[newIndex] = [...(this.sourceTeamsOptions || [])];
    this.awayTeamOptions[newIndex] = [...(this.sourceTeamsOptions || [])];

    // Update all filtered options
    this.updateAllFilteredOptions();
  }

  removeGame(index: number) {
    if (this.games.length > 1) { // Ensure at least one game remains
      this.games.removeAt(index);

      // Remove the corresponding filtered options
      this.homeTeamOptions.splice(index, 1);
      this.awayTeamOptions.splice(index, 1);

      // Update all filtered options
      this.updateAllFilteredOptions();
    }
  }

  async loadTeams() {
    const teamsResponse = await this.leagueService.getLeagueTable(LEAGUE_ID);
    this.teamsOptions = teamsResponse.map(team => { return { value: team.teamId, displayText: team.teamName } });
    this.sourceTeamsOptions = [...this.teamsOptions];

    // Initialize filtered options for existing games
    this.initializeFilteredOptions();
    this.isLoading = false;
  }

  initializeFilteredOptions() {
    for (let i = 0; i < this.games.length; i++) {
      this.homeTeamOptions[i] = [...(this.sourceTeamsOptions || [])];
      this.awayTeamOptions[i] = [...(this.sourceTeamsOptions || [])];
    }
    this.updateAllFilteredOptions();
  }

  clearForm() {
    this.addFixtureFormGroup!.reset();
    this.games.setValue([]);
  }

  onPlayoffChange(isPlayoff: boolean) {
    const playoffStageControl = this.addFixtureFormGroup!.get('playoffStage');
    const seriesFormatControl = this.addFixtureFormGroup!.get('seriesFormat');
    const roundControl = this.addFixtureFormGroup!.get('round');

    if (isPlayoff) {
      playoffStageControl?.setValidators([Validators.required]);
      // Make round optional for playoff games since stage name will be displayed
      // Clear the round value as it won't be used for playoff games
      roundControl?.clearValidators();
      roundControl?.setValue('');

      // Update games based on current series format
      const currentFormat = seriesFormatControl?.value || SERIES_FORMAT.BEST_OF_1;
      this.updateGamesForSeriesFormat(currentFormat);
    } else {
      playoffStageControl?.clearValidators();
      playoffStageControl?.setValue('');

      // Reset to single game for regular fixtures
      this.resetToSingleGame();
      // Make round required for regular season games
      roundControl?.setValidators([Validators.required]);
    }

    playoffStageControl?.updateValueAndValidity();
    roundControl?.updateValueAndValidity();

    // Update filtered options when playoff status changes
    this.updateAllFilteredOptions();
  }

  isPlayoffSelected(): boolean {
    return this.addFixtureFormGroup?.get('isPlayoff')?.value || false;
  }

  onSeriesFormatChange(format: SERIES_FORMAT) {
    if (this.isPlayoffSelected()) {
      this.updateGamesForSeriesFormat(format);
    }
  }

  private updateGamesForSeriesFormat(format: SERIES_FORMAT) {
    const requiredGames = this.getGamesCountForFormat(format);
    const currentGamesCount = this.games.length;

    if (requiredGames > currentGamesCount) {
      // Add more games
      for (let i = currentGamesCount; i < requiredGames; i++) {
        this.addGame();
      }
    } else if (requiredGames < currentGamesCount) {
      // Remove excess games
      for (let i = currentGamesCount - 1; i >= requiredGames; i--) {
        this.removeGame(i);
      }
    }
  }

  private resetToSingleGame() {
    // Remove all games except the first one
    while (this.games.length > 1) {
      this.removeGame(this.games.length - 1);
    }
  }

  private getGamesCountForFormat(format: SERIES_FORMAT): number {
    switch (format) {
      case SERIES_FORMAT.BEST_OF_1:
        return 1;
      case SERIES_FORMAT.BEST_OF_3:
        return 3;
      case SERIES_FORMAT.BEST_OF_5:
        return 5;
      case SERIES_FORMAT.BEST_OF_7:
        return 7;
      default:
        return 1;
    }
  }

  async onSubmit() {
    if (this.addFixtureFormGroup!.valid) {
      const convertedForm = this.convertFormToModel();
      // Here you can send form data to your backend or perform any necessary action

      try {
        await this.leagueService.createFixture(LEAGUE_ID, convertedForm);
        const displayTitle = this.getDisplayTitle();
        this.notificationService.success(`${displayTitle} Added successfully`);
        this.clearForm();
      } catch (error) {
        console.error('Error creating fixture:', error);
        this.notificationService.error('Failed to create fixture. Please try again.');
      }

    } else {
    }
  }

  // when the user presses on submit, converting the form group into model before passing it to the server
  convertFormToModel(): AddFixtureDataRequest {
    const formValue = this.addFixtureFormGroup!.value;
    const isPlayoff = formValue.isPlayoff;
    const playoffStage = formValue.playoffStage;
    const seriesFormat = formValue.seriesFormat;

    // For playoff games, use a default round number (0) since round is required by the server
    // but not meaningful for playoff games (playoff stage is used instead)
    const round = isPlayoff ? 0 : parseInt(formValue.round) || 0;

    const convertedForm: AddFixtureDataRequest = {
      startDate: formValue.startDate,
      endDate: formValue.endDate,
      round: round,
      isPlayoff: isPlayoff,
      playoffDetails: isPlayoff ? {
        stage: playoffStage,
        format: seriesFormat
      } : undefined,
      games: formValue.games.map((game: any, index: number) => ({
        homeTeamId: game.homeTeamId,
        awayTeamId: game.awayTeamId,
        isPlayoff: isPlayoff,
        playoffStage: isPlayoff ? playoffStage : undefined,
        matchNumber: isPlayoff ? index + 1 : undefined
      }))
    };

    return convertedForm;
  }

  onSelectionChange($chosenTeam: ListOption, index: number, isHomeTeam: boolean) {
    if (!$chosenTeam) return;

    // Update the form value
    isHomeTeam ?
      this.games.at(index).patchValue({ homeTeamId: $chosenTeam.value }) :
      this.games.at(index).patchValue({ awayTeamId: $chosenTeam.value });

    // Update filtered options for all dropdowns
    this.updateAllFilteredOptions();
  }

  onPlayoffStageChange($chosenOption: ListOption) {
    if (!$chosenOption) return;

    this.addFixtureFormGroup?.get('playoffStage')?.setValue($chosenOption.value);
    // Trigger change detection to update the display title
  }

  updateAllFilteredOptions() {
    if (!this.removeSelectedTeamsFromOtherDropdowns && !this.preventSameTeamMatchup) {
      return; // Skip filtering if both flags are disabled
    }

    for (let i = 0; i < this.games.length; i++) {
      this.updateFilteredOptionsForGame(i);
    }
  }

  updateFilteredOptionsForGame(gameIndex: number) {
    const currentGame = this.games.at(gameIndex);
    const currentHomeTeamId = currentGame.get('homeTeamId')?.value;
    const currentAwayTeamId = currentGame.get('awayTeamId')?.value;
    const isPlayoff = this.isPlayoffSelected();

    // Start with all teams
    let homeOptions = [...(this.sourceTeamsOptions || [])];
    let awayOptions = [...(this.sourceTeamsOptions || [])];

    // For playoff games (best of 3/5 series), allow same teams in multiple games
    // Remove teams that are already selected in other games (only for non-playoff games)
    if (this.removeSelectedTeamsFromOtherDropdowns && !isPlayoff) {
      // Regular season: Remove teams already selected in other games to prevent duplicates
      const allSelectedTeamIds = this.getAllSelectedTeamIds(gameIndex);
      homeOptions = homeOptions.filter(team => !allSelectedTeamIds.includes(team.value));
      awayOptions = awayOptions.filter(team => !allSelectedTeamIds.includes(team.value));
    }
    // Note: For playoff games (isPlayoff = true), teams are NOT removed from dropdowns,
    // allowing the same teams to be selected multiple times for series matches

    // Prevent same team matchup within the same game
    if (this.preventSameTeamMatchup) {
      if (currentHomeTeamId) {
        awayOptions = awayOptions.filter(team => team.value !== currentHomeTeamId);
      }
      if (currentAwayTeamId) {
        homeOptions = homeOptions.filter(team => team.value !== currentAwayTeamId);
      }
    }

    // Add back the currently selected teams for this game (so they remain visible in their own dropdowns)
    if (currentHomeTeamId) {
      const homeTeam = this.sourceTeamsOptions?.find(team => team.value === currentHomeTeamId);
      if (homeTeam && !homeOptions.find(team => team.value === currentHomeTeamId)) {
        homeOptions.push(homeTeam);
      }
    }
    if (currentAwayTeamId) {
      const awayTeam = this.sourceTeamsOptions?.find(team => team.value === currentAwayTeamId);
      if (awayTeam && !awayOptions.find(team => team.value === currentAwayTeamId)) {
        awayOptions.push(awayTeam);
      }
    }

    // Update the filtered options
    this.homeTeamOptions[gameIndex] = homeOptions;
    this.awayTeamOptions[gameIndex] = awayOptions;
  }

  getAllSelectedTeamIds(excludeGameIndex: number): string[] {
    const selectedTeamIds: string[] = [];

    for (let i = 0; i < this.games.length; i++) {
      if (i === excludeGameIndex) continue; // Skip the current game

      const game = this.games.at(i);
      const homeTeamId = game.get('homeTeamId')?.value;
      const awayTeamId = game.get('awayTeamId')?.value;

      if (homeTeamId) selectedTeamIds.push(homeTeamId);
      if (awayTeamId) selectedTeamIds.push(awayTeamId);
    }

    return selectedTeamIds;
  }

  getHomeTeamOptions(gameIndex: number): ListOption[] {
    return this.homeTeamOptions[gameIndex] || this.sourceTeamsOptions || [];
  }

  getAwayTeamOptions(gameIndex: number): ListOption[] {
    return this.awayTeamOptions[gameIndex] || this.sourceTeamsOptions || [];
  }

  getDisplayTitle(): string {
    if (this.isPlayoffSelected()) {
      const selectedStage = this.addFixtureFormGroup?.get('playoffStage')?.value;
      if (selectedStage) {
        return selectedStage;
      }
      return 'Playoff Match';
    }
    const fixtureNumber = this.addFixtureFormGroup?.get('round')?.value;
    return fixtureNumber ? `Fixture ${fixtureNumber}` : 'Fixture';
  }

  trackByIndex(index: number): number {
    return index;
  }
}