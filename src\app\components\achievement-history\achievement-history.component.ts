import { Component, Input, OnInit } from '@angular/core';
import { SeasonAchievementService, PlayerAchievementHistory, TeamAchievementHistory, ACHIEVEMENT_TYPE } from '../../services/season-achievement.service';
import { NotificationService } from '../../services/notification.service';

export interface AchievementDisplay {
  seasonNumber: number;
  leagueName: string;
  achievementType: string;
  rank?: number;
  description?: string;
  year: number;
  icon: string;
  color: string;
}

@Component({
  selector: 'app-achievement-history',
  templateUrl: './achievement-history.component.html',
  styleUrls: ['./achievement-history.component.scss']
})
export class AchievementHistoryComponent implements OnInit {
  @Input() entityId: string = ''; // Player ID or Team ID
  @Input() entityType: 'player' | 'team' = 'player';
  @Input() entityName: string = '';

  achievements: AchievementDisplay[] = [];
  isLoading: boolean = false;
  groupedAchievements: { [year: number]: AchievementDisplay[] } = {};

  constructor(
    private seasonAchievementService: SeasonAchievementService,
    private notificationService: NotificationService
  ) {}

  async ngOnInit(): Promise<void> {
    if (this.entityId) {
      await this.loadAchievements();
    }
  }

  async loadAchievements(): Promise<void> {
    if (!this.entityId) return;

    this.isLoading = true;
    try {
      let history: PlayerAchievementHistory | TeamAchievementHistory;
      
      if (this.entityType === 'player') {
        history = await this.seasonAchievementService.getPlayerAchievementHistory(this.entityId);
      } else {
        history = await this.seasonAchievementService.getTeamAchievementHistory(this.entityId);
      }

      this.achievements = history.achievements.map(achievement => ({
        ...achievement,
        icon: this.getAchievementIcon(achievement.achievementType),
        color: this.getAchievementColor(achievement.achievementType)
      }));

      this.groupAchievementsByYear();
    } catch (error: any) {
      console.error('Error loading achievement history:', error);

      // More detailed error handling
      if (error?.status === 404) {
        console.log('No achievement history found for this entity');
        this.achievements = [];
        this.groupedAchievements = {};
      } else if (error?.status === 0) {
        console.error('Network error - server may be down');
        this.notificationService.error('Unable to connect to server. Please check your connection.');
      } else {
        const errorMessage = error?.error?.message || error?.message || 'Unknown error occurred';
        console.error('Achievement API error:', errorMessage);
        this.notificationService.error(`Failed to load achievements: ${errorMessage}`);
      }
    } finally {
      this.isLoading = false;
    }
  }

  private groupAchievementsByYear(): void {
    this.groupedAchievements = {};
    
    this.achievements.forEach(achievement => {
      if (!this.groupedAchievements[achievement.year]) {
        this.groupedAchievements[achievement.year] = [];
      }
      this.groupedAchievements[achievement.year].push(achievement);
    });

    // Sort achievements within each year by season number (descending)
    Object.keys(this.groupedAchievements).forEach(year => {
      this.groupedAchievements[parseInt(year)].sort((a, b) => b.seasonNumber - a.seasonNumber);
    });
  }

  getYears(): number[] {
    return Object.keys(this.groupedAchievements)
      .map(year => parseInt(year))
      .sort((a, b) => b - a); // Most recent first
  }

  getAchievementIcon(achievementType: string): string {
    switch (achievementType) {
      case ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER:
        return 'fas fa-trophy';
      case ACHIEVEMENT_TYPE.FINALIST:
        return 'fas fa-medal';
      case ACHIEVEMENT_TYPE.THIRD_PLACE:
        return 'fas fa-award';
      case ACHIEVEMENT_TYPE.TOP_SCORER:
        return 'fas fa-futbol';
      case ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER:
        return 'fas fa-hands-helping';
      case ACHIEVEMENT_TYPE.BEST_GOALKEEPER:
        return 'fas fa-hand-paper';
      case ACHIEVEMENT_TYPE.BEST_CENTER_BACK:
      case ACHIEVEMENT_TYPE.BEST_DEFENSIVE_MIDFIELDER:
        return 'fas fa-shield-alt';
      case ACHIEVEMENT_TYPE.BEST_MIDFIELDER:
      case ACHIEVEMENT_TYPE.BEST_ATTACKING_MIDFIELDER:
        return 'fas fa-running';
      case ACHIEVEMENT_TYPE.BEST_WINGER:
        return 'fas fa-arrows-alt-h';
      case ACHIEVEMENT_TYPE.BEST_STRIKER:
        return 'fas fa-crosshairs';
      case ACHIEVEMENT_TYPE.MOST_CLEAN_SHEETS:
        return 'fas fa-shield-alt';
      case ACHIEVEMENT_TYPE.PLAYER_OF_THE_SEASON:
        return 'fas fa-star';
      case ACHIEVEMENT_TYPE.TEAM_OF_THE_SEASON:
        return 'fas fa-users';
      default:
        return 'fas fa-star';
    }
  }

  getAchievementColor(achievementType: string): string {
    switch (achievementType) {
      case ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER:
        return '#ffd700'; // Gold
      case ACHIEVEMENT_TYPE.FINALIST:
        return '#c0c0c0'; // Silver
      case ACHIEVEMENT_TYPE.THIRD_PLACE:
        return '#cd7f32'; // Bronze
      case ACHIEVEMENT_TYPE.TOP_SCORER:
        return '#ff6b35'; // Orange-red
      case ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER:
        return '#4ecdc4'; // Teal
      case ACHIEVEMENT_TYPE.BEST_GOALKEEPER:
        return '#9b59b6'; // Purple
      case ACHIEVEMENT_TYPE.BEST_CENTER_BACK:
      case ACHIEVEMENT_TYPE.BEST_DEFENSIVE_MIDFIELDER:
        return '#34495e'; // Dark blue-gray
      case ACHIEVEMENT_TYPE.BEST_MIDFIELDER:
      case ACHIEVEMENT_TYPE.BEST_ATTACKING_MIDFIELDER:
        return '#3498db'; // Blue
      case ACHIEVEMENT_TYPE.BEST_WINGER:
        return '#e74c3c'; // Red
      case ACHIEVEMENT_TYPE.BEST_STRIKER:
        return '#e67e22'; // Orange
      case ACHIEVEMENT_TYPE.MOST_CLEAN_SHEETS:
        return '#27ae60'; // Green
      case ACHIEVEMENT_TYPE.PLAYER_OF_THE_SEASON:
      case ACHIEVEMENT_TYPE.TEAM_OF_THE_SEASON:
        return '#f39c12'; // Yellow-orange
      default:
        return '#95a5a6'; // Gray
    }
  }

  getRankSuffix(rank: number): string {
    if (!rank) return '';
    const suffixes = ["th", "st", "nd", "rd"];
    const v = rank % 100;
    return rank + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);
  }

  getAchievementTitle(achievement: AchievementDisplay): string {
    let title = achievement.achievementType;

    if (achievement.rank && achievement.rank <= 3) {
      title = `${this.getRankSuffix(achievement.rank)} ${title}`;
    }

    return title;
  }

  getAchievementStats(achievement: AchievementDisplay): string {
    if (achievement.achievementType === ACHIEVEMENT_TYPE.TOP_SCORER && achievement.description) {
      // Extract goals and games from description if available
      const match = achievement.description.match(/(\d+) goals in (\d+) games \(([0-9.]+) per game\)/);
      if (match) {
        return `${match[1]} goals (${match[3]} per game)`;
      }
    }

    if (achievement.achievementType === ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER && achievement.description) {
      // Extract assists and games from description if available
      const match = achievement.description.match(/(\d+) assists in (\d+) games \(([0-9.]+) per game\)/);
      if (match) {
        return `${match[1]} assists (${match[3]} per game)`;
      }
    }

    return achievement.description || '';
  }

  getAchievementSubtitle(achievement: AchievementDisplay): string {
    return `Season ${achievement.seasonNumber} • ${achievement.leagueName}`;
  }

  hasAchievements(): boolean {
    return this.achievements.length > 0;
  }

  getAchievementCount(): number {
    return this.achievements.length;
  }

  getUniqueAchievementTypes(): string[] {
    const types = new Set(this.achievements.map(a => a.achievementType));
    return Array.from(types);
  }

  getAchievementsByType(type: string): AchievementDisplay[] {
    return this.achievements.filter(a => a.achievementType === type);
  }

  getMostRecentAchievement(): AchievementDisplay | null {
    if (this.achievements.length === 0) return null;
    
    return this.achievements.reduce((latest, current) => {
      if (current.year > latest.year) return current;
      if (current.year === latest.year && current.seasonNumber > latest.seasonNumber) return current;
      return latest;
    });
  }

  getChampionshipCount(): number {
    return this.achievements.filter(a => a.achievementType === ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER).length;
  }

  getTopScorerCount(): number {
    return this.achievements.filter(a => a.achievementType === ACHIEVEMENT_TYPE.TOP_SCORER).length;
  }

  getTopAssistCount(): number {
    return this.achievements.filter(a => a.achievementType === ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER).length;
  }

  // Medal-specific methods
  getPersonalAchievements(): AchievementDisplay[] {
    const personalTypes = [
      ACHIEVEMENT_TYPE.TOP_SCORER,
      ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER,
      ACHIEVEMENT_TYPE.BEST_GOALKEEPER,
      ACHIEVEMENT_TYPE.BEST_CENTER_BACK,
      ACHIEVEMENT_TYPE.BEST_DEFENSIVE_MIDFIELDER,
      ACHIEVEMENT_TYPE.BEST_MIDFIELDER,
      ACHIEVEMENT_TYPE.BEST_ATTACKING_MIDFIELDER,
      ACHIEVEMENT_TYPE.BEST_WINGER,
      ACHIEVEMENT_TYPE.BEST_STRIKER,
      ACHIEVEMENT_TYPE.MOST_CLEAN_SHEETS,
      ACHIEVEMENT_TYPE.PLAYER_OF_THE_SEASON
    ];

    return this.achievements
      .filter(a => personalTypes.includes(a.achievementType as ACHIEVEMENT_TYPE))
      .sort((a, b) => {
        // Sort by year desc, then season desc
        if (a.year !== b.year) return b.year - a.year;
        return b.seasonNumber - a.seasonNumber;
      });
  }

  getTeamAchievements(): AchievementDisplay[] {
    const teamTypes = [
      ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER,
      ACHIEVEMENT_TYPE.FINALIST,
      ACHIEVEMENT_TYPE.THIRD_PLACE,
      ACHIEVEMENT_TYPE.TEAM_OF_THE_SEASON
    ];

    return this.achievements
      .filter(a => teamTypes.includes(a.achievementType as ACHIEVEMENT_TYPE))
      .sort((a, b) => {
        // Sort by year desc, then season desc
        if (a.year !== b.year) return b.year - a.year;
        return b.seasonNumber - a.seasonNumber;
      });
  }

  isMedalType(achievement: AchievementDisplay, medalType: 'gold' | 'silver' | 'bronze' | 'special'): boolean {
    switch (medalType) {
      case 'gold':
        return achievement.achievementType === ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER ||
               achievement.achievementType === ACHIEVEMENT_TYPE.TOP_SCORER ||
               achievement.achievementType === ACHIEVEMENT_TYPE.PLAYER_OF_THE_SEASON;
      case 'silver':
        return achievement.achievementType === ACHIEVEMENT_TYPE.FINALIST ||
               achievement.achievementType === ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER;
      case 'bronze':
        return achievement.achievementType === ACHIEVEMENT_TYPE.THIRD_PLACE;
      case 'special':
        return achievement.achievementType === ACHIEVEMENT_TYPE.BEST_GOALKEEPER ||
               achievement.achievementType === ACHIEVEMENT_TYPE.BEST_CENTER_BACK ||
               achievement.achievementType === ACHIEVEMENT_TYPE.BEST_DEFENSIVE_MIDFIELDER ||
               achievement.achievementType === ACHIEVEMENT_TYPE.BEST_MIDFIELDER ||
               achievement.achievementType === ACHIEVEMENT_TYPE.BEST_ATTACKING_MIDFIELDER ||
               achievement.achievementType === ACHIEVEMENT_TYPE.BEST_WINGER ||
               achievement.achievementType === ACHIEVEMENT_TYPE.BEST_STRIKER ||
               achievement.achievementType === ACHIEVEMENT_TYPE.MOST_CLEAN_SHEETS ||
               achievement.achievementType === ACHIEVEMENT_TYPE.TEAM_OF_THE_SEASON;
      default:
        return false;
    }
  }

  getMedalGradient(achievement: AchievementDisplay): string {
    if (this.isMedalType(achievement, 'gold')) {
      return 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)';
    } else if (this.isMedalType(achievement, 'silver')) {
      return 'linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 50%, #c0c0c0 100%)';
    } else if (this.isMedalType(achievement, 'bronze')) {
      return 'linear-gradient(135deg, #cd7f32 0%, #e6a85c 50%, #cd7f32 100%)';
    } else {
      return `linear-gradient(135deg, ${achievement.color} 0%, ${this.lightenColor(achievement.color, 20)} 50%, ${achievement.color} 100%)`;
    }
  }

  getMedalIcon(achievement: AchievementDisplay): string {
    // Use medal-specific icons
    if (this.isMedalType(achievement, 'gold') || this.isMedalType(achievement, 'silver') || this.isMedalType(achievement, 'bronze')) {
      return 'fas fa-medal';
    }
    return achievement.icon;
  }

  private lightenColor(color: string, percent: number): string {
    // Simple color lightening function
    const num = parseInt(color.replace("#", ""), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;
    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
  }
}
