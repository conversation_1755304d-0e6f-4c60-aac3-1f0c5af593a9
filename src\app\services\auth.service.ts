import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ApiService } from './api.service';
import { Router } from '@angular/router';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  profilePicture?: string;
  isEmailVerified: boolean;
  associatedPlayers: string[];
  role: 'user' | 'admin';
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  message: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly USER_KEY = 'auth_user';
  
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(
    private apiService: ApiService,
    private router: Router
  ) {
    this.initializeAuth();
  }

  private initializeAuth(): void {
    const token = this.getToken();
    const user = this.getStoredUser();
    
    if (token && user) {
      this.currentUserSubject.next(user);
      this.isAuthenticatedSubject.next(true);
    }
  }

  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await this.apiService.post<AuthResponse>('user/login', credentials);
      this.handleAuthSuccess(response.data);
      return response.data;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      const response = await this.apiService.post<AuthResponse>('user/register', userData);
      this.handleAuthSuccess(response.data);
      return response.data;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async googleAuth(idToken: string): Promise<AuthResponse> {
    try {
      const response = await this.apiService.post<AuthResponse>('user/google-auth', { idToken });
      this.handleAuthSuccess(response.data);
      return response.data;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async getProfile(): Promise<User> {
    try {
      const response = await this.apiService.get<{ user: User }>('user/profile');
      const user = response.data.user;
      this.currentUserSubject.next(user);
      this.storeUser(user);
      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async updateProfile(updateData: Partial<User>): Promise<User> {
    try {
      const response = await this.apiService.put<{ user: User }>('user/profile', updateData);
      const user = response.data.user;
      this.currentUserSubject.next(user);
      this.storeUser(user);
      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Request player association (requires admin approval)
   */
  async requestPlayerAssociation(playerId?: string, playerEmail?: string, userMessage?: string): Promise<any> {
    try {
      const response = await this.apiService.post<{ request: any }>('player-association-requests', {
        playerId,
        playerEmail,
        userMessage
      });

      return response.data.request;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Associate a player with the current user (deprecated - now creates a request)
   */
  async associatePlayer(playerId?: string, playerEmail?: string): Promise<User> {
    // This method now redirects to the request system
    await this.requestPlayerAssociation(playerId, playerEmail);
    // Return current user since we can't return the updated user until admin approval
    return this.getCurrentUser()!;
  }

  /**
   * Get user's player association requests
   */
  async getUserAssociationRequests(): Promise<any[]> {
    try {
      const response = await this.apiService.get<{ requests: any[] }>('player-association-requests/my-requests');
      return response.data.requests;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Cancel a player association request
   */
  async cancelAssociationRequest(requestId: string): Promise<void> {
    try {
      await this.apiService.delete(`player-association-requests/${requestId}`);
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async removePlayer(playerId: string): Promise<User> {
    try {
      const response = await this.apiService.post<{ user: User }>('user/remove-player', { playerId });
      const user = response.data.user;
      this.currentUserSubject.next(user);
      this.storeUser(user);
      return user;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Check if a player is associated with any user account
   */
  async checkPlayerAssociation(playerId: string): Promise<{ isAssociated: boolean }> {
    try {
      const response = await this.apiService.get<{ isAssociated: boolean }>(`user/player-association/check/${playerId}`);
      return response.data;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async searchAvailablePlayers(search?: string, email?: string): Promise<any[]> {
    try {
      const params: any = {};
      if (search) params.search = search;
      if (email) params.email = email;

      const response = await this.apiService.get<{ players: any[] }>('user/search-players', { params });
      return response.data.players;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async searchAvailablePlayersPublic(search: string): Promise<any[]> {
    try {
      const params: any = { search };

      const response = await this.apiService.get<{ players: any[] }>('user/public/search-players', { params });
      return response.data.players;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async getAssociatedPlayers(): Promise<any[]> {
    try {
      const response = await this.apiService.get<{ players: any[] }>('user/associated-players');
      return response.data.players;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  async checkPlayerOwnership(playerId: string): Promise<boolean> {
    try {
      const response = await this.apiService.get<{ isOwner: boolean }>(`user/check-ownership/${playerId}`);
      return response.data.isOwner;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  logout(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.router.navigate(['/login']);
  }

  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  isAdmin(): boolean {
    const user = this.getCurrentUser();
    const isAdmin = user?.role === 'admin' || false;
    return isAdmin;
  }

  private handleAuthSuccess(authResponse: AuthResponse): void {
    this.storeToken(authResponse.token);
    this.storeUser(authResponse.user);
    this.currentUserSubject.next(authResponse.user);
    this.isAuthenticatedSubject.next(true);
  }

  private handleAuthError(error: any): Error {
    if (error.response?.status === 401) {
      this.logout();
    }
    return new Error(error.response?.data?.message || 'Authentication failed');
  }

  private storeToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  private storeUser(user: User): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }

  private getStoredUser(): User | null {
    const userStr = localStorage.getItem(this.USER_KEY);
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch {
        return null;
      }
    }
    return null;
  }

}
