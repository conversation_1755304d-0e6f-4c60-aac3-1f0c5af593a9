<div class="transfer-requests-container">
  <!-- Header -->
  <div class="header">
    <div class="header-content">
      <h2>
        <i class="fas fa-exchange-alt"></i>
        Transfer Requests
      </h2>
      <div class="header-actions">
        <button 
          class="btn btn-primary"
          (click)="showCreateForm = !showCreateForm"
          *ngIf="isAdmin() || (currentUser?.associatedPlayers?.length > 0)">
          <i class="fas fa-plus"></i>
          New Request
        </button>
        <button 
          class="btn btn-secondary"
          (click)="refreshTransferRequests()"
          [disabled]="loading">
          <i class="fas fa-sync-alt" [class.spinning]="loading"></i>
          Refresh
        </button>
      </div>
    </div>
  </div>

  <!-- Team Filter -->
  <div class="filters">
    <div class="filter-group">
      <label for="teamSelect">Filter by Team:</label>
      <select
        id="teamSelect"
        class="form-select"
        [value]="selectedTeamId || ''"
        (change)="onTeamChange($any($event.target).value)">
        <option value="">All Teams</option>
        <option *ngFor="let team of teams" [value]="team.id">
          {{ team.name }}
        </option>
      </select>
    </div>
  </div>

  <!-- Create Transfer Request Form -->
  <div class="create-form" *ngIf="showCreateForm">
    <div class="form-card">
      <h3>Create Transfer Request</h3>
      <form [formGroup]="createForm" (ngSubmit)="createTransferRequest()">
        <div class="form-row">
          <div class="form-group">
            <label for="fromTeam">From Team:</label>
            <select 
              id="fromTeam"
              formControlName="fromTeamId"
              class="form-select"
              (change)="onFromTeamChange()">
              <option value="">Select team</option>
              <option *ngFor="let team of teams" [value]="team.id">
                {{ team.name }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="toTeam">To Team:</label>
            <select 
              id="toTeam"
              formControlName="toTeamId"
              class="form-select">
              <option value="">Select team</option>
              <option *ngFor="let team of getAvailableTeams(createForm.get('fromTeamId')?.value)" [value]="team.id">
                {{ team.name }}
              </option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label for="player">Player:</label>
          <select 
            id="player"
            formControlName="playerId"
            class="form-select"
            [disabled]="!createForm.get('fromTeamId')?.value">
            <option value="">Select player</option>
            <option *ngFor="let player of getAvailablePlayers(createForm.get('fromTeamId')?.value)" [value]="player.id">
              {{ player.name }} ({{ player.position }})
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="message">Message (Optional):</label>
          <textarea 
            id="message"
            formControlName="message"
            class="form-textarea"
            placeholder="Add a message to explain the transfer request..."
            rows="3"
            maxlength="500"></textarea>
          <small class="character-count">
            {{ createForm.get('message')?.value?.length || 0 }}/500
          </small>
        </div>

        <div class="form-actions">
          <button 
            type="submit"
            class="btn btn-primary"
            [disabled]="createForm.invalid || loading">
            <i class="fas fa-paper-plane"></i>
            Send Request
          </button>
          <button 
            type="button"
            class="btn btn-secondary"
            (click)="showCreateForm = false; createForm.reset()">
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Transfer Requests List -->
  <div class="requests-list">
    <div class="loading-indicator" *ngIf="loading && transferRequests.length === 0">
      <i class="fas fa-spinner fa-spin"></i>
      Loading transfer requests...
    </div>

    <div class="empty-state" *ngIf="!loading && transferRequests.length === 0">
      <i class="fas fa-exchange-alt"></i>
      <h3>No Transfer Requests</h3>
      <p>{{ selectedTeamId ? 'No transfer requests found for this team.' : 'Select a team to view transfer requests.' }}</p>
    </div>

    <div class="request-card" *ngFor="let request of transferRequests; trackBy: trackByRequestId">
      <!-- Request Header -->
      <div class="request-header">
        <div class="request-status">
          <span class="status-badge" [class]="'status-' + request.status">
            <i [class]="getStatusIcon(request.status)"></i>
            {{ request.status | titlecase }}
          </span>
          <span class="request-date">{{ formatDate(request.requestedAt) }}</span>
        </div>
        <div class="request-actions" *ngIf="canProcess(request) || canCancel(request)">
          <button 
            *ngIf="canProcess(request) && isCurrentUserCaptain(request.fromTeamId)"
            class="btn btn-sm btn-primary"
            (click)="openProcessModal(request)">
            <i class="fas fa-gavel"></i>
            Process
          </button>
          <button 
            *ngIf="canCancel(request) && (request.requestedBy === currentUser?.id || isAdmin())"
            class="btn btn-sm btn-danger"
            (click)="cancelTransferRequest(request)">
            <i class="fas fa-times"></i>
            Cancel
          </button>
        </div>
      </div>

      <!-- Request Content -->
      <div class="request-content">
        <!-- Player Info -->
        <div class="player-info">
          <img 
            [src]="getPlayerImage(request)" 
            [alt]="request.player?.name"
            class="player-avatar"
            onerror="this.src='assets/icons/default-profile.png'">
          <div class="player-details">
            <h4>{{ request.player?.name }}</h4>
            <span class="player-position">{{ request.player?.position }}</span>
          </div>
        </div>

        <!-- Transfer Direction -->
        <div class="transfer-direction">
          <div class="team-info">
            <img 
              [src]="getTeamImage(request.fromTeam)" 
              [alt]="request.fromTeam?.name"
              class="team-logo"
              onerror="this.src='assets/icons/default-team.png'">
            <span class="team-name">{{ request.fromTeam?.name }}</span>
          </div>
          
          <div class="transfer-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>
          
          <div class="team-info">
            <img 
              [src]="getTeamImage(request.toTeam)" 
              [alt]="request.toTeam?.name"
              class="team-logo"
              onerror="this.src='assets/icons/default-team.png'">
            <span class="team-name">{{ request.toTeam?.name }}</span>
          </div>
        </div>

        <!-- Request Message -->
        <div class="request-message" *ngIf="request.message">
          <p><strong>Message:</strong> {{ request.message }}</p>
        </div>

        <!-- Request Details -->
        <div class="request-details">
          <div class="detail-item">
            <span class="label">Requested by:</span>
            <span class="value">{{ request.requester?.firstName }} {{ request.requester?.lastName }}</span>
          </div>
          <div class="detail-item" *ngIf="request.status === 'pending'">
            <span class="label">Expires:</span>
            <span class="value" [class.text-danger]="isExpired(request)">
              {{ getTimeRemaining(request) }}
            </span>
          </div>
          <div class="detail-item" *ngIf="request.processedAt">
            <span class="label">Processed:</span>
            <span class="value">{{ formatDate(request.processedAt) }}</span>
          </div>
          <div class="detail-item" *ngIf="request.processor">
            <span class="label">Processed by:</span>
            <span class="value">{{ request.processor.firstName }} {{ request.processor.lastName }}</span>
          </div>
          <div class="detail-item" *ngIf="request.reason">
            <span class="label">Reason:</span>
            <span class="value">{{ request.reason }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Process Transfer Request Modal -->
  <div class="modal-overlay" *ngIf="showProcessModal" (click)="closeProcessModal()">
    <div class="modal-content" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h3>Process Transfer Request</h3>
        <button class="close-btn" (click)="closeProcessModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body" *ngIf="selectedRequest">
        <div class="request-summary">
          <p><strong>Player:</strong> {{ selectedRequest.player?.name }}</p>
          <p><strong>From:</strong> {{ selectedRequest.fromTeam?.name }}</p>
          <p><strong>To:</strong> {{ selectedRequest.toTeam?.name }}</p>
          <p *ngIf="selectedRequest.message"><strong>Message:</strong> {{ selectedRequest.message }}</p>
        </div>

        <form [formGroup]="processForm" (ngSubmit)="processTransferRequest()">
          <div class="form-group">
            <label>Decision:</label>
            <div class="radio-group">
              <label class="radio-option">
                <input type="radio" formControlName="action" value="approve">
                <span class="radio-label">
                  <i class="fas fa-check-circle text-success"></i>
                  Approve Transfer
                </span>
              </label>
              <label class="radio-option">
                <input type="radio" formControlName="action" value="reject">
                <span class="radio-label">
                  <i class="fas fa-times-circle text-danger"></i>
                  Reject Transfer
                </span>
              </label>
            </div>
          </div>

          <div class="form-group">
            <label for="reason">Reason (Optional):</label>
            <textarea 
              id="reason"
              formControlName="reason"
              class="form-textarea"
              placeholder="Add a reason for your decision..."
              rows="3"
              maxlength="500"></textarea>
          </div>

          <div class="modal-actions">
            <button 
              type="submit"
              class="btn btn-primary"
              [disabled]="processForm.invalid || loading">
              <i class="fas fa-gavel"></i>
              Process Request
            </button>
            <button 
              type="button"
              class="btn btn-secondary"
              (click)="closeProcessModal()">
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
