/* === MODERN ADD FIXTURE DESIGN === */

.add-fixture-container {
    min-height: 100vh;
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    font-family: var(--font-sans);
    width: 100%;

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md);
    }
}

.add-fixture-card {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-2xl);
    position: relative;
    overflow: hidden;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary));
        border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-xl);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-lg);
    }
}

.add-fixture-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);

    .add-fixture-title {
        font-size: var(--text-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;
        background: linear-gradient(135deg, var(--primary), var(--accent-primary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);

        i {
            color: var(--primary);
            -webkit-text-fill-color: var(--primary);
        }

        @media (max-width: 768px) {
            font-size: var(--text-2xl);
        }
    }

    .add-fixture-subtitle {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
        font-weight: var(--font-weight-medium);
    }
}

.add-fixture-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.form-section {
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);

    .section-title {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-lg) 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        i {
            color: var(--primary);
        }
    }

    .playoff-mode-indicator {
        background: linear-gradient(135deg, var(--warning-100), var(--warning-50));
        border: 1px solid var(--warning-200);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--warning-700);
        font-size: var(--text-sm);
        font-weight: var(--font-weight-medium);

        i {
            color: var(--warning-600);
            font-size: var(--text-base);
        }

        @media (max-width: 768px) {
            font-size: var(--text-xs);
            padding: var(--spacing-sm);
        }
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
    }
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    .form-label {
        font-size: var(--text-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);

        .required-indicator {
            color: var(--error);
            font-size: var(--text-xs);
        }
    }

    .form-input {
        background: var(--surface-primary);
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-base);
        color: var(--text-primary);
        transition: all 0.3s ease;
        font-family: var(--font-sans);

        &::placeholder {
            color: var(--text-tertiary);
        }

        &:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
            background: var(--surface-secondary);
        }

        &:hover:not(:focus) {
            border-color: var(--border-secondary);
        }

        &.error {
            border-color: var(--error);
            box-shadow: 0 0 0 3px rgba(var(--error-rgb), 0.1);
        }
    }

    .playoff-display {
        background: var(--surface-secondary);
        border: 2px solid var(--primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-base);
        color: var(--text-primary);
        font-family: var(--font-sans);
        font-weight: var(--font-weight-semibold);
        display: flex;
        align-items: center;
        min-height: 48px;
        background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
        box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.1);
    }
}

/* === PLAYOFF SECTION === */
.playoff-section {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-secondary);

    .playoff-checkbox {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-md);

        .checkbox-input {
            width: 18px;
            height: 18px;
            accent-color: var(--primary);
            cursor: pointer;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            cursor: pointer;
            user-select: none;

            i {
                color: var(--warning);
                font-size: var(--text-sm);
            }
        }
    }
}

.teams-section {
    .teams-row {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: var(--spacing-lg);
        align-items: center;
        margin-bottom: var(--spacing-lg);

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
            text-align: center;
        }

        .vs-indicator {
            background: linear-gradient(135deg, var(--primary), var(--accent-primary));
            color: var(--text-inverse);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-full);
            font-weight: var(--font-weight-bold);
            font-size: var(--text-sm);
            text-transform: uppercase;
            letter-spacing: 0.1em;
            box-shadow: var(--shadow-md);

            @media (max-width: 768px) {
                margin: var(--spacing-sm) 0;
                display: inline-block;
            }
        }
    }

    .add-match-button {
        background: var(--surface-tertiary);
        border: 2px dashed var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: var(--text-secondary);

        &:hover {
            border-color: var(--primary);
            background: var(--surface-secondary);
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        i {
            font-size: var(--text-xl);
            margin-bottom: var(--spacing-sm);
            display: block;
            color: var(--primary);
        }

        .button-text {
            font-size: var(--text-base);
            font-weight: var(--font-weight-medium);
        }
    }
}

.game-item {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: var(--shadow-md);
        border-color: var(--border-secondary);
    }

    &:last-child {
        margin-bottom: 0;
    }

    .game-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--border-primary);

        .game-title {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            &::before {
                content: '⚽';
                font-size: var(--text-base);
            }
        }

        .remove-game-btn {
            background: var(--error);
            border: none;
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            color: var(--text-inverse);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;

            &:hover:not(:disabled) {
                background: var(--error-600);
                transform: translateY(-1px);
                box-shadow: var(--shadow-sm);
            }

            &:disabled {
                background: var(--surface-tertiary);
                color: var(--text-tertiary);
                cursor: not-allowed;
                opacity: 0.5;
            }

            i {
                font-size: var(--text-sm);
            }
        }
    }

    .team-selection {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);

        .form-label {
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
            margin: 0;
        }
    }
}

.submit-button {
    background: linear-gradient(135deg, var(--primary), var(--accent-primary));
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--text-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-inverse);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

        &::before {
            left: 100%;
        }
    }

    &:active:not(:disabled) {
        transform: translateY(0);
    }

    &:disabled {
        background: var(--surface-tertiary);
        color: var(--text-tertiary);
        cursor: not-allowed;
        opacity: 0.6;
    }
}