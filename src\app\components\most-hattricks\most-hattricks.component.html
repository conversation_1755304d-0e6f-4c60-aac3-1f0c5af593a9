<div class="most-hattricks-container theme-transition">
  <!-- Header -->
  <div class="header-section" *ngIf="!hideTitle">
    <div class="header-content">
      <h1 class="header-title">
        <i class="fas fa-fire"></i>
        Most Hattricks
      </h1>
      <p class="header-subtitle">Players with the most hat-tricks across all seasons</p>
    </div>
  </div>

  <!-- League Selection -->
  <div class="controls-section" *ngIf="!leagueId">
    <div class="control-group">
      <label for="league-select" class="control-label">
        <i class="fas fa-trophy"></i>
        League
      </label>
      <select 
        id="league-select"
        class="control-select"
        [(ngModel)]="selectedLeagueId"
        (change)="onLeagueChange()">
        <option value="">Select a league</option>
        <option *ngFor="let league of leagues" [value]="league.id">
          {{ league.name }}
        </option>
      </select>
    </div>
  </div>

  <!-- Hattricks Rankings -->
  <div class="rankings-section" *ngIf="selectedLeagueId && !isLoading">
    <div class="rankings-header">
      <h3 class="rankings-title">
        <i class="fas fa-list-ol"></i>
        Hattrick Leaders
      </h3>
      <p class="rankings-subtitle">
        Players who have scored 3+ goals in a single game
      </p>
    </div>

    <div class="players-list" *ngIf="mostHattricksData.length > 0; else noDataTemplate">
      <div 
        class="player-row"
        *ngFor="let player of mostHattricksData; let i = index"
        (click)="onPlayerClick(player)"
        [class.first-place]="i === 0"
        [class.second-place]="i === 1"
        [class.third-place]="i === 2">
        
        <div class="rank-section">
          <span class="rank">{{ getPlayerRank(i) }}</span>
          <i class="fas fa-crown crown-icon" *ngIf="i === 0"></i>
        </div>

        <div class="player-info">
          <img 
            [src]="player.playerImgUrl || 'assets/Icons/User.jpg'"
            [alt]="player.playerName"
            class="player-avatar"
            (error)="onImageError($event)">
          
          <div class="player-details">
            <h4 class="player-name">{{ player.playerName }}</h4>
            <p class="player-position">{{ player.position }}</p>
          </div>
        </div>

        <div class="team-info" (click)="onTeamClick(player.teamId); $event.stopPropagation()">
          <span class="team-name">{{ player.teamName }}</span>
        </div>

        <div class="stats-section">
          <div class="primary-stat">
            <span class="stat-value">{{ player.hattricks }}</span>
            <span class="stat-label">Hattricks</span>
          </div>
          <div class="secondary-stats">
            <div class="stat-item">
              <span class="stat-value">{{ player.games }}</span>
              <span class="stat-label">Games</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ player.totalGoals }}</span>
              <span class="stat-label">Total Goals</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ getHattrickPercentage(player) }}</span>
              <span class="stat-label">Hattrick %</span>
            </div>
          </div>
        </div>

        <!-- Hattrick Indicator -->
        <div class="hattrick-indicator">
          <div class="fire-icons">
            <i class="fas fa-fire" *ngFor="let fire of getFireIconsArray(player.hattricks)"></i>
            <span class="more-indicator" *ngIf="player.hattricks > 5">+{{ player.hattricks - 5 }}</span>
          </div>
        </div>
      </div>
    </div>

    <ng-template #noDataTemplate>
      <div class="no-data">
        <i class="fas fa-futbol"></i>
        <h3>No Hattricks Yet</h3>
        <p>No players have scored a hat-trick in this league</p>
      </div>
    </ng-template>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading statistics...</p>
    </div>
  </div>
</div>
