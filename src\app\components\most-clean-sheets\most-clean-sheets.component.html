<div class="most-clean-sheets-container theme-transition">
  <!-- Header -->
  <div class="header-section" *ngIf="!hideTitle">
    <div class="header-content">
      <h1 class="header-title">
        <i class="fas fa-shield-alt"></i>
        Most Clean Sheets
      </h1>
      <p class="header-subtitle">Defensive masters who kept the most clean sheets</p>
    </div>
  </div>

  <!-- League Selection -->
  <div class="controls-section" *ngIf="!leagueId">
    <div class="control-group">
      <label for="league-select" class="control-label">
        <i class="fas fa-trophy"></i>
        League
      </label>
      <select 
        id="league-select"
        class="control-select"
        [(ngModel)]="selectedLeagueId"
        (change)="onLeagueChange()">
        <option value="">Select a league</option>
        <option *ngFor="let league of leagues" [value]="league.id">
          {{ league.name }}
        </option>
      </select>
    </div>
  </div>

  <!-- Clean Sheets Rankings -->
  <div class="rankings-section" *ngIf="selectedLeagueId && !isLoading">
    <div class="rankings-header">
      <h3 class="rankings-title">
        <i class="fas fa-list-ol"></i>
        Clean Sheet Leaders
      </h3>
      <p class="rankings-subtitle">
        Players who have kept their goal clean across all seasons
      </p>
    </div>

    <div class="players-list" *ngIf="mostCleanSheetsData.length > 0; else noDataTemplate">
      <div 
        class="player-row"
        *ngFor="let player of mostCleanSheetsData; let i = index"
        (click)="onPlayerClick(player)"
        [class.first-place]="i === 0"
        [class.second-place]="i === 1"
        [class.third-place]="i === 2">
        
        <div class="rank-section">
          <span class="rank">{{ getPlayerRank(i) }}</span>
          <i class="fas fa-crown crown-icon" *ngIf="i === 0"></i>
        </div>

        <div class="player-info">
          <img 
            [src]="player.playerImgUrl || 'assets/Icons/User.jpg'"
            [alt]="player.playerName"
            class="player-avatar"
            (error)="onImageError($event)">
          
          <div class="player-details">
            <h4 class="player-name">{{ player.playerName }}</h4>
            <p class="player-position">{{ player.position }}</p>
          </div>
        </div>

        <div class="team-info" (click)="onTeamClick(player.teamId); $event.stopPropagation()">
          <span class="team-name">{{ player.teamName }}</span>
        </div>

        <div class="stats-section">
          <div class="primary-stat">
            <span class="stat-value">{{ player.cleanSheets }}</span>
            <span class="stat-label">Clean Sheets</span>
          </div>
          <div class="secondary-stats">
            <div class="stat-item">
              <span class="stat-value">{{ player.games }}</span>
              <span class="stat-label">Games</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ getCleanSheetPercentage(player) }}</span>
              <span class="stat-label">CS %</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ player.avgRating }}</span>
              <span class="stat-label">Avg Rating</span>
            </div>
          </div>
        </div>

        <!-- Clean Sheet Indicator -->
        <div class="clean-sheet-indicator">
          <div class="shield-icons">
            <i class="fas fa-shield-alt" *ngFor="let shield of getShieldIconsArray(player.cleanSheets)"></i>
            <span class="more-indicator" *ngIf="player.cleanSheets > 5">+{{ player.cleanSheets - 5 }}</span>
          </div>
        </div>
      </div>
    </div>

    <ng-template #noDataTemplate>
      <div class="no-data">
        <i class="fas fa-shield-alt"></i>
        <h3>No Clean Sheets Yet</h3>
        <p>No players have kept a clean sheet in this league</p>
      </div>
    </ng-template>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading statistics...</p>
    </div>
  </div>
</div>
