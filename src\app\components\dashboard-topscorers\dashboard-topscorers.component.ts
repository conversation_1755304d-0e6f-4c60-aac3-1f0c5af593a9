import { Component } from '@angular/core';
import { TopScorer } from '@pro-clubs-manager/shared-dtos';
import { LeagueService } from '../../services/league.service';
import { LEAGUE_ID } from '../../constants/constants';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard-topscorers',
  templateUrl: './dashboard-topscorers.component.html',
  styleUrl: './dashboard-topscorers.component.scss'
})
export class DashboardTopscorersComponent {
  topScorers: TopScorer[] | null = null;
  isLoading: boolean = false;

  constructor(private leagueService: LeagueService, private router: Router) { }

  ngOnInit() {
    this.loadTopScorersData();
  }

  private async loadTopScorersData() {
    this.isLoading = true;

    const topScorersResponse = await this.leagueService.getTopScorers(LEAGUE_ID, 5);

    topScorersResponse.map(topScorer => {
      topScorer.tableIcon = { name: topScorer.playerName, imgUrl: topScorer.playerImgUrl!, isTeam: false };
      if (topScorer.goalsPerGame)
        topScorer.goalsPerGame = parseFloat(topScorer.goalsPerGame.toFixed(2));
    });
    this.topScorers = topScorersResponse;
    this.isLoading = false;

  };

    public onPlayerClick(player: TopScorer): void {
      if (!player || !player.playerId) {
        return;
      };
      
      this.router.navigate(['/player-details', { id: player.playerId }])
    }
}
