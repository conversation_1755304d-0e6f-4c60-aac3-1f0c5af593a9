.games-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  animation: fadeInUp 0.6s ease-out;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  @media (max-width: 480px) {
    gap: var(--spacing-xs);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--border-color);
  margin: var(--spacing-lg) 0;

  i {
    font-size: 3rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    opacity: 0.6;
  }

  h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
  }

  p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    max-width: 300px;
  }

  @media (max-width: 768px) {
    padding: var(--spacing-lg);
    margin: var(--spacing-md) 0;

    i {
      font-size: 2.5rem;
    }

    h3 {
      font-size: var(--font-size-md);
    }

    p {
      font-size: var(--font-size-xs);
    }
  }
}
