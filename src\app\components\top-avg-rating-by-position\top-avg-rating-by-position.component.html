<div class="top-rating-container" *ngIf="topAvgRatingData && !isLoading">
    <!-- Header Section -->
    <div class="header-section" *ngIf="!hideTitle">
        <div class="header-content">
            <div class="header-icon">
                <i class="fas fa-star"></i>
            </div>
            <div class="header-text">
                <h1 class="header-title">Top Average Rating</h1>
                <p class="header-subtitle">Best performers by position</p>
            </div>
        </div>
    </div>

    <!-- Controls Section -->
    <div class="controls-section">
        <div class="controls-container">
            <div class="control-group">
                <label class="control-label">Position</label>
                <div class="position-selector">
                    <pro-clubs-auto-complete-select
                        [placeholder]="'Position'"
                        [defaultOption]="chosenPosition"
                        [width]="'120px'"
                        (selectionChange)="onSelectionChange($event)"
                        [selectOptions]="playablePositionOptions">
                    </pro-clubs-auto-complete-select>
                </div>
            </div>

            <div class="control-group">
                <label class="control-label">Min Games</label>
                <input type="number"
                       [(ngModel)]="minimumGames"
                       class="games-input"
                       min="1"
                       max="100" />
            </div>

            <button (click)="onLoadClick()" class="load-button">
                <i class="fas fa-sync-alt"></i>
                <span>Load Data</span>
            </button>
        </div>
    </div>

    <!-- Players Grid -->
    <div class="players-grid">
        <!-- Top Players by Position - Only show when no specific position is selected -->
        <div class="position-leaders-section" *ngIf="getTopPlayersByPositionArray().length > 0 && !isFilteringByPosition()">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-trophy"></i>
                    Position Leaders
                </h2>
                <p class="section-subtitle">Top performers with {{minimumGames}}+ games played</p>
            </div>

            <div class="position-leaders-grid">
                <div class="position-card"
                     *ngFor="let item of getTopPlayersByPositionArray()"
                     (click)="onPlayerClick(item.player)">
                    <div class="position-header">
                        <div class="position-icon">
                            <i [class]="getPositionIcon(item.position)"></i>
                        </div>
                        <div class="position-info">
                            <h4 class="position-name">{{getPositionDisplayName(item.position)}}</h4>
                            <span class="position-code">{{item.position}}</span>
                        </div>
                    </div>

                    <div class="player-section">
                        <div class="player-avatar">
                            <img [src]="item.player.playerImgUrl || 'assets/Icons/User.jpg'"
                                 [alt]="item.player.playerName">
                            <div class="team-badge">
                                <img [src]="item.player.teamImgUrl || 'assets/Icons/TeamLogo.jpg'"
                                     [alt]="item.player.teamName">
                            </div>
                        </div>

                        <div class="player-details">
                            <h3 class="player-name">{{item.player.playerName}}</h3>
                            <p class="team-name">{{item.player.teamName}}</p>
                        </div>
                    </div>

                    <div class="stats-section">
                        <div class="rating-display">
                            <span class="rating-value"
                                  [class.excellent]="item.player.avgRating >= 8.5"
                                  [class.good]="item.player.avgRating >= 7.5 && item.player.avgRating < 8.5"
                                  [class.average]="item.player.avgRating >= 6.5 && item.player.avgRating < 7.5"
                                  [class.poor]="item.player.avgRating < 6.5">
                                {{item.player.avgRating}}
                            </span>
                            <span class="rating-label">AVG</span>
                        </div>

                        <div class="mini-stats">
                            <div class="stat-item">
                                <span class="stat-value">{{item.player.games}}</span>
                                <span class="stat-label">G</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">{{item.player.goals}}</span>
                                <span class="stat-label">⚽</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">{{item.player.assists}}</span>
                                <span class="stat-label">🅰️</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Remaining Players List -->
        <div class="players-list" *ngIf="topAvgRatingData.length > 0">
            <div class="list-header">
                <h3 class="list-title">
                    <i class="fas fa-list-ol"></i>
                    <span *ngIf="isFilteringByPosition()">{{getPositionDisplayName(chosenPosition)}} Rankings</span>
                    <span *ngIf="!isFilteringByPosition()">Complete Rankings</span>
                </h3>
            </div>
            <div class="players-list-container">
                <div class="player-card"
                     *ngFor="let player of topAvgRatingData; let i = index"
                     (click)="onPlayerClick(player)"
                     [class.top-three]="i < 3">
                    <div class="player-rank">
                        <span class="rank-number"
                              [class.gold]="i === 0"
                              [class.silver]="i === 1"
                              [class.bronze]="i === 2">
                            {{i + 1}}
                        </span>
                    </div>

                    <div class="player-avatar-small">
                        <img [src]="player.playerImgUrl || 'assets/Icons/User.jpg'"
                             [alt]="player.playerName">
                    </div>

                    <div class="player-details">
                        <h4 class="player-name">{{player.playerName}}</h4>
                        <p class="team-name">{{player.teamName}}</p>
                    </div>

                    <div class="player-stats">
                        <div class="stat-group">
                            <span class="stat-value">{{player.games}}</span>
                            <span class="stat-label">Games</span>
                        </div>
                        <div class="stat-group">
                            <span class="stat-value">{{player.goals}}</span>
                            <span class="stat-label">Goals</span>
                        </div>
                        <div class="stat-group">
                            <span class="stat-value">{{player.assists}}</span>
                            <span class="stat-label">Assists</span>
                        </div>
                    </div>

                    <div class="player-rating">
                        <div class="rating-circle"
                             [class.excellent]="player.avgRating >= 8.5"
                             [class.good]="player.avgRating >= 7.5 && player.avgRating < 8.5"
                             [class.average]="player.avgRating >= 6.5 && player.avgRating < 7.5"
                             [class.poor]="player.avgRating < 6.5">
                            <span class="rating-value">{{player.avgRating}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="topAvgRatingData.length === 0 && !isLoading">
            <div class="empty-state-content">
                <div class="empty-state-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="empty-state-title">No Players Found</h3>
                <p class="empty-state-message" *ngIf="isFilteringByPosition()">
                    No {{getPositionDisplayName(chosenPosition).toLowerCase()}} players found with {{minimumGames}}+ games played.
                </p>
                <p class="empty-state-message" *ngIf="!isFilteringByPosition()">
                    No players found with {{minimumGames}}+ games played.
                </p>
                <div class="empty-state-suggestions">
                    <p class="suggestion-text">Try:</p>
                    <ul class="suggestion-list">
                        <li>Reducing the minimum games requirement</li>
                        <li *ngIf="isFilteringByPosition()">Selecting a different position</li>
                        <li>Checking if there are any recorded matches</li>
                    </ul>
                </div>
                <button (click)="onLoadClick()" class="retry-button">
                    <i class="fas fa-sync-alt"></i>
                    <span>Retry</span>
                </button>
            </div>
        </div>
    </div>
</div>

<div class="loading-container" *ngIf="isLoading">
    <pro-clubs-spinner></pro-clubs-spinner>
</div>