import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { LeagueService } from '../../services/league.service';
import { TeamService } from '../../services/team.service';
import { NotificationService } from '../../services/notification.service';
import { ILeague } from '../../shared/models/league-table.model';
import { TeamDTO } from '@pro-clubs-manager/shared-dtos';

@Component({
  selector: 'app-admin-team-league-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './admin-team-league-management.component.html',
  styleUrls: ['./admin-team-league-management.component.scss']
})
export class AdminTeamLeagueManagementComponent implements OnInit {
  leagues: ILeague[] = [];
  allTeams: TeamDTO[] = [];
  selectedLeagueId: string = '';
  selectedTeamId: string = '';
  leagueTeams: TeamDTO[] = [];
  availableTeams: TeamDTO[] = [];
  isLoading = false;
  isLoadingTeams = false;

  constructor(
    private leagueService: LeagueService,
    private teamService: TeamService,
    private notificationService: NotificationService
  ) {}

  async ngOnInit(): Promise<void> {
    await this.loadInitialData();
  }

  private async loadInitialData(): Promise<void> {
    this.isLoading = true;
    try {
      const [leagues, teams] = await Promise.all([
        this.leagueService.getAllLeagues(),
        this.teamService.getAllTeams()
      ]);
      
      this.leagues = leagues;
      this.allTeams = teams;
    } catch (error) {
      console.error('Error loading initial data:', error);
      this.notificationService.error('Failed to load data');
    } finally {
      this.isLoading = false;
    }
  }

  async onLeagueChange(): Promise<void> {
    if (!this.selectedLeagueId) {
      this.leagueTeams = [];
      this.availableTeams = [];
      return;
    }

    this.isLoadingTeams = true;
    try {
      this.leagueTeams = await this.teamService.getTeamsByLeagueId(this.selectedLeagueId);
      this.updateAvailableTeams();
    } catch (error) {
      console.error('Error loading league teams:', error);
      this.notificationService.error('Failed to load league teams');
    } finally {
      this.isLoadingTeams = false;
    }
  }

  private updateAvailableTeams(): void {
    const leagueTeamIds = this.leagueTeams.map(team => team.id);
    this.availableTeams = this.allTeams.filter(team => !leagueTeamIds.includes(team.id));
  }

  async addTeamToLeague(): Promise<void> {
    if (!this.selectedLeagueId || !this.selectedTeamId) {
      this.notificationService.warning('Please select both a league and a team');
      return;
    }

    try {
      await this.leagueService.addTeamToLeague(this.selectedLeagueId, this.selectedTeamId);
      
      const selectedTeam = this.availableTeams.find(team => team.id === this.selectedTeamId);
      if (selectedTeam) {
        this.leagueTeams.push(selectedTeam);
        this.updateAvailableTeams();
      }
      
      this.selectedTeamId = '';
      this.notificationService.success('Team added to league successfully');
    } catch (error) {
      console.error('Error adding team to league:', error);
      this.notificationService.error('Failed to add team to league');
    }
  }

  async removeTeamFromLeague(teamId: string): Promise<void> {
    if (!this.selectedLeagueId) {
      return;
    }

    const team = this.leagueTeams.find(t => t.id === teamId);
    if (!team) {
      return;
    }

    const confirmRemove = confirm(
      `Are you sure you want to remove "${team.name}" from this league?\n\n` +
      'This action will remove the team from the current season and cannot be undone.'
    );

    if (!confirmRemove) {
      return;
    }

    try {
      await this.leagueService.removeTeamFromLeague(this.selectedLeagueId, teamId);
      
      this.leagueTeams = this.leagueTeams.filter(t => t.id !== teamId);
      this.updateAvailableTeams();
      
      this.notificationService.success('Team removed from league successfully');
    } catch (error) {
      console.error('Error removing team from league:', error);
      this.notificationService.error('Failed to remove team from league');
    }
  }

  get selectedLeague(): ILeague | undefined {
    return this.leagues.find(league => league.id === this.selectedLeagueId);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/Team.png';
    }
  }
}
