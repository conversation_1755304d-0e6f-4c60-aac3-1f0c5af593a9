/* === PLAYER PROFILE CARD COMPONENT === */
.player-profile-card {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    height: fit-content;
    position: sticky;
    top: var(--spacing-xl);

    @media (max-width: 1024px) {
        position: static;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
        gap: var(--spacing-lg);
        margin: 0 5px;
        max-width: calc(100vw - 10px);
        box-sizing: border-box;
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
        margin: 0 5px;
        max-width: calc(100vw - 10px);
        box-sizing: border-box;
    }

    .profile-header {
        text-align: center;
        padding-bottom: var(--spacing-lg);
        border-bottom: 1px solid var(--border-primary);

        .player-avatar-section {
            margin-bottom: var(--spacing-lg);

            .avatar-container {
                position: relative;
                display: inline-block;

                &.uploading {
                    .player-avatar {
                        filter: brightness(0.7);
                    }
                }

                .player-avatar {
                    width: 120px;
                    height: 120px;
                    border-radius: var(--radius-full);
                    object-fit: cover;
                    border: 4px solid var(--border-primary);
                    box-shadow: var(--shadow-lg);
                    transition: all 0.3s ease;

                    &.preview {
                        border-color: var(--primary-500);
                        box-shadow:
                            var(--shadow-lg),
                            0 0 0 2px var(--primary-200);
                        animation: pulse 2s infinite;
                    }

                    @media (max-width: 768px) {
                        width: 100px;
                        height: 100px;
                    }
                }

                .avatar-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.7);
                    border-radius: var(--radius-full);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    cursor: pointer;

                    &:hover {
                        opacity: 1;
                    }

                    .upload-label {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: var(--spacing-xs);
                        color: white;
                        font-size: var(--text-xs);
                        font-weight: 600;
                        cursor: pointer;

                        i {
                            font-size: var(--text-lg);
                        }
                    }

                    .file-input {
                        display: none;
                    }
                }

                .upload-progress-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.8);
                    border-radius: var(--radius-full);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    z-index: 10;

                    .progress-circle {
                        position: relative;
                        margin-bottom: var(--spacing-sm);

                        .progress-ring {
                            transform: rotate(-90deg);
                        }

                        .progress-ring-circle {
                            transition: stroke-dashoffset 0.3s ease;
                        }

                        .progress-text {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            font-size: var(--text-sm);
                            font-weight: var(--font-weight-bold);
                            color: white;
                        }
                    }

                    .upload-status {
                        font-size: var(--text-xs);
                        color: var(--primary-200);
                        font-weight: var(--font-weight-medium);
                    }
                }

                .upload-success-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(34, 197, 94, 0.9);
                    border-radius: var(--radius-full);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    z-index: 10;
                    animation: successPulse 0.6s ease-out;

                    .success-icon {
                        font-size: var(--text-2xl);
                        margin-bottom: var(--spacing-xs);
                        animation: bounceIn 0.6s ease-out;
                    }

                    .success-text {
                        font-size: var(--text-xs);
                        font-weight: var(--font-weight-bold);
                    }
                }

                .preview-badge {
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    background: var(--primary-500);
                    color: white;
                    padding: var(--spacing-xs) var(--spacing-sm);
                    border-radius: var(--radius-full);
                    font-size: var(--text-xs);
                    font-weight: var(--font-weight-bold);
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    box-shadow: var(--shadow-md);
                    animation: slideInFromRight 0.3s ease-out;

                    i {
                        font-size: 10px;
                    }
                }
            }
        }

        .player-name-section {
            .player-name-container {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: var(--spacing-sm);
                margin-bottom: var(--spacing-sm);
            }

            .player-name {
                font-size: var(--text-2xl);
                font-weight: 700;
                color: var(--text-primary);
                margin: 0;
                line-height: 1.2;

                @media (max-width: 768px) {
                    font-size: var(--text-xl);
                }
            }

            .verified-icon {
                display: flex;
                align-items: center;
                justify-content: center;

                i {
                    font-size: var(--text-lg);
                    color: #00bcd4; /* Cyan color like Facebook verified */
                    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
                    transition: all 0.2s ease;

                    @media (max-width: 768px) {
                        font-size: var(--text-base);
                    }
                }

                &:hover i {
                    transform: scale(1.1);
                    filter: drop-shadow(0 2px 4px rgba(0, 188, 212, 0.3));
                }
            }

            .name-edit-container {
                margin: 0 0 var(--spacing-sm) 0;

                .name-input {
                    width: 100%;
                    background: var(--surface-secondary);
                    border: 2px solid var(--border-primary);
                    border-radius: var(--radius-lg);
                    padding: var(--spacing-md);
                    font-family: var(--font-sans);
                    font-size: var(--text-2xl);
                    font-weight: 700;
                    color: var(--text-primary);
                    text-align: left;
                    transition: all 0.2s ease-in-out;

                    &:focus {
                        outline: none;
                        border-color: var(--primary-500);
                        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
                    }

                    &::placeholder {
                        color: var(--text-secondary);
                        opacity: 0.7;
                    }

                    @media (max-width: 768px) {
                        font-size: var(--text-xl);
                        padding: var(--spacing-sm);
                    }
                }
            }

            .player-subtitle {
                .position-badge {
                    display: inline-flex;
                    align-items: center;
                    padding: var(--spacing-xs) var(--spacing-md);
                    background: var(--primary-500);
                    color: white;
                    border-radius: var(--radius-full);
                    font-size: var(--text-sm);
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                }
            }
        }
    }

    .team-section, .free-agent-section {
        .section-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--text-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-md) 0;

            i {
                color: var(--primary-500);
            }
        }

        .team-card {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background: var(--surface-hover);
                transform: translateY(-2px);
                box-shadow: var(--shadow-md);
                border-color: var(--primary-300);
            }

            .team-logo-container {
                flex-shrink: 0;

                .team-logo {
                    width: 48px;
                    height: 48px;
                    border-radius: var(--radius-lg);
                    object-fit: cover;
                    border: 1px solid var(--border-primary);
                }
            }

            .team-info {
                flex: 1;
                min-width: 0;

                .team-name {
                    display: block;
                    font-size: var(--text-base);
                    font-weight: 600;
                    color: var(--text-primary);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .team-role {
                    display: block;
                    font-size: var(--text-sm);
                    color: var(--text-secondary);
                    margin-top: var(--spacing-xs);
                }
            }

            .team-arrow {
                color: var(--text-tertiary);
                font-size: var(--text-sm);
            }
        }

        .free-agent-card {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);

            .free-agent-icon {
                width: 48px;
                height: 48px;
                background: var(--warning-500);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: var(--text-lg);
            }

            .free-agent-info {
                .free-agent-title {
                    display: block;
                    font-size: var(--text-base);
                    font-weight: 600;
                    color: var(--text-primary);
                }

                .free-agent-subtitle {
                    display: block;
                    font-size: var(--text-sm);
                    color: var(--text-secondary);
                    margin-top: var(--spacing-xs);
                }
            }
        }
    }

    .player-info-grid {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);

        .info-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            transition: all 0.2s ease;
            max-width: 100%;
            box-sizing: border-box;

            &:hover {
                background: var(--surface-hover);
            }

            @media (max-width: 480px) {
                gap: var(--spacing-sm);
                padding: var(--spacing-sm);
            }

            .info-icon {
                width: 40px;
                height: 40px;
                background: var(--primary-500);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: var(--text-base);
                flex-shrink: 0;
            }

            .info-content {
                flex: 1;
                min-width: 0;
                overflow: hidden;

                .info-label {
                    display: block;
                    font-size: var(--text-xs);
                    font-weight: 600;
                    color: var(--text-secondary);
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    margin-bottom: var(--spacing-xs);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .info-value {
                    display: block;
                    font-size: var(--text-sm);
                    font-weight: 600;
                    color: var(--text-primary);
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                    hyphens: auto;

                    @media (max-width: 480px) {
                        font-size: var(--text-xs);
                    }
                }

                .position-select {
                    width: 100%;
                }

                .age-edit {
                    width: 100%;

                    .age-input {
                        width: 100%;
                        padding: var(--spacing-xs) var(--spacing-sm);
                        border: 1px solid var(--border-primary);
                        border-radius: var(--radius-md);
                        background: var(--surface-primary);
                        color: var(--text-primary);
                        font-size: var(--text-base);
                        transition: all 0.2s ease;

                        &:focus {
                            outline: none;
                            border-color: var(--primary);
                            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
                        }

                        &::placeholder {
                            color: var(--text-tertiary);
                        }

                        // Remove spinner arrows for number input
                        &::-webkit-outer-spin-button,
                        &::-webkit-inner-spin-button {
                            -webkit-appearance: none;
                            margin: 0;
                        }

                        &[type=number] {
                            -moz-appearance: textfield;
                        }
                    }
                }
            }
        }
    }

    .profile-actions {
        padding-top: var(--spacing-lg);
        border-top: 1px solid var(--border-primary);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);

        .compare-btn, .history-btn {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            border: none;
            border-radius: var(--radius-lg);
            font-size: var(--text-sm);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                transform: translateY(-1px);
                box-shadow: var(--shadow-md);
            }
        }

        .compare-btn {
            background: var(--primary-500);
            color: white;

            &:hover {
                background: var(--primary-600);
            }
        }

        .history-btn {
            background: var(--secondary-500);
            color: white;

            &:hover {
                background: var(--secondary-600);
            }
        }

        .danger-btn {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            background: var(--error-500);
            color: white;
            border: none;
            border-radius: var(--radius-lg);
            font-size: var(--text-sm);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background: var(--error-600);
                transform: translateY(-1px);
                box-shadow: var(--shadow-md);
            }
        }
    }
}

/* === ANIMATIONS === */
@keyframes pulse {
    0% {
        box-shadow:
            var(--shadow-lg),
            0 0 0 0 var(--primary-200);
    }
    50% {
        box-shadow:
            var(--shadow-lg),
            0 0 0 4px var(--primary-200);
    }
    100% {
        box-shadow:
            var(--shadow-lg),
            0 0 0 0 var(--primary-200);
    }
}

@keyframes successPulse {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(20px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}
