/* === SCROLLABLE TABS COMPONENT === */
.scrollable-tabs {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    overflow: hidden;
}

/* === SCROLL ARROWS === */
.scroll-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--surface-primary), var(--surface-secondary));
    border: 1px solid var(--border-primary);
    border-radius: 50%;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    box-shadow: var(--shadow-md);

    &.visible {
        opacity: 1;
        visibility: visible;
    }

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
        color: var(--primary-600);
        transform: translateY(-50%) scale(1.05);
        box-shadow: var(--shadow-lg);
    }

    &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
    }

    &.left-arrow {
        left: 8px;
    }

    &.right-arrow {
        right: 8px;
    }

    i {
        font-size: var(--text-sm);
        font-weight: bold;
    }

    @media (max-width: 768px) {
        width: 36px;
        height: 36px;

        &.left-arrow {
            left: 6px;
        }

        &.right-arrow {
            right: 6px;
        }

        i {
            font-size: var(--text-xs);
        }
    }
}

/* === TABS CONTAINER === */
.tabs-container {
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */

    &::-webkit-scrollbar {
        display: none; /* Chrome/Safari */
    }
}

.tabs-list {
    display: flex;
    align-items: center;
    min-width: 100%;
    padding: var(--spacing-xs);
    gap: var(--spacing-xs);

    @media (max-width: 768px) {
        padding: var(--spacing-xs) 50px; /* Add padding for arrows */
    }
}

/* === TAB ITEMS === */
.tab-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    font-size: var(--text-sm);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    min-width: fit-content;
    flex-shrink: 0;

    &:hover:not(.disabled) {
        background: var(--surface-hover);
        color: var(--text-primary);
        transform: translateY(-1px);
    }

    &.active {
        background: var(--primary-100);
        color: var(--primary-600);
        font-weight: var(--font-weight-semibold);
        box-shadow: var(--shadow-sm);
    }

    &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--text-xs);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

/* === TAB CONTENT === */
.tab-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    @media (max-width: 768px) {
        gap: var(--spacing-xs);
    }
}

.tab-icon {
    font-size: var(--text-sm);
    flex-shrink: 0;

    @media (max-width: 768px) {
        font-size: var(--text-xs);
    }
}

.tab-label {
    flex-shrink: 0;
}

.tab-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    padding: 0 var(--spacing-xs);
    background: var(--surface-tertiary);
    color: var(--text-secondary);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    border: 1px solid var(--border-primary);

    .tab-item.active & {
        background: var(--primary-500);
        color: white;
        border-color: var(--primary-600);
    }

    @media (max-width: 768px) {
        min-width: 18px;
        height: 18px;
        font-size: 10px;
    }
}

/* === ACTIVE INDICATOR === */
.active-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-full);
    animation: slideIn 0.3s ease-out;

    @media (max-width: 768px) {
        height: 2px;
    }
}

@keyframes slideIn {
    from {
        width: 0;
        opacity: 0;
    }
    to {
        width: 80%;
        opacity: 1;
    }
}

/* === VARIANT STYLES === */

/* Pills Variant */
.variant-pills {
    .tab-item {
        border-radius: var(--radius-full);
        
        &.active {
            background: var(--primary-500);
            color: white;
        }
    }

    .active-indicator {
        display: none;
    }
}

/* Underline Variant */
.variant-underline {
    background: transparent;
    border: none;
    border-bottom: 1px solid var(--border-primary);
    border-radius: 0;

    .tab-item {
        border-radius: 0;
        padding-bottom: var(--spacing-lg);

        &.active {
            background: transparent;
            color: var(--primary-600);
            border-bottom: 2px solid var(--primary-500);
        }
    }

    .active-indicator {
        bottom: -1px;
        height: 2px;
        width: 100%;
    }
}

/* === RESPONSIVE ADJUSTMENTS === */
@media (max-width: 480px) {
    .scrollable-tabs {
        border-radius: var(--radius-md);
    }

    .tab-item {
        .tab-content {
            flex-direction: column;
            gap: 2px;
        }

        .tab-icon {
            display: none; /* Hide icons on very small screens */
        }
    }

    /* Show icons only for active tab on mobile */
    .tab-item.active .tab-icon {
        display: block;
    }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
    .scroll-arrow,
    .tab-item,
    .active-indicator {
        transition: none;
    }

    .tabs-container {
        scroll-behavior: auto;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .tab-item.active {
        border: 2px solid var(--primary-500);
    }

    .scroll-arrow {
        border: 2px solid var(--border-primary);
    }
}
