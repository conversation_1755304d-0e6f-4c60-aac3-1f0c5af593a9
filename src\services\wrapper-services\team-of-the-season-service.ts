import { inject, injectable } from "tsyringe";
import { IGameRepository } from "../../interfaces/game/game-repository.interface";
import { IPlayerRepository } from "../../interfaces/player/player-repository.interface";
import { ITeamOfTheWeekService } from "../../interfaces/wrapper-services/team-of-the-week-service.interface";
import TeamOfTheSeason, { ITeamOfTheSeason, ITeamOfTheSeasonPlayer } from "../../models/team-of-the-season";
import logger from "../../config/logger";
import { ClientSession, Types } from "mongoose";
import { GAME_STATUS } from "@pro-clubs-manager/shared-dtos";

// Temporary types for TOTW data
interface TOTWPlayer {
  playerId: string;
  name: string;
  imgUrl?: string;
  position: string;
  totalGames: number;
  totalScore: number;
  team: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  positionsStats: {
    [position: string]: {
      goals: number;
      assists: number;
      avgRating: number;
      cleanSheets: number;
      playerOfTheMatch: number;
    };
  };
}

export interface TOTSFormation {
  name: string;
  positions: {
    [position: string]: number; // position -> count
  };
}

export const TOTS_FORMATIONS: TOTSFormation[] = [
  {
    name: "3-5-2",
    positions: {
      GK: 1,
      CB: 3,
      CDM: 2,
      LM: 1,
      RM: 1,
      CAM: 1,
      ST: 2,
    },
  },
  {
    name: "4-3-3",
    positions: {
      GK: 1,
      CB: 2,
      LM: 1, // LB
      RM: 1, // RB
      CDM: 3,
      ST: 3, // Including wingers
    },
  },
];

@injectable()
export class TeamOfTheSeasonService {
  constructor(
    @inject("IGameRepository") private gameRepository: IGameRepository,
    @inject("IPlayerRepository") _playerRepository: IPlayerRepository,
    @inject("ITeamOfTheWeekService") private teamOfTheWeekService: ITeamOfTheWeekService
  ) {}

  async generateTeamOfTheSeason(
    leagueId: string,
    seasonNumber: number,
    formationName: string = "3-5-2",
    session?: ClientSession
  ): Promise<ITeamOfTheSeason> {
    logger.info(`TeamOfTheSeasonService: Generating TOTS for season ${seasonNumber}, formation ${formationName}`);

    // Check if TOTS already exists for this season/formation
    const existingTOTS = await TeamOfTheSeason.findOne({
      league: new Types.ObjectId(leagueId),
      seasonNumber,
      formation: formationName,
    });

    if (existingTOTS) {
      logger.info(`TOTS already exists for season ${seasonNumber}, formation ${formationName}`);
      return existingTOTS;
    }

    // Get all games from the completed season
    const seasonGames = await this.gameRepository.getLeagueGamesBySeason({
      leagueId,
      seasonNumber,
    });

    const playedGames = seasonGames.filter(game => game.status === GAME_STATUS.PLAYED);

    if (playedGames.length === 0) {
      throw new Error(`No played games found for season ${seasonNumber}`);
    }

    // Use the existing TOTW logic but for the entire season
    const { teamOfTheWeek } = await this.teamOfTheWeekService.getTeamOfTheWeek(playedGames);

    // Find the formation
    const formation = TOTS_FORMATIONS.find(f => f.name === formationName);
    if (!formation) {
      throw new Error(`Formation ${formationName} not supported`);
    }

    // Convert TOTW format to TOTS format
    const totsPlayers: ITeamOfTheSeasonPlayer[] = (teamOfTheWeek as TOTWPlayer[]).map((player: TOTWPlayer) => ({
      playerId: new Types.ObjectId(player.playerId),
      playerName: player.name,
      playerImgUrl: player.imgUrl,
      position: player.position,
      teamId: new Types.ObjectId(player.team.id),
      teamName: player.team.name,
      teamImgUrl: player.team.imgUrl,
      stats: {
        games: player.totalGames,
        goals: player.positionsStats[player.position]?.goals || 0,
        assists: player.positionsStats[player.position]?.assists || 0,
        cleanSheets: player.positionsStats[player.position]?.cleanSheets || 0,
        avgRating: player.positionsStats[player.position]?.avgRating || 0,
        playerOfTheMatch: player.positionsStats[player.position]?.playerOfTheMatch || 0,
        score: player.totalScore,
      },
    }));

    // Create and save TOTS
    const tots = new TeamOfTheSeason({
      seasonNumber,
      league: new Types.ObjectId(leagueId),
      formation: formationName,
      players: totsPlayers,
      generatedBy: 'System',
    });

    await tots.save({ session });

    logger.info(`TOTS generated successfully for season ${seasonNumber}`);
    return tots;
  }

  async getTeamOfTheSeason(
    leagueId: string,
    seasonNumber: number,
    formationName: string = "3-5-2"
  ): Promise<ITeamOfTheSeason | null> {
    return await TeamOfTheSeason.findOne({
      league: new Types.ObjectId(leagueId),
      seasonNumber,
      formation: formationName,
    });
  }

  async getAllTeamsOfTheSeason(leagueId: string): Promise<ITeamOfTheSeason[]> {
    return await TeamOfTheSeason.find({
      league: new Types.ObjectId(leagueId),
    }).sort({ seasonNumber: -1 }); // Most recent first
  }

  async getAvailableSeasons(leagueId: string): Promise<number[]> {
    const tots = await TeamOfTheSeason.find({
      league: new Types.ObjectId(leagueId),
    }).distinct('seasonNumber');

    return tots.sort((a, b) => b - a); // Most recent first
  }

  async getAvailableFormations(leagueId: string, seasonNumber: number): Promise<string[]> {
    const tots = await TeamOfTheSeason.find({
      league: new Types.ObjectId(leagueId),
      seasonNumber,
    }).distinct('formation');

    return tots;
  }

  async deleteTeamOfTheSeason(
    leagueId: string,
    seasonNumber: number,
    formationName: string
  ): Promise<boolean> {
    const result = await TeamOfTheSeason.deleteOne({
      league: new Types.ObjectId(leagueId),
      seasonNumber,
      formation: formationName,
    });

    return result.deletedCount > 0;
  }

  async regenerateTeamOfTheSeason(
    leagueId: string,
    seasonNumber: number,
    formationName: string = "3-5-2",
    session?: ClientSession
  ): Promise<ITeamOfTheSeason> {
    // Delete existing TOTS
    await this.deleteTeamOfTheSeason(leagueId, seasonNumber, formationName);

    // Generate new TOTS
    return await this.generateTeamOfTheSeason(leagueId, seasonNumber, formationName, session);
  }

  getFormationPositions(formationName: string): { [position: string]: number } | null {
    const formation = TOTS_FORMATIONS.find(f => f.name === formationName);
    return formation ? formation.positions : null;
  }

  getSupportedFormations(): string[] {
    return TOTS_FORMATIONS.map(f => f.name);
  }
}
