// models/news.ts
import mongoose, { Document, Schema } from 'mongoose';

export interface ITransferData {
  playerDetails: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  fromTeam: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  toTeam: {
    id: string;
    name: string;
    imgUrl?: string;
  };
}

export interface IFreeAgentData {
  playerDetails: {
    id: string;
    name: string;
    imgUrl?: string;
    position: string;
    age: number;
  };
  previousTeams: {
    id: string;
    name: string;
    imgUrl?: string;
    seasonNumber: number;
  }[];
}

// Define the interface for the news item
export interface INews extends Document {
  title: string;
  content: string;
  type: 'General' | 'Transfer' | 'FreeAgent';
  createdBy: string;
  createdAt: Date;
  transferData?: ITransferData;
  freeAgentData?: IFreeAgentData;
  likes: string[]; // Array of user IDs who liked this news
  likeCount: number; // Computed field for like count
}

const transferDataSchema = new Schema(
  {
    playerDetails: {
      id: { type: String, required: true },
      name: { type: String, required: true },
      imgUrl: { type: String },
    },
    fromTeam: {
      id: { type: String },
      name: { type: String },
      imgUrl: { type: String },
    },
    toTeam: {
      id: { type: String, required: true },
      name: { type: String, required: true },
      imgUrl: { type: String },
    }
  },
  { _id: false }
);

const freeAgentDataSchema = new Schema(
  {
    playerDetails: {
      id: { type: String, required: true },
      name: { type: String, required: true },
      imgUrl: { type: String, required: false },
      position: { type: String, required: true },
      age: { type: Number, required: true },
    },
    previousTeams: [{
      id: { type: String, required: true },
      name: { type: String, required: true },
      imgUrl: { type: String, required: false },
      seasonNumber: { type: Number, required: true },
    }],
  },
  { _id: false }
);

const newsSchema = new Schema(
  {
    title: { type: String, required: true },
    content: { type: String, required: true },
    type: {
      type: String,
      enum: ['General', 'Transfer', 'FreeAgent'],
      required: true
    },
    createdBy: { type: String, required: true },
    createdAt: { type: Date, default: Date.now },
    likes: { type: [String], default: [] }, // Array of user IDs
    transferData: {
      type: transferDataSchema,
      required: false,
    },
    freeAgentData: {
      type: freeAgentDataSchema,
      required: false,
    }
  },
  { timestamps: true }
);

// Virtual field for like count
newsSchema.virtual('likeCount').get(function() {
  return this.likes ? this.likes.length : 0;
});

// Ensure virtual fields are serialized
newsSchema.set('toJSON', { virtuals: true });
newsSchema.set('toObject', { virtuals: true });

// Create the model
const News = mongoose.model<INews>('News', newsSchema);

export default News;