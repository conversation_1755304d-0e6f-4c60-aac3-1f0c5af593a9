/* === PAGE HEADER === */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg) 0;

    .back-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--primary);
        color: var(--primary-foreground);
        border: 2px solid var(--primary);
        border-radius: var(--radius-lg);
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: var(--text-sm);
        font-weight: var(--font-weight-semibold);
        box-shadow: var(--shadow-sm);

        &:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateX(-2px);
            box-shadow: var(--shadow-md);
        }

        &:active {
            transform: translateX(-1px);
            box-shadow: var(--shadow-sm);
        }

        i {
            font-size: var(--text-sm);
            font-weight: bold;
        }

        span {
            font-weight: var(--font-weight-semibold);
        }
    }

    .page-title {
        margin: 0;
        font-size: var(--text-2xl);
        font-weight: 700;
        color: var(--text-primary);
    }

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);

        .page-title {
            font-size: var(--text-xl);
        }
    }
}

/* === MATCH HEADER === */
.match-header {
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-2xl);
    position: relative;
    overflow: hidden;
    animation: scaleInBounce 0.8s ease-out;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary), var(--primary));
        background-size: 200% 100%;
        animation: shimmer 3s ease-in-out infinite;
    }

    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 50% 0%, rgba(var(--primary-rgb), 0.05) 0%, transparent 50%);
        pointer-events: none;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }
}

.match-status-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-lg);
    width: fit-content;

    .status-indicator {
        font-size: var(--text-xs);
        animation: pulse 2s infinite;
    }

    &.scheduled {
        background: var(--info-100);
        color: var(--info-700);
        border: 1px solid var(--info-200);

        .status-indicator {
            color: var(--info-500);
        }
    }

    &.played,
    &.completed {
        background: var(--success-100);
        color: var(--success-700);
        border: 1px solid var(--success-200);

        .status-indicator {
            color: var(--success-500);
        }
    }

    &.cancelled {
        background: var(--danger-100);
        color: var(--danger-700);
        border: 1px solid var(--danger-200);

        .status-indicator {
            color: var(--danger-500);
        }
    }
}

.teams-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    gap: var(--spacing-xl);

    @media (max-width: 768px) {
        gap: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        text-align: center;
    }
}

.team-section {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);

    &:hover {
        background: var(--surface-secondary);
        transform: translateY(-2px);
    }

    .team-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);

        @media (max-width: 480px) {
            justify-content: center;
        }
    }

    &.away-team .team-info {
        flex-direction: row-reverse;

        @media (max-width: 480px) {
            flex-direction: row;
        }
    }

    .team-logo {
        width: 80px;
        height: 80px;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 3px solid var(--border-primary);
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;

        @media (max-width: 768px) {
            width: 60px;
            height: 60px;
        }

        @media (max-width: 480px) {
            width: 50px;
            height: 50px;
        }
    }

    .team-details {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);

        .team-name {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0;
            line-height: 1.2;

            @media (max-width: 768px) {
                font-size: var(--text-lg);
            }

            @media (max-width: 480px) {
                font-size: var(--text-base);
            }
        }

        .team-label {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }

    &.away-team .team-details {
        text-align: right;

        @media (max-width: 480px) {
            text-align: center;
        }
    }
}

.score-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);

    .score-container,
    .vs-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .score-display {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .score-number {
        font-size: clamp(2rem, 8vw, 4rem);
        font-weight: var(--font-weight-black);
        color: var(--text-primary);
        line-height: 1;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &.home-score {
            color: var(--primary);
        }

        &.away-score {
            color: var(--accent-primary);
        }
    }

    .score-separator {
        font-size: clamp(1.5rem, 6vw, 3rem);
        font-weight: var(--font-weight-bold);
        color: var(--text-tertiary);
        margin: 0 var(--spacing-sm);
    }

    .vs-text {
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-secondary);
        padding: var(--spacing-md) var(--spacing-lg);
        background: var(--surface-secondary);
        border-radius: var(--radius-full);
        border: 2px solid var(--border-primary);
    }

    .penalty-result {
        margin: var(--spacing-xs) 0;

        .penalty-display {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-xs);

            .penalty-label {
                font-size: var(--text-xs);
                font-weight: var(--font-weight-bold);
                color: var(--text-secondary);
                background: var(--surface-tertiary);
                padding: var(--spacing-xs) var(--spacing-sm);
                border-radius: var(--radius-sm);
                border: 1px solid var(--border-secondary);
            }

            .penalty-score {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);

                .penalty-number {
                    font-size: var(--text-sm);
                    font-weight: var(--font-weight-semibold);
                    color: var(--text-primary);
                    background: var(--surface-secondary);
                    padding: var(--spacing-xs) var(--spacing-sm);
                    border-radius: var(--radius-sm);
                    border: 1px solid var(--border-primary);
                    min-width: 24px;
                    text-align: center;

                    &.home-penalty {
                        border-color: var(--primary);
                        background: var(--primary-light);
                    }

                    &.away-penalty {
                        border-color: var(--secondary);
                        background: var(--secondary-light);
                    }
                }

                .penalty-separator {
                    font-size: var(--text-sm);
                    font-weight: var(--font-weight-bold);
                    color: var(--text-secondary);
                }
            }
        }
    }

    .match-time {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--text-sm);
        color: var(--text-secondary);
        background: var(--surface-tertiary);
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--radius-full);
        border: 1px solid var(--border-secondary);

        i {
            color: var(--primary);
        }
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes scaleInBounce {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
