import { Component, Input, OnInit } from '@angular/core';
import { PlayerService } from '../../../services/player.service';
import { ITransferHistoryEntry, TransferType, TransferHistoryDisplay } from '../../../shared/models/transfer-history.model';
import { Router } from '@angular/router';

@Component({
  selector: 'app-transfer-history',
  templateUrl: './transfer-history.component.html',
  styleUrls: ['./transfer-history.component.scss']
})
export class TransferHistoryComponent implements OnInit {
  @Input() playerId!: string;
  
  transferHistory: TransferHistoryDisplay[] = [];
  isLoading = true;
  hasError = false;

  constructor(
    private playerService: PlayerService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadTransferHistory();
  }

  async loadTransferHistory(): Promise<void> {
    try {
      this.isLoading = true;
      this.hasError = false;
      
      const history = await this.playerService.getTransferHistoryByPlayerId(this.playerId);
      
      // Transform and sort transfer history
      this.transferHistory = history
        .map(entry => this.transformTransferEntry(entry))
        .sort((a, b) => new Date(b.transferDate).getTime() - new Date(a.transferDate).getTime());
        
    } catch (error) {
      console.error('Error loading transfer history:', error);
      this.hasError = true;
    } finally {
      this.isLoading = false;
    }
  }

  private transformTransferEntry(entry: ITransferHistoryEntry): TransferHistoryDisplay {
    const transferDate = new Date(entry.transferDate);
    
    return {
      ...entry,
      transferType: this.getTransferType(entry),
      formattedDate: this.formatDate(transferDate),
      timeAgo: this.getTimeAgo(transferDate)
    };
  }

  private getTransferType(entry: ITransferHistoryEntry): TransferType {
    if (!entry.fromTeam && entry.toTeam) {
      return TransferType.SIGNING;
    } else if (entry.fromTeam && !entry.toTeam) {
      return TransferType.RELEASE;
    } else {
      return TransferType.TRANSFER;
    }
  }

  private formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 30) {
      return `${diffInDays} days ago`;
    } else if (diffInDays < 365) {
      const months = Math.floor(diffInDays / 30);
      return `${months} month${months > 1 ? 's' : ''} ago`;
    } else {
      const years = Math.floor(diffInDays / 365);
      return `${years} year${years > 1 ? 's' : ''} ago`;
    }
  }

  getTransferIcon(transferType: TransferType): string {
    switch (transferType) {
      case TransferType.SIGNING:
        return 'fas fa-user-plus';
      case TransferType.TRANSFER:
        return 'fas fa-exchange-alt';
      case TransferType.RELEASE:
        return 'fas fa-user-minus';
      default:
        return 'fas fa-arrow-right';
    }
  }

  getTransferTypeLabel(transferType: TransferType): string {
    switch (transferType) {
      case TransferType.SIGNING:
        return 'Signed';
      case TransferType.TRANSFER:
        return 'Transferred';
      case TransferType.RELEASE:
        return 'Released';
      default:
        return 'Transfer';
    }
  }

  onTeamClick(teamId: string): void {
    if (teamId) {
      this.router.navigate(['/team-details', teamId]);
    }
  }

  getDefaultTeamImage(): string {
    return 'assets/Icons/TeamLogo.jpg';
  }

  getFreeAgentImage(): string {
    return 'assets/Icons/FreeAgent.png';
  }
}
