import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { FixtureDTO } from '../../../shared/models/game.model';
import { ListOption } from '../../../shared/models/list-option.model';

export interface FixtureNavigationEvent {
  type: 'selection' | 'page' | 'first' | 'last' | 'previous' | 'next';
  data?: any;
}

@Component({
  selector: 'app-fixture-navigation',
  templateUrl: './fixture-navigation.component.html',
  styleUrls: ['./fixture-navigation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FixtureNavigationComponent {
  @Input() fixturesOptions: Array<{ value: string; displayText: string }> = [];
  @Input() currentFixtureDisplayText: string = '';
  @Input() currentFixtureNumber: number = 1;
  @Input() totalFixtures: number = 0;
  @Input() showNavigation: boolean = true;
  @Input() showPagination: boolean = true;
  
  @Output() navigationEvent = new EventEmitter<FixtureNavigationEvent>();

  onSelectionChange(selectedOption: ListOption): void {
    // Only emit if we have a valid selection
    if (selectedOption && selectedOption.value) {
      this.navigationEvent.emit({
        type: 'selection',
        data: selectedOption
      });
    }
  }

  onSelectChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const selectedOption: ListOption = {
      value: target.value,
      displayText: target.options[target.selectedIndex].text
    };
    this.onSelectionChange(selectedOption);
  }

  onPageChange(event: any): void {
    this.navigationEvent.emit({
      type: 'page',
      data: event
    });
  }

  goToFirstFixture(): void {
    if (this.currentFixtureNumber > 1) {
      this.navigationEvent.emit({ type: 'first' });
    }
  }

  goToLastFixture(): void {
    if (this.currentFixtureNumber < this.totalFixtures) {
      this.navigationEvent.emit({ type: 'last' });
    }
  }

  goToPreviousFixture(): void {
    if (this.currentFixtureNumber > 1) {
      this.navigationEvent.emit({ type: 'previous' });
    }
  }

  goToNextFixture(): void {
    if (this.currentFixtureNumber < this.totalFixtures) {
      this.navigationEvent.emit({ type: 'next' });
    }
  }

  canGoToPrevious(): boolean {
    return this.currentFixtureNumber > 1;
  }

  canGoToNext(): boolean {
    return this.currentFixtureNumber < this.totalFixtures;
  }

  canGoToFirst(): boolean {
    return this.currentFixtureNumber > 1;
  }

  canGoToLast(): boolean {
    return this.currentFixtureNumber < this.totalFixtures;
  }
}
