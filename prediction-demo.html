<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prediction Component Demo - Fixed Layout</title>
    <style>
        :root {
            --surface-primary: #1a1a1a;
            --surface-secondary: #2a2a2a;
            --surface-tertiary: #3a3a3a;
            --border-primary: #444;
            --text-primary: #fff;
            --text-secondary: #ccc;
            --text-tertiary: #999;
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --primary-50: #e6f3ff;
            --primary-500: #3b82f6;
            --primary-700: #1d4ed8;
            --success-50: #f0fdf4;
            --success-200: #bbf7d0;
            --success-500: #22c55e;
            --warning-50: #fffbeb;
            --warning-200: #fed7aa;
            --warning-500: #f59e0b;
            --info-50: #eff6ff;
            --info-200: #bfdbfe;
            --info-500: #3b82f6;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --radius-sm: 0.25rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
        }

        body {
            background: #111;
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-title {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary-500);
        }

        .demo-section {
            margin-bottom: 3rem;
        }

        .demo-section h2 {
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .demo-section p {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
        }

        /* Fixed Predictions Grid - No more 4 in a row */
        .predictions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: var(--spacing-lg);
            max-width: 1200px;
            margin: 0 auto;
        }

        @media (min-width: 1400px) {
            .predictions-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 1200px) {
            .predictions-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .predictions-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }
        }

        .prediction-card {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-primary);
            overflow: hidden;
            min-height: 280px;
            max-height: 320px;
        }

        /* Compact Prediction Component */
        .prediction-voting.compact {
            padding: var(--spacing-xs);
            min-height: auto;
            max-height: 280px;
        }

        .compact-header {
            margin-bottom: 2px;
        }

        .compact-predictions-count {
            font-size: var(--text-xs);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 2px;
            line-height: 1;
        }

        .voting-section {
            margin-bottom: 4px;
        }

        .voting-buttons {
            gap: 2px;
            display: grid;
            grid-template-columns: 1fr auto 1fr;
        }

        .vote-btn {
            padding: 3px 6px;
            font-size: var(--text-xs);
            min-height: 32px;
            min-width: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1px;
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--text-primary);
        }

        .vote-btn:hover {
            background: var(--surface-tertiary);
            border-color: var(--primary-500);
        }

        .team-logo {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: var(--primary-500);
            margin-bottom: 2px;
        }

        .team-name {
            font-size: var(--text-xs);
            text-align: center;
            max-width: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .results-section {
            margin-top: 4px;
            max-height: 120px;
            overflow-y: auto;
        }

        .results-title {
            font-size: var(--text-xs);
            margin-bottom: 2px;
            font-weight: var(--font-weight-semibold);
        }

        .results-bars {
            gap: 1px;
            display: flex;
            flex-direction: column;
        }

        .result-bar {
            margin-bottom: 1px;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1px;
        }

        .team-info {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .team-logo-small {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary-500);
        }

        .percentage {
            font-size: var(--text-xs);
            font-weight: var(--font-weight-bold);
        }

        .progress-bar {
            width: 100%;
            height: 2px;
            background: var(--surface-tertiary);
            border-radius: var(--radius-sm);
            margin-bottom: 1px;
        }

        .progress-fill {
            height: 100%;
            border-radius: var(--radius-sm);
            background: var(--primary-500);
        }

        .vote-count {
            font-size: var(--text-xs);
            color: var(--text-tertiary);
            margin-bottom: 1px;
        }

        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .before, .after {
            padding: 1rem;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
        }

        .before h3 {
            color: #ef4444;
        }

        .after h3 {
            color: #22c55e;
        }

        .old-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .old-prediction-card {
            background: var(--surface-primary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            min-height: 400px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Prediction Component Layout Fixes</h1>
        
        <div class="demo-section">
            <h2>Problem 1: 4 Fixtures in a Row Covering Each Other</h2>
            <p>Fixed by increasing minimum width from 300px to 380px and adding responsive breakpoints to limit maximum columns.</p>
            
            <div class="before-after">
                <div class="before">
                    <h3>❌ Before (4 columns, overlapping)</h3>
                    <div class="old-grid">
                        <div class="old-prediction-card">Old Card 1</div>
                        <div class="old-prediction-card">Old Card 2</div>
                        <div class="old-prediction-card">Old Card 3</div>
                        <div class="old-prediction-card">Old Card 4</div>
                    </div>
                </div>
                
                <div class="after">
                    <h3>✅ After (Max 3 columns, proper spacing)</h3>
                    <div class="predictions-grid">
                        <div class="prediction-card">Fixed Card 1</div>
                        <div class="prediction-card">Fixed Card 2</div>
                        <div class="prediction-card">Fixed Card 3</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>Problem 2: Improved Prediction Component</h2>
            <p>✅ Better title: "Who's gonna win? Vote now!" instead of generic "Match Predictions"</p>
            <p>✅ Results always visible after voting with prominent percentages</p>
            <p>✅ Reduced height and better spacing for compact layout</p>
            
            <div class="predictions-grid">
                <div class="prediction-card">
                    <div class="prediction-voting compact">
                        <div class="compact-header">
                            <span class="compact-predictions-count">
                                🗳️ Who's gonna win? Vote now!
                            </span>
                            <span class="prediction-count">15 votes</span>
                        </div>
                        
                        <div class="voting-section">
                            <div class="voting-buttons">
                                <button class="vote-btn">
                                    <div class="team-logo"></div>
                                    <span class="team-name">Team A</span>
                                </button>
                                <button class="vote-btn">
                                    <span>Draw</span>
                                </button>
                                <button class="vote-btn">
                                    <div class="team-logo"></div>
                                    <span class="team-name">Team B</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="results-section">
                            <h4 class="results-title">Results</h4>
                            <div class="results-bars">
                                <div class="result-bar">
                                    <div class="result-header">
                                        <div class="team-info">
                                            <div class="team-logo-small"></div>
                                            <span class="team-name">Team A Win</span>
                                        </div>
                                        <span class="percentage">60%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 60%"></div>
                                    </div>
                                    <div class="vote-count">9 votes</div>
                                </div>
                                
                                <div class="result-bar">
                                    <div class="result-header">
                                        <div class="team-info">
                                            <span class="team-name">Draw</span>
                                        </div>
                                        <span class="percentage">20%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 20%"></div>
                                    </div>
                                    <div class="vote-count">3 votes</div>
                                </div>
                                
                                <div class="result-bar">
                                    <div class="result-header">
                                        <div class="team-info">
                                            <div class="team-logo-small"></div>
                                            <span class="team-name">Team B Win</span>
                                        </div>
                                        <span class="percentage">20%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 20%"></div>
                                    </div>
                                    <div class="vote-count">3 votes</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="prediction-card">
                    <div class="prediction-voting compact">
                        <div class="compact-header">
                            <span class="compact-predictions-count">
                                ⚡ 8 predictions
                            </span>
                        </div>
                        
                        <div class="voting-section">
                            <div class="voting-buttons">
                                <button class="vote-btn">
                                    <div class="team-logo"></div>
                                    <span class="team-name">Home Team</span>
                                </button>
                                <button class="vote-btn">
                                    <span>Draw</span>
                                </button>
                                <button class="vote-btn">
                                    <div class="team-logo"></div>
                                    <span class="team-name">Away Team</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="results-section">
                            <h4 class="results-title">Results</h4>
                            <div class="results-bars">
                                <div class="result-bar">
                                    <div class="result-header">
                                        <div class="team-info">
                                            <div class="team-logo-small"></div>
                                            <span class="team-name">Home Win</span>
                                        </div>
                                        <span class="percentage">50%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 50%"></div>
                                    </div>
                                    <div class="vote-count">4 votes</div>
                                </div>
                                
                                <div class="result-bar">
                                    <div class="result-header">
                                        <div class="team-info">
                                            <span class="team-name">Draw</span>
                                        </div>
                                        <span class="percentage">25%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 25%"></div>
                                    </div>
                                    <div class="vote-count">2 votes</div>
                                </div>
                                
                                <div class="result-bar">
                                    <div class="result-header">
                                        <div class="team-info">
                                            <div class="team-logo-small"></div>
                                            <span class="team-name">Away Win</span>
                                        </div>
                                        <span class="percentage">25%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 25%"></div>
                                    </div>
                                    <div class="vote-count">2 votes</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>Key Improvements Made:</h2>
            <ul style="color: var(--text-secondary); line-height: 1.6;">
                <li><strong>🎯 Tighter Card Spacing:</strong> Reduced gaps from large to small spacing for better screen utilization</li>
                <li><strong>📝 Better Prediction Title:</strong> Changed from "Match Predictions" to "Who's gonna win? Vote now!"</li>
                <li><strong>📊 Always Show Results:</strong> Results with percentages are now always visible after voting</li>
                <li><strong>📐 Grid Layout:</strong> Increased minimum width from 300px to 380px to prevent 4 columns</li>
                <li><strong>📱 Responsive Design:</strong> Added breakpoints to limit max columns (3 on large screens, 2 on medium)</li>
                <li><strong>📏 Compact Height:</strong> Limited prediction cards to max 280-320px height</li>
                <li><strong>🎨 Enhanced Styling:</strong> Better colors, prominent percentages, and improved visual hierarchy</li>
                <li><strong>🔄 Scrollable Results:</strong> Made results section scrollable with max height of 140px</li>
                <li><strong>🎛️ Better Button Layout:</strong> Improved voting button sizing and spacing</li>
                <li><strong>📐 Consistent Sizing:</strong> Set consistent card heights across all prediction components</li>
            </ul>
        </div>
    </div>
</body>
</html>
