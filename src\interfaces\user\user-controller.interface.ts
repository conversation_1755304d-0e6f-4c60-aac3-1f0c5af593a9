import { Request, Response, NextFunction } from "express";

export interface IUserController {
  register(req: Request, res: Response, next: NextFunction): Promise<void>;
  login(req: Request, res: Response, next: NextFunction): Promise<void>;
  googleAuth(req: Request, res: Response, next: NextFunction): Promise<void>;
  googleCallback(req: Request, res: Response, next: NextFunction): Promise<void>;
  getProfile(req: Request, res: Response, next: NextFunction): Promise<void>;
  updateProfile(req: Request, res: Response, next: NextFunction): Promise<void>;
  deleteAccount(req: Request, res: Response, next: NextFunction): Promise<void>;
  associatePlayer(req: Request, res: Response, next: NextFunction): Promise<void>;
  removePlayer(req: Request, res: Response, next: NextFunction): Promise<void>;
  verifyEmail(req: Request, res: Response, next: NextFunction): Promise<void>;
  searchAvailablePlayers(req: Request, res: Response, next: NextFunction): Promise<void>;
  getAssociatedPlayers(req: Request, res: Response, next: NextFunction): Promise<void>;
  checkPlayerOwnership(req: Request, res: Response, next: NextFunction): Promise<void>;
  checkPlayerAssociation(req: Request, res: Response, next: NextFunction): Promise<void>;
}
