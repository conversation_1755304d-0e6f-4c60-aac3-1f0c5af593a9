import { Request, Response, NextFunction } from "express";
import { inject, injectable } from "tsyringe";
import { AIService, PlayerMatchResult } from "../services/ai-service";
import { IPlayerService } from "../interfaces/player";
import logger from "../config/logger";
import { BadRequestError } from "../errors";
import { ITeamService } from "../interfaces/team";

@injectable()
export class AIController {
  constructor(
    @inject("AIService") private aiService: AIService,
    @inject("ITeamService") private teamService: ITeamService,
    @inject("IPlayerService") private playerService: IPlayerService
  ) {}

  /**
   * Analyze uploaded image and extract player stats
   */
  async analyzePlayerStatsImage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.file) {
        throw new BadRequestError("No image file uploaded");
      }

      const { buffer, mimetype } = req.file;
      const { teamId } = req.body;

      // Validate file type
      if (!mimetype.startsWith('image/')) {
        throw new BadRequestError("File must be an image");
      }

      logger.info(`Processing player stats image analysis for file type: ${mimetype}${teamId ? ` with teamId: ${teamId}` : ''}`);

      // Extract player data from image using AI
      const extractionResult = await this.aiService.extractPlayerStatsFromImage(buffer, mimetype);

      if (!extractionResult.success) {
        res.status(400).json({
          success: false,
          error: extractionResult.error || "Failed to extract player data from image"
        });
        return;
      }

      // Get players from database for matching - either from specific team or all players
      let allPlayers;
      if (teamId) {
        allPlayers = await this.teamService.getTeamPlayers(teamId);
        logger.info(`Using ${allPlayers.length} players from team ${teamId} for matching`);
      } else {
        // Fallback to all players if no teamId provided (for backward compatibility)
        allPlayers = await this.playerService.getAllPlayers();
        logger.info(`Using all ${allPlayers.length} players from database for matching`);
      }

      const playerList = allPlayers.map(player => ({
        id: player.id,
        name: player.name
      }));

      // Match extracted players with database players
      const matchedPlayers = this.aiService.matchPlayersWithDatabase(
        extractionResult.players,
        playerList
      );

      // Prepare response
      const response = {
        success: true,
        extractedCount: extractionResult.players.length,
        matchedCount: matchedPlayers.filter(p => p.matchedPlayer).length,
        players: matchedPlayers,
        message: `Successfully extracted ${extractionResult.players.length} players, matched ${matchedPlayers.filter(p => p.matchedPlayer).length} with database`
      };

      logger.info(`AI analysis complete: ${response.message}`);
      res.json(response);

    } catch (error: any) {
      logger.error("Error in AI player stats analysis:", error);
      next(error);
    }
  }

  /**
   * Get analysis status and capabilities
   */
  async getAnalysisCapabilities(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const capabilities = {
        supportedFormats: ['image/jpeg', 'image/png', 'image/webp'],
        maxFileSize: '10MB',
        extractedData: [
          'Player names',
          'Positions (GK, DEF, MID, ATT)',
          'Goals scored',
          'Assists made',
          'Player of the match indicator'
        ],
        matchingFeatures: [
          'Fuzzy name matching',
          'Partial name matching',
          'Case-insensitive matching',
          'Word-based matching'
        ]
      };

      res.json({
        success: true,
        capabilities
      });

    } catch (error: any) {
      logger.error("Error getting AI capabilities:", error);
      next(error);
    }
  }
}
