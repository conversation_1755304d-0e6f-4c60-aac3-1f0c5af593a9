import { Router } from "express";
import BracketsController from "../controllers/brackets-controller";
import { container } from "../config/container.config";

const router = Router();
const bracketsController = container.resolve(BracketsController);

// Get playoff bracket for a league
router.get("/:leagueId", (req, res, next) => bracketsController.getPlayoffBracket(req, res, next));

// Get playoff matches for a league
router.get("/:leagueId/matches", (req, res, next) => bracketsController.getPlayoffMatches(req, res, next));

export { router as bracketsRoutes };
