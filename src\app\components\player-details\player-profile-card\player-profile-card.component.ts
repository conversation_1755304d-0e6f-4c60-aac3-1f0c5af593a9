import { Component, Input, Output, EventEmitter } from '@angular/core';
import { PlayerDTO } from '@pro-clubs-manager/shared-dtos';

@Component({
  selector: 'app-player-profile-card',
  templateUrl: './player-profile-card.component.html',
  styleUrls: ['./player-profile-card.component.scss']
})
export class PlayerProfileCardComponent {
  @Input() player!: PlayerDTO;
  @Input() editPlayerMode: boolean = false;
  @Input() isViewOnly: boolean = false;
  @Input() canRemoveFromTeam: boolean = false;
  @Input() positionOptions: any[] = [];
  @Input() editedPlayerPosition: any;
  @Input() editedPlayerName: string | null = null;
  @Input() editedPlayerAge: number | null = null;
  @Input() editedPlayablePositions: string[] = [];
  @Input() isPlayerAssociated: boolean = false;

  // Image upload state
  isUploadingImage: boolean = false;
  imagePreviewUrl: string | null = null;
  uploadProgress: number = 0;

  @Output() imageUpload = new EventEmitter<Event>();
  @Output() positionChange = new EventEmitter<any>();
  @Output() nameChange = new EventEmitter<string>();
  @Output() ageChange = new EventEmitter<number>();
  @Output() playablePositionsChange = new EventEmitter<any[]>();
  @Output() removeFromTeam = new EventEmitter<void>();
  @Output() teamNavigate = new EventEmitter<void>();
  @Output() comparePlayer = new EventEmitter<void>();
  @Output() viewSeasonHistory = new EventEmitter<void>();

  onImageUpload(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file type
      if (!file.type.startsWith('image/')) {
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        return;
      }

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        this.imagePreviewUrl = e.target?.result as string;
      };
      reader.readAsDataURL(file);

      // Set uploading state
      this.isUploadingImage = true;
      this.uploadProgress = 0;

      // Simulate upload progress (you can replace this with actual progress tracking)
      this.simulateUploadProgress();
    }

    this.imageUpload.emit(event);
  }

  private simulateUploadProgress(): void {
    const interval = setInterval(() => {
      this.uploadProgress += Math.random() * 30;
      if (this.uploadProgress >= 100) {
        this.uploadProgress = 100;
        clearInterval(interval);
        // Keep loading state until actual upload completes
      }
    }, 200);
  }

  onUploadSuccess(): void {
    this.isUploadingImage = false;
    this.uploadProgress = 100;
    // Clear preview after successful upload
    setTimeout(() => {
      this.imagePreviewUrl = null;
      this.uploadProgress = 0;
    }, 1000);
  }

  onUploadError(): void {
    this.isUploadingImage = false;
    this.imagePreviewUrl = null;
    this.uploadProgress = 0;
  }

  getCurrentImageUrl(): string {
    return this.imagePreviewUrl || this.player.imgUrl || 'assets/Icons/User.jpg';
  }

  onPositionChange(position: any): void {
    this.positionChange.emit(position);
  }

  onNameChange(name: string): void {
    this.nameChange.emit(name);
  }

  onAgeChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const age = parseInt(target.value, 10);
    if (!isNaN(age) && age >= 16 && age <= 50) {
      this.ageChange.emit(age);
    }
  }

  onPlayablePositionsChange(positions: any): void {
    this.playablePositionsChange.emit(positions);
  }

  getSelectedPlayablePositions(): any[] {
    return this.positionOptions.filter(option =>
      this.editedPlayablePositions.includes(option.value)
    );
  }

  onRemoveFromTeam(): void {
    this.removeFromTeam.emit();
  }

  onTeamNavigate(): void {
    this.teamNavigate.emit();
  }

  onComparePlayer(): void {
    this.comparePlayer.emit();
  }

  onViewSeasonHistory(): void {
    this.viewSeasonHistory.emit();
  }

  getDefaultTeamImage(): string {
    return 'assets/Icons/TeamLogo.jpg';
  }
}
