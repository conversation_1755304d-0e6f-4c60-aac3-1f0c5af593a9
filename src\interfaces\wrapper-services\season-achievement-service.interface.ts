import { ClientSession } from "mongoose";
import { ISeasonAchievements, IPlayerAchievement, ITeamAchievement, ACHIEVEMENT_TYPE } from "../../models/season-achievement";

export interface SeasonEndRequest {
  leagueId: string;
  seasonNumber: number;
  endDate?: Date;
  forceEnd?: boolean; // Allow ending even if not all games are complete
}

export interface SeasonEndPreview {
  seasonNumber: number;
  leagueId: string;
  leagueName: string;
  isSeasonComplete: boolean;
  totalGames: number;
  completedGames: number;
  remainingGames: number;
  champion?: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
  };
  finalist?: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
  };
  thirdPlace?: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
  };
  topScorers: Array<{
    playerId: string;
    playerName: string;
    playerImgUrl?: string;
    teamName: string;
    goals: number;
    rank: number;
  }>;
  topAssists: Array<{
    playerId: string;
    playerName: string;
    playerImgUrl?: string;
    teamName: string;
    assists: number;
    rank: number;
  }>;
  bestByPosition: Array<{
    position: string;
    playerId: string;
    playerName: string;
    playerImgUrl?: string;
    teamName: string;
    avgRating: number;
    games: number;
  }>;
  warnings: string[]; // Any issues that should be noted
}

export interface SeasonEndResult {
  success: boolean;
  seasonAchievements: ISeasonAchievements;
  playersUpdated: number;
  teamsUpdated: number;
  message: string;
}

export interface PlayerAchievementHistory {
  playerId: string;
  achievements: Array<{
    seasonNumber: number;
    leagueName: string;
    achievementType: string;
    rank?: number;
    description?: string;
    year: number;
  }>;
}

export interface TeamAchievementHistory {
  teamId: string;
  achievements: Array<{
    seasonNumber: number;
    leagueName: string;
    achievementType: string;
    rank?: number;
    description?: string;
    year: number;
  }>;
}

export interface ISeasonAchievementService {
  /**
   * Preview what achievements would be recorded if season ended now
   */
  previewSeasonEnd(leagueId: string, seasonNumber?: number): Promise<SeasonEndPreview>;

  /**
   * Manually end a season and record all achievements
   */
  endSeason(request: SeasonEndRequest, adminUserId: string, session?: ClientSession): Promise<SeasonEndResult>;

  /**
   * Get all season achievements for a league
   */
  getLeagueSeasonAchievements(leagueId: string): Promise<ISeasonAchievements[]>;

  /**
   * Get season achievements for a specific season
   */
  getSeasonAchievements(leagueId: string, seasonNumber: number): Promise<ISeasonAchievements | null>;

  /**
   * Get achievement history for a player
   */
  getPlayerAchievementHistory(playerId: string): Promise<PlayerAchievementHistory>;

  /**
   * Get achievement history for a team
   */
  getTeamAchievementHistory(teamId: string): Promise<TeamAchievementHistory>;

  /**
   * Check if a season can be ended (all games complete or force allowed)
   */
  canEndSeason(leagueId: string, seasonNumber: number): Promise<{ canEnd: boolean; reason?: string }>;

  /**
   * Calculate top performers for a season
   */
  calculateSeasonTopPerformers(leagueId: string, seasonNumber: number): Promise<{
    topScorers: IPlayerAchievement[];
    topAssists: IPlayerAchievement[];
    bestByPosition: IPlayerAchievement[];
  }>;

  /**
   * Calculate championship results from playoff bracket
   */
  calculateChampionshipResults(leagueId: string, seasonNumber: number): Promise<{
    champion?: ITeamAchievement;
    finalist?: ITeamAchievement;
    thirdPlace?: ITeamAchievement;
  }>;

  /**
   * Manually add an achievement to a player
   */
  addManualPlayerAchievement(
    playerId: string,
    achievementData: {
      seasonNumber: number;
      achievementType: ACHIEVEMENT_TYPE;
      rank?: number;
      stats?: {
        goals?: number;
        assists?: number;
        cleanSheets?: number;
        avgRating?: number;
        games?: number;
        playerOfTheMatch?: number;
      };
      description?: string;
    },
    adminUserId: string,
    session?: ClientSession
  ): Promise<{ success: boolean; message: string }>;
}
