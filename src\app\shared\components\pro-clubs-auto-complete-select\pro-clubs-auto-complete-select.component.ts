import { ChangeDetectorRef, Component, EventEmitter, Input, Output, SimpleChanges, OnChanges } from '@angular/core';
import { ListOption } from '../../models/list-option.model';
import { Observable, map, startWith } from 'rxjs';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'pro-clubs-auto-complete-select',
  templateUrl: './pro-clubs-auto-complete-select.component.html',
  styleUrl: './pro-clubs-auto-complete-select.component.scss'
})
export class ProClubsAutoCompleteSelectComponent implements OnChanges {
  @Input() selectOptions: ListOption[] = [];
  @Input() placeholder: string = '';
  @Input() set defaultOption(defaultOption: any) {
    this._defaultOption = defaultOption;
    this.setSelectedInput(); // change name
  };

  @Input() width?: string;

  private _defaultOption?: any;
  selectedOption = new FormControl(this._defaultOption);
  filteredOptions?: Observable<string[]>;
  selectOptionsTextOnly: string[] = [];
  isFocused: boolean = false;

  @Output() selectionChange = new EventEmitter<ListOption>();

  constructor(private cdRef: ChangeDetectorRef) { }

  ngOnInit() {
    this.initializeOptions();
    this.setSelectedInput();

    // Subscribe to form control value changes to handle input changes
    this.selectedOption.valueChanges.subscribe(value => {
      if (value !== null && value !== undefined) {
        this.onInputChange(value);
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['selectOptions'] && !changes['selectOptions'].firstChange) {
      this.initializeOptions();
      // Re-apply the default option when select options change
      this.setSelectedInput();
      this.cdRef.detectChanges();
    }
  }

  initializeOptions() {
    // Check if selectOptions is defined and is an array
    if (this.selectOptions && Array.isArray(this.selectOptions)) {
      this.selectOptionsTextOnly = this.selectOptions.map(x => x.displayText);
      this.filteredOptions = this.selectedOption.valueChanges.pipe(
        startWith(''),
        map(value => this._filter(value || '')),
      );
    } else {
      // Initialize with empty arrays if selectOptions is undefined
      this.selectOptionsTextOnly = [];
      this.filteredOptions = this.selectedOption.valueChanges.pipe(
        startWith(''),
        map(value => []),
      );
    }
  }

  setSelectedInput() {
    if (this._defaultOption && this.selectOptions.length > 0) {
      const selectedOption = this.selectOptions.find(option => option.value === this._defaultOption);
      this.selectedOption.setValue(selectedOption ? selectedOption.displayText : '');
      this.selectionChange.emit(selectedOption);
    }
  }

  // view --> model
  registerOnChange(fn: (value: string) => void) {
    this.onChanged = fn;

    this.selectedOption.valueChanges.pipe(
      startWith(''),
      map((value: any) => this._filter(value || '')),
    );
  }

  private _filter(value: string): string[] {
    const filterValue = value.toLowerCase();

    return this.selectOptionsTextOnly.filter(option => option.toLowerCase().includes(filterValue));
  }

  onInputChange(value: any) {
    var selectedValue = this.selectOptions.find(x => x.displayText == value);
    this.selectionChange.emit(selectedValue);

    // The FormControl will handle the input value display

    if (this.onChanged)
      this.onChanged(value);
    this.cdRef.detectChanges();
  }

  onFocus() {
    this.isFocused = true;
  }

  onBlur() {
    this.isFocused = false;
  }

  clearInput() {

    this.selectedOption.setValue('');
    // Emit null/undefined to indicate no selection
    this.selectionChange.emit(undefined);

    if (this.onChanged) {
      this.onChanged('');
    }

    // Trigger change detection
    this.cdRef.detectChanges();
  }

  onChanged?: (val: any) => void;
}
