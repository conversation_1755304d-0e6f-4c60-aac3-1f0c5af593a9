import { Injectable } from '@angular/core';
import { Subject, timer, merge } from 'rxjs';
import { debounceTime, switchMap, takeUntil } from 'rxjs/operators';
import { LeagueService } from './league.service';
import { LeagueDataStateService } from './state/league-data-state.service';
import { LEAGUE_ID } from '../constants/constants';

export interface SyncRequest {
  type: 'league-table' | 'top-scorers' | 'top-assisters' | 'all';
  priority: 'high' | 'normal' | 'low';
  gameId?: string;
}

@Injectable({
  providedIn: 'root'
})
export class BackgroundSyncService {
  private syncQueue$ = new Subject<SyncRequest>();
  private destroy$ = new Subject<void>();
  private isProcessing = false;
  private syncHistory = new Map<string, Date>();

  // Debounce times for different sync types (in milliseconds)
  private readonly SYNC_DEBOUNCE_TIMES = {
    'league-table': 2000,    // 2 seconds
    'top-scorers': 5000,     // 5 seconds
    'top-assisters': 5000,   // 5 seconds
    'all': 3000              // 3 seconds
  };

  // Minimum intervals between syncs for the same type (in milliseconds)
  private readonly MIN_SYNC_INTERVALS = {
    'league-table': 30000,   // 30 seconds
    'top-scorers': 60000,    // 1 minute
    'top-assisters': 60000,  // 1 minute
    'all': 60000             // 1 minute
  };

  constructor(
    private leagueService: LeagueService,
    private leagueDataState: LeagueDataStateService
  ) {
    this.initializeSyncProcessor();
  }

  private initializeSyncProcessor(): void {
    // Process sync requests with debouncing and priority handling
    this.syncQueue$.pipe(
      debounceTime(1000), // General debounce to batch requests
      switchMap(request => this.processSyncRequest(request)),
      takeUntil(this.destroy$)
    ).subscribe({
      next: (result) => {
        console.log('Background sync completed:', result);
      },
      error: (error) => {
        console.error('Background sync error:', error);
        this.isProcessing = false;
      }
    });
  }

  /**
   * Queue a sync request for background processing
   */
  requestSync(request: SyncRequest): void {
    // Check if we should skip this sync based on recent history
    if (this.shouldSkipSync(request)) {
      console.log(`Skipping sync for ${request.type} - too recent`);
      return;
    }

    console.log(`Queuing background sync: ${request.type} (priority: ${request.priority})`);
    this.syncQueue$.next(request);
  }

  /**
   * Request sync after game result update
   */
  syncAfterGameUpdate(gameId: string): void {
    // High priority sync for league table since game results directly affect standings
    this.requestSync({
      type: 'league-table',
      priority: 'high',
      gameId
    });

    // Normal priority sync for scoring stats
    setTimeout(() => {
      this.requestSync({
        type: 'top-scorers',
        priority: 'normal',
        gameId
      });
    }, 2000);

    setTimeout(() => {
      this.requestSync({
        type: 'top-assisters',
        priority: 'normal',
        gameId
      });
    }, 3000);
  }

  /**
   * Request periodic background refresh
   */
  schedulePeriodicSync(): void {
    // Schedule periodic syncs every 5 minutes for league table
    timer(0, 5 * 60 * 1000).pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      if (!this.isProcessing) {
        this.requestSync({
          type: 'league-table',
          priority: 'low'
        });
      }
    });

    // Schedule periodic syncs every 15 minutes for other data
    timer(0, 15 * 60 * 1000).pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      if (!this.isProcessing) {
        this.requestSync({
          type: 'top-scorers',
          priority: 'low'
        });

        setTimeout(() => {
          this.requestSync({
            type: 'top-assisters',
            priority: 'low'
          });
        }, 5000);
      }
    });
  }

  private async processSyncRequest(request: SyncRequest): Promise<string> {
    if (this.isProcessing) {
      return 'Sync already in progress';
    }

    this.isProcessing = true;
    this.syncHistory.set(request.type, new Date());

    try {
      switch (request.type) {
        case 'league-table':
          await this.syncLeagueTable();
          break;
        case 'top-scorers':
          await this.syncTopScorers();
          break;
        case 'top-assisters':
          await this.syncTopAssisters();
          break;
        case 'all':
          await this.syncAll();
          break;
        default:
          throw new Error(`Unknown sync type: ${request.type}`);
      }

      return `Successfully synced ${request.type}`;
    } catch (error) {
      console.error(`Failed to sync ${request.type}:`, error);
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  private async syncLeagueTable(): Promise<void> {
    try {
      console.log('Background sync: Updating league table...');
      await this.leagueService.getLeagueTable(LEAGUE_ID, true);
      console.log('Background sync: League table updated successfully');
    } catch (error) {
      console.error('Background sync: Failed to update league table:', error);
      throw error;
    }
  }

  private async syncTopScorers(): Promise<void> {
    try {
      console.log('Background sync: Updating top scorers...');
      await this.leagueService.getTopScorers(LEAGUE_ID, null, true);
      console.log('Background sync: Top scorers updated successfully');
    } catch (error) {
      console.error('Background sync: Failed to update top scorers:', error);
      throw error;
    }
  }

  private async syncTopAssisters(): Promise<void> {
    try {
      console.log('Background sync: Updating top assisters...');
      await this.leagueService.getTopAssists(LEAGUE_ID, null, true);
      console.log('Background sync: Top assisters updated successfully');
    } catch (error) {
      console.error('Background sync: Failed to update top assisters:', error);
      throw error;
    }
  }

  private async syncAll(): Promise<void> {
    console.log('Background sync: Updating all data...');

    // Sync in order of priority
    await this.syncLeagueTable();

    // Add small delays between syncs to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 1000));
    await this.syncTopScorers();

    await new Promise(resolve => setTimeout(resolve, 1000));
    await this.syncTopAssisters();

    console.log('Background sync: All data updated successfully');
  }

  private shouldSkipSync(request: SyncRequest): boolean {
    const lastSync = this.syncHistory.get(request.type);
    if (!lastSync) return false;

    const minInterval = this.MIN_SYNC_INTERVALS[request.type];
    const timeSinceLastSync = Date.now() - lastSync.getTime();

    // Always allow high priority syncs
    if (request.priority === 'high') return false;

    return timeSinceLastSync < minInterval;
  }

  /**
   * Force sync all data (ignores intervals and debouncing)
   */
  async forceSyncAll(): Promise<void> {
    if (this.isProcessing) {
      console.log('Sync already in progress, skipping force sync');
      return;
    }

    try {
      await this.syncAll();
    } catch (error) {
      console.error('Force sync failed:', error);
      throw error;
    }
  }

  /**
   * Get sync status
   */
  getSyncStatus(): { isProcessing: boolean; lastSyncs: Record<string, Date | null> } {
    const lastSyncs: Record<string, Date | null> = {};

    ['league-table', 'top-scorers', 'top-assisters'].forEach(type => {
      lastSyncs[type] = this.syncHistory.get(type) || null;
    });

    return {
      isProcessing: this.isProcessing,
      lastSyncs
    };
  }

  destroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
