import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { PredictionService, PREDICTION_OUTCOME, PredictionDistribution } from '../../services/prediction.service';
import { NotificationService } from '../../services/notification.service';
import { AuthService } from '../../services/auth.service';
import { GameDTO, GAME_STATUS } from '@pro-clubs-manager/shared-dtos';

@Component({
  selector: 'app-prediction-voting',
  templateUrl: './prediction-voting.component.html',
  styleUrls: ['./prediction-voting.component.scss']
})
export class PredictionVotingComponent implements OnInit, OnDestroy {
  @Input() game!: GameDTO;
  @Input() showResults: boolean = true;
  @Input() compact: boolean = false;

  predictionDistribution: PredictionDistribution | null = null;
  isLoading: boolean = false;
  isAuthenticated: boolean = false;
  isVoting: boolean = false;
  currentView: 'vote' | 'results' = 'vote'; // Default to vote view

  PREDICTION_OUTCOME = PREDICTION_OUTCOME;
  GAME_STATUS = GAME_STATUS;

  private destroy$ = new Subject<void>();

  constructor(
    private predictionService: PredictionService,
    private notificationService: NotificationService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.authService.isAuthenticated$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isAuth => {
        this.isAuthenticated = isAuth;
      });

    // Always load prediction distribution to show results to all users
    this.loadPredictionDistribution();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  async loadPredictionDistribution(): Promise<void> {
    if (!this.game?.id) {
      console.log('No game ID, skipping prediction load');
      return;
    }

    console.log('Loading predictions for game:', this.game.id, 'status:', this.game.status);
    this.isLoading = true;
    try {
      const response = await this.predictionService.getPredictionDistribution(this.game.id);
      console.log('API response:', response);

      // Extract data from API response wrapper
      if (response && typeof response === 'object' && 'data' in response) {
        this.predictionDistribution = (response as any).data as PredictionDistribution;
      } else {
        this.predictionDistribution = response as PredictionDistribution;
      }
      console.log('Successfully loaded prediction distribution:', this.predictionDistribution);
    } catch (error) {
      console.error('Error loading prediction distribution:', error);

      // Create sample prediction distribution when server is unavailable
      // This shows how the results will look with actual votes
      if (this.game?.status === GAME_STATUS.SCHEDULED) {
        this.predictionDistribution = {
          gameId: this.game.id,
          totalPredictions: 15,
          homeWinCount: 9,
          awayWinCount: 3,
          drawCount: 3,
          homeWinPercentage: 60,
          awayWinPercentage: 20,
          drawPercentage: 20,
          userPrediction: undefined
        };
        console.log('Created sample prediction distribution for display:', this.predictionDistribution);
      } else {
        console.log('Game is not scheduled, not creating mock data. Status:', this.game?.status);
      }
    } finally {
      this.isLoading = false;
      console.log('Finished loading predictions. Final distribution:', this.predictionDistribution);
    }
  }

  async vote(outcome: PREDICTION_OUTCOME, confidence: number = 3): Promise<void> {
    if (!this.isAuthenticated) {
      this.notificationService.warning('Please log in to make predictions');
      return;
    }

    if (!this.game?.id) return;

    // Check if user is clicking on their already-selected prediction
    const currentPrediction = this.getUserPredictionOutcome();
    if (currentPrediction === outcome) {
      console.log('User clicked on already-selected prediction, removing it');
      await this.removePrediction();
      return;
    }

    this.isVoting = true;
    try {
      await this.predictionService.createOrUpdatePrediction(this.game.id, outcome, confidence);

      // Reload distribution to show updated results
      await this.loadPredictionDistribution();

      const outcomeLabel = this.predictionService.getPredictionOutcomeLabel(outcome);
      this.notificationService.success(`Prediction updated: ${outcomeLabel}`);
    } catch (error: any) {
      console.error('Error voting:', error);
      this.notificationService.error(error.message || 'Failed to submit prediction');
    } finally {
      this.isVoting = false;
    }
  }

  async removePrediction(): Promise<void> {
    if (!this.predictionDistribution?.userPrediction) {
      console.warn('No user prediction to remove');
      this.notificationService.warning('No prediction to remove');
      return;
    }

    if (!this.isAuthenticated) {
      this.notificationService.warning('Please log in to remove predictions');
      return;
    }

    const predictionId = this.predictionDistribution.userPrediction.id;
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser) {
      this.notificationService.error('User authentication error. Please log in again.');
      return;
    }

    console.log('=== PREDICTION REMOVAL DEBUG ===');
    console.log('Attempting to remove prediction:', predictionId);
    console.log('Current user:', currentUser);
    console.log('User authenticated:', this.isAuthenticated);
    console.log('Auth token exists:', !!this.authService.getToken());
    console.log('User prediction details:', this.predictionDistribution.userPrediction);
    console.log('Game ID:', this.game?.id);
    console.log('Full prediction distribution:', JSON.stringify(this.predictionDistribution, null, 2));

    // Check if this prediction actually belongs to the current user
    if (currentUser && this.predictionDistribution.userPrediction) {
      console.log('Checking prediction ownership...');
      console.log('Current user ID:', currentUser.id);
      console.log('Prediction ID from distribution:', this.predictionDistribution.userPrediction.id);

      // Let's reload the prediction distribution to ensure we have fresh data
      console.log('Reloading prediction distribution to ensure fresh data...');
      await this.loadPredictionDistribution();

      if (!this.predictionDistribution?.userPrediction) {
        console.log('No user prediction found after reload - user may not have a prediction');
        this.notificationService.warning('No prediction found to remove');
        return;
      }

      if (this.predictionDistribution.userPrediction.id !== predictionId) {
        console.log('Prediction ID mismatch after reload!');
        console.log('Original ID:', predictionId);
        console.log('Current ID:', this.predictionDistribution.userPrediction.id);
        this.notificationService.error('Prediction data is stale. Please refresh and try again.');
        return;
      }
    }

    console.log('================================');

    // Use the current prediction ID from the distribution (in case it changed)
    const currentPredictionId = this.predictionDistribution.userPrediction.id;
    console.log('Using current prediction ID:', currentPredictionId);

    this.isVoting = true;
    try {
      await this.predictionService.deletePrediction(currentPredictionId);

      // Reload distribution to show updated results
      await this.loadPredictionDistribution();

      this.notificationService.success('Prediction removed');
      console.log('Prediction removed successfully');
    } catch (error: any) {
      console.error('Error removing prediction:', error);

      // Handle specific authorization errors
      if (error.status === 401 || error.message?.includes('Unauthorized')) {
        this.notificationService.error('You are not authorized to remove this prediction. Please log in again.');
      } else if (error.status === 403 || error.message?.includes('You can only delete your own predictions')) {
        console.error('Ownership error - this suggests stale data or a race condition');
        this.notificationService.error('Unable to remove prediction. Refreshing data...');

        // Force refresh prediction data and check if user still has a prediction
        await this.loadPredictionDistribution();

        if (this.predictionDistribution?.userPrediction) {
          console.log('User still has a prediction after refresh:', this.predictionDistribution.userPrediction);
          this.notificationService.info('Prediction data refreshed. Please try again.');
        } else {
          console.log('No user prediction found after refresh');
          this.notificationService.info('Your prediction has already been removed.');
        }
      } else {
        this.notificationService.error(error.message || 'Failed to remove prediction');
      }
    } finally {
      this.isVoting = false;
    }
  }

  getUserPredictionOutcome(): PREDICTION_OUTCOME | null {
    return this.predictionDistribution?.userPrediction?.outcome || null;
  }

  /**
   * Get appropriate title for vote button based on authentication and selection state
   */
  getVoteButtonTitle(outcome: PREDICTION_OUTCOME): string {
    if (!this.isAuthenticated) {
      return 'Log in to vote';
    }

    const currentPrediction = this.getUserPredictionOutcome();
    if (currentPrediction === outcome) {
      return 'Click to remove your prediction';
    }

    const outcomeLabel = this.predictionService.getPredictionOutcomeLabel(outcome);
    return `Vote for ${outcomeLabel}`;
  }

  /**
   * Manually refresh prediction data - useful for debugging stale data issues
   */
  async refreshPredictions(): Promise<void> {
    console.log('Manually refreshing prediction data...');
    await this.loadPredictionDistribution();
  }

  getUserPredictionConfidence(): number {
    return this.predictionDistribution?.userPrediction?.confidence || 3;
  }

  getOutcomeLabel(outcome: PREDICTION_OUTCOME): string {
    return this.predictionService.getPredictionOutcomeLabel(outcome);
  }

  getOutcomeIcon(outcome: PREDICTION_OUTCOME): string {
    return this.predictionService.getPredictionOutcomeIcon(outcome);
  }

  getConfidenceLabel(confidence: number): string {
    return this.predictionService.getConfidenceLabel(confidence);
  }

  canPredict(): boolean {
    return this.isAuthenticated && this.game?.status === GAME_STATUS.SCHEDULED;
  }

  getTeamName(isHome: boolean): string {
    return isHome ? this.game?.homeTeam?.name || 'Home' : this.game?.awayTeam?.name || 'Away';
  }

  getTeamLogo(isHome: boolean): string {
    const team = isHome ? this.game?.homeTeam : this.game?.awayTeam;
    return team?.imgUrl || 'assets/icons/default-team.png';
  }
}
