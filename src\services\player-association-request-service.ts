import { injectable, inject } from "tsyringe";
import { Types, ClientSession } from "mongoose";
import { IPlayerAssociationRequestRepository } from "../repositories/player-association-request-repository";
import { IUserRepository } from "../interfaces/user";
import { IPlayerService } from "../interfaces/player";
import logger from "../config/logger";
import CustomError from "../errors/custom-error";
import { BadRequestError, NotFoundError } from "../errors";
import { IPlayerAssociationRequest } from "../models/player-association-request";
import { transactionService } from "./util-services/transaction-service";

export interface IPlayerAssociationRequestService {
  createRequest(userId: string, playerId?: string, playerEmail?: string, userMessage?: string): Promise<IPlayerAssociationRequest>;
  getUserRequests(userId: string): Promise<IPlayerAssociationRequest[]>;
  getPendingRequests(): Promise<IPlayerAssociationRequest[]>;
  approveRequest(requestId: string, adminId: string): Promise<IPlayerAssociationRequest>;
  rejectRequest(requestId: string, adminId: string, reason?: string): Promise<IPlayerAssociationRequest>;
  cancelRequest(requestId: string, userId: string): Promise<void>;
}

@injectable()
export class PlayerAssociationRequestService implements IPlayerAssociationRequestService {
  constructor(
    @inject("IPlayerAssociationRequestRepository") private requestRepository: IPlayerAssociationRequestRepository,
    @inject("IUserRepository") private userRepository: IUserRepository,
    @inject("IPlayerService") private playerService: IPlayerService
  ) {}

  async createRequest(userId: string, playerId?: string, playerEmail?: string, userMessage?: string): Promise<IPlayerAssociationRequest> {
    try {
      // Validate user exists
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new NotFoundError("User not found");
      }

      // Check if user already has an associated player
      if (user.associatedPlayers.length > 0) {
        throw new BadRequestError("You can only associate one player per account");
      }

      let targetPlayerId: string;

      // Find player by ID or email
      if (playerId) {
        const player = await this.playerService.getPlayerById(playerId);
        if (!player) {
          throw new NotFoundError("Player not found");
        }
        targetPlayerId = player.id;
      } else if (playerEmail) {
        const player = await this.playerService.getPlayerByEmail(playerEmail);
        if (!player) {
          throw new NotFoundError("Player with this email not found");
        }
        targetPlayerId = player.id;
      } else {
        throw new BadRequestError("Either playerId or playerEmail must be provided");
      }

      // Check if player is already associated with any user
      const allUsers = await this.userRepository.findAll();
      const isAlreadyAssociated = allUsers.some(u =>
        u.associatedPlayers.some(id => id.toString() === targetPlayerId)
      );

      if (isAlreadyAssociated) {
        throw new BadRequestError("This player is already associated with another user");
      }

      // Check if there's already a pending request for this player by this user
      const existingRequest = await this.requestRepository.findByUserAndPlayer(userId, targetPlayerId);
      if (existingRequest) {
        throw new BadRequestError("You already have a pending request for this player");
      }

      // Create the request
      const request = await this.requestRepository.create({
        userId: new Types.ObjectId(userId),
        playerId: new Types.ObjectId(targetPlayerId),
        playerEmail,
        userMessage
      });

      logger.info(`Player association request created: User ${userId} requesting player ${targetPlayerId}`);
      return request;

    } catch (error: any) {
      logger.error(`Error creating player association request: ${error.message}`);
      throw error;
    }
  }

  async getUserRequests(userId: string): Promise<IPlayerAssociationRequest[]> {
    try {
      return await this.requestRepository.findByUserId(userId);
    } catch (error: any) {
      logger.error(`Error getting user requests: ${error.message}`);
      throw error;
    }
  }

  async getPendingRequests(): Promise<IPlayerAssociationRequest[]> {
    try {
      return await this.requestRepository.findPendingRequests();
    } catch (error: any) {
      logger.error(`Error getting pending requests: ${error.message}`);
      throw error;
    }
  }

  async approveRequest(requestId: string, adminId: string): Promise<IPlayerAssociationRequest> {
    try {
      logger.info(`Starting approval process for request ${requestId} by admin ${adminId}`);

      // Get the request (without population to avoid casting issues)
      const request = await this.requestRepository.findByIdRaw(requestId);
      if (!request) {
        throw new NotFoundError("Request not found");
      }

      if (request.status !== 'pending') {
        throw new BadRequestError("Request has already been processed");
      }

      logger.info(`Found pending request for user ${request.userId} and player ${request.playerId}`);

      // Get user and verify they don't already have a player
      const userId = request.userId.toString();
      logger.info(`User ID: ${userId}`);

      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new NotFoundError("User not found");
      }

      if (user.associatedPlayers.length > 0) {
        throw new BadRequestError("User already has an associated player");
      }

      logger.info(`User ${user.id} is eligible for player association`);

      // Verify the player exists
      const playerId = request.playerId.toString();
      logger.info(`Player ID: ${playerId}`);

      const player = await this.playerService.getPlayerById(playerId);
      if (!player) {
        throw new NotFoundError("Player not found");
      }

      logger.info(`Player ${player.id} found, proceeding with association`);

      // Use transaction to ensure data consistency
      const updatedRequest = await transactionService.withTransaction(async (session: ClientSession) => {
        // Associate the player with the user
        user.associatedPlayers.push(new Types.ObjectId(playerId));
        await user.save({ session });

        logger.info(`User ${user.id} successfully associated with player ${player.id}`);

        // Update request status
        const updatedRequest = await this.requestRepository.updateStatus(
          requestId,
          'approved',
          new Types.ObjectId(adminId),
          undefined,
          session
        );

        return updatedRequest;
      });

      logger.info(`Player association request approved: User ${request.userId} now associated with player ${request.playerId}`);
      return updatedRequest;

    } catch (error: any) {
      logger.error(`Error approving player association request ${requestId}: ${error.message}`, error);
      throw error;
    }
  }

  async rejectRequest(requestId: string, adminId: string, reason?: string): Promise<IPlayerAssociationRequest> {
    try {
      const request = await this.requestRepository.findById(requestId);
      if (!request) {
        throw new NotFoundError("Request not found");
      }

      if (request.status !== 'pending') {
        throw new BadRequestError("Request has already been processed");
      }

      const updatedRequest = await this.requestRepository.updateStatus(
        requestId,
        'rejected',
        new Types.ObjectId(adminId),
        reason
      );

      logger.info(`Player association request rejected: Request ${requestId} by admin ${adminId}`);
      return updatedRequest;

    } catch (error: any) {
      logger.error(`Error rejecting player association request: ${error.message}`);
      throw error;
    }
  }

  async cancelRequest(requestId: string, userId: string): Promise<void> {
    try {
      const request = await this.requestRepository.findById(requestId);
      if (!request) {
        throw new NotFoundError("Request not found");
      }

      if (request.userId.toString() !== userId) {
        throw new BadRequestError("You can only cancel your own requests");
      }

      if (request.status !== 'pending') {
        throw new BadRequestError("Only pending requests can be cancelled");
      }

      await this.requestRepository.deleteById(requestId);
      logger.info(`Player association request cancelled: Request ${requestId} by user ${userId}`);

    } catch (error: any) {
      logger.error(`Error cancelling player association request: ${error.message}`);
      throw error;
    }
  }
}
