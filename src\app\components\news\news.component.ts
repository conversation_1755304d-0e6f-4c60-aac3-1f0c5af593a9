import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { News } from './news.model';
import { NewsService } from '../../services/news.service';
import { NotificationService } from '../../services/notification.service';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { NewsStateService, NewsState } from '../../services/state/news-state.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'news',
  templateUrl: './news.component.html',
  styleUrl: './news.component.scss'
})
export class NewsComponent implements OnInit, OnDestroy {
  isLoading: boolean = false;
  allNews: Array<News> = [];
  filteredNews: Array<News> = [];
  selectedIndex = 0;
  tabTypeMap = ['all', 'General', 'Transfer', 'FreeAgent'];
  private stateSubscription?: Subscription;

  // Share functionality properties
  showShareModal: boolean = false;
  selectedNewsForShare: News | null = null;

  // Edit functionality properties
  showEditModal: boolean = false;
  selectedNewsForEdit: News | null = null;

  constructor(private newsService: NewsService,
    private router: Router,
    private notificationService: NotificationService,
    private authService: AuthService,
    private newsStateService: NewsStateService) { }

  ngOnInit() {
    // Subscribe to state changes
    this.stateSubscription = this.newsStateService.state$.subscribe(
      (state: NewsState) => {
        this.isLoading = state.isNewsLoading;
        // Use news directly from state (already sorted newest first from server)
        this.allNews = [...state.news];
        this.filterNews();
      }
    );

    // Only load news if cache is stale or empty
    if (this.newsStateService.isNewsStale() || this.newsStateService.currentState.news.length === 0) {
      this.loadNews();
    }
  }

  ngOnDestroy(): void {
    this.stateSubscription?.unsubscribe();
  }

  async loadNews() {
    try {
      this.newsStateService.setNewsLoading(true);
      const newsData = await this.newsService.getAllNews();

      // Update state with full News objects (preserving transferData and freeAgentData)
      this.newsStateService.updateNews(newsData);

    } catch (error) {
      console.error('Error loading news:', error);
      this.newsStateService.setNewsLoading(false);
    }
  }

  async refreshNews() {
    this.newsStateService.forceRefresh();
    await this.loadNews();
  }

  onTabChange(index: number) {
    this.selectedIndex = index;
    this.filterNews();
  }

  filterNews() {
    const type = this.tabTypeMap[this.selectedIndex];
    this.filteredNews =
      type === 'all' ? this.allNews : this.allNews.filter(n => n.type === type);
  }

  async onDeleteNewsClick(newsToRemove: News) {
    if (!newsToRemove) {
      return;
    }

    this.isLoading = true;

    try {
      await this.newsService.deleteNews(newsToRemove._id);
      this.notificationService.success('News removed successfully.');
      this.loadNews();
    } catch (error) {
      console.error('Error deleting news:', error);
      this.notificationService.success('Error removing news. Please try again.');
    } finally {
      this.isLoading = false;
    }
  }

  isAdmin() {
    return this.authService.isAdmin();
  }

  onTeamClick(teamId: string): void {
    if (!teamId) {
      return;
    };
    
    this.router.navigate(['/team-details', { id: teamId }])
  }

  onPlayerClick(playerId: string): void {
    this.router.navigate(['/player-details', { id: playerId }]);
  }

  async onLikeNewsClick(news: News): Promise<void> {
    if (!this.authService.isAuthenticated()) {
      this.notificationService.info('Please log in to like news');
      return;
    }

    try {
      const updatedNews = await this.newsService.likeNews(news._id);

      // Update the news in our local arrays
      this.updateNewsInArrays(updatedNews);

      this.notificationService.success('News liked!');
    } catch (error: any) {
      console.error('Error liking news:', error);
      this.notificationService.error('Failed to like news. Please try again.');
    }
  }

  async onUnlikeNewsClick(news: News): Promise<void> {
    if (!this.authService.isAuthenticated()) {
      return;
    }

    try {
      const updatedNews = await this.newsService.unlikeNews(news._id);

      // Update the news in our local arrays
      this.updateNewsInArrays(updatedNews);

      this.notificationService.success('News unliked!');
    } catch (error: any) {
      console.error('Error unliking news:', error);
      this.notificationService.error('Failed to unlike news. Please try again.');
    }
  }

  private updateNewsInArrays(updatedNews: News): void {
    // Update in allNews array
    const allNewsIndex = this.allNews.findIndex(n => n._id === updatedNews._id);
    if (allNewsIndex !== -1) {
      this.allNews[allNewsIndex] = updatedNews;
    }

    // Update in filteredNews array
    const filteredNewsIndex = this.filteredNews.findIndex(n => n._id === updatedNews._id);
    if (filteredNewsIndex !== -1) {
      this.filteredNews[filteredNewsIndex] = updatedNews;
    }

    // Update state service using updateNewsItem to avoid order issues
    // This method updates only the specific news item without affecting the array order
    this.newsStateService.updateNewsItem(updatedNews);
  }

  // Edit functionality methods
  onEditNewsClick(news: News): void {
    this.selectedNewsForEdit = news;
    this.showEditModal = true;
  }

  closeEditModal(): void {
    this.showEditModal = false;
    this.selectedNewsForEdit = null;
  }

  async onSaveEditedNews(event: { newsId: string, newsData: any }): Promise<void> {
    try {
      const updatedNews = await this.newsService.updateNews(event.newsId, event.newsData);

      // Update the news in our local arrays
      this.updateNewsInArrays(updatedNews);

      this.notificationService.success('News updated successfully!');
      this.closeEditModal();
    } catch (error: any) {
      console.error('Error updating news:', error);
      this.notificationService.error('Failed to update news. Please try again.');
    }
  }

  // === SHARING FUNCTIONALITY === //

  onShareNewsClick(news: News): void {
    this.selectedNewsForShare = news;
    this.showShareModal = true;
  }

  closeShareModal(): void {
    this.showShareModal = false;
    this.selectedNewsForShare = null;
  }
}
