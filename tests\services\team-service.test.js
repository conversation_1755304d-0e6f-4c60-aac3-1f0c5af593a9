"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const tsyringe_1 = require("tsyringe");
const errors_1 = require("../../src/errors");
const mock_team_repository_1 = require("../../src/mocks/repositories/mock-team-repository");
const mock_player_service_1 = require("../../src/mocks/services/mock-player.service");
const mock_image_service_1 = require("../../src/mocks/services/util-services/mock-image-service");
const services_1 = require("../../src/services");
describe("TeamService", () => {
    let teamService;
    let mockTeamRepository;
    let mockImageService;
    let mockPlayerService;
    beforeAll(() => {
        mockTeamRepository = new mock_team_repository_1.MockTeamRepository();
        mockImageService = new mock_image_service_1.MockImageService();
        mockPlayerService = new mock_player_service_1.MockPlayerService();
        tsyringe_1.container.registerInstance("ITeamRepository", mockTeamRepository);
        tsyringe_1.container.registerInstance("ImageService", mockImageService);
        tsyringe_1.container.registerInstance("IPlayerService", mockPlayerService);
        teamService = tsyringe_1.container.resolve(services_1.TeamService);
    });
    describe("renameTeam", () => {
        it("should rename the team if the new name does not exist", () => __awaiter(void 0, void 0, void 0, function* () {
            mockTeamRepository.isTeamNameExists = jest.fn().mockResolvedValue(false);
            mockTeamRepository.renameTeam = jest.fn().mockResolvedValue(undefined);
            yield teamService.renameTeam("teamId", "newName");
            expect(mockTeamRepository.isTeamNameExists).toHaveBeenCalledWith("newName");
            expect(mockTeamRepository.renameTeam).toHaveBeenCalledWith("teamId", "newName");
        }));
        it("should throw BadRequestError if the new name already exists", () => __awaiter(void 0, void 0, void 0, function* () {
            mockTeamRepository.isTeamNameExists = jest.fn().mockResolvedValue(true);
            yield expect(teamService.renameTeam("teamId", "newName")).rejects.toThrow(errors_1.BadRequestError);
        }));
    });
    //   describe("getAllTeams", () => {
    //     it("should return all teams as TeamDTOs", async () => {
    //       const teams = [{ name: "team1" }, { name: "team2" }];
    //       mockTeamRepository.getTeams = jest.fn().mockResolvedValue(teams);
    //       const result = await teamService.getAllTeams();
    //       expect(result).toEqual(await TeamMapper.mapToDtos(teams));
    //       expect(mockTeamRepository.getTeams).toHaveBeenCalled();
    //     });
    //   });
    //   describe("getTeamPlayers", () => {
    //     it("should return the players of a team as PlayerDTOs", async () => {
    //       const players = [{ name: "player1" }, { name: "player2" }];
    //       mockTeamRepository.getTeamWithPlayers = jest.fn().mockResolvedValue({ players });
    //       const result = await teamService.getTeamPlayers("teamId");
    //       expect(result).toEqual(await PlayerMapper.mapToDtos(players));
    //       expect(mockTeamRepository.getTeamWithPlayers).toHaveBeenCalledWith("teamId");
    //     });
    //   });
});
