/* === AI IMPORT COMPONENT === */

.ai-import-container {
  padding: var(--spacing-xl);
  background: var(--bg-primary);
  min-height: 100vh;
  font-family: var(--font-sans);

  @media (max-width: 768px) {
    padding: var(--spacing-lg);
  }
}

/* === HEADER SECTION === */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
}

.header-content {
  .page-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;

    i {
      color: var(--primary);
      font-size: var(--text-xl);
    }
  }

  .page-subtitle {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin: 0;
  }
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: flex-end;
  }
}

.ai-import-btn, .reset-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.ai-import-btn {
  background: linear-gradient(135deg, var(--primary), var(--primary-hover));
  color: white;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
}

.reset-btn {
  background: var(--surface-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);

  &:hover {
    background: var(--surface-tertiary);
    transform: translateY(-1px);
  }
}

/* === STATS SUMMARY === */
.stats-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  i {
    font-size: var(--text-xl);
    color: var(--primary);
    width: 24px;
    text-align: center;
  }

  .stat-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);

    .stat-value {
      font-size: var(--text-xl);
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
    }

    .stat-label {
      font-size: var(--text-sm);
      color: var(--text-secondary);
    }
  }
}

/* === PITCH CONTAINER === */
.pitch-container {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);
}

.pitch {
  position: relative;
  width: 100%;
  height: 600px;
  background: linear-gradient(135deg, #2d5a27, #4a7c59);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.3);

  @media (max-width: 768px) {
    height: 500px;
  }

  @media (max-width: 480px) {
    height: 400px;
  }
}

.pitch-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;

  .goal-area {
    position: absolute;
    left: 35%;
    width: 30%;
    height: 15%;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-sm);

    &.top {
      top: 0;
      border-bottom: none;
    }

    &.bottom {
      bottom: 0;
      border-top: none;
    }
  }

  .center-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120px;
    height: 120px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);

    @media (max-width: 768px) {
      width: 100px;
      height: 100px;
    }
  }

  .center-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-50%);
  }
}

/* === PLAYER POSITIONS === */
.player-positions {
  position: relative;
  width: 100%;
  height: 100%;
}

.player-slot {
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  &:hover {
    transform: translate(-50%, -50%) scale(1.1);
    z-index: 20;
  }

  &.filled {
    .player-avatar {
      background: var(--primary);
      border-color: var(--primary);
    }
  }

  &.player-of-match {
    .player-avatar {
      box-shadow: 0 0 20px var(--warning-400);
      border-color: var(--warning-400);
    }

    &::after {
      content: '⭐';
      position: absolute;
      top: -10px;
      right: -10px;
      font-size: 16px;
      z-index: 21;
    }
  }
}

.player-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--surface-secondary);
  border: 3px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-xs);
  position: relative;
  overflow: hidden;

  @media (max-width: 768px) {
    width: 40px;
    height: 40px;
  }

  i {
    color: var(--text-secondary);
    font-size: var(--text-lg);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .player-initials {
    font-size: var(--text-sm);
    font-weight: var(--font-weight-bold);
    color: white;
    display: none;
  }

  &:has(img[style*="display: none"]) .player-initials {
    display: flex;
  }
}

.player-info {
  text-align: center;
  min-width: 80px;

  .player-name {
    display: block;
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    margin-bottom: var(--spacing-xs);
  }

  .player-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xs);

    .stat {
      font-size: var(--text-xs);
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 2px 4px;
      border-radius: var(--radius-sm);
    }
  }
}

.clear-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--danger);
  color: white;
  border: none;
  font-size: var(--text-xs);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: var(--danger-hover);
    transform: scale(1.1);
  }
}

/* === ACTION BUTTONS === */
.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.cancel-btn, .submit-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: var(--surface-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);

  &:hover {
    background: var(--surface-tertiary);
    transform: translateY(-1px);
  }
}

.submit-btn {
  background: linear-gradient(135deg, var(--success), var(--success-hover));
  color: white;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

/* === MODAL STYLES === */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-lg);
}

.modal-content {
  background: var(--surface-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--border-primary);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);

  h3 {
    margin: 0;
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
  }

  .close-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: var(--danger);
      color: white;
      border-color: var(--danger);
    }
  }
}

.modal-body {
  padding: var(--spacing-lg);
}

.form-group {
  margin-bottom: var(--spacing-lg);

  label {
    display: block;
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
  }

  .form-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background: var(--surface-secondary);
    color: var(--text-primary);
    font-size: var(--text-base);
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: var(--text-base) !important;
  margin-bottom: 0 !important;

  input[type="checkbox"] {
    width: auto;
    margin: 0;
  }

  .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-sm);
    background: var(--surface-secondary);
    position: relative;
    transition: all 0.2s ease;

    &::after {
      content: '✓';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 12px;
      opacity: 0;
      transition: opacity 0.2s ease;
    }
  }

  input[type="checkbox"]:checked + .checkmark {
    background: var(--primary);
    border-color: var(--primary);

    &::after {
      opacity: 1;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-primary);

  .cancel-btn, .save-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .cancel-btn {
    background: var(--surface-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);

    &:hover {
      background: var(--surface-tertiary);
    }
  }

  .save-btn {
    background: var(--primary);
    color: white;

    &:hover {
      background: var(--primary-hover);
      transform: translateY(-1px);
    }
  }
}
