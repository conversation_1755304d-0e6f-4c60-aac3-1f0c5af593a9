import { Schema, model, Types, Document } from "mongoose";

export interface IComment extends Document {
  _id: Types.ObjectId;
  gameId: Types.ObjectId;
  userId: Types.ObjectId;
  content: string;
  parentCommentId?: Types.ObjectId;
  likes: Types.ObjectId[]; // Array of user IDs who liked this comment
  isEdited: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const commentSchema = new Schema<IComment>({
  gameId: {
    type: Schema.Types.ObjectId,
    ref: "Game",
    required: true,
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true
  },
  content: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000 // Limit comment length
  },
  parentCommentId: {
    type: Schema.Types.ObjectId,
    ref: "Comment",
    default: null,
    index: true
  },
  likes: [{
    type: Schema.Types.ObjectId,
    ref: "User"
  }],
  isEdited: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  collection: "comments"
});

// Index for efficient querying by game
commentSchema.index({ gameId: 1, createdAt: -1 });

// Index for efficient querying of replies
commentSchema.index({ parentCommentId: 1, createdAt: 1 });

// Index for user's comments
commentSchema.index({ userId: 1, createdAt: -1 });

export const Comment = model<IComment>("Comment", commentSchema);
