import { NextFunction, Request, Response } from "express";

export interface INewsController {
    getAllNews: (req: Request, res: Response, next: NextFunction) => void;
    addNews: (req: Request, res: Response, next: NextFunction) => void;
    updateNews: (req: Request, res: Response, next: NextFunction) => void;
    deleteNews: (req: Request, res: Response, next: NextFunction) => void;
    likeNews: (req: Request, res: Response, next: NextFunction) => void;
    unlikeNews: (req: Request, res: Response, next: NextFunction) => void;
}