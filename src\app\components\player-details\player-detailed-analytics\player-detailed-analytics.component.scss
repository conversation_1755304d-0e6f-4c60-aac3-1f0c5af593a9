/* === PLAYER DETAILED ANALYTICS COMPONENT === */
.detailed-stats-section {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    width: 100%;
    max-width: 100%;

    .stats-container {
        .stats-header {
            background: var(--surface-secondary);
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-primary);

            .section-title {
                display: flex;
                align-items: center;
                gap: var(--spacing-md);
                font-size: var(--text-xl);
                font-weight: 700;
                color: var(--text-primary);
                margin: 0;

                i {
                    color: var(--primary-500);
                    font-size: var(--text-lg);
                }

                @media (max-width: 768px) {
                    font-size: var(--text-lg);
                }
            }
        }

        .stats-content {
            padding: var(--spacing-xl);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-2xl);

            @media (max-width: 768px) {
                padding: var(--spacing-lg);
                gap: var(--spacing-xl);
            }

            .position-stats {
                .position-stats-container {
                    background: var(--surface-secondary);
                    border-radius: var(--radius-xl);
                    padding: var(--spacing-lg);
                    border: 1px solid var(--border-primary);
                    width: 100%;
                    max-width: 100%;
                    overflow: hidden;

                    // Custom styling for the position stats component
                    ::ng-deep {
                        .stats-table {
                            background: transparent;
                            border: none;
                        }

                        .table-header {
                            background: var(--surface-tertiary);
                            color: var(--text-primary);
                        }

                        .table-row {
                            &:hover {
                                background: var(--surface-hover);
                            }
                        }

                        // Ensure charts are responsive
                        .chart-container {
                            width: 100% !important;
                            max-width: 100% !important;
                            overflow: hidden;
                        }

                        ag-charts-angular {
                            width: 100% !important;
                            max-width: 100% !important;
                        }
                    }

                    @media (max-width: 768px) {
                        padding: var(--spacing-md);
                    }

                    @media (max-width: 480px) {
                        padding: var(--spacing-sm);
                        margin: 0 5px;
                        max-width: calc(100vw - 10px);
                    }
                }
            }

            .recent-games {
                .recent-games-container {
                    background: var(--surface-secondary);
                    border-radius: var(--radius-xl);
                    padding: var(--spacing-lg);
                    border: 1px solid var(--border-primary);
                    width: 100%;
                    max-width: 100%;
                    overflow: hidden;

                    // Custom styling for the recent games component
                    ::ng-deep {
                        .games-list {
                            background: transparent;
                        }

                        .game-item {
                            background: var(--surface-primary);
                            border: 1px solid var(--border-primary);
                            border-radius: var(--radius-lg);
                            margin-bottom: var(--spacing-sm);
                            transition: all 0.2s ease-in-out;

                            &:hover {
                                background: var(--surface-hover);
                                transform: translateX(4px);
                            }
                        }

                        .game-result {
                            &.win {
                                border-left: 4px solid var(--success-500);
                            }

                            &.draw {
                                border-left: 4px solid var(--warning-500);
                            }

                            &.loss {
                                border-left: 4px solid var(--error-500);
                            }
                        }

                        // Ensure charts are responsive
                        .chart-container {
                            width: 100% !important;
                            max-width: 100% !important;
                            overflow: hidden;
                        }

                        ag-charts-angular {
                            width: 100% !important;
                            max-width: 100% !important;
                        }
                    }

                    @media (max-width: 768px) {
                        padding: var(--spacing-md);
                    }

                    @media (max-width: 480px) {
                        padding: var(--spacing-sm);
                        margin: 0 5px;
                        max-width: calc(100vw - 10px);
                    }
                }
            }
        }
    }
}
