/* === MODERN MULTISELECT DESIGN === */

.pro-clubs-multiselect-container {
    width: 100%;
    position: relative;
}

.multiselect-wrapper {
    position: relative;
    width: 100%;
    min-height: 44px;
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;

    &:hover {
        border-color: var(--border-secondary);
        box-shadow: var(--shadow-sm);
    }

    &.focused {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    &.has-value {
        .select-icon i {
            color: var(--text-primary);
        }
    }
}

.select-trigger {
    display: flex;
    align-items: center;
    min-height: 44px;
    padding: var(--spacing-xs) var(--spacing-md);
    cursor: pointer;
    position: relative;
}

.selected-values {
    flex: 1;
    display: flex;
    align-items: center;
    min-height: 32px;
}

.value-chips {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    width: 100%;
}

.value-chip {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--primary);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    max-width: 150px;
    transition: all 0.2s ease;

    &:hover {
        background: var(--primary-600);
        transform: scale(1.02);
    }
}

.chip-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.chip-remove {
    cursor: pointer;
    font-size: var(--text-xs);
    padding: 2px;
    border-radius: var(--radius-full);
    transition: all 0.2s ease;

    &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }
}

.placeholder-text {
    color: var(--text-muted);
    font-size: var(--text-base);
    font-weight: var(--font-weight-normal);
}

.select-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    margin-left: var(--spacing-sm);

    i {
        color: var(--text-secondary);
        font-size: var(--text-sm);
        transition: all 0.2s ease;

        &.rotated {
            transform: rotate(180deg);
        }
    }
}

/* === DROPDOWN PANEL === */
.dropdown-panel {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    margin-top: 4px;
    max-height: 300px;
    overflow: hidden;
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-header {
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-secondary);
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    z-index: 1;
}

.search-input {
    width: 100%;
    height: 36px;
    padding: var(--spacing-xs) var(--spacing-sm);
    padding-left: 32px;
    background: var(--surface-secondary);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--text-sm);
    outline: none;
    transition: all 0.2s ease;

    &:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
    }

    &::placeholder {
        color: var(--text-muted);
    }
}

.options-container {
    max-height: 240px;
    overflow-y: auto;
    padding: var(--spacing-xs) 0;
}

.select-all-option,
.option-item {
    padding: 0;
    border-bottom: 1px solid var(--border-secondary);

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background: var(--surface-secondary);
    }
}

.select-all-option {
    border-bottom: 2px solid var(--border-primary);
    background: var(--surface-secondary);
    font-weight: var(--font-weight-semibold);
}

.option-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    width: 100%;
    min-height: 40px;
    margin: 0;
    transition: all 0.2s ease;

    &:hover {
        background: var(--surface-secondary);
    }
}

.option-checkbox {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkbox-custom {
    position: relative;
    width: 18px;
    height: 18px;
    background: var(--surface-primary);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    flex-shrink: 0;

    &::after {
        content: '';
        position: absolute;
        left: 5px;
        top: 2px;
        width: 4px;
        height: 8px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        opacity: 0;
        transition: all 0.2s ease;
    }

    .option-checkbox:checked + & {
        background: var(--primary);
        border-color: var(--primary);

        &::after {
            opacity: 1;
        }
    }

    .option-checkbox:indeterminate + & {
        background: var(--primary);
        border-color: var(--primary);

        &::after {
            left: 3px;
            top: 7px;
            width: 8px;
            height: 2px;
            border: none;
            background: white;
            transform: none;
            opacity: 1;
        }
    }

    .option-checkbox:focus + & {
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
    }
}

.option-text {
    flex: 1;
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    line-height: 1.4;
}

.no-options {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    gap: var(--spacing-sm);

    i {
        font-size: var(--text-lg);
        opacity: 0.5;
    }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
    .multiselect-wrapper {
        min-height: 40px;
    }

    .select-trigger {
        min-height: 40px;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .value-chip {
        font-size: var(--text-xs);
        padding: 2px var(--spacing-xs);
        max-width: 100px;

        .chip-text {
            max-width: 80px;
        }
    }

    .dropdown-panel {
        max-height: 250px;
    }

    .options-container {
        max-height: 200px;
    }

    .option-label {
        padding: var(--spacing-xs) var(--spacing-sm);
        min-height: 36px;
    }

    .option-text {
        font-size: var(--text-sm);
    }

    .search-input {
        height: 32px;
        font-size: var(--text-sm);
    }
}

@media (max-width: 480px) {
    .value-chips {
        gap: 2px;
    }

    .value-chip {
        font-size: 10px;
        padding: 1px var(--spacing-xs);
        max-width: 80px;

        .chip-text {
            max-width: 60px;
        }

        .chip-remove {
            font-size: 8px;
        }
    }

    .dropdown-panel {
        max-height: 200px;
    }

    .options-container {
        max-height: 150px;
    }
}

/* === DISABLED STATE === */
.multiselect-wrapper.disabled {
    background: var(--surface-tertiary);
    border-color: var(--border-secondary);
    cursor: not-allowed;
    opacity: 0.6;

    .select-trigger {
        cursor: not-allowed;
    }

    &:hover {
        border-color: var(--border-secondary);
        box-shadow: none;
    }

    .value-chip {
        background: var(--text-muted);
    }

    .select-icon i {
        color: var(--text-muted);
    }
}

/* === ERROR STATE === */
.multiselect-wrapper.error {
    border-color: var(--danger);

    &:focus-within {
        border-color: var(--danger);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
}

/* === ACCESSIBILITY === */
.multiselect-wrapper {
    &:focus-within {
        outline: 2px solid var(--primary);
        outline-offset: 2px;
    }
}

.option-checkbox:focus-visible + .checkbox-custom {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* === SCROLLBAR STYLING === */
.options-container {
    scrollbar-width: thin;
    scrollbar-color: var(--border-secondary) var(--surface-secondary);

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: var(--surface-secondary);
        border-radius: var(--radius-sm);
    }

    &::-webkit-scrollbar-thumb {
        background: var(--border-secondary);
        border-radius: var(--radius-sm);

        &:hover {
            background: var(--border-primary);
        }
    }
}

/* === ANIMATIONS === */
.value-chip {
    animation: chipSlideIn 0.2s ease-out;
}

@keyframes chipSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.option-item {
    transition: all 0.15s ease;

    &:hover {
        transform: translateX(2px);
    }
}