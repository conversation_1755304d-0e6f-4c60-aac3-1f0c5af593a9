.date-view {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  animation: fadeInUp 0.6s ease-out;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.date-loading {
  display: flex;
  justify-content: center;
  padding: var(--spacing-md);

  .loading-spinner {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);

    i {
      color: var(--primary);
    }
  }
}

.load-more-section {
  display: flex;
  justify-content: center;
  padding: var(--spacing-sm) 0;

  .load-more-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    background: transparent;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--primary);
      color: var(--primary);
      background: var(--primary-light);
    }

    i {
      font-size: var(--font-size-xs);
    }
  }
}

.date-groups {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;

  @media (max-width: 768px) {
    gap: var(--spacing-lg);
    padding: 0;
  }
}

.date-group {
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideInFromRight 0.5s ease-out;
  animation-fill-mode: both;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  &.date-today {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
    position: relative;
    animation: pulseHighlight 2s infinite alternate;

    &::before {
      content: '• TODAY •';
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      background: var(--primary);
      color: var(--text-on-primary);
      padding: 2px 10px;
      border-radius: var(--radius-full);
      font-size: var(--font-size-xs);
      font-weight: 700;
      letter-spacing: 1px;
      z-index: 1;
    }

    .date-header {
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
      color: var(--text-on-primary);

      .date-subtitle {
        color: rgba(var(--text-on-primary-rgb), 0.8);
      }

      .count-badge {
        background: rgba(var(--text-on-primary-rgb), 0.2);
        color: var(--text-on-primary);
      }
    }
  }

  &.date-past {
    opacity: 0.8;

    .date-header {
      background: var(--surface-tertiary);
    }
  }

  &.date-future {
    .date-header {
      background: linear-gradient(135deg, var(--success), var(--success-dark));
      color: var(--text-on-success);

      .date-subtitle {
        color: rgba(var(--text-on-success-rgb), 0.8);
      }

      .count-badge {
        background: rgba(var(--text-on-success-rgb), 0.2);
        color: var(--text-on-success);
      }
    }
  }

  @media (max-width: 768px) {
    width: 100% !important;
    max-width: 100vw !important;
    margin: 0 auto !important;
    border-radius: var(--radius-md);
    overflow: hidden;
    box-sizing: border-box !important;
    padding: 0 !important;

    &:hover {
      transform: none; // Disable hover transform on mobile
    }
  }
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--surface-tertiary);
  border-bottom: 1px solid var(--border-color);

  .date-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);

    .date-title {
      font-size: var(--font-size-lg);
      font-weight: 700;
      margin: 0;
      color: var(--text-primary);
    }

    .date-subtitle {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      font-weight: 500;
    }
  }

  .games-count {
    .count-badge {
      display: inline-flex;
      align-items: center;
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--surface-primary);
      color: var(--text-secondary);
      border-radius: var(--radius-full);
      font-size: var(--font-size-xs);
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

.date-games {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  animation: fadeInScale 0.4s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;

  @media (max-width: 768px) {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: var(--spacing-sm) !important;
    padding: 0 !important;
    margin: 0 auto !important;
    width: 100% !important;
    max-width: 100vw !important;
    box-sizing: border-box !important;
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseHighlight {
  from {
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
  }
  to {
    box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.3);
  }
}

.date-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  gap: var(--spacing-sm);

  i {
    font-size: var(--font-size-lg);
    opacity: 0.5;
  }
}

.no-dates-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--border-color);

  i {
    font-size: 3rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    opacity: 0.6;
  }

  h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
  }

  p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    max-width: 300px;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .date-view {
    gap: var(--spacing-md);
  }

  .date-header {
    padding: var(--spacing-sm) var(--spacing-md);
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;

    .date-info {
      text-align: center;

      .date-title {
        font-size: var(--font-size-md);
      }

      .date-subtitle {
        font-size: var(--font-size-xs);
      }
    }

    .games-count {
      text-align: center;
    }
  }

  .load-more-btn {
    padding: var(--spacing-sm) var(--spacing-md) !important;
    font-size: var(--font-size-xs) !important;
  }
}
