<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iframe Broadcast Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 12px;
            border: 2px solid #3a3a3a;
        }
        
        .iframe-container {
            background: #333;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .iframe-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 200, 255, 0.05));
            border-bottom: 1px solid #444;
        }
        
        .iframe-header h5 {
            margin: 0;
            color: #ffffff;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .live-indicator {
            color: #00ff88;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .fullscreen-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: #444;
            border: 1px solid #555;
            border-radius: 8px;
            color: #ccc;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .fullscreen-btn:hover {
            background: #555;
            color: #00ff88;
            transform: translateY(-2px);
        }
        
        .iframe-wrapper {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            background: #222;
        }
        
        .iframe-wrapper iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .url-examples {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .url-examples h4 {
            color: #00ff88;
            margin-top: 0;
        }
        
        .url-example {
            margin: 10px 0;
            padding: 8px;
            background: #333;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .original { color: #ff6b6b; }
        .converted { color: #51cf66; }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🎮 ProClubs Stats - Iframe Broadcast Demo</h1>
    
    <div class="demo-section">
        <h2>📺 Live Broadcast Feature</h2>
        <p>This demo shows how the iframe broadcast functionality works when it's game time and there's a broadcast available.</p>
        
        <div class="iframe-container">
            <div class="iframe-header">
                <h5>
                    <span class="live-indicator">●</span>
                    Live Stream - Team Alpha
                </h5>
                <a href="#" class="fullscreen-btn" title="Open in new tab">
                    ↗
                </a>
            </div>
            <div class="iframe-wrapper">
                <!-- Example with a placeholder video -->
                <iframe 
                    src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=0" 
                    frameborder="0" 
                    allowfullscreen
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    sandbox="allow-scripts allow-same-origin allow-presentation">
                </iframe>
            </div>
        </div>
    </div>
    
    <div class="demo-section">
        <h2>🔗 URL Conversion Examples</h2>
        <p>The system automatically converts regular streaming URLs to embeddable iframe URLs:</p>
        
        <div class="url-examples">
            <h4>Twitch URLs</h4>
            <div class="url-example">
                <div class="original">Original: https://twitch.tv/channelname</div>
                <div class="converted">Converted: https://player.twitch.tv/?channel=channelname&parent=localhost&autoplay=false</div>
            </div>
        </div>
        
        <div class="url-examples">
            <h4>YouTube URLs</h4>
            <div class="url-example">
                <div class="original">Original: https://youtube.com/watch?v=VIDEO_ID</div>
                <div class="converted">Converted: https://www.youtube.com/embed/VIDEO_ID?autoplay=0</div>
            </div>
            <div class="url-example">
                <div class="original">Original: https://youtu.be/VIDEO_ID</div>
                <div class="converted">Converted: https://www.youtube.com/embed/VIDEO_ID?autoplay=0</div>
            </div>
            <div class="url-example">
                <div class="original">Original: https://youtube.com/channel/CHANNEL_ID/live</div>
                <div class="converted">Converted: https://www.youtube.com/embed/live_stream?channel=CHANNEL_ID&autoplay=0</div>
            </div>
        </div>
    </div>
    
    <div class="demo-section">
        <h2>⚙️ Implementation Features</h2>
        <ul>
            <li><strong>Automatic Detection:</strong> Shows iframe only when it's game time (within 2 hours) and broadcast is available</li>
            <li><strong>Platform Support:</strong> Handles Twitch, YouTube, and YouTube Live streams</li>
            <li><strong>Responsive Design:</strong> 16:9 aspect ratio that works on all screen sizes</li>
            <li><strong>Security:</strong> Proper iframe sandbox and allow attributes</li>
            <li><strong>Fallback Option:</strong> "Open in new tab" button for full-screen viewing</li>
            <li><strong>User Experience:</strong> Keeps users on the site while watching streams</li>
        </ul>
    </div>
    
    <div class="demo-section">
        <h2>🧪 Test URL Conversion</h2>
        <p>Test the URL conversion logic with different streaming platforms:</p>
        
        <input type="text" id="testUrl" placeholder="Enter a streaming URL..." style="width: 70%; padding: 8px; margin: 10px 0; background: #333; color: white; border: 1px solid #555; border-radius: 4px;">
        <button class="test-button" onclick="testUrlConversion()">Convert URL</button>
        
        <div id="conversionResult" style="margin-top: 15px; padding: 10px; background: #333; border-radius: 4px; min-height: 20px;"></div>
        
        <div style="margin-top: 10px;">
            <button class="test-button" onclick="testUrl('https://twitch.tv/testchannel')">Test Twitch</button>
            <button class="test-button" onclick="testUrl('https://youtube.com/watch?v=dQw4w9WgXcQ')">Test YouTube</button>
            <button class="test-button" onclick="testUrl('https://youtu.be/dQw4w9WgXcQ')">Test YouTube Short</button>
        </div>
    </div>
    
    <script>
        // Replicate the URL conversion logic from the component
        function convertToEmbedUrl(url) {
            try {
                const urlObj = new URL(url);
                const hostname = urlObj.hostname.toLowerCase();
                
                // Handle Twitch URLs
                if (hostname.includes('twitch.tv')) {
                    const pathParts = urlObj.pathname.split('/').filter(part => part);
                    if (pathParts.length > 0) {
                        const channelName = pathParts[0];
                        const currentDomain = window.location.hostname;
                        return `https://player.twitch.tv/?channel=${channelName}&parent=${currentDomain}&autoplay=false`;
                    }
                }
                
                // Handle YouTube URLs
                if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
                    // Handle youtube.com/watch?v=VIDEO_ID
                    if (urlObj.pathname === '/watch' && urlObj.searchParams.has('v')) {
                        const videoId = urlObj.searchParams.get('v');
                        return `https://www.youtube.com/embed/${videoId}?autoplay=0`;
                    }
                    
                    // Handle youtube.com/channel/CHANNEL_ID/live
                    if (urlObj.pathname.includes('/live')) {
                        const pathParts = urlObj.pathname.split('/').filter(part => part);
                        const channelIndex = pathParts.indexOf('channel');
                        if (channelIndex !== -1 && channelIndex + 1 < pathParts.length) {
                            const channelId = pathParts[channelIndex + 1];
                            return `https://www.youtube.com/embed/live_stream?channel=${channelId}&autoplay=0`;
                        }
                    }
                    
                    // Handle youtu.be/VIDEO_ID
                    if (hostname.includes('youtu.be')) {
                        const videoId = urlObj.pathname.substring(1); // Remove leading slash
                        return `https://www.youtube.com/embed/${videoId}?autoplay=0`;
                    }
                }
                
                return null;
            } catch (error) {
                console.warn('Failed to convert URL to embed format:', url, error);
                return null;
            }
        }
        
        function testUrlConversion() {
            const url = document.getElementById('testUrl').value;
            const result = convertToEmbedUrl(url);
            const resultDiv = document.getElementById('conversionResult');
            
            if (result) {
                resultDiv.innerHTML = `
                    <div style="color: #51cf66; margin-bottom: 5px;">✅ Conversion successful!</div>
                    <div style="color: #ff6b6b;">Original: ${url}</div>
                    <div style="color: #51cf66;">Converted: ${result}</div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="color: #ff6b6b;">❌ Could not convert URL</div>
                    <div>URL: ${url}</div>
                    <div style="color: #ffd43b;">This URL format is not supported for iframe embedding.</div>
                `;
            }
        }
        
        function testUrl(url) {
            document.getElementById('testUrl').value = url;
            testUrlConversion();
        }
    </script>
</body>
</html>
