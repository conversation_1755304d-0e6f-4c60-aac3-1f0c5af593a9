"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const tsyringe_1 = require("tsyringe");
const mock_player_repository_1 = require("../../src/mocks/repositories/mock-player-repository");
const services_1 = require("../../src/services");
const mock_image_service_1 = require("../../src/mocks/services/util-services/mock-image-service");
describe("PlayerService", () => {
    let playerService;
    let mockPlayerRepository;
    let mockImageService;
    beforeAll(() => {
        mockPlayerRepository = new mock_player_repository_1.MockPlayerRepository();
        mockImageService = new mock_image_service_1.MockImageService();
        tsyringe_1.container.registerInstance("IPlayerRepository", mockPlayerRepository);
        tsyringe_1.container.registerInstance("ImageService", mockImageService);
        playerService = tsyringe_1.container.resolve(services_1.PlayerService);
    });
    describe("renamePlayer", () => {
        it("should rename the player if the new name does not exist", () => __awaiter(void 0, void 0, void 0, function* () {
            mockPlayerRepository.renamePlayer = jest.fn().mockResolvedValue(undefined);
            yield playerService.renamePlayer("playerId", "newName");
            expect(mockPlayerRepository.renamePlayer).toHaveBeenCalledWith("playerId", "newName");
        }));
    });
});
