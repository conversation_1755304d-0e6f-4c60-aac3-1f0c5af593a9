<!-- <PERSON> Header with Breadcrumb -->
<header class="player-header">
    <div class="header-navigation">
        <button class="back-button" (click)="onBackClick()" aria-label="Go back">
            <i class="fas fa-arrow-left"></i>
            <span class="back-text">Back</span>
        </button>
        <div class="breadcrumb">
            <button class="breadcrumb-item breadcrumb-link" (click)="onTeamNavigate()">Players</button>
            <i class="fas fa-chevron-right breadcrumb-separator"></i>
            <span class="breadcrumb-current">{{player.name}}</span>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="header-actions" *ngIf="!isViewOnly">
        <button class="action-btn edit-btn" *ngIf="!editPlayerMode" (click)="onEditClick()"
                aria-label="Edit player">
            <i class="fas fa-edit"></i>
            <span>Edit</span>
        </button>
        <button class="action-btn cancel-btn" *ngIf="editPlayerMode" (click)="onCancelEditClick()"
                aria-label="Cancel editing">
            <i class="fas fa-times"></i>
            <span>Cancel</span>
        </button>
        <button class="action-btn save-btn" *ngIf="editPlayerMode" (click)="onSaveClick()"
                aria-label="Save changes">
            <i class="fas fa-save"></i>
            <span>Save</span>
        </button>
    </div>
</header>
