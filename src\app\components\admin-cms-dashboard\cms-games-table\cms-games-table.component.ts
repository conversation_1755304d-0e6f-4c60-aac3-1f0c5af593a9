import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { GameDTO, GAME_STATUS } from '@pro-clubs-manager/shared-dtos';
import { GameService } from '../../../services/game.service';
import { NotificationService } from '../../../services/notification.service';

@Component({
  selector: 'app-cms-games-table',
  templateUrl: './cms-games-table.component.html',
  styleUrl: './cms-games-table.component.scss'
})
export class CmsGamesTableComponent {
  @Input() games: GameDTO[] = [];
  @Output() gameDeleted = new EventEmitter<void>();
  @Output() gameUpdated = new EventEmitter<void>();

  GameStatus = GAME_STATUS;
  deletingGameIds = new Set<string>();

  constructor(
    private gameService: GameService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  async deleteGame(game: GameDTO): Promise<void> {
    const confirmDelete = confirm(
      `⚠️ Are you sure you want to delete this game?\n\n` +
      `${game.homeTeam.name} vs ${game.awayTeam.name}\n` +
      `${game.date ? new Date(game.date).toLocaleDateString() : 'No date set'}\n\n` +
      'This action cannot be undone and will permanently remove:\n' +
      '• Game result and statistics\n' +
      '• Player performances\n' +
      '• All comments and predictions\n' +
      '• Match broadcast information'
    );

    if (!confirmDelete) {
      return;
    }

    try {
      this.deletingGameIds.add(game.id);
      await this.gameService.deleteGame(game.id);
      this.notificationService.success('Game deleted successfully');
      this.gameDeleted.emit();
    } catch (error: any) {
      console.error('Error deleting game:', error);
      this.notificationService.error(error.message || 'Failed to delete game');
    } finally {
      this.deletingGameIds.delete(game.id);
    }
  }

  viewGameDetails(gameId: string): void {
    this.router.navigate(['/game-details', gameId]);
  }

  editGame(gameId: string): void {
    this.router.navigate(['/modify-game', gameId]);
  }

  getGameResult(game: GameDTO): string {
    if (game.status === GAME_STATUS.SCHEDULED) {
      return 'Not played';
    }
    
    if (!game.result) {
      return 'No result';
    }
    
    return `${game.result.homeTeamGoals} - ${game.result.awayTeamGoals}`;
  }

  getGameDate(game: GameDTO): string {
    if (!game.date) {
      return 'No date';
    }
    
    return new Date(game.date).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  getGameTime(game: GameDTO): string {
    if (!game.date) {
      return '';
    }
    
    return new Date(game.date).toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getStatusBadgeClass(status: GAME_STATUS): string {
    switch (status) {
      case GAME_STATUS.SCHEDULED:
        return 'status-scheduled';
      case GAME_STATUS.PLAYED:
        return 'status-played';
      case GAME_STATUS.COMPLETED:
        return 'status-completed';
      default:
        return 'status-unknown';
    }
  }

  getStatusText(status: GAME_STATUS): string {
    switch (status) {
      case GAME_STATUS.SCHEDULED:
        return 'Scheduled';
      case GAME_STATUS.PLAYED:
        return 'Played';
      case GAME_STATUS.COMPLETED:
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  isGameDeleting(gameId: string): boolean {
    return this.deletingGameIds.has(gameId);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/Team.png';
    }
  }
}
