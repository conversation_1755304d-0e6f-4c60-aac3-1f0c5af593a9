import { injectable, inject } from "tsyringe";
import { ClientSession, Types } from "mongoose";
import {
  ISeasonAchievementService,
  SeasonEndRequest,
  SeasonEndPreview,
  SeasonEndResult,
  PlayerAchievementHistory,
  TeamAchievementHistory
} from "../../interfaces/wrapper-services/season-achievement-service.interface";
import SeasonAchievements, {
  ISeasonAchievements,
  IPlayerAchievement,
  ITeamAchievement,
  ACHIEVEMENT_TYPE
} from "../../models/season-achievement";
import { IPlayerRepository } from "../../interfaces/player/player-repository.interface";
import { ITeamRepository } from "../../interfaces/team/team-repository.interface";
import { ILeagueRepository } from "../../interfaces/league/league-repository.interface";
import { transactionService } from "../util-services/transaction-service";
import { IGameRepository } from "../../interfaces/game/game-repository.interface";
import { IBracketsService } from "../brackets-service";
import { GAME_STATUS } from "@pro-clubs-manager/shared-dtos";
import { PLAYOFF_STAGE } from "../../models/game/game";
import { NotFoundError } from "../../errors";
import logger from "../../config/logger";

@injectable()
export class SeasonAchievementService implements ISeasonAchievementService {
  constructor(
    @inject("IPlayerRepository") private playerRepository: IPlayerRepository,
    @inject("ITeamRepository") private teamRepository: ITeamRepository,
    @inject("ILeagueRepository") private leagueRepository: ILeagueRepository,
    @inject("IGameRepository") private gameRepository: IGameRepository,
    @inject("IBracketsService") private bracketsService: IBracketsService
  ) {}

  async previewSeasonEnd(leagueId: string, seasonNumber?: number): Promise<SeasonEndPreview> {
    logger.info(`SeasonAchievementService: Previewing season end for league ${leagueId}`);

    const league = await this.leagueRepository.getLeagueById(leagueId);
    const currentSeasonNumber = seasonNumber || league.currentSeason.seasonNumber;

    // Get all games for the season
    const allGames = await this.gameRepository.getAllGames();
    const seasonGames = allGames.filter(game => 
      game.league.toString() === leagueId && 
      game.seasonNumber === currentSeasonNumber
    );

    const completedGames = seasonGames.filter(game => game.status === GAME_STATUS.PLAYED);
    const remainingGames = seasonGames.filter(game => game.status !== GAME_STATUS.PLAYED);

    // Calculate championship results
    const championshipResults = await this.calculateChampionshipResults(leagueId, currentSeasonNumber);
    
    // Calculate top performers
    const topPerformers = await this.calculateSeasonTopPerformers(leagueId, currentSeasonNumber);

    const warnings: string[] = [];
    if (remainingGames.length > 0) {
      warnings.push(`${remainingGames.length} games are still not completed`);
    }

    return {
      seasonNumber: currentSeasonNumber,
      leagueId,
      leagueName: league.name,
      isSeasonComplete: remainingGames.length === 0,
      totalGames: seasonGames.length,
      completedGames: completedGames.length,
      remainingGames: remainingGames.length,
      champion: championshipResults.champion ? {
        teamId: championshipResults.champion.teamId.toString(),
        teamName: championshipResults.champion.teamName,
        teamImgUrl: championshipResults.champion.teamImgUrl
      } : undefined,
      finalist: championshipResults.finalist ? {
        teamId: championshipResults.finalist.teamId.toString(),
        teamName: championshipResults.finalist.teamName,
        teamImgUrl: championshipResults.finalist.teamImgUrl
      } : undefined,
      thirdPlace: championshipResults.thirdPlace ? {
        teamId: championshipResults.thirdPlace.teamId.toString(),
        teamName: championshipResults.thirdPlace.teamName,
        teamImgUrl: championshipResults.thirdPlace.teamImgUrl
      } : undefined,
      topScorers: topPerformers.topScorers
        .filter(p => p.achievementType === ACHIEVEMENT_TYPE.TOP_SCORER)
        .map(p => ({
          playerId: p.playerId.toString(),
          playerName: p.playerName,
          playerImgUrl: p.playerImgUrl,
          teamName: p.teamName,
          goals: p.stats.goals || 0,
          games: p.stats.games || 0,
          rank: p.rank || 1
        })),
      topAssists: topPerformers.topAssists
        .filter(p => p.achievementType === ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER)
        .map(p => ({
          playerId: p.playerId.toString(),
          playerName: p.playerName,
          playerImgUrl: p.playerImgUrl,
          teamName: p.teamName,
          assists: p.stats.assists || 0,
          games: p.stats.games || 0,
          rank: p.rank || 1
        })),
      bestByPosition: topPerformers.bestByPosition.map(p => ({
        position: this.getPositionFromAchievementType(p.achievementType),
        playerId: p.playerId.toString(),
        playerName: p.playerName,
        playerImgUrl: p.playerImgUrl,
        teamName: p.teamName,
        avgRating: p.stats.avgRating || 0,
        games: p.stats.games || 0
      })),
      warnings
    };
  }

  async endSeason(request: SeasonEndRequest, adminUserId: string, session?: ClientSession): Promise<SeasonEndResult> {
    logger.info(`SeasonAchievementService: Ending season ${request.seasonNumber} for league ${request.leagueId}`);

    return await transactionService.withTransaction(async (txSession) => {
      const actualSession = session || txSession;

      // Check if season can be ended
      const canEnd = await this.canEndSeason(request.leagueId, request.seasonNumber);
      if (!canEnd.canEnd && !request.forceEnd) {
        throw new Error(canEnd.reason || "Season cannot be ended");
      }

      // Check if achievements already exist
      const existingAchievements = await this.getSeasonAchievements(request.leagueId, request.seasonNumber);
      if (existingAchievements) {
        throw new Error("Season achievements have already been recorded");
      }

      const league = await this.leagueRepository.getLeagueById(request.leagueId);
      
      // Calculate all achievements
      const championshipResults = await this.calculateChampionshipResults(request.leagueId, request.seasonNumber);
      const topPerformers = await this.calculateSeasonTopPerformers(request.leagueId, request.seasonNumber);

      // Create season achievements record
      const seasonAchievements = new SeasonAchievements({
        seasonNumber: request.seasonNumber,
        league: new Types.ObjectId(request.leagueId),
        leagueName: league.name,
        startDate: league.currentSeason.startDate,
        endDate: request.endDate || new Date(),
        playerAchievements: [
          ...topPerformers.topScorers,
          ...topPerformers.topAssists,
          ...topPerformers.bestByPosition
        ],
        teamAchievements: [
          championshipResults.champion,
          championshipResults.finalist,
          championshipResults.thirdPlace
        ].filter(Boolean) as ITeamAchievement[],
        champion: championshipResults.champion ? {
          teamId: championshipResults.champion.teamId,
          teamName: championshipResults.champion.teamName,
          teamImgUrl: championshipResults.champion.teamImgUrl
        } : undefined,
        finalist: championshipResults.finalist ? {
          teamId: championshipResults.finalist.teamId,
          teamName: championshipResults.finalist.teamName,
          teamImgUrl: championshipResults.finalist.teamImgUrl
        } : undefined,
        thirdPlace: championshipResults.thirdPlace ? {
          teamId: championshipResults.thirdPlace.teamId,
          teamName: championshipResults.thirdPlace.teamName,
          teamImgUrl: championshipResults.thirdPlace.teamImgUrl
        } : undefined,
        createdBy: new Types.ObjectId(adminUserId)
      });

      await seasonAchievements.save({ session: actualSession });

      // Update player achievement histories
      const playersUpdated = await this.updatePlayerAchievementHistories(
        seasonAchievements.playerAchievements,
        actualSession
      );

      // Update team achievement histories
      const teamsUpdated = await this.updateTeamAchievementHistories(
        seasonAchievements.teamAchievements,
        actualSession
      );

      // Update league season end date
      league.currentSeason.endDate = request.endDate || new Date();
      await league.save({ session: actualSession });

      return {
        success: true,
        seasonAchievements,
        playersUpdated,
        teamsUpdated,
        message: `Season ${request.seasonNumber} ended successfully with ${playersUpdated} players and ${teamsUpdated} teams updated`
      };
    });
  }

  async getLeagueSeasonAchievements(leagueId: string): Promise<ISeasonAchievements[]> {
    return await SeasonAchievements.find({
      league: new Types.ObjectId(leagueId)
    }).sort({ seasonNumber: -1 });
  }

  async getSeasonAchievements(leagueId: string, seasonNumber: number): Promise<ISeasonAchievements | null> {
    return await SeasonAchievements.findOne({
      league: new Types.ObjectId(leagueId),
      seasonNumber
    });
  }

  async getPlayerAchievementHistory(playerId: string): Promise<PlayerAchievementHistory> {
    // Get achievements from SeasonAchievements collection (official season endings)
    const seasonAchievements = await SeasonAchievements.find({
      "playerAchievements.playerId": new Types.ObjectId(playerId)
    }).sort({ seasonNumber: -1 });

    const seasonBasedAchievements = seasonAchievements.flatMap(season =>
      season.playerAchievements
        .filter(achievement => achievement.playerId.toString() === playerId)
        .map(achievement => ({
          seasonNumber: season.seasonNumber,
          leagueName: season.leagueName,
          achievementType: achievement.achievementType,
          rank: achievement.rank,
          description: achievement.description,
          year: season.endDate.getFullYear()
        }))
    );

    // Get achievements from player's own achievement history (manual achievements)
    let playerDirectAchievements: any[] = [];
    try {
      const player = await this.playerRepository.getPlayerById(playerId);
      playerDirectAchievements = player.achievementHistory.map(achievement => ({
        seasonNumber: achievement.seasonNumber,
        leagueName: achievement.leagueName,
        achievementType: achievement.achievementType,
        rank: achievement.rank,
        description: achievement.description,
        year: achievement.achievedDate.getFullYear()
      }));
    } catch (error: any) {
      // If player doesn't exist, we'll just use the season-based achievements
      // This can happen if a player was deleted but still has achievements in season records
      if (error instanceof NotFoundError || error.statusCode === 404 || error.message?.includes('not found')) {
        logger.warn(`Player ${playerId} not found, using only season-based achievements`);
        playerDirectAchievements = [];
      } else {
        // Re-throw other errors
        throw error;
      }
    }

    // Combine both sources and remove duplicates
    const allAchievements = [...seasonBasedAchievements, ...playerDirectAchievements];
    const uniqueAchievements = allAchievements.filter((achievement, index, self) =>
      index === self.findIndex(a =>
        a.seasonNumber === achievement.seasonNumber &&
        a.achievementType === achievement.achievementType
      )
    );

    // Sort by season number (descending)
    uniqueAchievements.sort((a, b) => b.seasonNumber - a.seasonNumber);

    return {
      playerId,
      achievements: uniqueAchievements
    };
  }

  async getTeamAchievementHistory(teamId: string): Promise<TeamAchievementHistory> {
    // Get achievements from SeasonAchievements collection (official season endings)
    const seasonAchievements = await SeasonAchievements.find({
      $or: [
        { "teamAchievements.teamId": new Types.ObjectId(teamId) },
        { "champion.teamId": new Types.ObjectId(teamId) },
        { "finalist.teamId": new Types.ObjectId(teamId) },
        { "thirdPlace.teamId": new Types.ObjectId(teamId) }
      ]
    }).sort({ seasonNumber: -1 });

    const seasonBasedAchievements = seasonAchievements.flatMap(season => {
      const results = [];

      // Add team achievements
      season.teamAchievements
        .filter(achievement => achievement.teamId.toString() === teamId)
        .forEach(achievement => {
          results.push({
            seasonNumber: season.seasonNumber,
            leagueName: season.leagueName,
            achievementType: achievement.achievementType,
            rank: achievement.rank,
            description: achievement.description,
            year: season.endDate.getFullYear()
          });
        });

      // Add championship results
      if (season.champion?.teamId.toString() === teamId) {
        results.push({
          seasonNumber: season.seasonNumber,
          leagueName: season.leagueName,
          achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER,
          rank: 1,
          description: "League Champion",
          year: season.endDate.getFullYear()
        });
      }

      if (season.finalist?.teamId.toString() === teamId) {
        results.push({
          seasonNumber: season.seasonNumber,
          leagueName: season.leagueName,
          achievementType: ACHIEVEMENT_TYPE.FINALIST,
          rank: 2,
          description: "League Finalist",
          year: season.endDate.getFullYear()
        });
      }

      if (season.thirdPlace?.teamId.toString() === teamId) {
        results.push({
          seasonNumber: season.seasonNumber,
          leagueName: season.leagueName,
          achievementType: ACHIEVEMENT_TYPE.THIRD_PLACE,
          rank: 3,
          description: "Third Place",
          year: season.endDate.getFullYear()
        });
      }

      return results;
    });

    // Get achievements from team's own achievement history (manual achievements)
    let teamDirectAchievements: any[] = [];
    try {
      const team = await this.teamRepository.getTeamById(teamId);
      teamDirectAchievements = team.achievementHistory.map(achievement => ({
        seasonNumber: achievement.seasonNumber,
        leagueName: achievement.leagueName,
        achievementType: achievement.achievementType,
        rank: achievement.rank,
        description: achievement.description,
        year: achievement.achievedDate.getFullYear()
      }));
    } catch (error: any) {
      // If team doesn't exist, we'll just use the season-based achievements
      // This can happen if a team was deleted but still has achievements in season records
      if (error instanceof NotFoundError || error.statusCode === 404 || error.message?.includes('not found')) {
        logger.warn(`Team ${teamId} not found, using only season-based achievements`);
        teamDirectAchievements = [];
      } else {
        // Re-throw other errors
        throw error;
      }
    }

    // Combine both sources and remove duplicates
    const allAchievements = [...seasonBasedAchievements, ...teamDirectAchievements];
    const uniqueAchievements = allAchievements.filter((achievement, index, self) =>
      index === self.findIndex(a =>
        a.seasonNumber === achievement.seasonNumber &&
        a.achievementType === achievement.achievementType
      )
    );

    // Sort by season number (descending)
    uniqueAchievements.sort((a, b) => b.seasonNumber - a.seasonNumber);

    return {
      teamId,
      achievements: uniqueAchievements
    };
  }

  async canEndSeason(leagueId: string, seasonNumber: number): Promise<{ canEnd: boolean; reason?: string }> {
    const allGames = await this.gameRepository.getAllGames();
    const seasonGames = allGames.filter(game => 
      game.league.toString() === leagueId && 
      game.seasonNumber === seasonNumber
    );

    const unplayedGames = seasonGames.filter(game => game.status !== GAME_STATUS.PLAYED);
    
    if (unplayedGames.length > 0) {
      return {
        canEnd: false,
        reason: `${unplayedGames.length} games are still not completed`
      };
    }

    return { canEnd: true };
  }

  private getPositionFromAchievementType(achievementType: ACHIEVEMENT_TYPE): string {
    switch (achievementType) {
      case ACHIEVEMENT_TYPE.BEST_GOALKEEPER:
        return "GK";
      case ACHIEVEMENT_TYPE.BEST_CENTER_BACK:
        return "CB";
      case ACHIEVEMENT_TYPE.BEST_DEFENSIVE_MIDFIELDER:
        return "CDM";
      case ACHIEVEMENT_TYPE.BEST_MIDFIELDER:
        return "CM";
      case ACHIEVEMENT_TYPE.BEST_ATTACKING_MIDFIELDER:
        return "CAM";
      case ACHIEVEMENT_TYPE.BEST_WINGER:
        return "LW/RW";
      case ACHIEVEMENT_TYPE.BEST_STRIKER:
        return "ST";
      default:
        return "Unknown";
    }
  }

  async calculateSeasonTopPerformers(leagueId: string, seasonNumber: number): Promise<{
    topScorers: IPlayerAchievement[];
    topAssists: IPlayerAchievement[];
    bestByPosition: IPlayerAchievement[];
  }> {
    const players = await this.playerRepository.getAllPlayers();
    const seasonPlayers = players.filter(player =>
      player.currentSeason?.league.toString() === leagueId &&
      player.currentSeason?.seasonNumber === seasonNumber
    );

    // Calculate top scorers (top 3) with tiebreaker logic
    const topScorers = seasonPlayers
      .filter(player => player.currentSeason!.stats.goals > 0)
      .sort((a, b) => {
        const goalsA = a.currentSeason!.stats.goals;
        const goalsB = b.currentSeason!.stats.goals;

        // Primary sort: by goals (descending)
        if (goalsB !== goalsA) {
          return goalsB - goalsA;
        }

        // Tiebreaker: if same goals, player with fewer games is ranked higher
        const gamesA = a.currentSeason!.stats.games;
        const gamesB = b.currentSeason!.stats.games;
        return gamesA - gamesB;
      })
      .slice(0, 3)
      .map((player, index) => {
        const goals = player.currentSeason!.stats.goals;
        const games = player.currentSeason!.stats.games;
        const ratio = games > 0 ? (goals / games).toFixed(2) : '0.00';

        return this.createPlayerAchievement(
          player,
          ACHIEVEMENT_TYPE.TOP_SCORER,
          index + 1,
          `${this.getOrdinal(index + 1)} highest goal scorer with ${goals} goals in ${games} games (${ratio} per game)`,
          seasonNumber
        );
      });

    // Calculate top assists (top 3) with tiebreaker logic
    const topAssists = seasonPlayers
      .filter(player => player.currentSeason!.stats.assists > 0)
      .sort((a, b) => {
        const assistsA = a.currentSeason!.stats.assists;
        const assistsB = b.currentSeason!.stats.assists;

        // Primary sort: by assists (descending)
        if (assistsB !== assistsA) {
          return assistsB - assistsA;
        }

        // Tiebreaker: if same assists, player with fewer games is ranked higher
        const gamesA = a.currentSeason!.stats.games;
        const gamesB = b.currentSeason!.stats.games;
        return gamesA - gamesB;
      })
      .slice(0, 3)
      .map((player, index) => {
        const assists = player.currentSeason!.stats.assists;
        const games = player.currentSeason!.stats.games;
        const ratio = games > 0 ? (assists / games).toFixed(2) : '0.00';

        return this.createPlayerAchievement(
          player,
          ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER,
          index + 1,
          `${this.getOrdinal(index + 1)} highest assist provider with ${assists} assists in ${games} games (${ratio} per game)`,
          seasonNumber
        );
      });

    // Calculate best by position
    const bestByPosition: IPlayerAchievement[] = [];
    const positionMappings = [
      { positions: ["GK"], achievement: ACHIEVEMENT_TYPE.BEST_GOALKEEPER },
      { positions: ["CB", "LCB", "RCB"], achievement: ACHIEVEMENT_TYPE.BEST_CENTER_BACK },
      { positions: ["CDM", "DM"], achievement: ACHIEVEMENT_TYPE.BEST_DEFENSIVE_MIDFIELDER },
      { positions: ["CM", "LCM", "RCM"], achievement: ACHIEVEMENT_TYPE.BEST_MIDFIELDER },
      { positions: ["CAM", "AM"], achievement: ACHIEVEMENT_TYPE.BEST_ATTACKING_MIDFIELDER },
      { positions: ["LW", "RW", "LM", "RM"], achievement: ACHIEVEMENT_TYPE.BEST_WINGER },
      { positions: ["ST", "CF", "LF", "RF", "LS", "RS"], achievement: ACHIEVEMENT_TYPE.BEST_STRIKER }
    ];

    for (const mapping of positionMappings) {
      const positionPlayers = seasonPlayers.filter(player =>
        mapping.positions.includes(player.position) &&
        player.currentSeason!.stats.games >= Math.ceil(seasonPlayers.length * 0.5) // At least 50% of team's matches
      );

      if (positionPlayers.length > 0) {
        const bestPlayer = positionPlayers
          .sort((a, b) => b.currentSeason!.stats.avgRating - a.currentSeason!.stats.avgRating)[0];

        bestByPosition.push(this.createPlayerAchievement(
          bestPlayer,
          mapping.achievement,
          1,
          `Best ${mapping.positions[0]} with ${bestPlayer.currentSeason!.stats.avgRating.toFixed(2)} average rating`,
          seasonNumber
        ));
      }
    }

    return {
      topScorers,
      topAssists,
      bestByPosition
    };
  }

  async calculateChampionshipResults(leagueId: string, seasonNumber: number): Promise<{
    champion?: ITeamAchievement;
    finalist?: ITeamAchievement;
    thirdPlace?: ITeamAchievement;
  }> {
    try {
      const bracket = await this.bracketsService.getPlayoffBracket(leagueId, seasonNumber);

      let champion, finalist, thirdPlace;

      if (bracket.champion) {
        const championTeam = await this.teamRepository.getTeamById(bracket.champion.id);
        champion = this.createTeamAchievement(
          championTeam,
          ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER,
          1,
          "League Champion",
          seasonNumber
        );
      }

      if (bracket.runnerUp) {
        const finalistTeam = await this.teamRepository.getTeamById(bracket.runnerUp.id);
        finalist = this.createTeamAchievement(
          finalistTeam,
          ACHIEVEMENT_TYPE.FINALIST,
          2,
          "League Finalist",
          seasonNumber
        );
      }

      if (bracket.thirdPlace) {
        const thirdPlaceTeam = await this.teamRepository.getTeamById(bracket.thirdPlace.id);
        thirdPlace = this.createTeamAchievement(
          thirdPlaceTeam,
          ACHIEVEMENT_TYPE.THIRD_PLACE,
          3,
          "Third Place",
          seasonNumber
        );
      }

      return { champion, finalist, thirdPlace };
    } catch (error) {
      logger.warn(`Could not calculate championship results: ${error}`);
      return {};
    }
  }

  private createPlayerAchievement(
    player: any,
    achievementType: ACHIEVEMENT_TYPE,
    rank?: number,
    description?: string,
    seasonNumber?: number
  ): IPlayerAchievement {
    const team = player.team || { _id: player.currentSeason?.team, name: "Unknown Team" };

    return {
      playerId: player._id,
      playerName: player.name,
      playerImgUrl: player.imgUrl,
      teamId: team._id,
      teamName: team.name,
      teamImgUrl: team.imgUrl,
      achievementType,
      rank,
      seasonNumber,
      stats: {
        goals: player.currentSeason?.stats.goals,
        assists: player.currentSeason?.stats.assists,
        cleanSheets: player.currentSeason?.stats.cleanSheets,
        avgRating: player.currentSeason?.stats.avgRating,
        games: player.currentSeason?.stats.games,
        playerOfTheMatch: player.currentSeason?.stats.playerOfTheMatch,
      },
      description
    };
  }

  private createTeamAchievement(
    team: any,
    achievementType: ACHIEVEMENT_TYPE,
    rank?: number,
    description?: string,
    seasonNumber?: number
  ): ITeamAchievement {
    return {
      teamId: team._id,
      teamName: team.name,
      teamImgUrl: team.imgUrl,
      achievementType,
      rank,
      seasonNumber,
      stats: {
        wins: team.currentSeason?.stats.wins,
        losses: team.currentSeason?.stats.losses,
        draws: team.currentSeason?.stats.draws,
        goalsScored: team.currentSeason?.stats.goalsScored,
        goalsConceded: team.currentSeason?.stats.goalsConceded,
        points: (team.currentSeason?.stats.wins || 0) * 3 + (team.currentSeason?.stats.draws || 0),
        goalDifference: (team.currentSeason?.stats.goalsScored || 0) - (team.currentSeason?.stats.goalsConceded || 0),
      },
      description
    };
  }

  async addManualPlayerAchievement(
    playerId: string,
    achievementData: {
      seasonNumber: number;
      achievementType: ACHIEVEMENT_TYPE;
      rank?: number;
      stats?: {
        goals?: number;
        assists?: number;
        cleanSheets?: number;
        avgRating?: number;
        games?: number;
        playerOfTheMatch?: number;
      };
      description?: string;
    },
    adminUserId: string,
    session?: ClientSession
  ): Promise<{ success: boolean; message: string }> {
    return await transactionService.withTransaction(async (txSession: ClientSession) => {
      const actualSession = session || txSession;

      // Get player and validate
      const player = await this.playerRepository.getPlayerById(playerId, actualSession);
      if (!player) {
        throw new Error(`Player with ID ${playerId} not found`);
      }

      // Get player's team information
      let teamId: Types.ObjectId;
      let teamName = 'Unknown Team';
      let leagueId: Types.ObjectId;
      let leagueName = 'Pro Clubs League';

      if (player.team) {
        const team = await this.teamRepository.getTeamById(player.team.toString(), actualSession);
        if (team) {
          teamName = team.name;
          teamId = team._id as Types.ObjectId;
          if (team.league) {
            const league = await this.leagueRepository.getLeagueById(team.league.toString());
            leagueId = league._id as Types.ObjectId;
            leagueName = league.name;
          }
        }
      }

      // Check if achievement already exists
      const existingAchievement = player.achievementHistory.find(a =>
        a.seasonNumber === achievementData.seasonNumber &&
        a.achievementType === achievementData.achievementType
      );

      if (existingAchievement) {
        throw new Error(`Player already has ${achievementData.achievementType} achievement for season ${achievementData.seasonNumber}`);
      }

      // Add achievement to player's history
      player.achievementHistory.push({
        seasonNumber: achievementData.seasonNumber,
        league: leagueId!,
        leagueName: leagueName,
        achievementType: achievementData.achievementType,
        rank: achievementData.rank,
        teamId: teamId!,
        teamName: teamName,
        stats: achievementData.stats || {},
        description: achievementData.description || `${achievementData.achievementType} - Season ${achievementData.seasonNumber}`,
        achievedDate: new Date()
      });

      await player.save({ session: actualSession });

      return {
        success: true,
        message: `Achievement "${achievementData.achievementType}" added to ${player.name} for season ${achievementData.seasonNumber}`
      };
    });
  }

  private async updatePlayerAchievementHistories(
    playerAchievements: IPlayerAchievement[],
    session: ClientSession
  ): Promise<number> {
    let updatedCount = 0;

    for (const achievement of playerAchievements) {
      const player = await this.playerRepository.getPlayerById(achievement.playerId.toString(), session);

      player.achievementHistory.push({
        seasonNumber: achievement.seasonNumber || 0,
        league: achievement.teamId, // This should be league ID, will fix in implementation
        leagueName: achievement.teamName, // This should be league name, will fix
        achievementType: achievement.achievementType,
        rank: achievement.rank,
        teamId: achievement.teamId,
        teamName: achievement.teamName,
        stats: achievement.stats,
        description: achievement.description,
        achievedDate: new Date()
      });

      await player.save({ session });
      updatedCount++;
    }

    return updatedCount;
  }

  private async updateTeamAchievementHistories(
    teamAchievements: ITeamAchievement[],
    session: ClientSession
  ): Promise<number> {
    let updatedCount = 0;

    for (const achievement of teamAchievements) {
      const team = await this.teamRepository.getTeamById(achievement.teamId.toString(), session);

      team.achievementHistory.push({
        seasonNumber: achievement.seasonNumber || 0,
        league: team.league!,
        leagueName: achievement.teamName, // This should be league name, will fix
        achievementType: achievement.achievementType,
        rank: achievement.rank,
        stats: achievement.stats,
        description: achievement.description,
        achievedDate: new Date()
      });

      await team.save({ session });
      updatedCount++;
    }

    return updatedCount;
  }

  private getOrdinal(num: number): string {
    const suffixes = ["th", "st", "nd", "rd"];
    const v = num % 100;
    return num + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);
  }
}
