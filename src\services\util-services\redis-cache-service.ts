import { RedisClientType, createClient } from "redis";
import dotenv from "dotenv";

import { injectable } from "tsyringe";
import logger from "../../config/logger";
import { CacheService } from "../../interfaces/util-services/cache-service.interface";

dotenv.config(); // set env variables

@injectable()
export class RedisCacheService implements CacheService {
  private client: RedisClientType;
  private isConnected: boolean = false;

  public constructor() {
    this.client = createClient({
      password: process.env.REDIS_PASSWORD,
      socket: {
        host: process.env.REDIS_HOST,
        port: 19402,
      },
    });
    this.initializeClient();
  }

  private async initializeClient() {
    try {
      if (!this.client.isOpen) {
        await this.client.connect();
        this.isConnected = true;
        logger.info("Redis cache service connected successfully");
      }
    } catch (error) {
      logger.error("Failed to connect to Redis cache service:", error);
      this.isConnected = false;
      // Don't throw error to prevent server startup failure
    }
  }

  async set(key: string, value: any, expiresIn?: number): Promise<void> {
    try {
      if (!this.isConnected) {
        logger.warn(`Cache: not connected, skipping set for key ${key}`);
        return;
      }
      const stringValue = JSON.stringify(value);
      logger.info(`Cache: Setting ${key} with expiry ${expiresIn || 'none'}`);
      if (expiresIn) {
        await this.client.set(key, stringValue, { EX: expiresIn });
      } else {
        await this.client.set(key, stringValue);
      }
    } catch (error) {
      logger.error(`Cache: error setting key ${key}:`, error);
    }
  }

  async get(key: string): Promise<any | null> {
    try {
      if (this.isConnected) {
        const value = await this.client.get(key);
        if (value) {
          logger.info(`Cache: cache hit for key ${key}`);
          return value;
        }
      }

      logger.info(`Cache: cache miss for key ${key}`);
      return null;
    } catch (error) {
      logger.error(`Cache: error getting key ${key}:`, error);
      return null;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      if (!this.isConnected) {
        logger.warn(`Cache: not connected, skipping delete for key ${key}`);
        return;
      }
      logger.info(`Cache: removing key ${key} from cache`);
      await this.client.del(key);
    } catch (error) {
      logger.error(`Cache: error deleting key ${key}:`, error);
    }
  }

  async quit(): Promise<void> {
    await this.client.quit();
  }
}
