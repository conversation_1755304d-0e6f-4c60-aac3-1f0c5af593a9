import { Component, Input, Output, EventEmitter } from '@angular/core';
import { News } from '../news.model';
import { AuthService } from '../../../services/auth.service';
import { TabItem } from '../../shared/scrollable-tabs/scrollable-tabs.component';

@Component({
  selector: 'app-news-list',
  templateUrl: './news-list.component.html',
  styleUrl: './news-list.component.scss'
})
export class NewsListComponent {
  @Input() allNews: Array<News> = [];
  @Input() filteredNews: Array<News> = [];
  @Input() selectedIndex: number = 0;
  @Input() isLoading: boolean = false;
  @Input() isAdmin: boolean = false;

  constructor(private authService: AuthService) {}

  @Output() tabChange = new EventEmitter<number>();
  @Output() deleteNews = new EventEmitter<News>();
  @Output() shareNews = new EventEmitter<News>();
  @Output() teamClick = new EventEmitter<string>();
  @Output() playerClick = new EventEmitter<string>();
  @Output() refreshNews = new EventEmitter<void>();
  @Output() likeNews = new EventEmitter<News>();
  @Output() unlikeNews = new EventEmitter<News>();
  @Output() editNews = new EventEmitter<News>();

  tabTypeMap = ['all', 'General', 'Transfer', 'FreeAgent'];

  get newsTabs(): TabItem[] {
    return [
      {
        id: 0,
        label: 'All',
        icon: 'fas fa-globe',
        count: this.allNews.length
      },
      {
        id: 1,
        label: 'General',
        icon: 'fas fa-info-circle',
        count: this.getNewsCountByType('General')
      },
      {
        id: 2,
        label: 'Transfers',
        icon: 'fas fa-exchange-alt',
        count: this.getNewsCountByType('Transfer')
      },
      {
        id: 3,
        label: 'Free Agents',
        icon: 'fas fa-user-free',
        count: this.getNewsCountByType('FreeAgent')
      }
    ];
  }

  onTabChange(index: number): void;
  onTabChange(tab: TabItem): void;
  onTabChange(indexOrTab: number | TabItem): void {
    if (typeof indexOrTab === 'number') {
      this.selectedIndex = indexOrTab;
      this.tabChange.emit(indexOrTab);
    } else {
      this.selectedIndex = indexOrTab.id as number;
      this.tabChange.emit(indexOrTab.id as number);
    }
  }

  onDeleteNewsClick(news: News): void {
    this.deleteNews.emit(news);
  }

  onShareNewsClick(news: News): void {
    this.shareNews.emit(news);
  }

  onTeamClick(teamId: string): void {
    this.teamClick.emit(teamId);
  }

  onPlayerClick(playerId: string): void {
    this.playerClick.emit(playerId);
  }

  onRefreshNews(): void {
    this.refreshNews.emit();
  }

  getNewsTypeLabel(type: string): string {
    return type === 'FreeAgent' ? 'Free Agent' : type;
  }

  getNewsCountByType(type: string): number {
    return this.allNews.filter(news => news.type === type).length;
  }

  trackByNewsId(index: number, news: News): string {
    return news._id || index.toString();
  }

  onLikeNewsClick(news: News): void {
    this.likeNews.emit(news);
  }

  onUnlikeNewsClick(news: News): void {
    this.unlikeNews.emit(news);
  }

  isNewsLikedByCurrentUser(news: News): boolean {
    const currentUser = this.authService.getCurrentUser();
    return currentUser ? news.likes.includes(currentUser.id) : false;
  }

  isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  onEditNewsClick(news: News): void {
    this.editNews.emit(news);
  }

  canEditNews(news: News): boolean {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) return false;

    // Admin can edit any news, or user can edit their own news
    return this.isAdmin || news.createdBy === currentUser.id;
  }

  getNewsTypeClass(type: string): string {
    switch (type) {
      case 'General':
        return 'general-badge';
      case 'Transfer':
        return 'transfer-badge';
      case 'FreeAgent':
        return 'free-agent-badge';
      default:
        return 'default-badge';
    }
  }

  getNewsTypeIcon(type: string): string {
    switch (type) {
      case 'General':
        return 'fas fa-info-circle';
      case 'Transfer':
        return 'fas fa-exchange-alt';
      case 'FreeAgent':
        return 'fas fa-user-free';
      default:
        return 'fas fa-newspaper';
    }
  }
}
