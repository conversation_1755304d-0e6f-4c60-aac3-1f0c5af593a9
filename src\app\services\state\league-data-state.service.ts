import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, timer } from 'rxjs';
import { LeagueTableRow } from '../../shared/models/leagueTableTeam';
import { TopScorer, TopAssister, PlayerDTO } from '@pro-clubs-manager/shared-dtos';

export interface LeagueDataState {
  leagueTable: LeagueTableRow[];
  topScorers: TopScorer[];
  topAssisters: TopAssister[];
  freeAgents: PlayerDTO[];
  lastLeagueTableUpdate: Date | null;
  lastTopScorersUpdate: Date | null;
  lastTopAssistersUpdate: Date | null;
  lastFreeAgentsUpdate: Date | null;
  isLeagueTableLoading: boolean;
  isTopScorersLoading: boolean;
  isTopAssistersLoading: boolean;
  isFreeAgentsLoading: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LeagueDataStateService {
  private readonly initialState: LeagueDataState = {
    leagueTable: [],
    topScorers: [],
    topAssisters: [],
    freeAgents: [],
    lastLeagueTableUpdate: null,
    lastTopScorersUpdate: null,
    lastTopAssistersUpdate: null,
    lastFreeAgentsUpdate: null,
    isLeagueTableLoading: false,
    isTopScorersLoading: false,
    isTopAssistersLoading: false,
    isFreeAgentsLoading: false
  };

  private stateSubject = new BehaviorSubject<LeagueDataState>(this.initialState);
  public state$ = this.stateSubject.asObservable();

  // Cache duration in milliseconds (30 minutes for league table, 12 hours for top scorers/assists, 6 hours for free agents)
  private readonly LEAGUE_TABLE_CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
  private readonly TOP_SCORERS_CACHE_DURATION = 12 * 60 * 60 * 1000; // 12 hours
  private readonly TOP_ASSISTERS_CACHE_DURATION = 12 * 60 * 60 * 1000; // 12 hours
  private readonly FREE_AGENTS_CACHE_DURATION = 6 * 60 * 60 * 1000; // 6 hours

  constructor() {
    this.loadStateFromStorage();
    this.startAutoRefresh();
  }

  get currentState(): LeagueDataState {
    return this.stateSubject.value;
  }

  updateLeagueTable(leagueTable: LeagueTableRow[]): void {
    const newState = {
      ...this.currentState,
      leagueTable,
      lastLeagueTableUpdate: new Date(),
      isLeagueTableLoading: false
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  updateTopScorers(topScorers: TopScorer[]): void {
    const newState = {
      ...this.currentState,
      topScorers,
      lastTopScorersUpdate: new Date(),
      isTopScorersLoading: false
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  updateTopAssisters(topAssisters: TopAssister[]): void {
    const newState = {
      ...this.currentState,
      topAssisters,
      lastTopAssistersUpdate: new Date(),
      isTopAssistersLoading: false
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  updateFreeAgents(freeAgents: PlayerDTO[]): void {
    const newState = {
      ...this.currentState,
      freeAgents,
      lastFreeAgentsUpdate: new Date(),
      isFreeAgentsLoading: false
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  setLeagueTableLoading(isLoading: boolean): void {
    this.updateState({ isLeagueTableLoading: isLoading });
  }

  setTopScorersLoading(isLoading: boolean): void {
    this.updateState({ isTopScorersLoading: isLoading });
  }

  setTopAssistersLoading(isLoading: boolean): void {
    this.updateState({ isTopAssistersLoading: isLoading });
  }

  setFreeAgentsLoading(isLoading: boolean): void {
    this.updateState({ isFreeAgentsLoading: isLoading });
  }

  isLeagueTableStale(): boolean {
    const lastUpdate = this.currentState.lastLeagueTableUpdate;
    if (!lastUpdate) return true;
    return Date.now() - lastUpdate.getTime() > this.LEAGUE_TABLE_CACHE_DURATION;
  }

  isTopScorersStale(): boolean {
    const lastUpdate = this.currentState.lastTopScorersUpdate;
    if (!lastUpdate) return true;
    return Date.now() - lastUpdate.getTime() > this.TOP_SCORERS_CACHE_DURATION;
  }

  isTopAssistersStale(): boolean {
    const lastUpdate = this.currentState.lastTopAssistersUpdate;
    if (!lastUpdate) return true;
    return Date.now() - lastUpdate.getTime() > this.TOP_ASSISTERS_CACHE_DURATION;
  }

  isFreeAgentsStale(): boolean {
    const lastUpdate = this.currentState.lastFreeAgentsUpdate;
    if (!lastUpdate) return true;
    return Date.now() - lastUpdate.getTime() > this.FREE_AGENTS_CACHE_DURATION;
  }

  clearCache(): void {
    this.stateSubject.next(this.initialState);
    this.clearStateFromStorage();
  }

  forceRefreshAll(): void {
    // Clear cache to force fresh data on next request
    const newState = {
      ...this.currentState,
      lastLeagueTableUpdate: null,
      lastTopScorersUpdate: null,
      lastTopAssistersUpdate: null,
      lastFreeAgentsUpdate: null
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  clearAllData(): void {
    console.log('LeagueDataState: Clearing all cached data');
    const newState = {
      ...this.initialState
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
    // Also clear localStorage
    localStorage.removeItem('league-data-state');
  }

  private updateState(partialState: Partial<LeagueDataState>): void {
    const newState = { ...this.currentState, ...partialState };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  private startAutoRefresh(): void {
    // Check every 5 minutes if data needs refreshing
    timer(0, 5 * 60 * 1000).subscribe(() => {
      // This will be used by components to trigger refresh when needed
      // Components should subscribe to this service and check staleness
    });
  }

  private saveStateToStorage(state: LeagueDataState): void {
    try {
      const stateToSave = {
        ...state,
        lastLeagueTableUpdate: state.lastLeagueTableUpdate?.toISOString(),
        lastTopScorersUpdate: state.lastTopScorersUpdate?.toISOString(),
        lastTopAssistersUpdate: state.lastTopAssistersUpdate?.toISOString()
      };
      localStorage.setItem('league-data-state', JSON.stringify(stateToSave));
    } catch (error) {
      console.warn('Failed to save league data state to localStorage:', error);
    }
  }

  private loadStateFromStorage(): void {
    try {
      const savedState = localStorage.getItem('league-data-state');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        const restoredState = {
          ...this.initialState,
          ...parsedState,
          lastLeagueTableUpdate: parsedState.lastLeagueTableUpdate ? new Date(parsedState.lastLeagueTableUpdate) : null,
          lastTopScorersUpdate: parsedState.lastTopScorersUpdate ? new Date(parsedState.lastTopScorersUpdate) : null,
          lastTopAssistersUpdate: parsedState.lastTopAssistersUpdate ? new Date(parsedState.lastTopAssistersUpdate) : null,
          // Reset loading states on app start
          isLeagueTableLoading: false,
          isTopScorersLoading: false,
          isTopAssistersLoading: false
        };
        this.stateSubject.next(restoredState);
      }
    } catch (error) {
      console.warn('Failed to load league data state from localStorage:', error);
    }
  }

  private clearStateFromStorage(): void {
    try {
      localStorage.removeItem('league-data-state');
    } catch (error) {
      console.warn('Failed to clear league data state from localStorage:', error);
    }
  }
}
