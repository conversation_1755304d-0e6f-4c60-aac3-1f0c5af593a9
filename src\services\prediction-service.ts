import { ClientSession, Types } from "mongoose";
import { inject, injectable } from "tsyringe";
import logger from "../config/logger";
import { BadRequestError, NotFoundError, UnauthorizedError } from "../errors";
import { IPredictionRepository, PredictionDistribution } from "../repositories/prediction-repository";
import { IGameRepository } from "../interfaces/game";
import { PREDICTION_OUTCOME } from "../models/prediction/prediction";
import { GAME_STATUS } from "@pro-clubs-manager/shared-dtos";
import { transactionService } from "./util-services/transaction-service";

export interface PredictionDTO {
  id: string;
  gameId: string;
  userId: string;
  predictedOutcome: PREDICTION_OUTCOME;
  confidence: number;
  createdAt: Date;
  updatedAt: Date;
  user?: {
    id: string;
    name: string;
    profileImage?: string;
  };
}

export interface PredictionDistributionWithUser extends PredictionDistribution {
  userPrediction?: {
    id: string;
    outcome: PREDICTION_OUTCOME;
    confidence: number;
    createdAt: Date;
  };
}

export interface IPredictionService {
  createOrUpdatePrediction(gameId: string, userId: string, predictedOutcome: PREDICTION_OUTCOME, confidence?: number): Promise<PredictionDTO>;
  deletePrediction(predictionId: string, userId: string): Promise<void>;
  getPredictionDistribution(gameId: string, userId?: string): Promise<PredictionDistributionWithUser>;
  getUserPredictions(userId: string, limit?: number): Promise<PredictionDTO[]>;
  getPredictionById(predictionId: string): Promise<PredictionDTO>;
}

@injectable()
export class PredictionService implements IPredictionService {
  
  constructor(
    @inject("IPredictionRepository") private predictionRepository: IPredictionRepository,
    @inject("IGameRepository") private gameRepository: IGameRepository
  ) {}

  async createOrUpdatePrediction(
    gameId: string, 
    userId: string, 
    predictedOutcome: PREDICTION_OUTCOME, 
    confidence: number = 3
  ): Promise<PredictionDTO> {
    logger.info(`PredictionService: creating/updating prediction for game ${gameId} by user ${userId}`);

    // Validate game exists and is scheduled
    const game = await this.gameRepository.getGameById(gameId);
    
    if (game.status !== GAME_STATUS.SCHEDULED) {
      throw new BadRequestError("Cannot predict on games that have already started or finished");
    }

    // Check if prediction already exists
    const existingPrediction = await this.predictionRepository.getUserPrediction(gameId, userId);

    return await transactionService.withTransaction(async (session) => {
      let prediction;
      
      if (existingPrediction) {
        // Update existing prediction
        prediction = await this.predictionRepository.updatePrediction(
          existingPrediction._id.toString(),
          predictedOutcome,
          confidence,
          session
        );
      } else {
        // Create new prediction
        prediction = await this.predictionRepository.createPrediction(
          new Types.ObjectId(gameId),
          new Types.ObjectId(userId),
          predictedOutcome,
          confidence,
          session
        );
      }

      return this.mapToDTO(prediction);
    });
  }

  async deletePrediction(predictionId: string, userId: string): Promise<void> {
    logger.info(`PredictionService: deleting prediction ${predictionId} by user ${userId}`);

    const prediction = await this.predictionRepository.getPredictionById(predictionId);

    logger.info(`PredictionService: retrieved prediction:`, {
      id: prediction.id,
      gameId: prediction.gameId,
      gameIdType: typeof prediction.gameId,
      userId: prediction.userId,
      userIdType: typeof prediction.userId,
      gameIdIsObjectId: prediction.gameId instanceof Types.ObjectId
    });

    // Since we're not populating gameId anymore, it should be a simple ObjectId
    const gameIdString = prediction.gameId.toString();

    logger.info(`PredictionService: using gameId string: ${gameIdString}`);

    // Verify user owns this prediction
    if (!prediction.userId.equals(userId)) {
      throw new UnauthorizedError("You can only delete your own predictions");
    }

    // Verify game is still scheduled
    logger.info(`PredictionService: verifying game status for game ${gameIdString}`);

    try {
      const game = await this.gameRepository.getGameById(gameIdString);

      logger.info(`PredictionService: game ${gameIdString} has status: ${game.status}`);

      if (game.status !== GAME_STATUS.SCHEDULED) {
        throw new BadRequestError("Cannot delete predictions for games that have already started or finished");
      }
    } catch (error: any) {
      logger.error(`PredictionService: Error getting game ${gameIdString}:`, error);
      throw error; // Re-throw the original error
    }

    await transactionService.withTransaction(async (session) => {
      await this.predictionRepository.deletePrediction(predictionId, session);
    });
  }

  async getPredictionDistribution(gameId: string, userId?: string): Promise<PredictionDistributionWithUser> {
    logger.info(`PredictionService: getting prediction distribution for game ${gameId}`);

    // Verify game exists
    await this.gameRepository.getGameById(gameId);

    const distribution = await this.predictionRepository.getPredictionDistribution(gameId);
    
    let userPrediction;
    if (userId) {
      const prediction = await this.predictionRepository.getUserPrediction(gameId, userId);
      if (prediction) {
        userPrediction = {
          id: prediction._id.toString(),
          outcome: prediction.predictedOutcome,
          confidence: prediction.confidence || 3,
          createdAt: prediction.createdAt
        };
      }
    }

    return {
      ...distribution,
      userPrediction
    };
  }

  async getUserPredictions(userId: string, limit: number = 20): Promise<PredictionDTO[]> {
    logger.info(`PredictionService: getting predictions for user ${userId}`);

    const predictions = await this.predictionRepository.getUserPredictions(userId, limit);
    
    return predictions.map(prediction => this.mapToDTO(prediction));
  }

  async getPredictionById(predictionId: string): Promise<PredictionDTO> {
    logger.info(`PredictionService: getting prediction ${predictionId}`);

    const prediction = await this.predictionRepository.getPredictionById(predictionId);
    
    return this.mapToDTO(prediction);
  }

  private mapToDTO(prediction: any): PredictionDTO {
    return {
      id: prediction._id.toString(),
      gameId: prediction.gameId.toString(),
      userId: prediction.userId.toString(),
      predictedOutcome: prediction.predictedOutcome,
      confidence: prediction.confidence || 3,
      createdAt: prediction.createdAt,
      updatedAt: prediction.updatedAt,
      user: prediction.userId?.name ? {
        id: prediction.userId._id?.toString() || prediction.userId.toString(),
        name: prediction.userId.name,
        profileImage: prediction.userId.profileImage
      } : undefined
    };
  }
}
