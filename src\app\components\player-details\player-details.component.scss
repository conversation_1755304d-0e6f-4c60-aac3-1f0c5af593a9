/* === PLAYER DETAILS COMPONENT === */

.player-details-container {
    padding: var(--spacing-xl);
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }
}

/* === CONTENT GRID === */
.player-content {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);

    @media (max-width: 1024px) {
        grid-template-columns: 300px 1fr;
        gap: var(--spacing-lg);
    }

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}

/* === STATS AREA === */
.player-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);

    .transfer-history {
        background: var(--surface-primary);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-primary);
        box-shadow: var(--shadow-md);
        overflow: hidden;
    }
}

/* === RESPONSIVE === */
@media (max-width: 480px) {
    .player-details-container {
        padding: var(--spacing-md);
        margin: 0 5px;
        max-width: calc(100vw - 10px);
        box-sizing: border-box;
    }

    .player-content {
        gap: var(--spacing-md);
        max-width: 100%;
        overflow-x: hidden;
    }

    .player-stats {
        gap: var(--spacing-md);
        max-width: 100%;
        overflow-x: hidden;
    }
}
