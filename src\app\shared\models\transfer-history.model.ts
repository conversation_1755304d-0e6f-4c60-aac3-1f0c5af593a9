export interface ITransferHistoryEntry {
  fromTeam: {
    id: string;
    name: string;
    imgUrl?: string;
  } | null; // null for free agent
  toTeam: {
    id: string;
    name: string;
    imgUrl?: string;
  } | null; // null when released to free agency
  transferDate: Date;
  seasonNumber: number;
  league: string; // ObjectId as string
}

export enum TransferType {
  SIGNING = 'signing',      // Free Agent → Team
  TRANSFER = 'transfer',    // Team → Team
  RELEASE = 'release'       // Team → Free Agent
}

export interface TransferHistoryDisplay extends ITransferHistoryEntry {
  transferType: TransferType;
  formattedDate: string;
  timeAgo: string;
}
