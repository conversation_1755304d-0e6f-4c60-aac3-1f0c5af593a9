<!-- Share Modal -->
<div class="share-modal-overlay" *ngIf="show" (click)="closeModal()">
  <div class="share-modal-content" (click)="$event.stopPropagation()">
    <!-- Modal Header -->
    <div class="modal-header">
      <h3 class="modal-title">
        <i class="fas fa-share-alt"></i>
        Share News
      </h3>
      <button class="close-button" (click)="closeModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="modal-body" *ngIf="selectedNews">
      <!-- News Preview -->
      <div class="news-preview">
        <div class="news-type-badge" [ngClass]="selectedNews.type.toLowerCase()">
          {{ getNewsTypeLabel(selectedNews.type) }}
        </div>
        <h4 class="news-title">{{ selectedNews.title }}</h4>
        <p class="news-content">{{ selectedNews.content }}</p>
      </div>

      <!-- Share Options -->
      <div class="share-options">
        <h5 class="share-section-title">Share via Social Media</h5>
        
        <div class="share-buttons-grid">
          <button class="share-button whatsapp" (click)="shareViaWhatsApp()">
            <i class="fab fa-whatsapp"></i>
            <span>WhatsApp</span>
          </button>

          <button class="share-button twitter" (click)="shareViaTwitter()">
            <i class="fab fa-twitter"></i>
            <span>Twitter</span>
          </button>

          <button class="share-button facebook" (click)="shareViaFacebook()">
            <i class="fab fa-facebook"></i>
            <span>Facebook</span>
          </button>

          <button class="share-button telegram" (click)="shareViaTelegram()">
            <i class="fab fa-telegram"></i>
            <span>Telegram</span>
          </button>

          <button class="share-button copy" (click)="copyToClipboard()">
            <i class="fas fa-copy"></i>
            <span>Copy Link</span>
          </button>
        </div>
      </div>

      <!-- Image Generation Section -->
      <div class="image-generation-section">
        <h5 class="share-section-title">Generate Share Image</h5>
        
        <div class="image-actions">
          <button class="generate-image-button" (click)="generateShareImage()" [disabled]="!!generatedImageUrl">
            <i class="fas fa-image"></i>
            <span>{{ generatedImageUrl ? 'Image Generated' : 'Generate Image' }}</span>
          </button>

          <div class="image-download-actions" *ngIf="generatedImageUrl">
            <button class="download-button" (click)="downloadImage()">
              <i class="fas fa-download"></i>
              <span>Download</span>
            </button>

            <button class="share-image-button" (click)="shareImageViaWhatsApp()">
              <i class="fab fa-whatsapp"></i>
              <span>Share Image</span>
            </button>
          </div>
        </div>

        <!-- Generated Image Preview -->
        <div class="image-preview" *ngIf="generatedImageUrl">
          <img [src]="generatedImageUrl" alt="Generated share image" class="generated-image">
        </div>
      </div>
    </div>

    <!-- Modal Footer -->
    <div class="modal-footer">
      <button class="cancel-button" (click)="closeModal()">
        <i class="fas fa-times"></i>
        Close
      </button>
    </div>
  </div>
</div>
