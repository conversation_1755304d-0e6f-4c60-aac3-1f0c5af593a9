import { Component, On<PERSON>nit, On<PERSON><PERSON><PERSON>, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ChatService, ChatMessage } from '../../services/chat.service';
import { AuthService } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss']
})
export class ChatComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  @ViewChild('messageInput') private messageInput!: ElementRef;

  messageForm: FormGroup;
  messages: ChatMessage[] = [];
  loading = false;
  hasMore = true;
  replyingTo: ChatMessage | null = null;
  editingMessage: ChatMessage | null = null;
  showEmojiPicker = false;
  selectedMessageForEmoji: string | null = null;

  private subscriptions: Subscription[] = [];
  private shouldScrollToBottom = false;

  // Common emojis for quick reactions
  commonEmojis = ['👍', '👎', '❤️', '😂', '😮', '😢', '😡', '🎉', '🔥', '💯'];

  constructor(
    private chatService: ChatService,
    private authService: AuthService,
    private notificationService: NotificationService,
    private fb: FormBuilder
  ) {
    this.messageForm = this.fb.group({
      content: ['', [Validators.required, Validators.maxLength(1000)]]
    });
  }

  ngOnInit(): void {
    this.subscribeToChat();
    this.loadInitialMessages();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.chatService.clearMessages();
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  private subscribeToChat(): void {
    const messagesSubscription = this.chatService.messages$.subscribe(messages => {
      const previousCount = this.messages.length;
      this.messages = messages;
      
      // Scroll to bottom if new message was added (not when loading more)
      if (messages.length > previousCount && previousCount > 0) {
        this.shouldScrollToBottom = true;
      }
    });

    const loadingSubscription = this.chatService.loading$.subscribe(loading => {
      this.loading = loading;
    });

    const hasMoreSubscription = this.chatService.hasMore$.subscribe(hasMore => {
      this.hasMore = hasMore;
    });

    this.subscriptions.push(messagesSubscription, loadingSubscription, hasMoreSubscription);
  }

  private async loadInitialMessages(): Promise<void> {
    try {
      await this.chatService.loadMessages();
      this.shouldScrollToBottom = true;
    } catch (error) {
      this.notificationService.error('Failed to load chat messages');
    }
  }

  async loadMoreMessages(): Promise<void> {
    if (!this.hasMore || this.loading) {
      return;
    }

    try {
      const scrollHeight = this.messagesContainer.nativeElement.scrollHeight;
      await this.chatService.loadMoreMessages();
      
      // Maintain scroll position after loading more messages
      setTimeout(() => {
        const newScrollHeight = this.messagesContainer.nativeElement.scrollHeight;
        this.messagesContainer.nativeElement.scrollTop = newScrollHeight - scrollHeight;
      }, 100);
    } catch (error) {
      this.notificationService.error('Failed to load more messages');
    }
  }

  async sendMessage(): Promise<void> {
    if (this.messageForm.invalid || this.loading) {
      return;
    }

    const content = this.messageForm.get('content')?.value?.trim();
    if (!content) {
      return;
    }

    try {
      await this.chatService.sendMessage({
        content,
        replyTo: this.replyingTo?.id
      });

      this.messageForm.reset();
      this.cancelReply();
      this.shouldScrollToBottom = true;
      this.messageInput.nativeElement.focus();
    } catch (error) {
      this.notificationService.error('Failed to send message');
    }
  }

  async editMessage(): Promise<void> {
    if (!this.editingMessage || this.messageForm.invalid || this.loading) {
      return;
    }

    const content = this.messageForm.get('content')?.value?.trim();
    if (!content) {
      return;
    }

    try {
      await this.chatService.editMessage(this.editingMessage.id, { content });
      this.cancelEdit();
      this.notificationService.success('Message updated');
    } catch (error: any) {
      this.notificationService.error(error.message || 'Failed to edit message');
    }
  }

  async deleteMessage(message: ChatMessage): Promise<void> {
    if (!confirm('Are you sure you want to delete this message?')) {
      return;
    }

    try {
      await this.chatService.deleteMessage(message.id);
      this.notificationService.success('Message deleted');
    } catch (error) {
      this.notificationService.error('Failed to delete message');
    }
  }

  async addReaction(messageId: string, emoji: string): Promise<void> {
    try {
      await this.chatService.addReaction(messageId, { emoji });
      this.hideEmojiPicker();
    } catch (error) {
      this.notificationService.error('Failed to add reaction');
    }
  }

  startReply(message: ChatMessage): void {
    this.replyingTo = message;
    this.cancelEdit();
    this.messageInput.nativeElement.focus();
  }

  cancelReply(): void {
    this.replyingTo = null;
  }

  startEdit(message: ChatMessage): void {
    this.editingMessage = message;
    this.messageForm.patchValue({ content: message.content });
    this.cancelReply();
    this.messageInput.nativeElement.focus();
  }

  cancelEdit(): void {
    this.editingMessage = null;
    this.messageForm.reset();
  }

  showEmojiPickerForMessage(messageId: string): void {
    this.selectedMessageForEmoji = messageId;
    this.showEmojiPicker = true;
  }

  hideEmojiPicker(): void {
    this.showEmojiPicker = false;
    this.selectedMessageForEmoji = null;
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      if (this.editingMessage) {
        this.editMessage();
      } else {
        this.sendMessage();
      }
    }
  }

  private scrollToBottom(): void {
    try {
      this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  onScroll(): void {
    const element = this.messagesContainer.nativeElement;
    if (element.scrollTop === 0 && this.hasMore && !this.loading) {
      this.loadMoreMessages();
    }
  }

  getMessageTime(timestamp: Date): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  }

  isCurrentUser(message: ChatMessage): boolean {
    const currentUser = this.authService.getCurrentUser();
    return currentUser?.id === message.author.id;
  }

  canEditMessage(message: ChatMessage): boolean {
    return this.chatService.canEditMessage(message);
  }

  canDeleteMessage(message: ChatMessage): boolean {
    return this.chatService.canDeleteMessage(message);
  }

  getProfilePicture(message: ChatMessage): string {
    return message.author.profilePicture || 'assets/icons/default-profile.png';
  }

  async refreshChat(): Promise<void> {
    try {
      await this.chatService.refreshMessages();
      this.shouldScrollToBottom = true;
      this.notificationService.success('Chat refreshed');
    } catch (error) {
      this.notificationService.error('Failed to refresh chat');
    }
  }

  trackByMessageId(index: number, message: ChatMessage): string {
    return message.id;
  }
}
