<div class="cms-table-container">
    <div class="table-header">
        <h3>
            <i class="fas fa-newspaper"></i>
            News Management
        </h3>
        <div class="table-actions">
            <button class="action-btn create-btn" routerLink="/add-news">
                <i class="fas fa-plus"></i>
                Add News
            </button>
        </div>
    </div>

    <div class="table-wrapper">
        <table class="cms-table">
            <thead>
                <tr>
                    <th>Title</th>
                    <th>Type</th>
                    <th>Content</th>
                    <th>Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let newsItem of news" class="table-row">
                    <td class="title-cell">
                        <div class="news-title">
                            <span class="title-text">{{ newsItem.title }}</span>
                            <span class="news-id">ID: {{ newsItem._id }}</span>
                        </div>
                    </td>
                    <td>
                        <span class="type-badge" [class]="getNewsTypeClass(newsItem)">
                            {{ getNewsType(newsItem) }}
                        </span>
                    </td>
                    <td class="content-cell">
                        <span class="content-preview">{{ truncateContent(newsItem.content) }}</span>
                    </td>
                    <td>
                        <span class="date-text">{{ getNewsDate(newsItem) }}</span>
                    </td>
                    <td class="actions-cell">
                        <div class="action-buttons">
                            <button class="action-btn view-btn" 
                                    (click)="viewNews(newsItem._id)"
                                    title="View News">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn edit-btn" 
                                    (click)="editNews(newsItem._id)"
                                    title="Edit News">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-btn" 
                                    (click)="deleteNews(newsItem)"
                                    [disabled]="isNewsDeleting(newsItem._id)"
                                    title="Delete News">
                                <i class="fas fa-trash" *ngIf="!isNewsDeleting(newsItem._id)"></i>
                                <i class="fas fa-spinner fa-spin" *ngIf="isNewsDeleting(newsItem._id)"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="news.length === 0">
            <div class="empty-icon">
                <i class="fas fa-newspaper"></i>
            </div>
            <h4>No News Found</h4>
            <p>No news articles match your current search criteria.</p>
            <button class="action-btn create-btn" routerLink="/add-news">
                <i class="fas fa-plus"></i>
                Create First News Article
            </button>
        </div>
    </div>
</div>
