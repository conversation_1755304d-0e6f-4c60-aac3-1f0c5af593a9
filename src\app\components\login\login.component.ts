import { Component, OnInit, AfterViewInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { NotificationService } from '../../services/notification.service';
import { AuthService, LoginRequest } from '../../services/auth.service';
import { GoogleAuthService, GoogleUser } from '../../services/google-auth.service';

@Component({
  selector: 'login',
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent implements OnInit, AfterViewInit {
  loginFormGroup: FormGroup = new FormGroup({});
  isLoading = false;
  returnUrl = '/dashboard';

  formControls = [
    { control: new FormControl('', [Validators.required, Validators.email]), field: 'email', displayText: 'Email', type: 'email' },
    { control: new FormControl('', Validators.required), field: 'password', displayText: 'Password', type: 'password' },
  ];

  constructor(
    private notificationService: NotificationService,
    private googleAuthService: GoogleAuthService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) { }

  ngOnInit() {
    this.loadFormControl();

    // Get return URL from route parameters or default to '/dashboard'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';

    // Check if user is already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate([this.returnUrl]);
      return;
    }
  }

  async ngAfterViewInit(): Promise<void> {
    try {
      await this.googleAuthService.initializeGoogleSignIn((response: GoogleUser) => {
        this.handleGoogleLogin(response);
      });

      // Render the Google Sign-In button
      setTimeout(() => {
        this.googleAuthService.renderSignInButton('google-signin-button');
      }, 100);
    } catch (error) {
      console.error('Failed to initialize Google Sign-In:', error);
    }
  }

  loadFormControl() {
    let group: any = {};
    this.formControls.forEach(item => {
      group[item.field] = item.control;
    });

    this.loginFormGroup = new FormGroup(group);
  }

  async onSubmit() {
    if (this.loginFormGroup.valid && !this.isLoading) {
      this.isLoading = true;

      try {
        const loginData: LoginRequest = {
          email: this.loginFormGroup.get('email')?.value,
          password: this.loginFormGroup.get('password')?.value
        };

        await this.authService.login(loginData);
        this.notificationService.success('Login successful!');
        this.router.navigate([this.returnUrl]);
      } catch (error: any) {
        this.notificationService.error(error.message || 'Login failed');
      } finally {
        this.isLoading = false;
      }
    }
  }

  async handleGoogleLogin(response: GoogleUser) {
    if (!response.credential) {
      this.notificationService.error('Google authentication failed');
      return;
    }

    this.isLoading = true;

    try {
      // Parse the JWT token to get user info
      const userInfo = this.googleAuthService.parseJWT(response.credential);
      if (!userInfo) {
        throw new Error('Failed to parse Google token');
      }

      await this.authService.googleAuth(response.credential);
      this.notificationService.success('Google login successful!');
      this.router.navigate([this.returnUrl]);
    } catch (error: any) {
      this.notificationService.error(error.message || 'Google login failed');
    } finally {
      this.isLoading = false;
    }
  }

  isRequiredForm(control: FormControl) {
    if (control.errors) {
      return control.errors['required'];
    }
    return false;
  }

  isEmailInvalid(control: FormControl) {
    if (control.errors) {
      return control.errors['email'];
    }
    return false;
  }

  loginWithGoogle() {
    // Google Sign-In is now handled automatically by the button
    // The button is rendered in ngAfterViewInit
    console.log('Google Sign-In button should be visible');
  }

  navigateToSignUp() {
    this.router.navigate(['/sign-up']);
  }
}
