<div class="news-list-container" *ngIf="!isLoading && allNews && allNews.length > 0">
    <!-- News Header -->
    <div class="news-header">
        <div class="header-content">
            <h1 class="news-title">
                <i class="fas fa-newspaper"></i>
                League <span class="highlight">News</span>
            </h1>
        </div>
        <div class="header-actions">
            <button class="refresh-btn"
                    (click)="onRefreshNews()"
                    [disabled]="isLoading"
                    title="Refresh news">
                <i class="fas fa-sync-alt" [class.spinning]="isLoading"></i>
                Refresh
            </button>
            <div class="action-button" *ngIf="isAdmin" routerLink="/add-news">
                <i class="fas fa-plus"></i>
                <span>Add News</span>
            </div>
        </div>
    </div>

    <!-- News Filters -->
    <div class="news-filters">
        <app-scrollable-tabs
            [tabs]="newsTabs"
            [activeTabId]="selectedIndex"
            [showArrows]="true"
            [showCounts]="true"
            variant="default"
            (tabChange)="onTabChange($event)">
        </app-scrollable-tabs>
    </div>

    <!-- News Feed -->
    <div class="news-feed">
        <div class="news-item"
             *ngFor="let item of filteredNews; trackBy: trackByNewsId"
             [class.transfer-news]="item.type === 'Transfer'">

            <!-- News Header -->
            <div class="news-item-header">
                <div class="news-meta">
                    <div class="news-type-badge" [class]="getNewsTypeClass(item.type)">
                        <i [class]="getNewsTypeIcon(item.type)"></i>
                        <span>{{ getNewsTypeLabel(item.type) }}</span>
                    </div>
                    <div class="news-date">
                        <i class="fas fa-clock"></i>
                        <span>{{ item.createdAt | date:'MMM d, y' }}</span>
                    </div>
                </div>
                <div class="news-actions">
                    <!-- Like/Unlike Button (Only for authenticated users) -->
                    <div *ngIf="isAuthenticated()" class="like-section">
                        <button class="action-btn like-btn"
                                [class.liked]="isNewsLikedByCurrentUser(item)"
                                (click)="isNewsLikedByCurrentUser(item) ? onUnlikeNewsClick(item) : onLikeNewsClick(item)"
                                [title]="isNewsLikedByCurrentUser(item) ? 'Unlike' : 'Like'">
                            <i [class]="isNewsLikedByCurrentUser(item) ? 'fas fa-heart' : 'far fa-heart'"></i>
                            <span class="like-count">{{ item.likeCount || 0 }}</span>
                        </button>
                    </div>

                    <!-- Like Count (For non-authenticated users) -->
                    <div *ngIf="!isAuthenticated() && (item.likeCount || 0) > 0" class="like-display">
                        <i class="fas fa-heart"></i>
                        <span class="like-count">{{ item.likeCount }}</span>
                    </div>

                    <!-- Share Button (Always Visible) -->
                    <button class="action-btn share-btn"
                            (click)="onShareNewsClick(item)"
                            title="Share News">
                        <i class="fas fa-share-alt"></i>
                    </button>

                    <!-- Edit/Delete (For admin or author) -->
                    <div *ngIf="canEditNews(item)" class="admin-actions">
                        <button class="action-btn edit-btn"
                                (click)="onEditNewsClick(item)"
                                title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button *ngIf="isAdmin"
                                class="action-btn delete-btn"
                                (click)="onDeleteNewsClick(item)"
                                title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- News Title -->
            <h2 class="news-item-title">{{ item.title }}</h2>

            <!-- News Content -->
            <div class="news-content">
                <!-- General News Content -->
                <div *ngIf="item.type !== 'Transfer' && item.type !== 'FreeAgent'" class="general-content">
                    <p>{{ item.content }}</p>
                </div>

                <!-- Transfer News Content -->
                <div *ngIf="item.type === 'Transfer' && item.transferData" class="transfer-content">
                    <div class="transfer-visual">
                        <!-- Player -->
                        <div class="transfer-entity player-entity"
                             (click)="onPlayerClick(item.transferData.playerDetails.id)">
                            <div class="entity-avatar">
                                <img [src]="item.transferData.playerDetails.imgUrl || 'assets/Icons/User.jpg'"
                                     [alt]="item.transferData.playerDetails.name"
                                     class="player-image">
                            </div>
                            <div class="entity-info">
                                <span class="entity-name">{{ item.transferData.playerDetails.name }}</span>
                                <span class="entity-label">Player</span>
                            </div>
                        </div>

                        <!-- Transfer Arrow -->
                        <div class="transfer-arrow">
                            <div class="arrow-line"></div>
                            <i class="fas fa-arrow-right"></i>
                        </div>

                        <!-- From Team -->
                        <div class="transfer-entity from-team"
                             (click)="onTeamClick(item.transferData.fromTeam.id)">
                            <div class="entity-avatar">
                                <img [src]="item.transferData.fromTeam.imgUrl || 'assets/Icons/FreeAgent.png'"
                                     [alt]="item.transferData.fromTeam.name || 'Free Agent'"
                                     class="team-image">
                            </div>
                            <div class="entity-info">
                                <span class="entity-name">{{ item.transferData.fromTeam.name || 'Free Agent' }}</span>
                                <span class="entity-label">From</span>
                            </div>
                        </div>

                        <!-- Transfer Arrow -->
                        <div class="transfer-arrow">
                            <div class="arrow-line"></div>
                            <i class="fas fa-arrow-right"></i>
                        </div>

                        <!-- To Team -->
                        <div class="transfer-entity to-team"
                             (click)="onTeamClick(item.transferData.toTeam.id)">
                            <div class="entity-avatar">
                                <img [src]="item.transferData.toTeam.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                                     [alt]="item.transferData.toTeam.name"
                                     class="team-image">
                            </div>
                            <div class="entity-info">
                                <span class="entity-name">{{ item.transferData.toTeam.name }}</span>
                                <span class="entity-label">To</span>
                            </div>
                        </div>
                    </div>

                    <div class="transfer-description" *ngIf="item.content">
                        <p>{{ item.content }}</p>
                    </div>
                </div>

                <!-- Free Agent News Content -->
                <div *ngIf="item.type === 'FreeAgent' && item.freeAgentData" class="free-agent-content">
                    <div class="free-agent-visual">
                        <!-- Player -->
                        <div class="free-agent-entity player-entity"
                             (click)="onPlayerClick(item.freeAgentData.playerDetails.id)">
                            <div class="entity-avatar">
                                <img [src]="item.freeAgentData.playerDetails.imgUrl || 'assets/Icons/User.jpg'"
                                     [alt]="item.freeAgentData.playerDetails.name"
                                     class="player-image">
                            </div>
                            <div class="entity-info">
                                <span class="entity-name">{{ item.freeAgentData.playerDetails.name }}</span>
                                <span class="entity-details">{{ item.freeAgentData.playerDetails.position }} • Age {{ item.freeAgentData.playerDetails.age }}</span>
                            </div>
                        </div>

                        <!-- Free Agent Status -->
                        <div class="free-agent-status">
                            <div class="status-badge">
                                <i class="fas fa-user-free"></i>
                                <span>Free Agent</span>
                            </div>
                            <div class="status-text">Looking for new team</div>
                        </div>

                        <!-- Previous Teams -->
                        <div class="previous-teams" *ngIf="item.freeAgentData.previousTeams && item.freeAgentData.previousTeams.length > 0">
                            <div class="previous-teams-header">
                                <i class="fas fa-history"></i>
                                <span>Previous Teams</span>
                            </div>
                            <div class="teams-list">
                                <div class="team-item"
                                     *ngFor="let team of item.freeAgentData.previousTeams.slice(0, 3)"
                                     (click)="onTeamClick(team.id)">
                                    <img [src]="team.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                                         [alt]="team.name"
                                         class="team-logo">
                                    <div class="team-info">
                                        <span class="team-name">{{ team.name }}</span>
                                        <span class="season">Season {{ team.seasonNumber }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="free-agent-description" *ngIf="item.content">
                        <p>{{ item.content }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading State -->
<div class="loading-container" *ngIf="isLoading">
    <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Loading news...</span>
    </div>
</div>

<!-- Empty State -->
<div class="empty-state" *ngIf="!isLoading && (!allNews || allNews.length === 0)">
    <div class="empty-content">
        <i class="fas fa-newspaper"></i>
        <h3>No News Available</h3>
        <p>There are no news articles to display at the moment.</p>
        <button class="refresh-btn" (click)="onRefreshNews()">
            <i class="fas fa-sync-alt"></i>
            Refresh
        </button>
    </div>
</div>
