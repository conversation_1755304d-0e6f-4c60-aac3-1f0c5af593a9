/* === MODERN ASSIGN PLAYER DESIGN === */

.assign-player-container {
    min-height: 100vh;
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    font-family: var(--font-sans);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }
}

.assign-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);

    .back-button {
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-sm) var(--spacing-md);
        color: var(--text-primary);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--text-sm);
        font-weight: var(--font-weight-medium);

        &:hover {
            background: var(--surface-tertiary);
            border-color: var(--border-secondary);
            transform: translateY(-1px);
        }

        i {
            font-size: var(--text-xs);
        }
    }

    .assign-title {
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        i {
            color: var(--primary);
        }

        @media (max-width: 768px) {
            font-size: var(--text-xl);
        }
    }
}

.assign-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.selection-section {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
    }

    .selection-group {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);

        .selection-label {
            font-size: var(--text-sm);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            i {
                color: var(--primary);
                font-size: var(--text-sm);
            }
        }
    }
}

.player-preview {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    overflow: hidden;

    .preview-header {
        padding: var(--spacing-lg);
        background: var(--surface-secondary);
        border-bottom: 1px solid var(--border-primary);

        .preview-title {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            i {
                color: var(--primary);
            }
        }
    }

    .player-card {
        padding: var(--spacing-xl);
        display: flex;
        gap: var(--spacing-xl);
        align-items: flex-start;

        @media (max-width: 768px) {
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: var(--spacing-lg);
        }

        .player-avatar {
            flex-shrink: 0;

            .player-image {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                object-fit: cover;
                border: 4px solid var(--border-primary);
                box-shadow: var(--shadow-lg);
                transition: all 0.3s ease;

                &:hover {
                    transform: scale(1.05);
                    border-color: var(--primary);
                }

                @media (max-width: 768px) {
                    width: 100px;
                    height: 100px;
                }
            }
        }

        .player-info {
            flex: 1;

            .player-name {
                font-size: var(--text-2xl);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
                margin: 0 0 var(--spacing-lg) 0;
                background: linear-gradient(135deg, var(--primary), var(--accent-primary));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;

                @media (max-width: 768px) {
                    font-size: var(--text-xl);
                }
            }

            .player-stats {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: var(--spacing-md);

                @media (max-width: 768px) {
                    grid-template-columns: 1fr;
                }

                .stat-item {
                    background: var(--surface-secondary);
                    border-radius: var(--radius-lg);
                    padding: var(--spacing-md);
                    border: 1px solid var(--border-primary);
                    transition: all 0.3s ease;

                    &:hover {
                        border-color: var(--border-secondary);
                        transform: translateY(-2px);
                    }

                    &.full-width {
                        grid-column: 1 / -1;
                    }

                    .stat-label {
                        display: block;
                        font-size: var(--text-xs);
                        color: var(--text-secondary);
                        font-weight: var(--font-weight-semibold);
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        margin-bottom: var(--spacing-xs);
                    }

                    .stat-value {
                        display: block;
                        font-size: var(--text-base);
                        color: var(--text-primary);
                        font-weight: var(--font-weight-semibold);
                    }
                }
            }
        }
    }
}

.assign-actions {
    display: flex;
    justify-content: center;
    padding: var(--spacing-lg);

    .assign-button {
        background: linear-gradient(135deg, var(--primary), var(--accent-primary));
        border: none;
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg) var(--spacing-2xl);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-inverse);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        &:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);

            &::before {
                left: 100%;
            }
        }

        &:active {
            transform: translateY(-1px);
        }
    }
}

.loading-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-primary);
}