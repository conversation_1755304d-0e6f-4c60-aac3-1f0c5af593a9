import { Component, OnInit } from '@angular/core';
import { ThemeService } from './services/theme.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})

export class AppComponent implements OnInit {
  title = 'IPL Season 5';

  constructor(private themeService: ThemeService) {}

  ngOnInit(): void {
    // Theme service is automatically initialized in constructor
    // Listen to system theme changes
    this.themeService.listenToSystemThemeChanges();
  }
}