import { Component, Input, HostListener, OnInit, OnDestroy } from '@angular/core';
import { LEAGUE_TABLE_DISPLAY_COLUMN, SHORTENED_LEAGUE_TABLE_DISPLAY_COLUMN, leagueSortColumns } from './league-table.constants';
import { Column } from '../../shared/models/column.model';
import { Router } from '@angular/router';
import { LeagueService } from '../../services/league.service';
import { LeagueTableRow } from '../../shared/models/leagueTableTeam';
import { LEAGUE_ID } from '../../constants/constants';
import { LeagueDataStateService } from '../../services/state/league-data-state.service';
import { AuthService } from '../../services/auth.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'league-table',
  templateUrl: './league-table.component.html',
  styleUrl: './league-table.component.scss'
})
export class LeagueTableComponent implements OnInit, OnDestroy {
  leagueSortColumns = leagueSortColumns;
  displayedColumns: Column[] = [];
  leagueTable: LeagueTableRow[] = [];
  isLoading: boolean = false;
  isMobile: boolean = false;
  @Input() hideTitle: boolean = false;

  private stateSubscription?: Subscription;

  constructor(
    private router: Router,
    private leagueService: LeagueService,
    private leagueDataState: LeagueDataStateService,
    private authService: AuthService
  ) {
    this.checkScreenSize();
  }

  ngOnInit() {
    // Subscribe to state changes
    this.stateSubscription = this.leagueDataState.state$.subscribe(state => {
      this.leagueTable = state.leagueTable;
      this.isLoading = state.isLeagueTableLoading;
    });

    this.loadLeagueTableData();
  }

  ngOnDestroy() {
    if (this.stateSubscription) {
      this.stateSubscription.unsubscribe();
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenSize();
  }

  private checkScreenSize() {
    this.isMobile = window.innerWidth <= 768;
  }

  isMobileView(): boolean {
    return this.isMobile;
  }

  trackByTeamId(index: number, team: LeagueTableRow): string {
    return team.teamId || index.toString();
  }

  getCurrentMatchday(): number {
    // Calculate current matchday based on games played
    if (this.leagueTable && this.leagueTable.length > 0) {
      const maxGamesPlayed = Math.max(...this.leagueTable.map(team => team.gamesPlayed));
      return Math.ceil(maxGamesPlayed / (this.leagueTable.length - 1));
    }
    return 1;
  }

  async loadLeagueTableData() {
    this.hideTitle ? (this.displayedColumns = SHORTENED_LEAGUE_TABLE_DISPLAY_COLUMN) : (this.displayedColumns = LEAGUE_TABLE_DISPLAY_COLUMN);

    try {
      const leagueTableResponse = await this.leagueService.getLeagueTable(LEAGUE_ID);

      leagueTableResponse.map(team => {
        team.tableIcon = {
          name: team.teamName,
          imgUrl: team.imgUrl || 'assets/Icons/Team.jpg',
          isTeam: true
        };
      });

      // The state is automatically updated by the service
    } catch (error) {
      console.error('Error loading league table data:', error);
    }
  }

  onTeamClick(teamDetails: LeagueTableRow) {
    this.router.navigate(['/team-details', { id: teamDetails.teamId }])
  }

  refreshData(): void {
    console.log('League Table: Manual refresh triggered');
    this.leagueDataState.forceRefreshAll();
    this.loadLeagueTableData();
  }

  async forceRefreshFromServer(): Promise<void> {
    console.log('League Table: Force refresh from server');
    try {
      this.isLoading = true;
      // Clear server cache first
      await this.leagueService.clearServerCache(LEAGUE_ID);
      // Clear all local state and force fresh data from server
      this.leagueDataState.clearAllData();
      await this.leagueService.getLeagueTable(LEAGUE_ID, true);
      await this.loadLeagueTableData();
      console.log('League Table: Force refresh completed successfully');
    } catch (error) {
      console.error('Error force refreshing league table:', error);
    } finally {
      this.isLoading = false;
    }
  }

  getFormResultTitle(result: string): string {
    switch (result) {
      case 'W': return 'Win';
      case 'D': return 'Draw';
      case 'L': return 'Loss';
      default: return result;
    }
  }

  getFormEmoji(result: string): string {
    switch (result) {
      case 'W': return '🟩';
      case 'D': return '🟨';
      case 'L': return '🟥';
      default: return '';
    }
  }

  isAdmin(): boolean {
    return this.authService.isAdmin();
  }
}
