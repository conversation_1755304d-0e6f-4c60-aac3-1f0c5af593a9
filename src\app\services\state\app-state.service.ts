import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface AppState {
  isLoading: boolean;
  lastUpdated: Date | null;
  currentLeagueId: string | null;
  currentSeasonId: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class AppStateService {
  private readonly initialState: AppState = {
    isLoading: false,
    lastUpdated: null,
    currentLeagueId: null,
    currentSeasonId: null
  };

  private stateSubject = new BehaviorSubject<AppState>(this.initialState);
  public state$ = this.stateSubject.asObservable();

  constructor() {
    // Load state from localStorage if available
    this.loadStateFromStorage();
  }

  get currentState(): AppState {
    return this.stateSubject.value;
  }

  updateState(partialState: Partial<AppState>): void {
    const newState = {
      ...this.currentState,
      ...partialState,
      lastUpdated: new Date()
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  setLoading(isLoading: boolean): void {
    this.updateState({ isLoading });
  }

  setCurrentLeague(leagueId: string): void {
    this.updateState({ currentLeagueId: leagueId });
  }

  setCurrentSeason(seasonId: string): void {
    this.updateState({ currentSeasonId: seasonId });
  }

  resetState(): void {
    this.stateSubject.next(this.initialState);
    this.clearStateFromStorage();
  }

  private saveStateToStorage(state: AppState): void {
    try {
      localStorage.setItem('app-state', JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save state to localStorage:', error);
    }
  }

  private loadStateFromStorage(): void {
    try {
      const savedState = localStorage.getItem('app-state');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        this.stateSubject.next({ ...this.initialState, ...parsedState });
      }
    } catch (error) {
      console.warn('Failed to load state from localStorage:', error);
    }
  }

  private clearStateFromStorage(): void {
    try {
      localStorage.removeItem('app-state');
    } catch (error) {
      console.warn('Failed to clear state from localStorage:', error);
    }
  }
}
