<div class="stat-card" [class]="'stat-card--' + (color || 'primary')">
    <div class="stat-icon">
        <i [class]="icon"></i>
    </div>
    <div class="stat-content">
        <div class="stat-header">
            <h3 class="stat-title">{{ title }}</h3>
            <div class="stat-trend" *ngIf="trend && trendValue" [class]="'stat-trend--' + trend">
                <i class="fas" [class.fa-arrow-up]="trend === 'up'" 
                   [class.fa-arrow-down]="trend === 'down'"
                   [class.fa-minus]="trend === 'neutral'"></i>
                <span>{{ trendValue }}</span>
            </div>
        </div>
        <div class="stat-value">{{ value }}</div>
        <div class="stat-subtitle" *ngIf="subtitle">{{ subtitle }}</div>
    </div>
</div>
