<div class="add-news-container">
    <div class="add-news-card">
        <div class="add-news-header">
            <h1 class="add-news-title">
                <i class="fas fa-newspaper"></i>
                Add News
            </h1>
            <p class="add-news-subtitle">Create a new news article</p>
        </div>

        <form [formGroup]="addNewsFormGroup" (ngSubmit)="onSubmit()" class="add-news-form">
            <div class="form-group" *ngFor="let control of formControls" [hidden]="!shouldShowField(control.field)">
                <label class="form-label">
                    {{ control.displayText }}
                    <span class="required-indicator" *ngIf="isRequiredForm(control.control)">*</span>
                </label>

                <input
                    *ngIf="control.type === 'text-input'"
                    [formControlName]="control.field"
                    [maxlength]="control.maxLength!"
                    [placeholder]="'Enter ' + control.displayText.toLowerCase()"
                    class="form-input"
                    [class.error]="control.control.invalid && control.control.touched">

                <textarea
                    *ngIf="control.type === 'large-text-input'"
                    [formControlName]="control.field"
                    [maxlength]="control.maxLength!"
                    [placeholder]="getContentPlaceholder(control.field)"
                    class="form-textarea"
                    rows="6"
                    [class.error]="control.control.invalid && control.control.touched">
                </textarea>

                <pro-clubs-auto-complete-select
                    *ngIf="control.type === 'select' && control.field === 'type'"
                    [placeholder]="'Select ' + control.displayText.toLowerCase()"
                    (selectionChange)="onSelectionChange($event)"
                    [selectOptions]="newsType">
                </pro-clubs-auto-complete-select>

                <pro-clubs-auto-complete-select
                    *ngIf="control.type === 'select' && control.field === 'freeAgentPlayer' && selectedNewsType === 'FreeAgent'"
                    [placeholder]="'Select ' + control.displayText.toLowerCase()"
                    (selectionChange)="onFreeAgentSelectionChange($event)"
                    [selectOptions]="freeAgentOptions">
                </pro-clubs-auto-complete-select>
            </div>

            <button
                type="submit"
                class="submit-button"
                [disabled]="addNewsFormGroup.invalid">
                <i class="fas fa-plus"></i>
                Publish News
            </button>
        </form>
    </div>
</div>