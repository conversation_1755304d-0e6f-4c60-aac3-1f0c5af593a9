import { Component, Input, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface FormationPlayer {
  id: string;
  name: string;
  position: string;
  jerseyNumber?: number;
  goals?: number;
  assists?: number;
  rating?: number;
  isPlayerOfMatch?: boolean;
  profileImage?: string;
  teamImage?: string;
  teamName?: string;
}

export interface TeamFormation {
  teamId: string;
  teamName: string;
  formation: string;
  players: FormationPlayer[];
}

@Component({
  selector: 'app-mini-pitch-formation',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './mini-pitch-formation.component.html',
  styleUrls: ['./mini-pitch-formation.component.scss']
})
export class MiniPitchFormationComponent implements OnInit, OnChanges {
  @Input() homeTeam!: TeamFormation;
  @Input() awayTeam!: TeamFormation;
  @Input() showPlayerStats = true;
  @Input() showPlayerNames = true;
  @Input() compactMode = false;
  @Input() totwMode = false;

  // Mobile tab state
  selectedTeamIndex = 0; // 0 for first team, 1 for second team
  teams: TeamFormation[] = [];

  ngOnInit(): void {
    this.initializeTeams();
  }

  ngOnChanges(): void {
    this.initializeTeams();
  }

  private initializeTeams(): void {
    if (this.homeTeam && this.awayTeam) {
      this.teams = [this.homeTeam, this.awayTeam];
    }
  }

  // Tab management for mobile
  selectTeam(index: number): void {
    this.selectedTeamIndex = index;
  }

  get selectedTeam(): TeamFormation {
    return this.teams[this.selectedTeamIndex] || this.teams[0];
  }

  // Vertical formation templates - attacking from bottom to top
  getFormationPositions(formation: string): { [position: string]: { x: number, y: number } } {
    // Debug: Log the formation being received
    console.log('Formation received:', formation);

    const formations: { [key: string]: any } = {
      '3-5-2': {
        'GK': { x: 50, y: 90 },      // Bottom (defending)
        'CB1': { x: 25, y: 70 },     // Left CB
        'CB2': { x: 50, y: 70 },     // Center CB
        'CB3': { x: 75, y: 70 },     // Right CB
        'CDM1': { x: 35, y: 50 },    // Left CDM
        'CDM2': { x: 65, y: 50 },    // Right CDM
        'LM': { x: 15, y: 35 },      // Left Mid
        'RM': { x: 85, y: 35 },      // Right Mid
        'CAM': { x: 50, y: 30 },     // Central Attacking Mid
        'ST1': { x: 35, y: 10 },     // Left ST
        'ST2': { x: 65, y: 10 }      // Right ST
      },
      '3-4-1-2': {
        'GK': { x: 50, y: 90 },      // Bottom (defending)
        'CB1': { x: 25, y: 70 },     // Left CB
        'CB2': { x: 50, y: 70 },     // Center CB
        'CB3': { x: 75, y: 70 },     // Right CB
        'CDM': { x: 50, y: 60 },     // Single CDM
        'LM': { x: 15, y: 45 },      // Left Mid
        'CM1': { x: 35, y: 45 },     // Left CM
        'CM2': { x: 65, y: 45 },     // Right CM
        'RM': { x: 85, y: 45 },      // Right Mid
        'CAM': { x: 50, y: 30 },     // Central Attacking Mid
        'ST1': { x: 35, y: 10 },     // Left ST
        'ST2': { x: 65, y: 10 }      // Right ST
      },
      '4-3-3': {
        'GK': { x: 50, y: 90 },      // Bottom (defending)
        'LB': { x: 15, y: 70 },      // Left Back
        'CB1': { x: 35, y: 70 },     // Left CB
        'CB2': { x: 65, y: 70 },     // Right CB
        'RB': { x: 85, y: 70 },      // Right Back
        'CDM': { x: 50, y: 60 },     // Defensive Mid
        'CM1': { x: 35, y: 45 },     // Left CM
        'CM2': { x: 65, y: 45 },     // Right CM
        'LW': { x: 20, y: 15 },      // Left Wing
        'ST': { x: 50, y: 10 },      // Striker
        'RW': { x: 80, y: 15 }       // Right Wing
      },
      '4-4-2': {
        'GK': { x: 50, y: 90 },      // Bottom (defending)
        'LB': { x: 15, y: 70 },      // Left Back
        'CB1': { x: 35, y: 70 },     // Left CB
        'CB2': { x: 65, y: 70 },     // Right CB
        'RB': { x: 85, y: 70 },      // Right Back
        'LM': { x: 15, y: 40 },      // Left Mid
        'CM1': { x: 35, y: 40 },     // Left CM
        'CM2': { x: 65, y: 40 },     // Right CM
        'RM': { x: 85, y: 40 },      // Right Mid
        'ST1': { x: 35, y: 10 },     // Left ST
        'ST2': { x: 65, y: 10 }      // Right ST
      },
      '4-1-2-1-2': {
        'GK': { x: 50, y: 90 },      // Bottom (defending)
        'LB': { x: 15, y: 75 },      // Left Back
        'CB1': { x: 35, y: 75 },     // Left CB
        'CB2': { x: 65, y: 75 },     // Right CB
        'RB': { x: 85, y: 75 },      // Right Back
        'CDM': { x: 50, y: 65 },     // Defensive Mid
        'CM1': { x: 35, y: 50 },     // Left CM
        'CM2': { x: 65, y: 50 },     // Right CM
        'CAM': { x: 50, y: 35 },     // Central Attacking Mid
        'ST1': { x: 35, y: 15 },     // Left ST
        'ST2': { x: 65, y: 15 }      // Right ST
      }
    };

    // Add common formation variations after the main object is created
    formations['352'] = formations['3-5-2'];
    formations['3412'] = formations['3-4-1-2'];
    formations['433'] = formations['4-3-3'];
    formations['442'] = formations['4-4-2'];

    const selectedFormation = formations[formation];
    if (!selectedFormation) {
      console.warn(`Formation "${formation}" not found. Available formations:`, Object.keys(formations));
      console.warn('Using 4-3-3 as fallback');
      return formations['4-3-3'];
    }

    return selectedFormation;
  }

  getPositionedPlayers(team: TeamFormation): Array<FormationPlayer & { x: number, y: number }> {
    const positions = this.getFormationPositions(team.formation);
    const positionedPlayers: Array<FormationPlayer & { x: number, y: number }> = [];

    // Group players by position
    const playersByPosition: { [position: string]: FormationPlayer[] } = {};
    team.players.forEach(player => {
      const pos = player.position;
      if (!playersByPosition[pos]) {
        playersByPosition[pos] = [];
      }
      playersByPosition[pos].push(player);
    });

    // Position players with automatic mapping to numbered positions
    Object.keys(playersByPosition).forEach(position => {
      const playersInPosition = playersByPosition[position];

      playersInPosition.forEach((player, index) => {
        // Try to find specific numbered position first (CB1, CB2, etc.)
        let specificPosition = `${position}${index + 1}`;
        let templatePosition = positions[specificPosition];

        // If numbered position doesn't exist, try the generic position
        if (!templatePosition) {
          templatePosition = positions[position];
        }

        // If still no position found, skip this player
        if (!templatePosition) {
          console.warn(`No template position found for ${position} or ${specificPosition}`);
          return;
        }

        positionedPlayers.push({
          ...player,
          x: templatePosition.x,
          y: templatePosition.y
        });
      });
    });

    return positionedPlayers;
  }

  // Utility methods
  getPlayerInitials(name: string): string {
    if (!name) return '';
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);
  }

  getPlayerDisplayName(player: FormationPlayer): string {
    if (!this.showPlayerNames || !player.name) {
      return player.jerseyNumber?.toString() || player.position;
    }

    // For multi-word last names, ignore the first word and use the rest
    const nameParts = player.name.split(' ');
    if (nameParts.length > 2) {
      // If more than 2 words, skip first word and join the rest
      return nameParts.slice(1).join(' ');
    }
    // If 2 or fewer words, use the last word
    return nameParts.pop() || player.name;
  }

  hasPlayerStats(player: FormationPlayer): boolean {
    return !!this.showPlayerStats && (
      (player.goals !== undefined && player.goals > 0) ||
      (player.assists !== undefined && player.assists > 0)
    );
  }

  getRatingColor(rating?: number): string {
    if (!rating) return 'transparent';

    if (rating >= 9.0) return '#00ff00';      // Bright green for 9.0+
    if (rating >= 8.5) return '#7fff00';      // Light green for 8.5-8.9
    if (rating >= 8.0) return '#ffff00';      // Yellow for 8.0-8.4
    if (rating >= 7.5) return '#ffa500';      // Orange for 7.5-7.9
    if (rating >= 7.0) return '#ff6600';      // Dark orange for 7.0-7.4
    if (rating >= 6.5) return '#ff3300';      // Red-orange for 6.5-6.9
    if (rating >= 6.0) return '#ff0000';      // Red for 6.0-6.4
    return '#cc0000';                         // Dark red for below 6.0
  }

  hasRating(player: FormationPlayer): boolean {
    return player.rating !== undefined && player.rating > 0;
  }

  trackByPlayerId(_index: number, player: FormationPlayer): string {
    return player.id;
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/User.jpg';
    }
  }


}