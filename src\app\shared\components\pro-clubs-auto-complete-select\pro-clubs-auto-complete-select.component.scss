/* === MODERN AUTOCOMPLETE DESIGN === */

.pro-clubs-autocomplete-container {
    width: 100%;
    position: relative;
}

.autocomplete-wrapper {
    position: relative;
    width: 100%;
    height: 44px;
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
    overflow: hidden;

    &:hover {
        border-color: var(--border-secondary);
        box-shadow: var(--shadow-sm);
    }

    &.focused {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    &.has-value {
        .input-icon {
            opacity: 0.7;
        }
    }
}

.input-container {
    display: flex;
    align-items: center;
    height: 100%;
    position: relative;
}

.autocomplete-input {
    flex: 1;
    height: 100%;
    padding: 0 var(--spacing-md);
    padding-right: 40px;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: var(--text-base);
    font-family: var(--font-sans);
    font-weight: var(--font-weight-medium);

    &::placeholder {
        color: var(--text-muted);
        font-weight: var(--font-weight-normal);
    }

    &:disabled {
        color: var(--text-muted);
        cursor: not-allowed;
    }
}

.input-icon {
    position: absolute;
    right: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    z-index: 2;

    i {
        font-size: var(--text-sm);
        transition: all 0.2s ease;
    }

    &.clear-icon {
        cursor: pointer;
        color: var(--text-muted);
        border-radius: var(--radius-full);
        background: var(--surface-secondary);

        &:hover {
            color: var(--danger);
            background: var(--danger-50);
            transform: scale(1.1);
        }
    }
}

/* === AUTOCOMPLETE PANEL === */
::ng-deep .pro-clubs-autocomplete-panel.mat-mdc-autocomplete-panel {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    margin-top: 4px;
    max-height: 300px;
    overflow-y: auto;

    .mat-mdc-option {
        background: transparent;
        color: var(--text-primary);
        padding: 0;
        min-height: 44px;
        border-bottom: 1px solid var(--border-secondary);

        &:last-child {
            border-bottom: none;
        }

        &:hover {
            background: var(--surface-secondary);
        }

        &.mat-mdc-option-active {
            background: var(--primary-50);
            color: var(--primary);
        }

        &.mdc-list-item--selected {
            background: var(--primary);
            color: white;

            .option-check {
                opacity: 1;
            }
        }
    }
}

.autocomplete-option {
    padding: 0 !important;
}

.option-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    width: 100%;
    min-height: 44px;
}

.option-check {
    color: var(--success);
    font-size: var(--text-sm);
    opacity: 0;
    transition: all 0.2s ease;
}

.option-text {
    flex: 1;
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
    .autocomplete-wrapper {
        height: 40px;
    }

    .autocomplete-input {
        font-size: var(--text-sm);
        padding: 0 var(--spacing-sm);
        padding-right: 36px;
    }

    .input-icon {
        right: var(--spacing-sm);
        width: 18px;
        height: 18px;

        i {
            font-size: var(--text-xs);
        }
    }

    .option-content {
        padding: var(--spacing-xs) var(--spacing-sm);
        min-height: 40px;
    }

    .option-text {
        font-size: var(--text-sm);
    }
}

/* === DISABLED STATE === */
.autocomplete-wrapper.disabled {
    background: var(--surface-tertiary);
    border-color: var(--border-secondary);
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
        border-color: var(--border-secondary);
        box-shadow: none;
    }

    .input-icon {
        color: var(--text-muted);
    }
}

/* === ERROR STATE === */
.autocomplete-wrapper.error {
    border-color: var(--danger);

    &:focus-within {
        border-color: var(--danger);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
}

/* === LOADING STATE === */
.autocomplete-wrapper.loading {
    .input-icon {
        animation: spin 1s linear infinite;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}