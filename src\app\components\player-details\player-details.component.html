<div class="player-details-container" *ngIf="chosenPlayer">
    <!-- Promotional Banner for Non-Authenticated Users -->
    <app-promotional-banner
        *ngIf="!isAuthenticated"
        [message]="promotionalMessage"
        size="medium"
        [dismissible]="true"
        [showSecondaryAction]="true">
    </app-promotional-banner>

    <!-- Player Header -->
    <app-player-header
        [player]="chosenPlayer"
        [editPlayerMode]="editPlayerMode"
        [isViewOnly]="!canEditPlayer"
        (backClick)="onArrowBackClick()"
        (teamNavigate)="navigateToTeamDetails()"
        (editClick)="onEditClick()"
        (cancelEditClick)="onCancelEditClick()"
        (saveClick)="onSaveClick()">
    </app-player-header>

    <!-- Content Grid -->
    <div class="player-content">
        <!-- Player Profile -->
        <app-player-profile-card
            [player]="chosenPlayer"
            [editPlayerMode]="editPlayerMode"
            [isViewOnly]="!canEditPlayer"
            [canRemoveFromTeam]="canRemoveFromTeam"
            [positionOptions]="positionOptions"
            [editedPlayerPosition]="editedPlayerPosition"
            [editedPlayerName]="editedPlayerName"
            [editedPlayerAge]="editedPlayerAge"
            [editedPlayablePositions]="editedPlayablePositions"
            [isPlayerAssociated]="isPlayerAssociated"
            (imageUpload)="onFileSelected($event)"
            (positionChange)="onPositionChange($event)"
            (nameChange)="onNameChange($event)"
            (ageChange)="onAgeChange($event)"
            (playablePositionsChange)="onPlayablePositionsChange($event)"
            (removeFromTeam)="removePlayerFromTeam()"
            (teamNavigate)="navigateToTeamDetails()"
            (comparePlayer)="onComparePlayer()"
            (viewSeasonHistory)="onViewSeasonHistory()">
        </app-player-profile-card>

        <!-- Stats Area -->
        <div class="player-stats">
            <app-player-quick-stats [player]="chosenPlayer"></app-player-quick-stats>
            <app-player-detailed-analytics [player]="chosenPlayer" [playerId]="playerID"></app-player-detailed-analytics>

            <!-- Transfer History -->
            <div class="transfer-history">
                <app-transfer-history [playerId]="playerID"></app-transfer-history>
            </div>

            <!-- Achievement History -->
            <div class="achievement-history">
                <app-achievement-history
                    [entityId]="playerID"
                    [entityType]="'player'"
                    [entityName]="chosenPlayer.name">
                </app-achievement-history>
            </div>
        </div>
    </div>
</div>