import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TopAvgRatingByPositionComponent } from './top-avg-rating-by-position.component';

describe('TopAvgRatingByPositionComponent', () => {
  let component: TopAvgRatingByPositionComponent;
  let fixture: ComponentFixture<TopAvgRatingByPositionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TopAvgRatingByPositionComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(TopAvgRatingByPositionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
