/* === BEAUTIFUL PLAYER COMPARISON STYLES === */

.player-comparison-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  min-height: 100vh;
  background: var(--background-primary);

  @media (max-width: 768px) {
    padding: var(--spacing-lg);
  }
}

/* Ensure full width coverage */
:host {
  display: block;
  width: 100%;
  background: var(--background-primary);
}

/* === HEADER === */
.comparison-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-xl);
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);

  .back-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--surface-hover);
      transform: translateX(-2px);
      border-color: var(--primary-300);
    }
  }

  .page-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;

    i {
      color: var(--primary-500);
    }

    @media (max-width: 768px) {
      font-size: var(--text-xl);
    }
  }
}

/* === LOADING & ERROR STATES === */
.loading-state, .error-state {
  text-align: center;
  padding: var(--spacing-3xl);
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);

  .loading-spinner, .error-icon {
    font-size: var(--text-3xl);
    margin-bottom: var(--spacing-lg);
  }

  .loading-spinner {
    color: var(--primary-500);
    
    i {
      animation: spin 1s linear infinite;
    }
  }

  .error-icon {
    color: var(--error-500);
  }

  .loading-text, .error-text {
    font-size: var(--text-lg);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
  }

  .retry-btn {
    background: var(--primary-500);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all 0.2s ease;

    &:hover {
      background: var(--primary-600);
      transform: translateY(-1px);
    }
  }
}

/* === STEP SECTIONS === */
.team-selection-step, .player-selection-step {
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--spacing-xl);
}

/* === STEP HEADERS === */
.step-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);

  .step-title {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
  }

  .step-subtitle {
    font-size: var(--text-base);
    color: var(--text-secondary);
    margin: 0;
  }
}

/* === TEAM SELECTION === */
.team-selection-step .teams-grid,
.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  background: transparent;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .team-card {
    background: var(--surface-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
    animation: slideInUp 0.6s ease-out;

    &:hover {
      background: var(--surface-hover) !important;
      transform: translateY(-8px) scale(1.02);
      box-shadow: var(--shadow-2xl);
      border-color: var(--primary-300);
    }

    &:hover .team-logo img {
      transform: scale(1.1) rotate(5deg);
    }

    .team-logo {
      width: 60px;
      height: 60px;
      border-radius: var(--radius-lg);
      overflow: hidden;
      border: 2px solid var(--border-primary);
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    .team-info {
      flex: 1;
      min-width: 0;

      .team-name {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .team-players {
        font-size: var(--text-sm);
        color: var(--text-secondary);
      }
    }

    .team-arrow {
      color: var(--text-tertiary);
      font-size: var(--text-lg);
    }
  }
}

/* === PLAYER SELECTION === */
.players-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .player-card {
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);

    &:hover {
      background: var(--surface-hover);
      transform: translateY(-4px);
      box-shadow: var(--shadow-xl);
      border-color: var(--primary-300);
    }

    .player-avatar {
      width: 50px;
      height: 50px;
      border-radius: var(--radius-full);
      overflow: hidden;
      border: 2px solid var(--border-primary);
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .player-info {
      flex: 1;
      min-width: 0;

      .player-name {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .player-position {
        display: block;
        font-size: var(--text-sm);
        color: var(--primary-500);
        font-weight: 600;
        margin-bottom: var(--spacing-xs);
      }

      .player-stats {
        display: flex;
        gap: var(--spacing-sm);

        .stat {
          font-size: var(--text-xs);
          color: var(--text-secondary);
          background: var(--surface-secondary);
          padding: 2px var(--spacing-xs);
          border-radius: var(--radius-sm);
        }
      }
    }

    .player-arrow {
      color: var(--text-tertiary);
      font-size: var(--text-base);
    }
  }
}

/* === EMPTY STATE === */
.empty-state {
  text-align: center;
  padding: var(--spacing-3xl);

  .empty-icon {
    font-size: var(--text-3xl);
    color: var(--primary);
    margin-bottom: var(--spacing-lg);
  }

  .empty-text {
    font-size: var(--text-lg);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
  }

  .secondary-btn {
    background: var(--surface-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all 0.2s ease;

    &:hover {
      background: var(--surface-hover);
      transform: translateY(-1px);
    }
  }
}

/* === COMPARISON RESULTS === */
.comparison-results {
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);

  .players-header {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl);
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-lg);
      text-align: center;
    }

    .player-summary {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);

      @media (max-width: 768px) {
        justify-content: center;
      }

      .player-avatar {
        width: 80px;
        height: 80px;
        border-radius: var(--radius-full);
        overflow: hidden;
        border: 3px solid var(--border-primary);
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .player-details {
        .player-name {
          font-size: var(--text-xl);
          font-weight: 700;
          color: var(--text-primary);
          margin: 0 0 var(--spacing-xs) 0;
        }

        .player-position {
          display: block;
          font-size: var(--text-sm);
          color: var(--primary-500);
          font-weight: 600;
          margin-bottom: var(--spacing-xs);
        }

        .player-team {
          display: block;
          font-size: var(--text-sm);
          color: var(--text-secondary);
        }
      }
    }

    .vs-divider {
      display: flex;
      align-items: center;
      justify-content: center;

      .vs-text {
        background: var(--primary-500);
        color: white;
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-full);
        font-size: var(--text-lg);
        font-weight: 700;
        box-shadow: var(--shadow-md);
      }
    }
  }
}

/* === COMPARISON STATS === */
.comparison-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);

  .stats-section {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);

    .section-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      font-size: var(--text-lg);
      font-weight: 700;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-xl) 0;
      padding-bottom: var(--spacing-lg);
      border-bottom: 1px solid var(--border-primary);

      i {
        color: var(--primary-500);
      }
    }

    .stats-grid {
      display: grid;
      gap: var(--spacing-lg);

      .stat-comparison {
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-xl);
        padding: var(--spacing-lg);
        transition: all 0.2s ease;

        &:hover {
          background: var(--surface-hover);
          transform: translateY(-2px);
          box-shadow: var(--shadow-md);
        }

        .stat-label {
          font-size: var(--text-sm);
          font-weight: 600;
          color: var(--text-secondary);
          text-transform: uppercase;
          letter-spacing: 0.05em;
          margin-bottom: var(--spacing-md);
          text-align: center;
        }

        .stat-values {
          display: grid;
          grid-template-columns: 1fr auto 1fr;
          gap: var(--spacing-md);
          align-items: center;

          .player1-value, .player2-value {
            font-size: var(--text-xl);
            font-weight: 700;
            color: var(--text-primary);
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-primary);
          }

          .difference {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm);
            border-radius: var(--radius-lg);
            font-size: var(--text-sm);
            font-weight: 600;
            min-width: 60px;

            &.positive {
              background: rgba(16, 185, 129, 0.2);
              color: var(--success-400);
              border: 1px solid var(--success-500);
            }

            &.negative {
              background: rgba(239, 68, 68, 0.2);
              color: var(--error-400);
              border: 1px solid var(--error-500);
            }

            &.neutral {
              background: var(--surface-tertiary);
              color: var(--text-secondary);
              border: 1px solid var(--border-secondary);
            }

            i {
              font-size: var(--text-xs);
            }
          }
        }
      }
    }
  }
}

/* === COMPARISON ACTIONS === */
.comparison-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .secondary-btn, .primary-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }

  .secondary-btn {
    background: var(--surface-secondary);
    color: var(--text-primary);
    border-color: var(--border-primary);

    &:hover {
      background: var(--surface-hover);
      transform: translateY(-1px);
    }
  }

  .primary-btn {
    background: var(--primary-500);
    color: white;
    border-color: var(--primary-500);

    &:hover {
      background: var(--primary-600);
      border-color: var(--primary-600);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .comparison-stats .stats-section .stats-grid .stat-comparison .stat-values {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
    text-align: center;

    .difference {
      order: -1;
      flex-direction: row;
      justify-content: center;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
