import { Schema, model, Types, Document } from "mongoose";

export enum PREDICTION_OUTCOME {
  HOME_WIN = "HOME_WIN",
  AWAY_WIN = "AWAY_WIN", 
  DRAW = "DRAW"
}

export interface IPrediction extends Document {
  _id: Types.ObjectId;
  gameId: Types.ObjectId;
  userId: Types.ObjectId;
  predictedOutcome: PREDICTION_OUTCOME;
  confidence?: number; // 1-5 scale, optional
  createdAt: Date;
  updatedAt: Date;
}

const predictionSchema = new Schema<IPrediction>({
  gameId: {
    type: Schema.Types.ObjectId,
    ref: "Game",
    required: true,
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true
  },
  predictedOutcome: {
    type: String,
    enum: Object.values(PREDICTION_OUTCOME),
    required: true
  },
  confidence: {
    type: Number,
    min: 1,
    max: 5,
    default: 3
  }
}, {
  timestamps: true,
  collection: "predictions"
});

// Compound index to ensure one prediction per user per game
predictionSchema.index({ gameId: 1, userId: 1 }, { unique: true });

// Index for efficient querying by game
predictionSchema.index({ gameId: 1, createdAt: -1 });

export const Prediction = model<IPrediction>("Prediction", predictionSchema);
