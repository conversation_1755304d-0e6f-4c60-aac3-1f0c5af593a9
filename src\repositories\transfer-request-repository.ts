import { injectable } from "tsyringe";
import { ClientSession, Types } from "mongoose";
import TransferRequest, { ITransferRequest } from "../models/transfer-request";
import logger from "../config/logger";

export interface ITransferRequestRepository {
  create(data: Partial<ITransferRequest>, session?: ClientSession): Promise<ITransferRequest>;
  findById(id: string): Promise<ITransferRequest | null>;
  findByTeam(teamId: string, status?: string): Promise<ITransferRequest[]>;
  findByPlayer(playerId: string, status?: string): Promise<ITransferRequest[]>;
  findPendingByPlayer(playerId: string): Promise<ITransferRequest | null>;
  updateStatus(id: string, status: string, processedBy: string, reason?: string, session?: ClientSession): Promise<ITransferRequest | null>;
  findExpiredRequests(): Promise<ITransferRequest[]>;
  deleteExpiredRequests(): Promise<number>;
}

@injectable()
export default class TransferRequestRepository implements ITransferRequestRepository {

  async create(data: Partial<ITransferRequest>, session?: ClientSession): Promise<ITransferRequest> {
    try {
      const transferRequest = new TransferRequest(data);
      await transferRequest.save({ session });
      
      logger.info(`Transfer request created: ${transferRequest.id}`);
      return transferRequest;
    } catch (error) {
      logger.error("Error creating transfer request:", error);
      throw error;
    }
  }

  async findById(id: string): Promise<ITransferRequest | null> {
    try {
      return await TransferRequest
        .findById(id)
        .populate('player', 'name position imgUrl')
        .populate('fromTeam', 'name imgUrl')
        .populate('toTeam', 'name imgUrl')
        .populate('requester', 'firstName lastName email')
        .populate('processor', 'firstName lastName email')
        .lean();
    } catch (error) {
      logger.error(`Error finding transfer request by id ${id}:`, error);
      throw error;
    }
  }

  async findByTeam(teamId: string, status?: string): Promise<ITransferRequest[]> {
    try {
      const query: any = {
        $or: [
          { fromTeamId: new Types.ObjectId(teamId) },
          { toTeamId: new Types.ObjectId(teamId) }
        ]
      };

      if (status) {
        query.status = status;
      }

      return await TransferRequest
        .find(query)
        .populate('player', 'name position imgUrl')
        .populate('fromTeam', 'name imgUrl')
        .populate('toTeam', 'name imgUrl')
        .populate('requester', 'firstName lastName email')
        .populate('processor', 'firstName lastName email')
        .sort({ requestedAt: -1 })
        .lean();
    } catch (error) {
      logger.error(`Error finding transfer requests for team ${teamId}:`, error);
      throw error;
    }
  }

  async findByPlayer(playerId: string, status?: string): Promise<ITransferRequest[]> {
    try {
      const query: any = { playerId: new Types.ObjectId(playerId) };
      
      if (status) {
        query.status = status;
      }

      return await TransferRequest
        .find(query)
        .populate('player', 'name position imgUrl')
        .populate('fromTeam', 'name imgUrl')
        .populate('toTeam', 'name imgUrl')
        .populate('requester', 'firstName lastName email')
        .populate('processor', 'firstName lastName email')
        .sort({ requestedAt: -1 })
        .lean();
    } catch (error) {
      logger.error(`Error finding transfer requests for player ${playerId}:`, error);
      throw error;
    }
  }

  async findPendingByPlayer(playerId: string): Promise<ITransferRequest | null> {
    try {
      return await TransferRequest
        .findOne({ 
          playerId: new Types.ObjectId(playerId), 
          status: 'pending',
          expiresAt: { $gt: new Date() }
        })
        .populate('player', 'name position imgUrl')
        .populate('fromTeam', 'name imgUrl')
        .populate('toTeam', 'name imgUrl')
        .populate('requester', 'firstName lastName email')
        .lean();
    } catch (error) {
      logger.error(`Error finding pending transfer request for player ${playerId}:`, error);
      throw error;
    }
  }

  async updateStatus(
    id: string, 
    status: string, 
    processedBy: string, 
    reason?: string, 
    session?: ClientSession
  ): Promise<ITransferRequest | null> {
    try {
      const updateData: any = {
        status,
        processedAt: new Date(),
        processedBy: new Types.ObjectId(processedBy)
      };

      if (reason) {
        updateData.reason = reason;
      }

      const updatedRequest = await TransferRequest
        .findByIdAndUpdate(id, updateData, { new: true, session })
        .populate('player', 'name position imgUrl')
        .populate('fromTeam', 'name imgUrl')
        .populate('toTeam', 'name imgUrl')
        .populate('requester', 'firstName lastName email')
        .populate('processor', 'firstName lastName email')
        .lean();

      logger.info(`Transfer request ${id} status updated to ${status} by ${processedBy}`);
      return updatedRequest;
    } catch (error) {
      logger.error(`Error updating transfer request status for ${id}:`, error);
      throw error;
    }
  }

  async findExpiredRequests(): Promise<ITransferRequest[]> {
    try {
      return await TransferRequest
        .find({
          status: 'pending',
          expiresAt: { $lt: new Date() }
        })
        .populate('player', 'name position imgUrl')
        .populate('fromTeam', 'name imgUrl')
        .populate('toTeam', 'name imgUrl')
        .lean();
    } catch (error) {
      logger.error("Error finding expired transfer requests:", error);
      throw error;
    }
  }

  async deleteExpiredRequests(): Promise<number> {
    try {
      const result = await TransferRequest.updateMany(
        {
          status: 'pending',
          expiresAt: { $lt: new Date() }
        },
        {
          status: 'cancelled',
          processedAt: new Date(),
          reason: 'Request expired'
        }
      );

      logger.info(`Marked ${result.modifiedCount} expired transfer requests as cancelled`);
      return result.modifiedCount;
    } catch (error) {
      logger.error("Error deleting expired transfer requests:", error);
      throw error;
    }
  }
}
