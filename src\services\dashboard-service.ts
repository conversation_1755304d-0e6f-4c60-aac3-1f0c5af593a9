import { inject, injectable } from "tsyringe";
import logger from "../config/logger";
import { ITeamRepository } from "../interfaces/team";
import { IPlayerRepository } from "../interfaces/player";
import { IGameRepository } from "../interfaces/game";
import { IFixtureRepository } from "../interfaces/fixture";
import { ILeagueRepository, ILeagueService } from "../interfaces/league";
import { CacheService } from "../interfaces/util-services/cache-service.interface";
import { GAME_STATUS } from "@pro-clubs-manager/shared-dtos";

const DASHBOARD_CACHE_KEY = "dashboard:summary";
const DASHBOARD_CACHE_DURATION = 5 * 24 * 60 * 60; // 5 days in seconds

export interface DashboardSummary {
  // Basic counts
  totalTeams: number;
  totalPlayers: number;
  totalFixtures: number;
  totalGames: number;

  // Season progress
  currentSeason: {
    seasonNumber: number;
    gamesPlayed: number;
    gamesRemaining: number;
    completionPercentage: number;
  };

  // Goals and scoring
  totalGoals: number;
  averageGoalsPerGame: number;
  topScorer: {
    playerId: string;
    playerName: string;
    playerImgUrl?: string;
    goals: number;
    teamName: string;
  } | null;

  // Team performance
  leagueLeader: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
    points: number;
    gamesPlayed: number;
  } | null;

  // Recent activity
  recentGames: {
    id: string;
    homeTeam: string;
    awayTeam: string;
    result: string;
    date: Date;
  }[];

  upcomingGames: {
    id: string;
    homeTeam: string;
    awayTeam: string;
    date: Date;
  }[];

  // Player statistics
  totalAssists: number;
  averageRating: number;
  cleanSheets: number;

  // Transfer activity
  recentTransfers: {
    playerId: string;
    playerName: string;
    fromTeam: string | null;
    toTeam: string;
    date: Date;
  }[];

  // Enhanced data for consolidated API
  topScorers: {
    playerId: string;
    playerName: string;
    playerImgUrl?: string;
    goals: number;
    assists: number;
    gamesPlayed: number;
    goalsPerGame: number;
    teamName: string;
    teamId: string;
  }[];

  topAssisters: {
    playerId: string;
    playerName: string;
    playerImgUrl?: string;
    assists: number;
    goals: number;
    gamesPlayed: number;
    assistsPerGame: number;
    teamName: string;
    teamId: string;
  }[];

  leagueTable: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
    position: number;
    gamesPlayed: number;
    wins: number;
    draws: number;
    losses: number;
    goalsFor: number;
    goalsAgainst: number;
    goalDifference: number;
    points: number;
    lastForm: string[];
  }[];
}

export interface IDashboardService {
  getDashboardSummary(): Promise<DashboardSummary>;
  refreshDashboardCache(): Promise<DashboardSummary>;
  clearDashboardCache(): Promise<void>;
}

@injectable()
export class DashboardService implements IDashboardService {
  constructor(
    @inject("ITeamRepository") private teamRepository: ITeamRepository,
    @inject("IPlayerRepository") private playerRepository: IPlayerRepository,
    @inject("IGameRepository") private gameRepository: IGameRepository,
    @inject("IFixtureRepository") private fixtureRepository: IFixtureRepository,
    @inject("ILeagueRepository") private leagueRepository: ILeagueRepository,
    @inject("ILeagueService") private leagueService: ILeagueService,
    @inject("CacheService") private cacheService: CacheService
  ) {}

  async getDashboardSummary(): Promise<DashboardSummary> {
    logger.info("DashboardService: getting dashboard summary");

    try {
      // Try to get from cache first
      const cachedDashboard = await this.getDashboardFromCache();
      if (cachedDashboard) {
        logger.info("DashboardService: returning cached dashboard");
        return cachedDashboard;
      }

      logger.info("DashboardService: generating fresh dashboard summary");
      // Get current league and season
      const currentLeague = await this.getCurrentLeague();
      if (!currentLeague) {
        throw new Error("No current league found");
      }

      const dashboardSummary = await this.generateFreshDashboardData(currentLeague);

      // Cache the dashboard summary for better performance
      await this.setDashboardInCache(dashboardSummary);

      return dashboardSummary;
    } catch (error) {
      logger.error("Error generating dashboard summary:", error);
      // Return empty dashboard instead of throwing to prevent server crashes
      return this.getEmptyDashboard();
    }
  }

  /**
   * Generate fresh dashboard data for a given league
   */
  private async generateFreshDashboardData(currentLeague: any): Promise<DashboardSummary> {
    const currentSeasonNumber = await this.getCurrentSeasonNumber(currentLeague.id);

    // Get filtered data for current league and season
    const [allTeams, allPlayers, currentSeasonGames] = await Promise.all([
      this.teamRepository.getTeamsByLeagueId(currentLeague.id).catch(err => {
        logger.error("Error fetching teams:", err);
        return [];
      }), // Teams in current league only
      this.playerRepository.getPlayersByLeague(currentLeague.id).catch(err => {
        logger.error("Error fetching players:", err);
        return [];
      }), // Players in current league only
      this.gameRepository.getLeagueGamesBySeason({
        leagueId: currentLeague.id,
        seasonNumber: currentSeasonNumber
      }).catch(err => {
          logger.error("Error fetching games:", err);
          return [];
        })
    ]);

    const totalTeams = allTeams.length;
    const totalPlayers = allPlayers.length;
    const totalGames = currentSeasonGames.length;

    // Calculate fixtures count from current season games
    const totalFixtures = await this.fixtureRepository.countFixturesByLeague(currentLeague.id);

    // Get current season info
    const currentSeason = {
      seasonNumber: currentSeasonNumber,
      gamesPlayed: 0,
      gamesRemaining: 0,
      completionPercentage: 0
    };

    // Get goals and scoring stats
    const totalGoals = this.calculateTotalGoals(currentSeasonGames);
    const averageGoalsPerGame = totalGames > 0 ? totalGoals / totalGames : 0;
    const topScorer = await this.getTopScorer(currentLeague.id, currentSeasonNumber);

    // Get team performance
    const leagueLeader = await this.getLeagueLeader(currentLeague.id, currentSeasonNumber);

    // Get recent activity
    const [recentGames, upcomingGames] = await Promise.all([
      this.getRecentGames(5, currentLeague.id, currentSeasonNumber),
      this.getUpcomingGames(5, currentLeague.id, currentSeasonNumber)
    ]);

    // Get player statistics
    const totalAssists = this.calculateTotalAssists(currentSeasonGames);
    const averageRating = this.calculateAverageRating(currentSeasonGames);
    const cleanSheets = this.calculateCleanSheets(currentSeasonGames);

    // Get recent transfers
    const recentTransfers = await this.getRecentTransfers(5);

    // Get consolidated data for dashboard components
    const [topScorers, topAssisters, leagueTable] = await Promise.all([
      this.getTopScorersData(currentLeague.id, 10),
      this.getTopAssistersData(currentLeague.id, 10),
      this.getLeagueTableData(currentLeague.id)
    ]);

    return {
      totalTeams,
      totalPlayers,
      totalFixtures,
      totalGames,
      currentSeason,
      totalGoals,
      averageGoalsPerGame,
      topScorer,
      leagueLeader,
      recentGames,
      upcomingGames,
      totalAssists,
      averageRating,
      cleanSheets,
      recentTransfers,
      topScorers,
      topAssisters,
      leagueTable
    };
  }

  private getEmptyDashboard(): DashboardSummary {
    return {
      totalTeams: 0,
      totalPlayers: 0,
      totalFixtures: 0,
      totalGames: 0,
      currentSeason: {
        seasonNumber: 1,
        gamesPlayed: 0,
        gamesRemaining: 0,
        completionPercentage: 0
      },
      totalGoals: 0,
      averageGoalsPerGame: 0,
      topScorer: null,
      leagueLeader: null,
      recentGames: [],
      upcomingGames: [],
      totalAssists: 0,
      averageRating: 0,
      cleanSheets: 0,
      recentTransfers: [],
      topScorers: [],
      topAssisters: [],
      leagueTable: []
    };
  }

  private async getCurrentLeague() {
    try {
      // Get the first/main league - you might want to implement proper current league logic
      const leagues = await this.leagueRepository.getAllLeagues();
      return leagues.length > 0 ? leagues[0] : null;
    } catch (error) {
      logger.error("Error getting current league:", error);
      return null;
    }
  }

  private async getCurrentSeasonNumber(leagueId: string): Promise<number> {
    // Get the highest season number for the league efficiently using aggregation
    try {
      logger.info(`Getting current season number for league: ${leagueId}`);
      const result = await this.gameRepository.getMaxSeasonNumberForLeague(leagueId);
      const seasonNumber = result || 1;
      logger.info(`Current season number for league ${leagueId}: ${seasonNumber}`);
      return seasonNumber;
    } catch (error) {
      logger.error("Error getting current season number:", error);
      return 1;
    }
  }

  private async getTopScorer(leagueId: string, seasonNumber: number) {
    try {
      // Get games for the current season
      const games = await this.gameRepository.getLeagueGamesBySeason({
        leagueId,
        seasonNumber
      });

      // Aggregate goals by player
      const playerGoals: { [playerId: string]: { goals: number, playerName: string, teamName: string } } = {};

      games.forEach(game => {
        // Process home team players
        game.homeTeamPlayersPerformance?.forEach(player => {
          if (player.goals && player.goals > 0) {
            const playerIdStr = player.playerId.toString();
            if (!playerGoals[playerIdStr]) {
              playerGoals[playerIdStr] = {
                goals: 0,
                playerName: 'Unknown Player', // Will be populated later
                teamName: (game.homeTeam as any)?.name || 'Unknown Team'
              };
            }
            playerGoals[playerIdStr].goals += player.goals;
          }
        });

        // Process away team players
        game.awayTeamPlayersPerformance?.forEach(player => {
          if (player.goals && player.goals > 0) {
            const playerIdStr = player.playerId.toString();
            if (!playerGoals[playerIdStr]) {
              playerGoals[playerIdStr] = {
                goals: 0,
                playerName: 'Unknown Player', // Will be populated later
                teamName: (game.awayTeam as any)?.name || 'Unknown Team'
              };
            }
            playerGoals[playerIdStr].goals += player.goals;
          }
        });
      });

      // Populate player names
      for (const [playerId, data] of Object.entries(playerGoals)) {
        try {
          const player = await this.playerRepository.getPlayerById(playerId);
          data.playerName = player.name;
        } catch (error) {
          logger.warn(`Could not fetch player name for ${playerId}:`, error);
          // Keep default 'Unknown Player'
        }
      }

      // Find the top scorer
      let topScorer = null;
      let maxGoals = 0;
      let topScorerPlayerId = null;

      Object.entries(playerGoals).forEach(([playerId, data]) => {
        if (data.goals > maxGoals) {
          maxGoals = data.goals;
          topScorerPlayerId = playerId;
          topScorer = {
            playerId,
            playerName: data.playerName,
            goals: data.goals,
            teamName: data.teamName
          };
        }
      });

      // Fetch player image URL if we have a top scorer
      if (topScorer && topScorerPlayerId) {
        try {
          const player = await this.playerRepository.getPlayerById(topScorerPlayerId);
          (topScorer as any).playerImgUrl = player.imgUrl;
        } catch (error) {
          logger.warn(`Could not fetch image for top scorer ${topScorerPlayerId}:`, error);
          // Keep topScorer without image URL
        }
      }

      return topScorer;
    } catch (error) {
      logger.error("Error getting top scorer:", error);
      return null;
    }
  }

  private async getLeagueLeader(leagueId: string, seasonNumber: number) {
    try {
      // Get games for the current season
      const games = await this.gameRepository.getLeagueGamesBySeason({
        leagueId,
        seasonNumber
      });

      // Aggregate team stats
      const teamStats: { [teamId: string]: { points: number, gamesPlayed: number, teamName: string } } = {};

      games.forEach(game => {
        if (game.status === GAME_STATUS.COMPLETED && game.result) {
          const homeTeamId = (game.homeTeam as any)?._id?.toString() || game.homeTeam.toString();
          const awayTeamId = (game.awayTeam as any)?._id?.toString() || game.awayTeam.toString();
          const homeTeamName = (game.homeTeam as any)?.name || 'Unknown';
          const awayTeamName = (game.awayTeam as any)?.name || 'Unknown';

          // Initialize team stats if not exists
          if (!teamStats[homeTeamId]) {
            teamStats[homeTeamId] = { points: 0, gamesPlayed: 0, teamName: homeTeamName };
          }
          if (!teamStats[awayTeamId]) {
            teamStats[awayTeamId] = { points: 0, gamesPlayed: 0, teamName: awayTeamName };
          }

          // Calculate points (3 for win, 1 for draw, 0 for loss)
          const homeGoals = game.result.homeTeamGoals || 0;
          const awayGoals = game.result.awayTeamGoals || 0;

          teamStats[homeTeamId].gamesPlayed++;
          teamStats[awayTeamId].gamesPlayed++;

          if (homeGoals > awayGoals) {
            // Home team wins
            teamStats[homeTeamId].points += 3;
          } else if (awayGoals > homeGoals) {
            // Away team wins
            teamStats[awayTeamId].points += 3;
          } else {
            // Draw
            teamStats[homeTeamId].points += 1;
            teamStats[awayTeamId].points += 1;
          }
        }
      });

      // Find the team with most points
      let leagueLeader: {
        teamId: string;
        teamName: string;
        teamImgUrl?: string;
        points: number;
        gamesPlayed: number;
      } | null = null;
      let maxPoints = 0;
      let leaderTeamId = null;

      Object.entries(teamStats).forEach(([teamId, stats]) => {
        if (stats.points > maxPoints) {
          maxPoints = stats.points;
          leaderTeamId = teamId;
          leagueLeader = {
            teamId,
            teamName: stats.teamName,
            points: stats.points,
            gamesPlayed: stats.gamesPlayed
          };
        }
      });

      // Fetch team image URL if we have a league leader
      if (leagueLeader && leaderTeamId) {
        try {
          const team = await this.teamRepository.getTeamById(leaderTeamId);
          (leagueLeader as any).teamImgUrl = team.imgUrl;
        } catch (error) {
          logger.warn(`Could not fetch image for league leader team ${leaderTeamId}:`, error);
          // Keep leagueLeader without image URL
        }
      }

      return leagueLeader;
    } catch (error) {
      logger.error("Error getting league leader:", error);
      return null;
    }
  }

  private async getRecentGames(limit: number, leagueId: string, seasonNumber: number) {
    try {
      const games = await this.gameRepository.getLeagueGamesBySeason({
        leagueId,
        seasonNumber
      });

      // Filter completed games and sort by date descending
      const completedGames = games
        .filter(game => game.status === GAME_STATUS.COMPLETED && game.result)
        .sort((a, b) => {
          if (!a.date || !b.date) return 0;
          return new Date(b.date).getTime() - new Date(a.date).getTime();
        })
        .slice(0, limit);

      return completedGames.map(game => ({
        id: (game._id as any).toString(),
        homeTeam: (game.homeTeam as any)?.name || 'Unknown',
        awayTeam: (game.awayTeam as any)?.name || 'Unknown',
        result: `${game.result?.homeTeamGoals || 0} - ${game.result?.awayTeamGoals || 0}`,
        date: game.date || new Date()
      }));
    } catch (error) {
      logger.error("Error getting recent games:", error);
      return [];
    }
  }

  private async getUpcomingGames(limit: number, leagueId: string, seasonNumber: number) {
    try {
      const games = await this.gameRepository.getLeagueGamesBySeason({
        leagueId,
        seasonNumber
      });

      // Filter scheduled games and sort by date ascending
      const upcomingGames = games
        .filter(game => game.status === GAME_STATUS.SCHEDULED && game.date)
        .sort((a, b) => {
          if (!a.date || !b.date) return 0;
          return new Date(a.date).getTime() - new Date(b.date).getTime();
        })
        .slice(0, limit);

      return upcomingGames.map(game => ({
        id: (game._id as any).toString(),
        homeTeam: (game.homeTeam as any)?.name || 'Unknown',
        awayTeam: (game.awayTeam as any)?.name || 'Unknown',
        date: game.date || new Date()
      }));
    } catch (error) {
      logger.error("Error getting upcoming games:", error);
      return [];
    }
  }

  private async getRecentTransfers(_limit: number) {
    // This would need to be implemented
    return [];
  }

  private calculateTotalGoals(games: any[]): number {
    try {
      if (!games || !Array.isArray(games)) {
        logger.warn("Invalid games array provided to calculateTotalGoals");
        return 0;
      }

      return games.reduce((total, game) => {
        if (game && game.result && typeof game.result === 'object') {
          const homeGoals = Number(game.result.homeTeamGoals) || 0;
          const awayGoals = Number(game.result.awayTeamGoals) || 0;
          return total + homeGoals + awayGoals;
        }
        return total;
      }, 0);
    } catch (error) {
      logger.error("Error calculating total goals:", error);
      return 0;
    }
  }

  private calculateTotalAssists(games: any[]): number {
    try {
      if (!games || !Array.isArray(games)) {
        logger.warn("Invalid games array provided to calculateTotalAssists");
        return 0;
      }

      return games.reduce((total, game) => {
        if (!game) return total;

        let gameAssists = 0;

        // Count assists from home team players
        if (game.homeTeamPlayersPerformance && Array.isArray(game.homeTeamPlayersPerformance)) {
          gameAssists += game.homeTeamPlayersPerformance.reduce((teamTotal: number, player: any) => {
            if (player && typeof player.assists === 'number') {
              return teamTotal + player.assists;
            }
            return teamTotal;
          }, 0);
        }

        // Count assists from away team players
        if (game.awayTeamPlayersPerformance && Array.isArray(game.awayTeamPlayersPerformance)) {
          gameAssists += game.awayTeamPlayersPerformance.reduce((teamTotal: number, player: any) => {
            if (player && typeof player.assists === 'number') {
              return teamTotal + player.assists;
            }
            return teamTotal;
          }, 0);
        }

        return total + gameAssists;
      }, 0);
    } catch (error) {
      logger.error("Error calculating total assists:", error);
      return 0;
    }
  }

  private calculateAverageRating(games: any[]): number {
    try {
      if (!games || !Array.isArray(games)) {
        logger.warn("Invalid games array provided to calculateAverageRating");
        return 0;
      }

      let totalRating = 0;
      let playerCount = 0;

      games.forEach(game => {
        if (!game) return;

        // Process home team players
        if (game.homeTeamPlayersPerformance && Array.isArray(game.homeTeamPlayersPerformance)) {
          game.homeTeamPlayersPerformance.forEach((player: any) => {
            if (player && typeof player.rating === 'number' && player.rating > 0) {
              totalRating += player.rating;
              playerCount++;
            }
          });
        }

        // Process away team players
        if (game.awayTeamPlayersPerformance && Array.isArray(game.awayTeamPlayersPerformance)) {
          game.awayTeamPlayersPerformance.forEach((player: any) => {
            if (player && typeof player.rating === 'number' && player.rating > 0) {
              totalRating += player.rating;
              playerCount++;
            }
          });
        }
      });

      return playerCount > 0 ? Number((totalRating / playerCount).toFixed(2)) : 0;
    } catch (error) {
      logger.error("Error calculating average rating:", error);
      return 0;
    }
  }

  private calculateCleanSheets(games: any[]): number {
    try {
      if (!games || !Array.isArray(games)) {
        logger.warn("Invalid games array provided to calculateCleanSheets");
        return 0;
      }

      return games.reduce((total, game) => {
        if (game && game.result && typeof game.result === 'object') {
          const homeGoals = Number(game.result.homeTeamGoals) || 0;
          const awayGoals = Number(game.result.awayTeamGoals) || 0;

          // Count clean sheets (games where a team didn't concede)
          if (homeGoals === 0 || awayGoals === 0) {
            return total + 1;
          }
        }
        return total;
      }, 0);
    } catch (error) {
      logger.error("Error calculating clean sheets:", error);
      return 0;
    }
  }

  // Cache helper methods - Reserved for future caching implementation
  // @ts-ignore - Method reserved for future use
  /**
   * Get consolidated top scorers data for dashboard
   */
  private async getTopScorersData(leagueId: string, limit: number = 10) {
    try {
      const topScorersStats = await this.leagueRepository.calculateLeagueTopScorers(leagueId, limit);

      return topScorersStats.map(player => ({
        playerId: player.playerId.toString(),
        playerName: player.playerName,
        playerImgUrl: player.playerImgUrl,
        goals: player.goals,
        assists: 0, // TopScorer doesn't include assists
        gamesPlayed: player.games,
        goalsPerGame: player.goalsPerGame || 0,
        teamName: player.teamName,
        teamId: player.teamId?.toString() || ''
      }));
    } catch (error) {
      logger.error("Error getting top scorers data:", error);
      return [];
    }
  }

  /**
   * Get consolidated top assisters data for dashboard
   */
  private async getTopAssistersData(leagueId: string, limit: number = 10) {
    try {
      const topAssistersStats = await this.leagueRepository.calculateLeagueTopAssisters(leagueId, limit);

      return topAssistersStats.map(player => ({
        playerId: player.playerId.toString(),
        playerName: player.playerName,
        playerImgUrl: player.playerImgUrl,
        assists: player.assists,
        goals: 0, // TopAssister doesn't include goals
        gamesPlayed: player.games,
        assistsPerGame: player.assistsPerGame || 0,
        teamName: player.teamName,
        teamId: player.teamId?.toString() || ''
      }));
    } catch (error) {
      logger.error("Error getting top assisters data:", error);
      return [];
    }
  }

  /**
   * Get consolidated league table data for dashboard
   */
  private async getLeagueTableData(leagueId: string) {
    try {
      // Use the existing league service to get league table data
      const leagueTableStats = await this.leagueService.getLeagueTable(leagueId);

      return leagueTableStats.map((team: any, index: number) => ({
        teamId: team.teamId.toString(),
        teamName: team.teamName,
        teamImgUrl: team.imgUrl || '',
        position: index + 1,
        gamesPlayed: team.gamesPlayed,
        wins: team.gamesWon,
        draws: team.draws,
        losses: team.gamesLost,
        goalsFor: team.goalsScored,
        goalsAgainst: team.goalsConceded,
        goalDifference: team.goalDifference,
        points: team.points,
        lastForm: team.lastForm || []
      }));
    } catch (error) {
      logger.error("Error getting league table data:", error);
      return [];
    }
  }

  private async getDashboardFromCache(): Promise<DashboardSummary | null> {
    try {
      const cachedData = await this.cacheService.get(DASHBOARD_CACHE_KEY);
      if (cachedData) {
        return JSON.parse(cachedData);
      }
      return null;
    } catch (error) {
      logger.error("Error getting dashboard from cache:", error);
      return null;
    }
  }

  // @ts-ignore - Method reserved for future use
  private async setDashboardInCache(dashboard: DashboardSummary): Promise<void> {
    try {
      // Cache for 5 days
      await this.cacheService.set(DASHBOARD_CACHE_KEY, dashboard, DASHBOARD_CACHE_DURATION);
      logger.info(`DashboardService: Dashboard cached for ${DASHBOARD_CACHE_DURATION / (24 * 60 * 60)} days`);
    } catch (error) {
      logger.error("Error setting dashboard in cache:", error);
    }
  }

  async clearDashboardCache(): Promise<void> {
    try {
      await this.cacheService.delete(DASHBOARD_CACHE_KEY);
      logger.info("DashboardService: Dashboard cache cleared");
    } catch (error) {
      logger.error("Error clearing dashboard cache:", error);
    }
  }

  /**
   * Force refresh the dashboard cache by generating fresh data
   * Used by cache warming service
   */
  async refreshDashboardCache(): Promise<DashboardSummary> {
    try {
      logger.info("DashboardService: Force refreshing dashboard cache");

      // Clear existing cache first
      await this.clearDashboardCache();

      // Generate fresh dashboard data (same logic as getDashboardSummary but skip cache check)
      const currentLeague = await this.getCurrentLeague();
      if (!currentLeague) {
        throw new Error("No current league found");
      }

      const dashboardSummary = await this.generateFreshDashboardData(currentLeague);

      // Cache the fresh data
      await this.setDashboardInCache(dashboardSummary);

      logger.info("DashboardService: Dashboard cache refreshed successfully");
      return dashboardSummary;
    } catch (error) {
      logger.error("Error refreshing dashboard cache:", error);
      throw error;
    }
  }
}
