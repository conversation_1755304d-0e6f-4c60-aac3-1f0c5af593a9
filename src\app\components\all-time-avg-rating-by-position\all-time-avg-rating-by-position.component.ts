import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { LeagueService } from '../../services/league.service';
import { NotificationService } from '../../services/notification.service';
import { ListOption } from '../../shared/models/list-option.model';
import { PLAYABLE_POSITIONS_OPTIONS } from '../top-scorers/top-scorers.definitions';
import { AllTimeTopAvgRatingByPosition } from '../../shared/models/all-time-statistics.model';

@Component({
  selector: 'app-all-time-avg-rating-by-position',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './all-time-avg-rating-by-position.component.html',
  styleUrls: ['./all-time-avg-rating-by-position.component.scss']
})
export class AllTimeAvgRatingByPositionComponent implements OnInit {
  allTimeTopAvgRatingData: AllTimeTopAvgRatingByPosition[] = [];
  topPlayersByPosition: { [position: string]: AllTimeTopAvgRatingByPosition } = {};
  playablePositionOptions: ListOption[] = [...PLAYABLE_POSITIONS_OPTIONS];
  isLoading: boolean = false;
  chosenPosition: string = 'ST';
  minimumGames: number = 50;
  selectedLeagueId: string = '';
  leagues: any[] = [];

  @Input() hideTitle: boolean = false;
  @Input() leagueId?: string;

  constructor(
    private router: Router,
    private leagueService: LeagueService,
    private notificationService: NotificationService
  ) { }

  async ngOnInit(): Promise<void> {
    await this.loadLeagues();
    if (this.leagueId) {
      this.selectedLeagueId = this.leagueId;
      await this.loadData();
    }
  }

  private async loadLeagues(): Promise<void> {
    try {
      this.leagues = await this.leagueService.getAllLeagues();
      if (this.leagues.length > 0 && !this.selectedLeagueId) {
        this.selectedLeagueId = this.leagues[0].id;
        await this.loadData();
      }
    } catch (error) {
      console.error('Error loading leagues:', error);
      this.notificationService.error('Failed to load leagues');
    }
  }

  async onLeagueChange(): Promise<void> {
    if (this.selectedLeagueId) {
      await this.loadData();
    }
  }

  async onPositionChange(): Promise<void> {
    await this.loadTopAvgData();
  }

  async onMinimumGamesChange(): Promise<void> {
    await this.loadData();
  }

  private async loadData(): Promise<void> {
    if (!this.selectedLeagueId) return;
    
      await this.loadTopAvgData();
  }

  private async loadTopAvgData(): Promise<void> {
    if (!this.selectedLeagueId) return;

    this.isLoading = true;
    try {
      const response = await this.leagueService.getAllTimeTopAvgRatingByPosition(
        this.selectedLeagueId, 
        this.chosenPosition, 
        this.minimumGames
      );

      this.allTimeTopAvgRatingData = response.map(player => ({
        ...player,
        tableIcon: { 
          name: player.playerName, 
          imgUrl: player.playerImgUrl || '', 
          isTeam: false 
        },
        avgRating: parseFloat(player.avgRating.toFixed(2))
      }));
    } catch (error) {
      console.error('Error loading all-time avg rating data:', error);
      this.notificationService.error('Failed to load statistics');
      this.allTimeTopAvgRatingData = [];
    } finally {
      this.isLoading = false;
    }
  }

  onPlayerClick(player: AllTimeTopAvgRatingByPosition): void {
    this.router.navigate(['/player', player.playerId]);
  }

  onTeamClick(teamId: string): void {
    this.router.navigate(['/team-details', teamId]);
  }

  isFilteringByPosition(): boolean {
    return this.chosenPosition.toLowerCase() !== 'any';
  }

  getPositionDisplayName(position: string): string {
    const option = this.playablePositionOptions.find(opt => opt.value === position);
    return option ? option.displayText : position;
  }

  getPlayerRank(index: number): string {
    return `#${index + 1}`;
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/User.jpg';
    }
  }
}
