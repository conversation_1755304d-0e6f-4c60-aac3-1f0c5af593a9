/* === MODERN FORMATION DISPLAY COMPONENT === */

.formation-display-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  font-family: var(--font-sans);
  position: relative;

  /* Add subtle background pattern */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }
}

/* === FORMATION HEADERS === */
.formation-headers {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xl);
  background: var(--surface-primary);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
  box-shadow:
    var(--shadow-xl),
    0 0 0 1px rgba(99, 102, 241, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;

  /* Subtle background gradient */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(99, 102, 241, 0.02) 0%,
      transparent 50%,
      rgba(16, 185, 129, 0.01) 100%
    );
    pointer-events: none;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
  }
}

.formation-header {
  flex: 1;
  display: flex;
  align-items: center;

  &.away-header {
    justify-content: flex-end;
  }
}

.team-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);

  .away-header & {
    flex-direction: row-reverse;
  }
}

.team-logo {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  object-fit: cover;
  border: 2px solid var(--border-primary);
}

.team-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);

  .away-header & {
    align-items: flex-end;
  }
}

.team-name {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.formation-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: 500;
  padding: 2px 8px;
  background: var(--surface-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-primary);
}

.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-lg);

  span {
    font-size: var(--text-lg);
    font-weight: 700;
    color: var(--text-secondary);
    background: var(--surface-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-full);
    border: 1px solid var(--border-primary);
  }

  @media (max-width: 768px) {
    padding: 0;
  }
}

/* === PITCH CONTAINER === */
.pitch-container {
  background: var(--surface-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.pitch {
  position: relative;
  width: 100%;
  height: 600px;
  background: linear-gradient(45deg, #16a34a 25%, #15803d 25%, #15803d 50%, #16a34a 50%, #16a34a 75%, #15803d 75%);
  background-size: 20px 20px;
  border-radius: var(--radius-lg);

  @media (max-width: 768px) {
    height: 400px;
  }

  @media (max-width: 480px) {
    height: 300px;
  }
}

/* === PITCH MARKINGS === */
.pitch-markings {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.center-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120px;
  height: 120px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%);

  @media (max-width: 768px) {
    width: 80px;
    height: 80px;
  }
}

.center-line {
  position: absolute;
  top: 0;
  left: 50%;
  width: 2px;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(-50%);
}

.penalty-area {
  position: absolute;
  width: 120px;
  height: 200px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  top: 50%;
  transform: translateY(-50%);

  &.left-penalty {
    left: 0;
    border-right: none;
  }

  &.right-penalty {
    right: 0;
    border-left: none;
  }

  @media (max-width: 768px) {
    width: 80px;
    height: 140px;
  }
}

.goal-area {
  position: absolute;
  width: 60px;
  height: 100px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  top: 50%;
  transform: translateY(-50%);

  &.left-goal {
    left: 0;
    border-right: none;
  }

  &.right-goal {
    right: 0;
    border-left: none;
  }

  @media (max-width: 768px) {
    width: 40px;
    height: 70px;
  }
}

/* === PLAYERS === */
.players-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.player-marker {
  position: absolute;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;

  &:hover {
    transform: translate(-50%, -50%) scale(1.15);
    z-index: 20;

    .player-image {
      border-color: var(--primary-400);
      box-shadow:
        var(--shadow-xl),
        0 0 20px rgba(99, 102, 241, 0.3),
        0 0 0 1px rgba(99, 102, 241, 0.2);
    }

    .player-info {
      background: rgba(0, 0, 0, 0.9);
      backdrop-filter: blur(15px);
      transform: translateY(-2px);
    }

    .rating-circle {
      transform: scale(1.1);
    }
  }

  &.potm {
    z-index: 15;

    .player-image {
      border-color: var(--accent-primary);
      box-shadow:
        var(--shadow-xl),
        0 0 20px rgba(255, 215, 0, 0.4),
        0 0 0 1px rgba(255, 215, 0, 0.3);
    }
  }

  &.away-player {
    .player-info {
      order: -1;
    }
  }
}

.player-image-container {
  position: relative;
  width: 50px;
  height: 50px;

  @media (max-width: 768px) {
    width: 40px;
    height: 40px;
  }

  @media (max-width: 480px) {
    width: 32px;
    height: 32px;
  }
}

.player-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--surface-primary);
  box-shadow:
    var(--shadow-lg),
    0 0 0 1px rgba(99, 102, 241, 0.1);
  transition: all 0.3s ease;
}

.rating-circle {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid;
  background: var(--surface-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 700;
  box-shadow:
    var(--shadow-lg),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    width: 24px;
    height: 24px;
    font-size: 9px;
  }

  @media (max-width: 480px) {
    width: 20px;
    height: 20px;
    font-size: 8px;
  }

  &.rating-excellent {
    border-color: var(--success-500);
    color: var(--success-500);
    box-shadow:
      var(--shadow-lg),
      0 0 10px rgba(16, 185, 129, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }

  &.rating-good {
    border-color: var(--info-500);
    color: var(--info-500);
    box-shadow:
      var(--shadow-lg),
      0 0 10px rgba(59, 130, 246, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }

  &.rating-average {
    border-color: var(--warning-500);
    color: var(--warning-500);
    box-shadow:
      var(--shadow-lg),
      0 0 10px rgba(245, 158, 11, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }

  &.rating-poor {
    border-color: var(--error-500);
    color: var(--error-500);
    box-shadow:
      var(--shadow-lg),
      0 0 10px rgba(239, 68, 68, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }

  &.rating-very-poor {
    border-color: var(--neutral-400);
    color: var(--neutral-400);
    box-shadow:
      var(--shadow-lg),
      0 0 10px rgba(107, 114, 128, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }
}

.potm-crown {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  color: var(--accent-primary);
  font-size: 18px;
  text-shadow:
    1px 1px 2px rgba(0, 0, 0, 0.8),
    0 0 10px rgba(255, 215, 0, 0.5);
  animation: crown-glow 2s ease-in-out infinite alternate;
  z-index: 5;

  @media (max-width: 768px) {
    font-size: 16px;
    top: -10px;
  }

  @media (max-width: 480px) {
    font-size: 14px;
    top: -8px;
  }
}

.player-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  text-align: center;
  min-width: 80px;
  backdrop-filter: blur(4px);

  @media (max-width: 768px) {
    min-width: 60px;
    font-size: 10px;
  }

  @media (max-width: 480px) {
    min-width: 50px;
    font-size: 9px;
  }
}

.player-name {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;

  @media (max-width: 768px) {
    max-width: 80px;
  }
}

.player-position {
  font-size: 10px;
  opacity: 0.8;
  font-weight: 500;

  @media (max-width: 768px) {
    font-size: 9px;
  }
}

.player-stats {
  margin-top: 2px;
}

.stats-text {
  font-size: 10px;
  color: #10b981;
  font-weight: 600;

  @media (max-width: 768px) {
    font-size: 9px;
  }
}

/* === FORMATION LEGEND === */
.formation-legend {
  background: var(--surface-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);

  @media (max-width: 768px) {
    padding: var(--spacing-md);
  }
}

.legend-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.legend-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);

  i {
    color: var(--primary);
  }
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);

  @media (max-width: 768px) {
    gap: var(--spacing-sm);
  }
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--text-sm);
  color: var(--text-secondary);

  @media (max-width: 480px) {
    font-size: var(--text-xs);
  }
}

.legend-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid;
  transition: all 0.3s ease;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 50%;
    background: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 0.3;
    animation: pulse 1s ease-in-out infinite;
  }

  &.rating-excellent {
    border-color: var(--success-500);
    background: rgba(16, 185, 129, 0.2);
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
  }

  &.rating-good {
    border-color: var(--info-500);
    background: rgba(59, 130, 246, 0.2);
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }

  &.rating-average {
    border-color: var(--warning-500);
    background: rgba(245, 158, 11, 0.2);
    box-shadow: 0 0 5px rgba(245, 158, 11, 0.3);
  }

  &.rating-poor {
    border-color: var(--error-500);
    background: rgba(239, 68, 68, 0.2);
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.3);
  }
}

.potm-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffd700;
  font-size: 12px;
}

/* === MODERN ANIMATIONS === */
@keyframes crown-glow {
  0% {
    text-shadow:
      1px 1px 2px rgba(0, 0, 0, 0.8),
      0 0 10px rgba(255, 215, 0, 0.5),
      0 0 20px rgba(255, 215, 0, 0.3);
    transform: translateX(-50%) scale(1);
  }
  50% {
    text-shadow:
      1px 1px 2px rgba(0, 0, 0, 0.8),
      0 0 20px rgba(255, 215, 0, 0.8),
      0 0 30px rgba(255, 215, 0, 0.5);
    transform: translateX(-50%) scale(1.05);
  }
  100% {
    text-shadow:
      1px 1px 2px rgba(0, 0, 0, 0.8),
      0 0 10px rgba(255, 215, 0, 0.5),
      0 0 20px rgba(255, 215, 0, 0.3);
    transform: translateX(-50%) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Apply animations to formation elements */
.formation-headers {
  animation: fadeInUp 0.6s ease-out;
}

.pitch-container {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.formation-legend {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.home-players .player-marker {
  animation: slideInFromLeft 0.6s ease-out both;
}

.away-players .player-marker {
  animation: slideInFromRight 0.6s ease-out both;
}

/* Stagger player animations */
.player-marker:nth-child(1) { animation-delay: 0.1s; }
.player-marker:nth-child(2) { animation-delay: 0.2s; }
.player-marker:nth-child(3) { animation-delay: 0.3s; }
.player-marker:nth-child(4) { animation-delay: 0.4s; }
.player-marker:nth-child(5) { animation-delay: 0.5s; }
.player-marker:nth-child(6) { animation-delay: 0.6s; }
.player-marker:nth-child(7) { animation-delay: 0.7s; }
.player-marker:nth-child(8) { animation-delay: 0.8s; }
.player-marker:nth-child(9) { animation-delay: 0.9s; }
.player-marker:nth-child(10) { animation-delay: 1.0s; }
.player-marker:nth-child(11) { animation-delay: 1.1s; }
