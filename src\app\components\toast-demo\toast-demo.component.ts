import { Component } from '@angular/core';
import { NotificationService } from '../../services/notification.service';

@Component({
  selector: 'app-toast-demo',
  template: `
    <div class="toast-demo-container">
      <div class="demo-header">
        <h2>🍞 Beautiful Toast Notifications</h2>
        <p>Click the buttons below to see the enhanced toast notifications in action!</p>
      </div>

      <div class="demo-grid">
        <div class="demo-section">
          <h3>Standard Notifications</h3>
          <div class="button-group">
            <button class="demo-btn success" (click)="showSuccess()">
              <i class="fas fa-check"></i>
              Success Toast
            </button>
            <button class="demo-btn error" (click)="showError()">
              <i class="fas fa-times"></i>
              Error Toast
            </button>
            <button class="demo-btn info" (click)="showInfo()">
              <i class="fas fa-info"></i>
              Info Toast
            </button>
            <button class="demo-btn warning" (click)="showWarning()">
              <i class="fas fa-exclamation"></i>
              Warning Toast
            </button>
          </div>
        </div>

        <div class="demo-section">
          <h3>AI-Specific Notifications</h3>
          <div class="button-group">
            <button class="demo-btn ai-success" (click)="showAISuccess()">
              <i class="fas fa-robot"></i>
              AI Success
            </button>
            <button class="demo-btn ai-error" (click)="showAIError()">
              <i class="fas fa-robot"></i>
              AI Error
            </button>
            <button class="demo-btn loading" (click)="showLoading()">
              <i class="fas fa-spinner fa-spin"></i>
              Loading Toast
            </button>
          </div>
        </div>

        <div class="demo-section">
          <h3>Custom Options</h3>
          <div class="button-group">
            <button class="demo-btn custom" (click)="showCustomDuration()">
              <i class="fas fa-clock"></i>
              Long Duration
            </button>
            <button class="demo-btn custom" (click)="showPersistent()">
              <i class="fas fa-lock"></i>
              Persistent
            </button>
            <button class="demo-btn dismiss" (click)="dismissAll()">
              <i class="fas fa-times-circle"></i>
              Dismiss All
            </button>
          </div>
        </div>
      </div>

      <div class="demo-features">
        <h3>✨ Enhanced Features</h3>
        <ul>
          <li>🎨 Beautiful gradients and animations</li>
          <li>🔔 Icon indicators for each notification type</li>
          <li>⏱️ Progress bars showing auto-dismiss timing</li>
          <li>🌙 Dark/Light mode support</li>
          <li>📱 Mobile-responsive design</li>
          <li>♿ Accessibility improvements</li>
          <li>🤖 Special AI-themed notifications</li>
          <li>🎭 Smooth entrance/exit animations</li>
        </ul>
      </div>
    </div>
  `,
  styles: [`
    .toast-demo-container {
      padding: var(--spacing-xl);
      max-width: 800px;
      margin: 0 auto;
      background: var(--bg-primary);
      min-height: 100vh;
    }

    .demo-header {
      text-align: center;
      margin-bottom: var(--spacing-xl);
      
      h2 {
        color: var(--text-primary);
        font-size: 2rem;
        margin-bottom: var(--spacing-sm);
      }
      
      p {
        color: var(--text-secondary);
        font-size: 1.1rem;
      }
    }

    .demo-grid {
      display: grid;
      gap: var(--spacing-xl);
      margin-bottom: var(--spacing-xl);
    }

    .demo-section {
      background: var(--surface-primary);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      border: 1px solid var(--border-primary);
      
      h3 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        font-size: 1.25rem;
      }
    }

    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-md);
    }

    .demo-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      padding: var(--spacing-sm) var(--spacing-md);
      border: none;
      border-radius: var(--radius-md);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }
      
      &.success {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
      }
      
      &.error {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
      }
      
      &.info {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
      }
      
      &.warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
      }
      
      &.ai-success {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        color: white;
      }
      
      &.ai-error {
        background: linear-gradient(135deg, #f43f5e, #e11d48);
        color: white;
      }
      
      &.loading {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
        color: white;
      }
      
      &.custom {
        background: linear-gradient(135deg, #6366f1, #4f46e5);
        color: white;
      }
      
      &.dismiss {
        background: var(--surface-secondary);
        color: var(--text-primary);
        border: 1px solid var(--border-primary);
      }
    }

    .demo-features {
      background: var(--surface-primary);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      border: 1px solid var(--border-primary);
      
      h3 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        font-size: 1.25rem;
      }
      
      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        
        li {
          color: var(--text-secondary);
          padding: var(--spacing-xs) 0;
          font-size: 0.95rem;
        }
      }
    }

    @media (max-width: 768px) {
      .button-group {
        flex-direction: column;
      }
      
      .demo-btn {
        justify-content: center;
      }
    }
  `]
})
export class ToastDemoComponent {

  constructor(private notificationService: NotificationService) {}

  showSuccess() {
    this.notificationService.success('Operation completed successfully! 🎉');
  }

  showError() {
    this.notificationService.error('Something went wrong. Please try again.');
  }

  showInfo() {
    this.notificationService.info('Here\'s some helpful information for you.');
  }

  showWarning() {
    this.notificationService.warning('Please review your input before proceeding.');
  }

  showAISuccess() {
    this.notificationService.aiSuccess('Player data extracted successfully from screenshot!');
  }

  showAIError() {
    this.notificationService.aiError('Could not analyze the image. Please try a clearer screenshot.');
  }

  showLoading() {
    const loadingToast = this.notificationService.loading('Processing your request...');
    
    // Auto-dismiss after 3 seconds for demo
    setTimeout(() => {
      loadingToast.dismiss();
      this.notificationService.success('Processing complete!');
    }, 3000);
  }

  showCustomDuration() {
    this.notificationService.success('This toast will stay for 10 seconds!', {
      duration: 10000,
      action: '⏰ LONG'
    });
  }

  showPersistent() {
    this.notificationService.info('This toast stays until you dismiss it manually.', {
      persistent: true,
      action: '🔒 DISMISS'
    });
  }

  dismissAll() {
    this.notificationService.dismissAll();
  }
}
