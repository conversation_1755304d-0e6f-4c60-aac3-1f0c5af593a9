import mongoose, { Document, Schema } from 'mongoose';

export enum ACHIEVEMENT_TYPE {
  CHAMPIONSHIP_WINNER = "Championship Winner",
  FINALIST = "Finalist", 
  THIRD_PLACE = "Third Place",
  TOP_SCORER = "Top Scorer",
  TOP_ASSIST_PROVIDER = "Top Assist Provider",
  BEST_GOALKEEPER = "Best Goalkeeper",
  BEST_CENTER_BACK = "Best Center Back",
  BEST_DEFENSIVE_MIDFIELDER = "Best Defensive Midfielder",
  BEST_MIDFIELDER = "Best Midfielder",
  BEST_ATTACKING_MIDFIELDER = "Best Attacking Midfielder",
  BEST_WINGER = "Best Winger",
  BEST_STRIKER = "Best Striker",
  MOST_CLEAN_SHEETS = "Most Clean Sheets",
  PLAYER_OF_THE_SEASON = "Player of the Season",
  TEAM_OF_THE_SEASON = "Team of the Season"
}

export interface IPlayerAchievement {
  playerId: mongoose.Types.ObjectId;
  playerName: string;
  playerImgUrl?: string;
  teamId: mongoose.Types.ObjectId;
  teamName: string;
  teamImgUrl?: string;
  achievementType: ACHIEVEMENT_TYPE;
  rank?: number; // 1st, 2nd, 3rd for top scorers/assists
  seasonNumber?: number; // Season when achievement was earned
  stats: {
    goals?: number;
    assists?: number;
    cleanSheets?: number;
    avgRating?: number;
    games?: number;
    playerOfTheMatch?: number;
  };
  description?: string; // Additional context about the achievement
}

export interface ITeamAchievement {
  teamId: mongoose.Types.ObjectId;
  teamName: string;
  teamImgUrl?: string;
  achievementType: ACHIEVEMENT_TYPE;
  rank?: number; // Final position
  seasonNumber?: number; // Season when achievement was earned
  stats: {
    wins?: number;
    losses?: number;
    draws?: number;
    goalsScored?: number;
    goalsConceded?: number;
    points?: number;
    goalDifference?: number;
  };
  description?: string;
}

export interface ISeasonAchievements extends Document {
  id: string;
  seasonNumber: number;
  league: mongoose.Types.ObjectId;
  leagueName: string;
  startDate: Date;
  endDate: Date;
  playerAchievements: IPlayerAchievement[];
  teamAchievements: ITeamAchievement[];
  champion: {
    teamId: mongoose.Types.ObjectId;
    teamName: string;
    teamImgUrl?: string;
  };
  finalist?: {
    teamId: mongoose.Types.ObjectId;
    teamName: string;
    teamImgUrl?: string;
  };
  thirdPlace?: {
    teamId: mongoose.Types.ObjectId;
    teamName: string;
    teamImgUrl?: string;
  };
  createdAt: Date;
  createdBy: mongoose.Types.ObjectId; // Admin who ended the season
}

const playerAchievementSchema = new Schema<IPlayerAchievement>(
  {
    playerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Player', required: true },
    playerName: { type: String, required: true },
    playerImgUrl: { type: String, required: false },
    teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'Team', required: true },
    teamName: { type: String, required: true },
    teamImgUrl: { type: String, required: false },
    achievementType: { type: String, enum: Object.values(ACHIEVEMENT_TYPE), required: true },
    rank: { type: Number, required: false },
    seasonNumber: { type: Number, required: false },
    stats: {
      goals: { type: Number, required: false },
      assists: { type: Number, required: false },
      cleanSheets: { type: Number, required: false },
      avgRating: { type: Number, required: false },
      games: { type: Number, required: false },
      playerOfTheMatch: { type: Number, required: false },
    },
    description: { type: String, required: false },
  },
  { _id: false }
);

const teamAchievementSchema = new Schema<ITeamAchievement>(
  {
    teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'Team', required: true },
    teamName: { type: String, required: true },
    teamImgUrl: { type: String, required: false },
    achievementType: { type: String, enum: Object.values(ACHIEVEMENT_TYPE), required: true },
    rank: { type: Number, required: false },
    seasonNumber: { type: Number, required: false },
    stats: {
      wins: { type: Number, required: false },
      losses: { type: Number, required: false },
      draws: { type: Number, required: false },
      goalsScored: { type: Number, required: false },
      goalsConceded: { type: Number, required: false },
      points: { type: Number, required: false },
      goalDifference: { type: Number, required: false },
    },
    description: { type: String, required: false },
  },
  { _id: false }
);

const seasonAchievementsSchema = new Schema<ISeasonAchievements>(
  {
    seasonNumber: { type: Number, required: true },
    league: { type: mongoose.Schema.Types.ObjectId, ref: 'League', required: true },
    leagueName: { type: String, required: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    playerAchievements: [playerAchievementSchema],
    teamAchievements: [teamAchievementSchema],
    champion: {
      teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'Team', required: true },
      teamName: { type: String, required: true },
      teamImgUrl: { type: String, required: false },
    },
    finalist: {
      teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'Team', required: false },
      teamName: { type: String, required: false },
      teamImgUrl: { type: String, required: false },
    },
    thirdPlace: {
      teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'Team', required: false },
      teamName: { type: String, required: false },
      teamImgUrl: { type: String, required: false },
    },
    createdAt: { type: Date, default: Date.now },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  },
  {
    toJSON: { virtuals: true },
    id: true,
  }
);

// Create compound index for efficient queries
seasonAchievementsSchema.index({ league: 1, seasonNumber: 1 }, { unique: true });

const SeasonAchievements = mongoose.model<ISeasonAchievements>("SeasonAchievements", seasonAchievementsSchema);

export default SeasonAchievements;
