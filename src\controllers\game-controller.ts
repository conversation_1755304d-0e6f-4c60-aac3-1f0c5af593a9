import { NextFunction, Request, Response } from "express";
import { inject, injectable } from "tsyringe";
import { IGameController, IGameService } from "../interfaces/game";
import { UpdateGameResult } from "@pro-clubs-manager/shared-dtos";
import { CacheService } from "../interfaces/util-services/cache-service.interface";
import { log } from "console";

@injectable()
export default class GameController implements IGameController {
  private gameService: IGameService;
  private cacheService: CacheService;

  constructor(
    @inject("IGameService") gameService: IGameService,
    @inject("CacheService") cacheService: CacheService
  ) {
    this.gameService = gameService;
    this.cacheService = cacheService;
  }

  async getGameById(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id } = req.params;

    if (!id) {
      res.status(404).send({ message: "Game not found" });
      return;
    }

    try {
      const game = await this.gameService.getGameById(id);
      res.json(game);
    } catch (error: any) {
      next(error);
    }
  };

  async getAllGames(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const games = await this.gameService.getAllGames();
      res.json(games);
    } catch (error: any) {
      next(error);
    }
  }

  async getTopAvgRatingByPosition(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { position } = req.params;

    const minimumGames = req.query.minimumGames ? parseInt(req.query.minimumGames as string) : undefined;


    if (!position) {
      res.status(404).send({ message: "missing position data" });
      return;
    }

    try {
      const topAvgRating = await this.gameService.getTopAvgRatingByPosition(position, minimumGames);
      res.json(topAvgRating);
    } catch (error: any) {
      next(error);
    }
  }

  async getCurrentSeasonTeamGames(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { teamId } = req.params;

    const limit = req.query.limit ? parseInt(req.query.limit as string) : undefined;

    if (!teamId) {
      res.status(400).send({ message: "missing data" });
      return;
    }

    try {
      const games = await this.gameService.getCurrentSeasonTeamGames(teamId, limit);
      res.json(games);
    } catch (error: any) {
      next(error);
    }
  }

  async updateGameResult(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id } = req.params;
    const { homeTeamGoals, awayTeamGoals, date, isPlayoffGame, penalties} = req.body;

    if (homeTeamGoals === undefined || homeTeamGoals === undefined || !date) {
      res.status(400).send({ message: "Invalid result provided" });
      return;
    }

    try {
      await this.gameService.updateGameResult(id, homeTeamGoals, awayTeamGoals, date, isPlayoffGame, penalties);
      res.status(200).json({ success: true });
    } catch (error: any) {
      next(error);
    }
  }

  async updateGameDate(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id } = req.params;
    const { date } = req.body;

    if (!date) {
      res.status(400).send({ message: "Date is required" });
      return;
    }

    try {
      await this.gameService.updateGameDate(id, new Date(date));
      res.status(200).json({ success: true });
    } catch (error: any) {
      next(error);
    }
  }
  async setTechnicalResult(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: gameId } = req.params;
    const { losingTeamId, reason, date } = req.body;

    if (!losingTeamId) {
      res.status(400).send({ message: "no losing team id provided" });
      return;
    }

    try {
      await this.gameService.setTechnicalResult(gameId, losingTeamId, reason, new Date(date));
      res.status(200).json({ success: true });
    } catch (error: any) {
      next(error);
    }
  }

  async updateTeamPlayersPerformance(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: gameId } = req.params;
    const { playersPerformace, isHomeTeam } = req.body;

    if (!gameId || !playersPerformace) {
      res.status(400).send({ message: "missing data" });
      return;
    }

    try {
      await this.gameService.updateTeamPlayersPerformance(gameId, isHomeTeam, playersPerformace);
      res.status(200).json({ success: true });
    } catch (error: any) {
      next(error);
    }
  }

  async updateGameBroadcast(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: gameId } = req.params;
    const { streamUrl, broadcastingTeam } = req.body;

    if (!gameId) {
      res.status(400).send({ message: "missing game id" });
      return;
    }

    try {
      await this.gameService.updateGameBroadcast(gameId, streamUrl, broadcastingTeam);
      res.status(200).json({ success: true });
    } catch (error: any) {
      next(error);
    }
  }

  async deleteGame(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: gameId } = req.params;

    if (!gameId) {
      res.status(400).send({ message: "No gameId provided" });
      return;
    }
    try {
      await this.gameService.deleteGame(gameId);
      res.sendStatus(204);
    } catch (error: any) {
      next(error);
    }
  }

  async clearAllCaches(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { leagueId } = req.params;

      if (!leagueId) {
        res.status(400).json({ message: "League ID is required" });
        return;
      }

      // Clear all caches that are affected by game changes
      const cacheKeys = [
        `leagueTable:${leagueId}`,
        `topScorers:${leagueId}`,
        `topAssists:${leagueId}`,
        "dashboard:summary"
      ];

      await Promise.all(cacheKeys.map(key => this.cacheService.delete(key)));

      res.json({
        message: "All caches cleared successfully",
        clearedKeys: cacheKeys
      });
    } catch (error: any) {
      next(error);
    }
  }

  async getTeamVsTeamHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { team1Id, team2Id } = req.params;
    const { limit } = req.query;

    try {
      const games = await this.gameService.getTeamVsTeamHistory(
        team1Id,
        team2Id,
        limit ? parseInt(limit as string) : undefined
      );
      res.status(200).json({ success: true, data: games });
    } catch (error: any) {
      next(error);
    }
  }
}