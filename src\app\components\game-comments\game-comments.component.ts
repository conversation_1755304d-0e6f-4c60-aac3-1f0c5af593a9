import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { CommentService, CommentDTO, CommentsResponse } from '../../services/comment.service';
import { NotificationService } from '../../services/notification.service';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-game-comments',
  templateUrl: './game-comments.component.html',
  styleUrls: ['./game-comments.component.scss']
})
export class GameCommentsComponent implements OnInit, OnDestroy {
  @Input() gameId!: string;

  comments: CommentDTO[] = [];
  isLoading: boolean = false;
  isLoadingMore: boolean = false;
  isAuthenticated: boolean = false;
  currentUserId: string | null = null;
  hasMore: boolean = false;
  totalCount: number = 0;
  currentPage: number = 1;
  pageSize: number = 40;

  // Comment form
  newCommentContent: string = '';
  isSubmittingComment: boolean = false;

  // Reply form
  replyingToCommentId: string | null = null;
  replyContent: string = '';
  isSubmittingReply: boolean = false;

  // Edit form
  editingCommentId: string | null = null;
  editContent: string = '';
  isSubmittingEdit: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(
    private commentService: CommentService,
    private notificationService: NotificationService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.authService.isAuthenticated$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isAuth => {
        this.isAuthenticated = isAuth;
        if (isAuth) {
          const currentUser = this.authService.getCurrentUser();
          this.currentUserId = currentUser?.id || null;
        }
      });

    this.loadComments();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  async loadComments(page: number = 1): Promise<void> {
    if (!this.gameId) return;

    this.isLoading = page === 1;
    this.isLoadingMore = page > 1;

    try {
      const response: CommentsResponse = await this.commentService.getCommentsByGame(
        this.gameId, 
        page, 
        this.pageSize
      );

      if (page === 1) {
        this.comments = response.comments || [];
      } else {
        this.comments = [...(this.comments || []), ...(response.comments || [])];
      }



      this.hasMore = response.hasMore;
      this.totalCount = response.totalCount;
      this.currentPage = page;
    } catch (error) {
      console.error('Error loading comments:', error);
      this.notificationService.error('Failed to load comments');
      // Ensure comments array is always initialized
      if (page === 1 && !this.comments) {
        this.comments = [];
      }
    } finally {
      this.isLoading = false;
      this.isLoadingMore = false;
    }
  }

  async loadMoreComments(): Promise<void> {
    if (this.hasMore && !this.isLoadingMore) {
      await this.loadComments(this.currentPage + 1);
    }
  }

  async submitComment(): Promise<void> {
    if (!this.isAuthenticated) {
      this.notificationService.warning('Please log in to comment');
      return;
    }

    const validation = this.commentService.validateCommentContent(this.newCommentContent);
    if (!validation.isValid) {
      this.notificationService.error(validation.error!);
      return;
    }

    this.isSubmittingComment = true;
    try {
      const newComment = await this.commentService.createComment(
        this.gameId,
        this.newCommentContent
      );

      // Add new comment to the beginning of the list
      if (!this.comments) {
        this.comments = [];
      }
      this.comments.unshift(newComment);
      this.totalCount++;
      this.newCommentContent = '';

      this.notificationService.success('Comment posted successfully');
    } catch (error: any) {
      console.error('Error posting comment:', error);
      this.notificationService.error(error.message || 'Failed to post comment');
    } finally {
      this.isSubmittingComment = false;
    }
  }

  async submitReply(): Promise<void> {
    if (!this.isAuthenticated || !this.replyingToCommentId) return;

    const validation = this.commentService.validateCommentContent(this.replyContent);
    if (!validation.isValid) {
      this.notificationService.error(validation.error!);
      return;
    }

    this.isSubmittingReply = true;
    try {
      const newReply = await this.commentService.createComment(
        this.gameId,
        this.replyContent,
        this.replyingToCommentId
      );

      // Find the parent comment and add the reply
      const parentComment = (this.comments || []).find(c => c.id === this.replyingToCommentId);
      if (parentComment) {
        if (!parentComment.replies) {
          parentComment.replies = [];
        }
        parentComment.replies.push(newReply);
      }

      this.cancelReply();
      this.notificationService.success('Reply posted successfully');
    } catch (error: any) {
      console.error('Error posting reply:', error);
      this.notificationService.error(error.message || 'Failed to post reply');
    } finally {
      this.isSubmittingReply = false;
    }
  }

  async submitEdit(): Promise<void> {
    if (!this.editingCommentId) return;

    const validation = this.commentService.validateCommentContent(this.editContent);
    if (!validation.isValid) {
      this.notificationService.error(validation.error!);
      return;
    }

    this.isSubmittingEdit = true;
    try {
      const updatedComment = await this.commentService.updateComment(
        this.editingCommentId,
        this.editContent
      );

      // Update the comment in the list
      this.updateCommentInList(updatedComment);
      
      this.cancelEdit();
      this.notificationService.success('Comment updated successfully');
    } catch (error: any) {
      console.error('Error updating comment:', error);
      this.notificationService.error(error.message || 'Failed to update comment');
    } finally {
      this.isSubmittingEdit = false;
    }
  }

  async deleteComment(commentId: string): Promise<void> {
    if (!confirm('Are you sure you want to delete this comment?')) return;

    try {
      await this.commentService.deleteComment(commentId);
      
      // Remove comment from list
      if (this.comments) {
        this.comments = this.comments.filter(c => c.id !== commentId);

        // Also remove from replies
        this.comments.forEach(comment => {
          if (comment.replies) {
            comment.replies = comment.replies.filter(r => r.id !== commentId);
          }
        });
      }

      this.totalCount--;
      this.notificationService.success('Comment deleted successfully');
    } catch (error: any) {
      console.error('Error deleting comment:', error);
      this.notificationService.error(error.message || 'Failed to delete comment');
    }
  }

  async toggleLike(comment: CommentDTO): Promise<void> {
    if (!this.isAuthenticated) {
      this.notificationService.warning('Please log in to like comments');
      return;
    }

    try {
      let updatedComment: CommentDTO;
      
      if (comment.isLikedByUser) {
        updatedComment = await this.commentService.unlikeComment(comment.id);
      } else {
        updatedComment = await this.commentService.likeComment(comment.id);
      }

      this.updateCommentInList(updatedComment);
    } catch (error: any) {
      console.error('Error toggling like:', error);
      this.notificationService.error(error.message || 'Failed to update like');
    }
  }

  startReply(commentId: string): void {
    this.replyingToCommentId = commentId;
    this.replyContent = '';
    this.cancelEdit();
  }

  cancelReply(): void {
    this.replyingToCommentId = null;
    this.replyContent = '';
  }

  startEdit(comment: CommentDTO): void {
    this.editingCommentId = comment.id;
    this.editContent = comment.content;
    this.cancelReply();
  }

  cancelEdit(): void {
    this.editingCommentId = null;
    this.editContent = '';
  }

  private updateCommentInList(updatedComment: CommentDTO): void {
    if (!this.comments) {
      this.comments = [];
      return;
    }

    // Update in main comments
    const commentIndex = this.comments.findIndex(c => c.id === updatedComment.id);
    if (commentIndex !== -1) {
      this.comments[commentIndex] = updatedComment;
      return;
    }

    // Update in replies
    for (const comment of this.comments) {
      if (comment.replies) {
        const replyIndex = comment.replies.findIndex(r => r.id === updatedComment.id);
        if (replyIndex !== -1) {
          comment.replies[replyIndex] = updatedComment;
          return;
        }
      }
    }
  }

  canEditComment(comment: CommentDTO): boolean {
    return this.isAuthenticated && this.currentUserId === comment.userId;
  }

  canDeleteComment(comment: CommentDTO): boolean {
    return this.isAuthenticated && this.currentUserId === comment.userId;
  }

  formatTimeAgo(date: Date): string {
    return this.commentService.formatTimeAgo(date);
  }

  getProfileImage(comment: CommentDTO): string {
    return comment.user.profileImage || this.commentService.getDefaultProfileImage();
  }

  getRemainingCharacters(content: string): number {
    return 1000 - content.length;
  }
}
