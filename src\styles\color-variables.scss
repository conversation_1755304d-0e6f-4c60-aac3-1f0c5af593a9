:root {
    /* === MODERN COLOR PALETTE === */

    /* Primary Brand Colors - Modern Blue Gradient */
    --primary-25: #f8faff;
    --primary-50: #f0f4ff;
    --primary-100: #e0e7ff;
    --primary-200: #c7d2fe;
    --primary-300: #a5b4fc;
    --primary-400: #818cf8;
    --primary-500: #6366f1;
    --primary-600: #4f46e5;
    --primary-700: #4338ca;
    --primary-800: #3730a3;
    --primary-900: #312e81;

    /* Neutral Colors - Modern Gray Scale */
    --neutral-50: #f8fafc;
    --neutral-100: #f1f5f9;
    --neutral-200: #e2e8f0;
    --neutral-300: #cbd5e1;
    --neutral-400: #94a3b8;
    --neutral-500: #64748b;
    --neutral-600: #475569;
    --neutral-700: #334155;
    --neutral-800: #1e293b;
    --neutral-900: #0f172a;
    --neutral-950: #020617;

    /* Semantic Colors */
    --success-25: #f0fdf4;
    --success-50: #ecfdf5;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #10b981;
    --success-600: #059669;
    --success-700: #047857;
    --success-800: #065f46;
    --success-900: #064e3b;

    --warning-25: #fffcf5;
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;

    --error-50: #fef2f2;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;
    --error-rgb: 239, 68, 68; /* RGB values for error-500 */

    /* Danger colors (alias for error colors) */
    --danger-25: #fefafa;
    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-200: #fecaca;
    --danger-300: #fca5a5;
    --danger-400: #f87171;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;
    --danger-800: #991b1b;
    --danger-900: #7f1d1d;

    --info-25: #f8fafc;
    --info-50: #eff6ff;
    --info-100: #dbeafe;
    --info-200: #bfdbfe;
    --info-300: #93c5fd;
    --info-400: #60a5fa;
    --info-500: #3b82f6;
    --info-600: #2563eb;
    --info-700: #1d4ed8;
    --info-800: #1e40af;
    --info-900: #1e3a8a;

    /* === THEME VARIABLES === */

    /* Dark Theme (Default) */
    --bg-primary: #020617;
    --bg-secondary: #0f172a;
    --bg-tertiary: #1e293b;
    --bg-elevated: #1e293b;
    --bg-overlay: rgba(0, 0, 0, 0.8);

    --surface-primary: #0f172a;
    --surface-secondary: #1e293b;
    --surface-tertiary: #334155;
    --surface-quaternary: #475569;
    --surface-disabled: #64748b;
    --surface-hover: #1e293b;
    --surface-active: #334155;

    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e1;
    --text-inverse: #0f172a;
    --text-muted: #94a3b8;

    /* Legacy color aliases */
    --text-color: var(--text-primary);
    --primary-color: var(--primary);
    --background-color: var(--bg-primary);
    --background-secondary: var(--surface-secondary);
    --border-color: var(--border-primary);
    --success-color: var(--success-500);
    --warning-color: var(--warning-500);
    --danger-color: var(--danger-500);
    --error-color: var(--error-500);
    --info-color: var(--info-500);

    --border-primary: #334155;
    --border-secondary: #475569;
    --border-focus: var(--primary-400);
    --border-error: var(--error-400);

    /* Primary Brand Colors */
    --primary: var(--primary-500);
    --primary-hover: var(--primary-400);
    --primary-active: var(--primary-300);
    --primary-light: var(--primary-900);
    --accent-primary: #ffd700; /* Gold accent */
    --primary-rgb: 99, 102, 241; /* RGB values for primary-500 */

    --secondary: var(--neutral-400);
    --secondary-hover: var(--neutral-300);
    --secondary-active: var(--neutral-200);

    /* Legacy Support */
    --green: var(--success-500);
    --red: var(--error-500);
    --yellow: var(--warning-500);
    --danger: var(--danger-500);

    /* Component-specific variables */
    --card-bg: var(--surface-primary);
    --card-border: var(--border-primary);
    --card-shadow: var(--shadow-lg);
    --input-bg: var(--surface-primary);
    --input-border: var(--border-primary);
    --input-focus: var(--border-focus);
    --button-primary-bg: var(--primary);
    --button-primary-hover: var(--primary-hover);
    --button-secondary-bg: var(--surface-secondary);
    --button-secondary-hover: var(--surface-hover);

    /* CMS Dashboard specific variables */
    --card-background: var(--surface-primary);
    --table-header-background: var(--surface-secondary);
    --hover-background: var(--surface-hover);
    --input-background: var(--surface-primary);
    --accent-color: var(--primary);
    --accent-hover: var(--primary-hover);
    --success-color: var(--success-500);
    --success-hover: var(--success-600);
    --info-color: var(--primary-500);
    --info-hover: var(--primary-600);
    --warning-color: var(--warning-500);
    --warning-hover: var(--warning-600);
    --danger-color: var(--error-500);
    --danger-hover: var(--error-600);

    /* Theme Transition */
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* === LIGHT THEME (Explicit) === */
[data-theme="light"] {
    /* Beautiful light backgrounds with colorful gradients */
    --bg-primary: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    --bg-secondary: linear-gradient(135deg, #e0e7ff 0%, #f0f4ff 100%);
    --bg-tertiary: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
    --bg-elevated: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    --bg-overlay: rgba(15, 23, 42, 0.8);

    /* Enhanced surface colors with beautiful color variations */
    --surface-primary: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    --surface-secondary: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
    --surface-tertiary: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    --surface-quaternary: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    --surface-disabled: #e2e8f0;
    --surface-hover: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
    --surface-active: linear-gradient(135deg, #c7d2fe 0%, #a5b4fc 100%);

    --text-primary: #0f172a;
    --text-secondary: #334155;
    --text-tertiary: #475569;
    --text-inverse: #ffffff;
    --text-muted: #64748b;
    --text-disabled: #cbd5e1;

    /* Legacy color aliases */
    --text-color: var(--text-primary);
    --primary-color: var(--primary);
    --background-color: var(--bg-primary);
    --background-secondary: var(--surface-secondary);
    --border-color: var(--border-primary);
    --success-color: var(--success-500);
    --warning-color: var(--warning-500);
    --danger-color: var(--danger-500);
    --error-color: var(--error-500);
    --info-color: var(--info-500);

    --border-primary: #e1e7ec;
    --border-secondary: #d1d9e0;
    --border-focus: var(--primary-500);
    --border-error: var(--error-500);

    --primary: var(--primary-600);
    --primary-hover: var(--primary-700);
    --primary-active: var(--primary-800);
    --primary-light: var(--primary-100);
    --accent-primary: #ffd700; /* Gold accent */
    --primary-rgb: 79, 70, 229; /* RGB values for primary-600 */

    --secondary: var(--neutral-600);
    --secondary-hover: var(--neutral-700);
    --secondary-active: var(--neutral-800);

    /* Enhanced light theme shadows with subtle colors */
    --shadow-sm: 0 1px 3px 0 rgba(99, 102, 241, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.02);
    --shadow-md: 0 4px 6px -1px rgba(99, 102, 241, 0.12), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
    --shadow-lg: 0 10px 15px -3px rgba(99, 102, 241, 0.15), 0 4px 6px -2px rgba(0, 0, 0, 0.03);
    --shadow-xl: 0 20px 25px -5px rgba(99, 102, 241, 0.18), 0 10px 10px -5px rgba(0, 0, 0, 0.02);
    --shadow-2xl: 0 25px 50px -12px rgba(99, 102, 241, 0.25), 0 10px 20px -5px rgba(0, 0, 0, 0.04);
    --shadow-3xl: 0 35px 60px -12px rgba(99, 102, 241, 0.30), 0 15px 25px -5px rgba(0, 0, 0, 0.05);

    /* Enhanced component-specific variables for light theme */
    --card-bg: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    --card-border: var(--border-primary);
    --card-shadow: 0 10px 25px rgba(99, 102, 241, 0.08), 0 4px 10px rgba(0, 0, 0, 0.03);
    --input-bg: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    --input-border: var(--border-primary);
    --input-focus: var(--border-focus);
    --button-primary-bg: linear-gradient(135deg, var(--primary) 0%, var(--primary-700) 100%);
    --button-primary-hover: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 100%);
    --button-secondary-bg: var(--surface-secondary);
    --button-secondary-hover: var(--surface-hover);

    /* CMS Dashboard specific variables for light theme */
    --card-background: var(--card-bg);
    --table-header-background: var(--surface-secondary);
    --hover-background: var(--surface-hover);
    --input-background: var(--input-bg);
    --accent-color: var(--primary);
    --accent-hover: var(--primary-hover);
    --success-color: var(--success-600);
    --success-hover: var(--success-700);
    --info-color: var(--primary-600);
    --info-hover: var(--primary-700);
    --warning-color: var(--warning-600);
    --warning-hover: var(--warning-700);
    --danger-color: var(--error-600);
    --danger-hover: var(--error-700);

    /* Additional colorful surface variations for light theme */
    --surface-success: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    --surface-warning: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    --surface-error: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    --surface-info: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    --surface-accent: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%);

    /* Enhanced border colors with subtle tints */
    --border-success: #a7f3d0;
    --border-warning: #fde68a;
    --border-error: #fca5a5;
    --border-info: #bfdbfe;
    --border-accent: #e9d5ff;
}

/* === DARK THEME === */
[data-theme="dark"] {
    --bg-primary: #020617;
    --bg-secondary: #0f172a;
    --bg-tertiary: #1e293b;
    --bg-elevated: #1e293b;
    --bg-overlay: rgba(0, 0, 0, 0.8);

    --surface-primary: #0f172a;
    --surface-secondary: #1e293b;
    --surface-tertiary: #334155;
    --surface-quaternary: #475569;
    --surface-disabled: #64748b;
    --surface-hover: #1e293b;
    --surface-active: #334155;

    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e1;
    --text-inverse: #ffffff;
    --text-muted: #94a3b8;

    /* Legacy color aliases */
    --text-color: var(--text-primary);
    --primary-color: var(--primary);
    --background-color: var(--bg-primary);
    --background-secondary: var(--surface-secondary);
    --border-color: var(--border-primary);
    --success-color: var(--success-500);
    --warning-color: var(--warning-500);
    --danger-color: var(--danger-500);
    --error-color: var(--error-500);
    --info-color: var(--info-500);

    --border-primary: #334155;
    --border-secondary: #475569;
    --border-focus: var(--primary-400);
    --border-error: var(--error-400);

    --primary: var(--primary-500);
    --primary-hover: var(--primary-400);
    --primary-active: var(--primary-300);
    --primary-light: var(--primary-900);
    --accent-primary: #ffd700; /* Gold accent */
    --primary-rgb: 99, 102, 241; /* RGB values for primary-500 */

    --secondary: var(--neutral-400);
    --secondary-hover: var(--neutral-300);
    --secondary-active: var(--neutral-200);

    /* Component-specific variables */
    --card-bg: var(--surface-primary);
    --card-border: var(--border-primary);
    --card-shadow: var(--shadow-lg);
    --input-bg: var(--surface-primary);
    --input-border: var(--border-primary);
    --input-focus: var(--border-focus);
    --button-primary-bg: var(--primary);
    --button-primary-hover: var(--primary-hover);
    --button-secondary-bg: var(--surface-secondary);
    --button-secondary-hover: var(--surface-hover);

    /* CMS Dashboard specific variables for dark theme */
    --card-background: var(--surface-primary);
    --table-header-background: var(--surface-secondary);
    --hover-background: var(--surface-hover);
    --input-background: var(--surface-primary);
    --accent-color: var(--primary);
    --accent-hover: var(--primary-hover);
    --success-color: var(--success-500);
    --success-hover: var(--success-600);
    --info-color: var(--primary-500);
    --info-hover: var(--primary-600);
    --warning-color: var(--warning-500);
    --warning-hover: var(--warning-600);
    --danger-color: var(--error-500);
    --danger-hover: var(--error-600);
}

/* === SPACING SYSTEM === */
:root {
    --space-0: 0;
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */
    --space-24: 6rem;     /* 96px */

    /* Semantic Spacing */
    --spacing-xs: var(--space-1);
    --spacing-sm: var(--space-2);
    --spacing-md: var(--space-4);
    --spacing-lg: var(--space-6);
    --spacing-xl: var(--space-8);
    --spacing-2xl: var(--space-12);
    --spacing-3xl: var(--space-16);
}

/* === BORDER RADIUS SYSTEM === */
:root {
    --radius-none: 0;
    --radius-sm: 0.25rem;   /* 4px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
    --radius-2xl: 1.5rem;   /* 24px */
    --radius-3xl: 2rem;     /* 32px */
    --radius-full: 9999px;
}

/* === SHADOW SYSTEM === */
:root {
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-3xl: 0 35px 60px -12px rgba(0, 0, 0, 0.35);
}

[data-theme="dark"] {
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
    --shadow-3xl: 0 35px 60px -12px rgba(0, 0, 0, 0.8);
}
