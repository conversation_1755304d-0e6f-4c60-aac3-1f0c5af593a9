import winston from "winston";
import "winston-daily-rotate-file";
import { IUser } from "../models/user";

const transport = new winston.transports.DailyRotateFile({
  filename: "logs/%DATE%.log",
  datePattern: "YYYY-MM-DD",
  zippedArchive: true,
  maxSize: "20m",
  maxFiles: "14d",
});

// Conditionally set the logger level based on the NODE_ENV
const loggerLevel = process.env.NODE_ENV === "test" ? "error" : "info";

// Custom format for dd/MM hh:mm timestamp
const customTimestampFormat = winston.format.timestamp({
  format: () => {
    const now = new Date();
    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${day}/${month} ${hours}:${minutes}`;
  }
});

// Custom format for console output with user info
const consoleFormat = winston.format.combine(
  customTimestampFormat,
  winston.format.colorize(),
  winston.format.printf((info) => {
    const { timestamp, level, message, user, userId, userName } = info;
    let userInfo = '';
    if (user || userId || userName) {
      const typedUser = user as IUser;
      const displayName = userName || (typedUser?.firstName && typedUser?.lastName ? `${typedUser.firstName} ${typedUser.lastName}` : typedUser?.email || userId || 'Unknown User');
      userInfo = ` [User: ${displayName}]`;
    }
    return `${timestamp} ${level}:${userInfo} ${message}`;
  })
);

// Custom format for file output with user info
const fileFormat = winston.format.combine(
  customTimestampFormat,
  winston.format.printf((info) => {
    const { timestamp, level, message, user, userId, userName, ...meta } = info;
    let userInfo = '';
    if (user || userId || userName) {
      const typedUser = user as IUser;
      const displayName = userName || (typedUser?.firstName && typedUser?.lastName ? `${typedUser.firstName} ${typedUser.lastName}` : typedUser?.email || userId || 'Unknown User');
      userInfo = ` [User: ${displayName}]`;
    }
    const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
    return `${timestamp} [${level.toUpperCase()}]${userInfo} ${message}${metaStr}`;
  })
);

const logger = winston.createLogger({
  level: loggerLevel, // Use the determined level
  transports: [
    new winston.transports.DailyRotateFile({
      ...transport.options,
      format: fileFormat
    }),
    new winston.transports.Console({
      format: consoleFormat,
      level: loggerLevel, // Also apply the level to the console transport
    }),
  ],
});

export default logger;
