.squad-container {
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--success), var(--primary), var(--info));
        background-size: 200% 100%;
        animation: shimmer 4s ease-in-out infinite;
    }
}

.squad-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--surface-secondary);
    border-bottom: 1px solid var(--border-primary);

    .squad-title {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        i {
            color: var(--primary-500);
        }

        .player-count {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            font-weight: var(--font-weight-normal);
        }
    }
}

.loading-state,
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
    color: var(--text-primary);

    .loading-spinner,
    .empty-icon {
        font-size: var(--text-3xl);
        margin-bottom: var(--spacing-md);
        color: var(--primary);
    }

    h4 {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;
    }

    p {
        font-size: var(--text-sm);
        margin: 0;
        color: var(--text-primary);
    }
}

.players-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
    }
}

.player-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-secondary);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.6s ease;
    }

    &:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-400);

        &::before {
            left: 100%;
        }

        .player-actions {
            opacity: 1;
        }
    }

    .player-avatar {
        position: relative;
        flex-shrink: 0;

        .avatar-image {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-full);
            object-fit: cover;
            border: 2px solid var(--border-primary);
        }

        .position-badge {
            position: absolute;
            bottom: -4px;
            right: -4px;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-md);
            font-size: var(--text-xs);
            font-weight: var(--font-weight-bold);
            text-transform: uppercase;
            border: 2px solid var(--surface-secondary);

            &.position-gk {
                background: var(--warning-100);
                color: var(--warning-700);
            }

            &.position-def {
                background: var(--info-100);
                color: var(--info-700);
            }

            &.position-mid {
                background: var(--success-100);
                color: var(--success-700);
            }

            &.position-att {
                background: var(--danger-100);
                color: var(--danger-700);
            }

            &.position-default {
                background: var(--surface-tertiary);
                color: var(--text-secondary);
            }
        }
    }

    .player-info {
        flex: 1;
        min-width: 0;

        .player-name {
            font-size: var(--text-base);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-sm) 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .player-stats {
            display: flex;
            gap: var(--spacing-md);
            flex-wrap: wrap;

            .stat-item {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
                font-size: var(--text-xs);
                color: var(--text-secondary);

                i {
                    color: var(--primary-400);
                }

                &.rating {
                    i {
                        color: var(--warning-500);
                    }
                }
            }
        }
    }

    .player-actions {
        opacity: 0;
        transition: opacity 0.2s ease;

        .remove-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: var(--danger-100);
            color: var(--danger-600);
            border: none;
            border-radius: var(--radius-full);
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background: var(--danger-200);
                transform: scale(1.1);
            }
        }
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md);
        gap: var(--spacing-sm);

        .player-avatar .avatar-image {
            width: 40px;
            height: 40px;
        }

        .player-info {
            .player-name {
                font-size: var(--text-sm);
            }

            .player-stats {
                gap: var(--spacing-sm);
            }
        }

        .player-actions {
            opacity: 1;
        }
    }
}
