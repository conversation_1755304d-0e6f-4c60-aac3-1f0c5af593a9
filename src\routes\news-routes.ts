import { Router } from "express";
import { container } from "../config/container.config";
import NewsController from "../controllers/news-controller";
import { authenticateToken } from "../middlewares/auth-middleware";

const router = Router();
const newsController = container.resolve(NewsController);

router.get("/", (req, res, next) => newsController.getAllNews(req, res, next));
router.post("/addNews", (req, res, next) => newsController.addNews(req, res, next));
router.put("/:id", authenticateToken, (req, res, next) => newsController.updateNews(req, res, next));
router.delete("/:id", (req, res, next) => newsController.deleteNews(req, res, next));

// Like/Unlike routes (require authentication)
router.post("/:id/like", authenticateToken, (req, res, next) => newsController.likeNews(req, res, next));
router.delete("/:id/like", authenticateToken, (req, res, next) => newsController.unlikeNews(req, res, next));

export default router;