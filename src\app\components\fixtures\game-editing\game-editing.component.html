<!-- Games List -->
<div class="games-list">
    <app-fixture-card
        *ngFor="let game of games; trackBy: trackByGameId; let i = index"
        [style.animation-delay]="(i * 0.1) + 's'"
        [game]="game"
        [canEdit$]="canEditGame(game.id)"
        [isCurrentlyEditing]="isCurrentlyEditing(game.id)"
        [isCurrentlyEditingTime]="isCurrentlyEditingTime(game.id)"
        [editingGameDate]="editingGameDate"
        [editingGameTime]="editingGameTime"
        [homeTeamGoals]="isCurrentlyEditing(game.id) ? homeTeamGoals : (game.result?.homeTeamGoals || 0)"
        [awayTeamGoals]="isCurrentlyEditing(game.id) ? awayTeamGoals : (game.result?.awayTeamGoals || 0)"
        [isAdmin]="isAdmin"
        [gameUpdateState]="getGameUpdateState(game.id)"
        (editClick)="onEditGameResultClick($event)"
        (saveClick)="onSaveClick($event)"
        (cancelClick)="onCancelClick()"
        (gameDetailsClick)="onGameClick($event)"
        (editTimeClick)="onEditTimeClick($event)"
        (saveTimeClick)="onSaveTimeClick($event)"
        (cancelTimeEdit)="onCancelTimeEdit()"
        (editingGameDateChange)="onEditingGameDateChange($event)"
        (editingGameTimeChange)="onEditingGameTimeChange($event)"
        (homeGoalsChange)="onHomeGoalsChange($event)"
        (awayGoalsChange)="onAwayGoalsChange($event)">
    </app-fixture-card>
</div>

<!-- Empty State -->
<div class="empty-state" *ngIf="!games || games.length === 0">
    <i class="fas fa-calendar-times"></i>
    <h3>No Matches Available</h3>
    <p>There are no matches to display at the moment.</p>
</div>
