import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { LeagueService } from '../../services/league.service';
import { NotificationService } from '../../services/notification.service';
import { MostWinningPercentageTeam } from '../../shared/models/all-time-statistics.model';

@Component({
  selector: 'app-most-winning-percentage-teams',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './most-winning-percentage-teams.component.html',
  styleUrls: ['./most-winning-percentage-teams.component.scss']
})
export class MostWinningPercentageTeamsComponent implements OnInit {
  mostWinningPercentageTeamsData: MostWinningPercentageTeam[] = [];
  isLoading: boolean = false;
  selectedLeagueId: string = '';
  leagues: any[] = [];
  minimumGames: number = 10;

  @Input() hideTitle: boolean = false;
  @Input() leagueId?: string;

  constructor(
    private router: Router,
    private leagueService: LeagueService,
    private notificationService: NotificationService
  ) { }

  async ngOnInit(): Promise<void> {
    await this.loadLeagues();
    if (this.leagueId) {
      this.selectedLeagueId = this.leagueId;
      await this.loadData();
    }
  }

  private async loadLeagues(): Promise<void> {
    try {
      this.leagues = await this.leagueService.getAllLeagues();
      if (this.leagues.length > 0 && !this.selectedLeagueId) {
        this.selectedLeagueId = this.leagues[0].id;
        await this.loadData();
      }
    } catch (error) {
      console.error('Error loading leagues:', error);
      this.notificationService.error('Failed to load leagues');
    }
  }

  async onLeagueChange(): Promise<void> {
    if (this.selectedLeagueId) {
      await this.loadData();
    }
  }

  async onMinimumGamesChange(): Promise<void> {
    await this.loadData();
  }

  private async loadData(): Promise<void> {
    if (!this.selectedLeagueId) return;

    this.isLoading = true;
    try {
      const response = await this.leagueService.getMostWinningPercentageTeams(
        this.selectedLeagueId, 
        this.minimumGames
      );

      this.mostWinningPercentageTeamsData = response.map(team => ({
        ...team,
        tableIcon: { 
          name: team.teamName, 
          imgUrl: team.teamImgUrl || '', 
          isTeam: true 
        },
        winningPercentage: parseFloat(team.winningPercentage.toFixed(1))
      }));
    } catch (error) {
      console.error('Error loading most winning percentage teams data:', error);
      this.notificationService.error('Failed to load team winning percentage statistics');
      this.mostWinningPercentageTeamsData = [];
    } finally {
      this.isLoading = false;
    }
  }

  onTeamClick(team: MostWinningPercentageTeam): void {
    this.router.navigate(['/team-details', team.teamId]);
  }

  getTeamRank(index: number): string {
    return `#${index + 1}`;
  }

  getWinPercentageDisplay(team: MostWinningPercentageTeam): string {
    return `${team.winningPercentage}%`;
  }

  getWinLossRecord(team: MostWinningPercentageTeam): string {
    return `${team.wins}-${team.draws}-${team.losses}`;
  }

  getTrophyIconsArray(wins: number): number[] {
    return Array(Math.min(wins, 5)).fill(0);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/Team.jpg';
    }
  }
}
