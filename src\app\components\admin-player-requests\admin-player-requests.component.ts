import { Component, OnInit } from '@angular/core';
import { AdminService, PlayerAssociationRequest } from '../../services/admin.service';
import { NotificationService } from '../../services/notification.service';
import { AuthService } from '../../services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-admin-player-requests',
  templateUrl: './admin-player-requests.component.html',
  styleUrls: ['./admin-player-requests.component.scss']
})
export class AdminPlayerRequestsComponent implements OnInit {
  pendingRequests: PlayerAssociationRequest[] = [];
  isLoading = false;
  processingRequestId: string | null = null;

  constructor(
    private adminService: AdminService,
    private notificationService: NotificationService,
    private authService: AuthService,
    private router: Router
  ) {}

  async ngOnInit(): Promise<void> {
    // Check if user is admin
    if (!this.authService.isAdmin()) {
      this.router.navigate(['/']);
      return;
    }

    await this.loadPendingRequests();
  }

  async loadPendingRequests(): Promise<void> {
    try {
      this.isLoading = true;
      this.pendingRequests = await this.adminService.getPendingAssociationRequests();
    } catch (error: any) {
      this.notificationService.error(error.message || 'Failed to load pending requests');
    } finally {
      this.isLoading = false;
    }
  }

  async approveRequest(request: PlayerAssociationRequest): Promise<void> {
    if (!confirm(`Are you sure you want to approve the association of ${request.playerId.name} with ${request.userId.firstName} ${request.userId.lastName}?`)) {
      return;
    }

    try {
      this.processingRequestId = request.id;
      await this.adminService.approveAssociationRequest(request.id);
      this.notificationService.success(`Request approved successfully! ${request.playerId.name} is now associated with ${request.userId.firstName} ${request.userId.lastName}.`);
      await this.loadPendingRequests();
    } catch (error: any) {
      this.notificationService.error(error.message || 'Failed to approve request');
    } finally {
      this.processingRequestId = null;
    }
  }

  async rejectRequest(request: PlayerAssociationRequest): Promise<void> {
    const reason = prompt(`Please provide a reason for rejecting the association of ${request.playerId.name} with ${request.userId.firstName} ${request.userId.lastName}:`);
    
    if (reason === null) {
      return; // User cancelled
    }

    try {
      this.processingRequestId = request.id;
      await this.adminService.rejectAssociationRequest(request.id, reason);
      this.notificationService.success(`Request rejected successfully.`);
      await this.loadPendingRequests();
    } catch (error: any) {
      this.notificationService.error(error.message || 'Failed to reject request');
    } finally {
      this.processingRequestId = null;
    }
  }

  isProcessing(requestId: string): boolean {
    return this.processingRequestId === requestId;
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  onImageError(event: Event): void {
    const target = event.target as HTMLImageElement;
    target.src = 'assets/default-player.png';
  }
}
