import { Injectable } from '@angular/core';
import { News } from '../components/news/news.model';

export interface ShareImageOptions {
  width?: number;
  height?: number;
  backgroundColor?: string;
  textColor?: string;
}

@Injectable({
  providedIn: 'root'
})
export class NewsImageGeneratorService {

  constructor() { }

  /**
   * Generate a share image for news
   */
  async generateShareImage(news: News, options: ShareImageOptions = {}): Promise<string> {
    // Create a canvas element for generating the image
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('Failed to create canvas context');
    }

    // Set canvas dimensions for better social media sharing
    canvas.width = options.width || 1200;
    canvas.height = options.height || 630; // Optimal for social media

    // Create dark theme gradient background
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, '#020617'); // --bg-primary
    gradient.addColorStop(0.5, '#0f172a'); // --bg-secondary
    gradient.addColorStop(1, '#1e293b'); // --bg-tertiary

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add subtle pattern overlay
    ctx.fillStyle = 'rgba(255, 255, 255, 0.02)';
    for (let i = 0; i < canvas.width; i += 60) {
      for (let j = 0; j < canvas.height; j += 60) {
        ctx.fillRect(i, j, 30, 30);
      }
    }

    // Add main content area with dark theme
    const contentX = 60;
    const contentY = 60;
    const contentWidth = canvas.width - 120;
    const contentHeight = canvas.height - 120;

    ctx.fillStyle = 'rgba(15, 23, 42, 0.95)'; // --surface-primary with transparency
    this.drawRoundedRect(ctx, contentX, contentY, contentWidth, contentHeight, 24);
    ctx.fill();

    // Add border with primary color
    ctx.strokeStyle = 'rgba(99, 102, 241, 0.3)'; // --primary with transparency
    ctx.lineWidth = 2;
    this.drawRoundedRect(ctx, contentX, contentY, contentWidth, contentHeight, 24);
    ctx.stroke();

    // Check news type to render differently
    if (news.type === 'Transfer' && news.transferData) {
      await this.renderTransferShareImage(ctx, canvas, contentX, contentY, contentWidth, contentHeight, news);
    } else if (news.type === 'FreeAgent' && news.freeAgentData) {
      await this.renderFreeAgentShareImage(ctx, canvas, contentX, contentY, contentWidth, contentHeight, news);
    } else {
      this.renderGeneralShareImage(ctx, canvas, contentX, contentY, contentWidth, contentHeight, news);
    }

    // Convert canvas to image URL
    return canvas.toDataURL('image/png');
  }

  private async renderTransferShareImage(
    ctx: CanvasRenderingContext2D, 
    canvas: HTMLCanvasElement,
    contentX: number, 
    contentY: number, 
    contentWidth: number, 
    contentHeight: number,
    news: News
  ): Promise<void> {
    if (!news.transferData) return;

    const transferData = news.transferData;

    // Add title
    ctx.fillStyle = '#f8fafc'; // --text-primary
    ctx.font = 'bold 36px Inter, Arial, sans-serif';
    ctx.textAlign = 'center';

    let currentY = contentY + 60;
    ctx.fillText('TRANSFER NEWS', canvas.width / 2, currentY);

    // Add player name
    currentY += 60;
    ctx.font = 'bold 28px Inter, Arial, sans-serif';
    ctx.fillStyle = '#ffd700'; // Gold accent
    ctx.fillText(transferData.playerDetails.name, canvas.width / 2, currentY);

    // Create transfer visual: FromTeam -> Player -> ToTeam
    currentY += 80;
    const sectionWidth = contentWidth / 3;
    const sectionCenterY = currentY + 100;

    // Load images and draw sections
    try {
      // From Team Section
      const fromTeamX = contentX + sectionWidth / 2;
      await this.drawTeamSectionWithImage(ctx, fromTeamX, sectionCenterY, transferData.fromTeam.name, 'FROM', transferData.fromTeam.imgUrl);

      // Arrow
      ctx.strokeStyle = '#ffd700';
      ctx.lineWidth = 4;
      ctx.beginPath();
      ctx.moveTo(fromTeamX + 80, sectionCenterY);
      ctx.lineTo(fromTeamX + 140, sectionCenterY);
      // Arrow head
      ctx.moveTo(fromTeamX + 130, sectionCenterY - 10);
      ctx.lineTo(fromTeamX + 140, sectionCenterY);
      ctx.lineTo(fromTeamX + 130, sectionCenterY + 10);
      ctx.stroke();

      // To Team Section
      const toTeamX = contentX + (sectionWidth * 2.5);
      await this.drawTeamSectionWithImage(ctx, toTeamX, sectionCenterY, transferData.toTeam.name, 'TO', transferData.toTeam.imgUrl);

    } catch (error) {
      console.error('Error loading team images:', error);
      // Fallback to text-only sections
      const fromTeamX = contentX + sectionWidth / 2;
      this.drawTeamSection(ctx, fromTeamX, sectionCenterY, transferData.fromTeam.name, 'FROM');

      const toTeamX = contentX + (sectionWidth * 2.5);
      this.drawTeamSection(ctx, toTeamX, sectionCenterY, transferData.toTeam.name, 'TO');
    }

    // Add footer
    currentY = contentY + contentHeight - 60;
    ctx.fillStyle = '#cbd5e1'; // --text-secondary
    ctx.font = '16px Inter, Arial, sans-serif';
    ctx.textAlign = 'center';
    const date = new Date(news.createdAt).toLocaleDateString();
    ctx.fillText(`IPL SEASON 6 • ${date}`, canvas.width / 2, currentY);
  }

  private async renderFreeAgentShareImage(
    ctx: CanvasRenderingContext2D, 
    canvas: HTMLCanvasElement,
    contentX: number, 
    contentY: number, 
    contentWidth: number, 
    contentHeight: number,
    news: News
  ): Promise<void> {
    if (!news.freeAgentData) return;

    const freeAgentData = news.freeAgentData;

    // Add title
    ctx.fillStyle = '#f8fafc'; // --text-primary
    ctx.font = 'bold 36px Inter, Arial, sans-serif';
    ctx.textAlign = 'center';

    let currentY = contentY + 60;
    ctx.fillText('FREE AGENT AVAILABLE', canvas.width / 2, currentY);

    // Add player name
    currentY += 60;
    ctx.font = 'bold 32px Inter, Arial, sans-serif';
    ctx.fillStyle = '#10b981'; // Green accent for free agent
    ctx.fillText(freeAgentData.playerDetails.name, canvas.width / 2, currentY);

    // Add position and age
    currentY += 50;
    ctx.font = 'bold 20px Inter, Arial, sans-serif';
    ctx.fillStyle = '#f8fafc'; // --text-primary
    ctx.fillText(`${freeAgentData.playerDetails.position} • Age ${freeAgentData.playerDetails.age}`, canvas.width / 2, currentY);

    // Player image section
    currentY += 80;
    const playerCenterX = canvas.width / 2;
    const playerCenterY = currentY + 80;

    try {
      if (freeAgentData.playerDetails.imgUrl) {
        const img = await this.loadImage(freeAgentData.playerDetails.imgUrl);

        // Save context
        ctx.save();

        // Create circular clipping path for player
        ctx.beginPath();
        ctx.arc(playerCenterX, playerCenterY, 70, 0, 2 * Math.PI);
        ctx.clip();

        // Draw player image
        const imgSize = 140;
        ctx.drawImage(img, playerCenterX - imgSize/2, playerCenterY - imgSize/2, imgSize, imgSize);

        // Restore context
        ctx.restore();
      }

      // Draw player circle border
      ctx.strokeStyle = '#10b981'; // Green border
      ctx.lineWidth = 4;
      ctx.beginPath();
      ctx.arc(playerCenterX, playerCenterY, 70, 0, 2 * Math.PI);
      ctx.stroke();

    } catch (error) {
      console.error('Failed to load player image:', error);
      // Fallback: draw circle with initials
      ctx.fillStyle = 'rgba(16, 185, 129, 0.2)';
      ctx.beginPath();
      ctx.arc(playerCenterX, playerCenterY, 70, 0, 2 * Math.PI);
      ctx.fill();

      ctx.strokeStyle = '#10b981';
      ctx.lineWidth = 4;
      ctx.stroke();

      // Add player initials
      ctx.fillStyle = '#f8fafc';
      ctx.font = 'bold 32px Inter, Arial, sans-serif';
      ctx.textAlign = 'center';
      const initials = freeAgentData.playerDetails.name.split(' ').map(n => n[0]).join('');
      ctx.fillText(initials, playerCenterX, playerCenterY + 10);
    }

    // Previous teams section
    if (freeAgentData.previousTeams.length > 0) {
      currentY = playerCenterY + 120;
      ctx.fillStyle = '#cbd5e1'; // --text-secondary
      ctx.font = '18px Inter, Arial, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('Previous Teams:', canvas.width / 2, currentY);

      currentY += 30;
      const teamSpacing = Math.min(150, contentWidth / Math.max(freeAgentData.previousTeams.length, 1));
      const startX = canvas.width / 2 - (freeAgentData.previousTeams.length - 1) * teamSpacing / 2;

      for (let i = 0; i < Math.min(freeAgentData.previousTeams.length, 4); i++) {
        const team = freeAgentData.previousTeams[i];
        const teamX = startX + i * teamSpacing;

        // Draw small team circle
        ctx.fillStyle = 'rgba(99, 102, 241, 0.2)';
        ctx.beginPath();
        ctx.arc(teamX, currentY, 25, 0, 2 * Math.PI);
        ctx.fill();

        ctx.strokeStyle = '#6366f1';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Add team name below
        ctx.fillStyle = '#94a3b8';
        ctx.font = '12px Inter, Arial, sans-serif';
        ctx.textAlign = 'center';
        const teamName = team.name.length > 8 ? team.name.substring(0, 8) + '...' : team.name;
        ctx.fillText(teamName, teamX, currentY + 45);

        // Add season
        ctx.fillText(`S${team.seasonNumber}`, teamX, currentY + 60);
      }
    }

    // Add footer
    currentY = contentY + contentHeight - 60;
    ctx.fillStyle = '#cbd5e1'; // --text-secondary
    ctx.font = '16px Inter, Arial, sans-serif';
    ctx.textAlign = 'center';
    const date = new Date(news.createdAt).toLocaleDateString();
    ctx.fillText(`IPL SEASON 6 • ${date}`, canvas.width / 2, currentY);
  }

  private renderGeneralShareImage(
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement,
    contentX: number,
    contentY: number,
    contentWidth: number,
    contentHeight: number,
    news: News
  ): void {
    // Add title
    ctx.fillStyle = '#f8fafc'; // --text-primary
    ctx.font = 'bold 32px Inter, Arial, sans-serif';
    ctx.textAlign = 'center';

    const title = news.title;
    const maxTitleWidth = contentWidth - 40;
    const titleLines = this.wrapText(ctx, title, maxTitleWidth);

    let currentY = contentY + 80;
    titleLines.forEach(line => {
      ctx.fillText(line, canvas.width / 2, currentY);
      currentY += 40;
    });

    // Add news type badge
    currentY += 20;
    const badgeText = this.getNewsTypeLabel(news.type);
    ctx.font = 'bold 16px Inter, Arial, sans-serif';
    ctx.fillStyle = this.getNewsTypeBadgeColor(news.type);

    const badgeWidth = ctx.measureText(badgeText).width + 20;
    const badgeX = (canvas.width - badgeWidth) / 2;

    this.drawRoundedRect(ctx, badgeX, currentY, badgeWidth, 30, 15);
    ctx.fill();

    ctx.fillStyle = '#f8fafc'; // --text-primary
    ctx.textAlign = 'center';
    ctx.fillText(badgeText, canvas.width / 2, currentY + 20);

    // Add content preview
    currentY += 60;
    ctx.fillStyle = '#cbd5e1'; // --text-secondary
    ctx.font = '18px Inter, Arial, sans-serif';

    const content = news.content;
    const contentPreview = content.length > 200 ? content.substring(0, 200) + '...' : content;
    const contentLines = this.wrapText(ctx, contentPreview, contentWidth - 40);

    contentLines.slice(0, 6).forEach(line => { // Limit to 6 lines
      ctx.fillText(line, canvas.width / 2, currentY);
      currentY += 25;
    });

    // Add footer
    currentY = contentY + contentHeight - 40;
    ctx.fillStyle = '#94a3b8'; // --text-tertiary
    ctx.font = '14px Inter, Arial, sans-serif';
    ctx.fillText('IPL SEASON 6 • League News', canvas.width / 2, currentY);

    currentY += 25;
    const date = new Date(news.createdAt).toLocaleDateString();
    ctx.fillText(date, canvas.width / 2, currentY);
  }

  private async drawTeamSectionWithImage(
    ctx: CanvasRenderingContext2D,
    centerX: number,
    centerY: number,
    teamName: string,
    label: string,
    imageUrl?: string
  ): Promise<void> {
    // Draw team circle background
    ctx.fillStyle = 'rgba(99, 102, 241, 0.2)'; // --primary with transparency
    ctx.beginPath();
    ctx.arc(centerX, centerY, 60, 0, 2 * Math.PI);
    ctx.fill();

    // Draw team circle border
    ctx.strokeStyle = '#6366f1'; // --primary
    ctx.lineWidth = 3;
    ctx.stroke();

    // Try to load and draw team image
    if (imageUrl) {
      try {
        const img = await this.loadImage(imageUrl);

        // Save context
        ctx.save();

        // Create circular clipping path
        ctx.beginPath();
        ctx.arc(centerX, centerY, 55, 0, 2 * Math.PI);
        ctx.clip();

        // Draw image
        const imgSize = 110;
        ctx.drawImage(img, centerX - imgSize/2, centerY - imgSize/2, imgSize, imgSize);

        // Restore context
        ctx.restore();

        // Redraw border over image
        ctx.strokeStyle = '#6366f1'; // --primary
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(centerX, centerY, 60, 0, 2 * Math.PI);
        ctx.stroke();

      } catch (error) {
        console.error('Failed to load team image:', error);
        // Fallback to text
        this.drawTeamNameInCircle(ctx, centerX, centerY, teamName);
      }
    } else {
      // No image URL, draw text
      this.drawTeamNameInCircle(ctx, centerX, centerY, teamName);
    }

    // Add label below circle
    ctx.fillStyle = '#94a3b8'; // --text-tertiary
    ctx.font = '12px Inter, Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(label, centerX, centerY + 85);

    // Add team name below label
    ctx.fillStyle = '#f8fafc'; // --text-primary
    ctx.font = 'bold 14px Inter, Arial, sans-serif';
    const maxWidth = 120;
    const teamNameLines = this.wrapText(ctx, teamName, maxWidth);
    let textY = centerY + 100;

    teamNameLines.forEach(line => {
      ctx.fillText(line, centerX, textY);
      textY += 16;
    });
  }

  private drawTeamNameInCircle(ctx: CanvasRenderingContext2D, centerX: number, centerY: number, teamName: string): void {
    // Add team name in circle
    ctx.fillStyle = '#f8fafc'; // --text-primary
    ctx.font = 'bold 14px Inter, Arial, sans-serif';
    ctx.textAlign = 'center';

    // Wrap team name if too long
    const maxWidth = 100;
    const teamNameLines = this.wrapText(ctx, teamName, maxWidth);
    let textY = centerY - (teamNameLines.length * 8);

    teamNameLines.forEach(line => {
      ctx.fillText(line, centerX, textY);
      textY += 16;
    });
  }

  private loadImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous'; // Handle CORS
      img.onload = () => resolve(img);
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      img.src = url;
    });
  }

  private drawTeamSection(ctx: CanvasRenderingContext2D, centerX: number, centerY: number, teamName: string, label: string): void {
    // Draw team circle background
    ctx.fillStyle = 'rgba(99, 102, 241, 0.2)'; // --primary with transparency
    ctx.beginPath();
    ctx.arc(centerX, centerY, 60, 0, 2 * Math.PI);
    ctx.fill();

    // Draw team circle border
    ctx.strokeStyle = '#6366f1'; // --primary
    ctx.lineWidth = 3;
    ctx.stroke();

    // Add team name
    this.drawTeamNameInCircle(ctx, centerX, centerY, teamName);

    // Add label
    ctx.fillStyle = '#94a3b8'; // --text-tertiary
    ctx.font = '12px Inter, Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(label, centerX, centerY + 85);
  }

  private wrapText(ctx: CanvasRenderingContext2D, text: string, maxWidth: number): string[] {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = words[0];

    for (let i = 1; i < words.length; i++) {
      const word = words[i];
      const width = ctx.measureText(currentLine + ' ' + word).width;
      if (width < maxWidth) {
        currentLine += ' ' + word;
      } else {
        lines.push(currentLine);
        currentLine = word;
      }
    }
    lines.push(currentLine);
    return lines;
  }

  private getNewsTypeLabel(type: string): string {
    return type === 'FreeAgent' ? 'Free Agent' : type;
  }

  private getNewsTypeBadgeColor(type: string): string {
    switch (type) {
      case 'General':
        return '#3b82f6'; // Blue
      case 'Transfer':
        return '#10b981'; // Green
      case 'FreeAgent':
        return '#f59e0b'; // Orange
      default:
        return '#6b7280'; // Gray
    }
  }

  private drawRoundedRect(ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, radius: number): void {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  }
}
