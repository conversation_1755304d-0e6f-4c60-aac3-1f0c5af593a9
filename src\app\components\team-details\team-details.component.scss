/* === TEAM DETAILS COMPONENT === */

.team-details-container {
    padding: var(--spacing-xl);
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md);
    }
}

/* === LIGHT THEME SUPPORT === */
:host-context(.light-theme) {
    .team-tabs .modern-tabs ::ng-deep .mat-mdc-tab-group .mat-mdc-tab-header {
        .mat-mdc-tab-header-pagination {
            background: var(--surface-primary) !important;
            border-color: var(--border-secondary) !important;

            &:hover {
                background: var(--surface-secondary) !important;
                border-color: var(--primary-400) !important;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            }

            &:active {
                background: var(--primary-100) !important;
            }

            &.mat-mdc-tab-header-pagination-disabled {
                background: var(--surface-tertiary) !important;
                opacity: 0.4 !important;

                &:hover {
                    background: var(--surface-tertiary) !important;
                    border-color: var(--border-secondary) !important;
                    box-shadow: none !important;
                }
            }
        }

        .mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron {
            border-color: var(--text-secondary) !important;
        }

        .mat-mdc-tab-header-pagination:hover:not(.mat-mdc-tab-header-pagination-disabled) .mat-mdc-tab-header-pagination-chevron {
            border-color: var(--primary-600) !important;
        }
    }
}

/* === MOBILE TAB IMPROVEMENTS === */
.team-tabs {
    @media (max-width: 768px) {
        .modern-tabs ::ng-deep {
            .mat-mdc-tab-group {
                .mat-mdc-tab-header {
                    .mat-mdc-tab-header-pagination {
                        width: 36px !important;
                        min-width: 36px !important;
                        height: 36px !important;
                        border-radius: 50% !important;
                        background: var(--surface-primary) !important;
                        border: 1px solid var(--border-primary) !important;
                        box-shadow: var(--shadow-sm) !important;
                        transition: all 0.3s ease !important;

                        &:hover:not(.mat-mdc-tab-header-pagination-disabled) {
                            background: var(--primary-100) !important;
                            border-color: var(--primary-300) !important;
                            transform: scale(1.05) !important;
                            box-shadow: var(--shadow-md) !important;
                        }

                        &.mat-mdc-tab-header-pagination-disabled {
                            opacity: 0.3 !important;
                            background: var(--surface-tertiary) !important;
                        }

                        .mat-mdc-tab-header-pagination-chevron {
                            border-width: 2px !important;
                            width: 8px !important;
                            height: 8px !important;
                            border-color: var(--text-secondary) !important;
                        }

                        &:hover:not(.mat-mdc-tab-header-pagination-disabled) .mat-mdc-tab-header-pagination-chevron {
                            border-color: var(--primary-600) !important;
                        }
                    }

                    .mat-mdc-tab-labels {
                        .mat-mdc-tab {
                            min-width: 80px !important;
                            padding: 0 12px !important;

                            .mdc-tab__content {
                                .mdc-tab__text-label {
                                    font-size: var(--text-sm) !important;
                                    font-weight: var(--font-weight-medium) !important;
                                }

                                .mat-mdc-tab-label-content {
                                    display: flex !important;
                                    align-items: center !important;
                                    gap: 4px !important;

                                    i {
                                        font-size: var(--text-xs) !important;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 480px) {
        .modern-tabs ::ng-deep {
            .mat-mdc-tab-group {
                .mat-mdc-tab-header {
                    .mat-mdc-tab-header-pagination {
                        width: 32px !important;
                        min-width: 32px !important;
                        height: 32px !important;
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        visibility: visible !important;

                        .mat-mdc-tab-header-pagination-chevron {
                            width: 6px !important;
                            height: 6px !important;
                            display: block !important;
                            visibility: visible !important;
                        }
                    }

                    .mat-mdc-tab-labels {
                        .mat-mdc-tab {
                            min-width: 60px !important;
                            padding: 0 8px !important;

                            .mdc-tab__content {
                                .mdc-tab__text-label {
                                    font-size: var(--text-xs) !important;
                                }

                                .mat-mdc-tab-label-content {
                                    flex-direction: column !important;
                                    gap: 2px !important;

                                    i {
                                        font-size: 10px !important;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/* === HEADER === */
.team-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    gap: var(--spacing-md);
    
    .back-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--surface-secondary);
        color: var(--text-primary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: var(--text-sm);

        &:hover {
            background: var(--surface-tertiary);
            transform: translateX(-2px);
        }
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-sm);

        .breadcrumb-link {
            background: none;
            border: none;
            color: var(--primary);
            cursor: pointer;
            text-decoration: underline;

            &:hover {
                color: var(--primary-600);
            }
        }

        i {
            color: var(--text-tertiary);
            font-size: var(--text-xs);
        }

        span {
            color: var(--text-primary);
            font-weight: var(--font-weight-semibold);
        }
    }

    @media (max-width: 768px) {
        gap: var(--spacing-md);
        align-items: flex-start;
        display: flex;
        align-items: center;
    }
}

/* === TABS === */
.team-tabs {
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    overflow: hidden;

    .modern-tabs {
        ::ng-deep {
            .mat-mdc-tab-group {
                .mat-mdc-tab-header {
                    background: var(--surface-secondary);
                    border-bottom: 1px solid var(--border-primary);

                    .mat-mdc-tab-label-container {
                        .mat-mdc-tab-list {
                            .mat-mdc-tab {
                                min-width: 120px;
                                padding: var(--spacing-md) var(--spacing-lg);
                                color: var(--text-secondary);
                                font-weight: var(--font-weight-medium);
                                transition: all 0.2s ease;

                                &:hover {
                                    background: var(--surface-tertiary);
                                    color: var(--text-primary);
                                }

                                &.mdc-tab--active {
                                    color: var(--primary);
                                    background: var(--primary-50);
                                }

                                .mdc-tab__content {
                                    display: flex;
                                    align-items: center;
                                    gap: var(--spacing-xs);

                                    i {
                                        font-size: var(--text-sm);
                                    }
                                }
                            }
                        }
                    }

                    .mat-mdc-tab-header-pagination {
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        width: 32px !important;
                        height: 48px !important;
                        background: var(--surface-secondary) !important;
                        border: 1px solid var(--border-primary) !important;
                        transition: all 0.2s ease !important;
                        cursor: pointer !important;
                        position: relative !important;
                        box-shadow: none !important;

                        &:hover {
                            background: var(--surface-tertiary) !important;
                            border-color: var(--primary-300) !important;
                        }

                        &:active {
                            background: var(--primary-50) !important;
                        }

                        &.mat-mdc-tab-header-pagination-disabled {
                            opacity: 0.3 !important;
                            cursor: not-allowed !important;
                            background: var(--surface-primary) !important;

                            &:hover {
                                background: var(--surface-primary) !important;
                                border-color: var(--border-primary) !important;
                            }
                        }
                    }

                    .mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron {
                        border-color: var(--text-primary) !important;
                        border-width: 0 2px 2px 0 !important;
                        width: 8px !important;
                        height: 8px !important;
                        transform: rotate(-45deg) !important;
                        transition: all 0.2s ease !important;
                        display: inline-block !important;
                        border-style: solid !important;
                    }

                    .mat-mdc-tab-header-pagination:hover:not(.mat-mdc-tab-header-pagination-disabled) .mat-mdc-tab-header-pagination-chevron {
                        border-color: var(--primary) !important;
                        transform: rotate(-45deg) scale(1.1) !important;
                    }

                    // Left arrow styling
                    .mat-mdc-tab-header-pagination.mat-mdc-tab-header-pagination-before {
                        border-right: none !important;
                        border-radius: var(--radius-md) 0 0 var(--radius-md) !important;
                    }

                    .mat-mdc-tab-header-pagination.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron {
                        transform: rotate(135deg) !important;
                    }

                    .mat-mdc-tab-header-pagination.mat-mdc-tab-header-pagination-before:hover:not(.mat-mdc-tab-header-pagination-disabled) .mat-mdc-tab-header-pagination-chevron {
                        transform: rotate(135deg) scale(1.1) !important;
                    }

                    // Right arrow styling
                    .mat-mdc-tab-header-pagination.mat-mdc-tab-header-pagination-after {
                        border-left: none !important;
                        border-radius: 0 var(--radius-md) var(--radius-md) 0 !important;
                    }

                    .mat-mdc-tab-header-pagination.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron {
                        transform: rotate(-45deg) !important;
                    }

                    .mat-mdc-tab-header-pagination.mat-mdc-tab-header-pagination-after:hover:not(.mat-mdc-tab-header-pagination-disabled) .mat-mdc-tab-header-pagination-chevron {
                        transform: rotate(-45deg) scale(1.1) !important;
                    }
                }

                .mat-mdc-tab-body-wrapper {
                    .mat-mdc-tab-body {
                        .mat-mdc-tab-body-content {
                            padding: var(--spacing-xl);
                            overflow: visible;

                            @media (max-width: 768px) {
                                padding: var(--spacing-lg);
                            }
                        }
                    }
                }
            }

            .mat-ink-bar {
                background: var(--primary);
                height: 3px;
                border-radius: var(--radius-sm);
            }
        }
    }
}

/* === LOADING STATE === */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
    padding: var(--spacing-xl);

    i {
        font-size: 3rem;
        color: var(--primary);
        margin-bottom: var(--spacing-lg);
        animation: spin 1s linear infinite;
    }

    h3 {
        margin: 0 0 var(--spacing-md) 0;
        color: var(--text-primary);
    }

    p {
        color: var(--text-secondary);
    }
}

/* === ERROR STATE === */
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
    padding: var(--spacing-xl);

    i {
        font-size: 3rem;
        color: var(--danger);
        margin-bottom: var(--spacing-lg);
    }

    h3 {
        margin: 0 0 var(--spacing-md) 0;
        color: var(--text-primary);
    }

    p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
    }

    .retry-btn {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-lg);
        background: var(--primary);
        color: white;
        border: none;
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background: var(--primary-600);
            transform: translateY(-1px);
        }
    }
}

/* === ANIMATIONS === */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .team-tabs .modern-tabs ::ng-deep .mat-mdc-tab-group .mat-mdc-tab-header {
        .mat-mdc-tab-header-pagination {
            width: 28px !important;
            height: 44px !important;
        }

        .mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron {
            width: 6px !important;
            height: 6px !important;
        }

        .mat-mdc-tab-label-container .mat-mdc-tab-list .mat-mdc-tab {
            min-width: 100px !important;
            padding: var(--spacing-sm) var(--spacing-md) !important;
            font-size: var(--text-sm) !important;
        }
    }
}

@media (max-width: 480px) {
    .team-details-container {
        padding: var(--spacing-md);
    }

    .team-header {
        padding: var(--spacing-md);
    }

    .team-tabs {
        .modern-tabs ::ng-deep .mat-mdc-tab-group {
            .mat-mdc-tab-header {
                .mat-mdc-tab-header-pagination {
                    width: 24px !important;
                    height: 40px !important;
                }

                .mat-mdc-tab-label-container .mat-mdc-tab-list .mat-mdc-tab {
                    min-width: 80px !important;
                    padding: var(--spacing-xs) var(--spacing-sm) !important;
                    font-size: var(--text-xs) !important;

                    .mdc-tab__content {
                        gap: var(--spacing-xs) !important;

                        i {
                            font-size: var(--text-xs) !important;
                        }
                    }
                }
            }

            .mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron {
                width: 5px !important;
                height: 5px !important;
                border-width: 0 1.5px 1.5px 0 !important;
            }

            .mat-mdc-tab-body-wrapper .mat-mdc-tab-body .mat-mdc-tab-body-content {
                padding: var(--spacing-md) !important;
            }
        }
    }
}

/* === ACHIEVEMENTS TAB === */
.achievements-tab-content {
    padding: var(--spacing-lg);
    min-height: 400px;

    @media (max-width: 768px) {
        padding: var(--spacing-md);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-sm);
    }
}
