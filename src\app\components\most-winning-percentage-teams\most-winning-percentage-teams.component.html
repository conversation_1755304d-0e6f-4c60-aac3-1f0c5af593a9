<div class="most-winning-percentage-teams-container theme-transition">
  <!-- Header -->
  <div class="header-section" *ngIf="!hideTitle">
    <div class="header-content">
      <h1 class="header-title">
        <i class="fas fa-trophy"></i>
        Most Winning Teams
      </h1>
      <p class="header-subtitle">Teams with the highest winning percentage across all seasons</p>
    </div>
  </div>

  <!-- League Selection -->
  <div class="controls-section" *ngIf="!leagueId">
    <div class="control-group">
      <label for="league-select" class="control-label">
        <i class="fas fa-futbol"></i>
        League
      </label>
      <select 
        id="league-select"
        class="control-select"
        [(ngModel)]="selectedLeagueId"
        (change)="onLeagueChange()">
        <option value="">Select a league</option>
        <option *ngFor="let league of leagues" [value]="league.id">
          {{ league.name }}
        </option>
      </select>
    </div>
  </div>

  <!-- Filters -->
  <div class="filters-section" *ngIf="selectedLeagueId">
    <div class="filter-group">
      <label for="minimum-games" class="filter-label">
        <i class="fas fa-gamepad"></i>
        Minimum Games
      </label>
      <input 
        id="minimum-games"
        type="number"
        class="filter-input"
        [(ngModel)]="minimumGames"
        (change)="onMinimumGamesChange()"
        min="1"
        max="100">
    </div>
  </div>

  <!-- Team Rankings -->
  <div class="rankings-section" *ngIf="selectedLeagueId && !isLoading">
    <div class="rankings-header">
      <h3 class="rankings-title">
        <i class="fas fa-list-ol"></i>
        Team Winning Percentage Leaders
      </h3>
      <p class="rankings-subtitle">
        Minimum {{ minimumGames }} games played
      </p>
    </div>

    <div class="teams-list" *ngIf="mostWinningPercentageTeamsData.length > 0; else noDataTemplate">
      <div 
        class="team-row"
        *ngFor="let team of mostWinningPercentageTeamsData; let i = index"
        (click)="onTeamClick(team)"
        [class.first-place]="i === 0"
        [class.second-place]="i === 1"
        [class.third-place]="i === 2">
        
        <div class="rank-section">
          <span class="rank">{{ getTeamRank(i) }}</span>
          <i class="fas fa-crown crown-icon" *ngIf="i === 0"></i>
        </div>

        <div class="team-info">
          <img 
            [src]="team.teamImgUrl || 'assets/Icons/Team.jpg'"
            [alt]="team.teamName"
            class="team-logo"
            (error)="onImageError($event)">
          
          <div class="team-details">
            <h4 class="team-name">{{ team.teamName }}</h4>
            <p class="team-record">{{ getWinLossRecord(team) }}</p>
          </div>
        </div>

        <div class="stats-section">
          <div class="primary-stat">
            <span class="stat-value">{{ getWinPercentageDisplay(team) }}</span>
            <span class="stat-label">Win %</span>
          </div>
          <div class="secondary-stats">
            <div class="stat-item">
              <span class="stat-value">{{ team.games }}</span>
              <span class="stat-label">Games</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ team.wins }}</span>
              <span class="stat-label">Wins</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ team.goalDifference > 0 ? '+' : '' }}{{ team.goalDifference }}</span>
              <span class="stat-label">Goal Diff</span>
            </div>
          </div>
        </div>

        <!-- Winning Indicator -->
        <div class="winning-indicator">
          <div class="trophy-icons">
            <i class="fas fa-trophy" *ngFor="let trophy of getTrophyIconsArray(team.wins)"></i>
            <span class="more-indicator" *ngIf="team.wins > 5">+{{ team.wins - 5 }}</span>
          </div>
        </div>
      </div>
    </div>

    <ng-template #noDataTemplate>
      <div class="no-data">
        <i class="fas fa-trophy"></i>
        <h3>No Team Data</h3>
        <p>No teams found with the current filters</p>
      </div>
    </ng-template>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading statistics...</p>
    </div>
  </div>
</div>
