import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { DatePipe } from "@angular/common";
import { AppComponent } from "./app.component";
import { DashboardComponent } from "./components/dashboard/dashboard.component";
import { AppRoutingModule } from "./app-routes.module";
import { BrowserModule } from "@angular/platform-browser";
import { NavbarComponent } from "./components/navbar/navbar.component";
import { FixturesComponent } from "./components/fixtures/fixtures.component";
import { ViewModeToggleComponent } from "./components/fixtures/view-mode-toggle/view-mode-toggle.component";
import { FixtureNavigationComponent } from "./components/fixtures/fixture-navigation/fixture-navigation.component";
import { GameEditingComponent } from "./components/fixtures/game-editing/game-editing.component";
import { DateViewComponent } from "./components/fixtures/date-view/date-view.component";

import { LeagueTableComponent } from "./components/league-table/league-table.component";
import { TopAssistsComponent } from "./components/top-assists/top-assists.component";
import { TopScorersComponent } from "./components/top-scorers/top-scorers.component";
import { GameDetailsComponent } from "./components/game-details/game-details.component";
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { SharedModule } from "./shared/shared.module";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { PlayerDetailsComponent } from "./components/player-details/player-details.component";
import { TransferHistoryComponent } from "./components/player-details/transfer-history/transfer-history.component";
import { PlayerHeaderComponent } from "./components/player-details/player-header/player-header.component";
import { PlayerProfileCardComponent } from "./components/player-details/player-profile-card/player-profile-card.component";
import { PlayerQuickStatsComponent } from "./components/player-details/player-quick-stats/player-quick-stats.component";
import { PlayerDetailedAnalyticsComponent } from "./components/player-details/player-detailed-analytics/player-detailed-analytics.component";
import { PlayerComparisonComponent } from "./components/player-comparison/player-comparison.component";
import { PlayerSeasonHistoryComponent } from "./components/player-season-history/player-season-history.component";
import { TeamDetailsComponent } from "./components/team-details/team-details.component";
import { PlayerService } from "./services/player.service";
import { TeamService } from "./services/team.service";
import { FixtureService } from "./services/fixtures.service";
import { LeagueService } from "./services/league.service";
import { HttpClientModule } from "@angular/common/http";
import { ModifyGameComponent } from "./components/modify-game/modify-game.component";
import { MatTabsModule } from '@angular/material/tabs';
import { TeamDetailsOverallComponent } from "./components/team-details/tabs/team-details-overall/team-details-overall.component";
import { TeamDetailsOverallDataComponent } from "./components/team-details/tabs/team-details-overall/team-details-overall-data/team-details-overall-data.component";
import { TeamDetailsMatchesComponent } from "./components/team-details/tabs/team-details-matches/team-details-matches.component";
import { TeamDetailsSquadComponent } from "./components/team-details/tabs/team-details-overall/team-details-squad/team-details-squad.component";
import { TeamDetailsStatsComponent } from "./components/team-details/tabs/team-details-stats/team-details-stats.component";
import { TeamDetailsOverallStatsComponent } from "./components/team-details/tabs/team-details-overall/team-details-overall-stats/team-details-overall-stats.component";
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { NotificationService } from "./services/notification.service";
import { AddFixtureComponent } from "./components/add-fixture/add-fixture.component";
import { MatPaginatorModule } from "@angular/material/paginator";
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { TeamGamesComponent } from "./components/team-games/team-games.component";
import { SignUpComponent } from "./components/sign-up/sign-up.component";
import { LoginComponent } from "./components/login/login.component";
import { CreatePlayerComponent } from "./components/create-player/create-player.component";
import { AssignPlayerToTeamComponent } from "./components/assign-player-to-team/assign-player-to-team.component";
import { CreateTeamComponent } from "./components/create-team/create-team.component";
import { LastGamesFormComponent } from "./components/last-games-form/last-games-form.component";
import { AgChartsModule } from "ag-charts-angular";
import { PlayerStatsByPositionComponent } from "./components/player-stats-by-position/player-stats-by-position.component";
import { TeamDetailsHistoryComponent } from "./components/team-details/tabs/team-details-history/team-details-history.component";
// Removed old social login - using modern Google Identity Services instead
import { ConfigurationService } from "./services/configuration.service";
import { DashboardTopscorersComponent } from "./components/dashboard-topscorers/dashboard-topscorers.component";
import { DashboardNextFixturesComponent } from "./components/dashboard-next-fixtures/dashboard-next-fixtures.component";
import { AuthService } from "./services/auth.service";
import { AuthInterceptor } from "./interceptors/auth.interceptor";
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { UserProfileComponent } from "./components/user-profile/user-profile.component";
import { AdminPlayerRequestsComponent } from "./components/admin-player-requests/admin-player-requests.component";
import { DashboardTopassistsComponent } from "./components/dashboard-topassists/dashboard-topassists.component";
import { TotwComponent } from "./components/totw/totw.component";

import { TeamVsTeamHistoryComponent } from "./components/team-vs-team-history/team-vs-team-history.component";
import { TotwCardComponent } from "./components/totw-card/totw-card.component";
import { TopAvgRatingByPositionComponent } from "./components/top-avg-rating-by-position/top-avg-rating-by-position.component";
import { RulesComponent } from "./components/rules/rules.component";
import { NewsComponent } from "./components/news/news.component";
import { NewsService } from "./services/news.service";
import { NewsListComponent } from "./components/news/news-list/news-list.component";
import { NewsShareModalComponent } from "./components/news/news-share-modal/news-share-modal.component";
import { NewsEditModalComponent } from "./components/news/news-edit-modal/news-edit-modal.component";
import { NewsImageGeneratorService } from "./services/news-image-generator.service";
import { GameFormationDisplayComponent } from "./components/game-details/game-formation-display/game-formation-display.component";
import { FormationTemplateService } from "./services/formation-template.service";
import { AddNewsComponent } from "./components/add-news/add-news.component";
import { PlayerSearchComponent } from "./components/player-search/player-search.component";
import { ThemeAwareDirective } from "./directives/theme-aware.directive";
import { MiniPitchFormationComponent } from "./components/mini-pitch-formation/mini-pitch-formation.component";
import { MiniPitchTestComponent } from "./components/mini-pitch-test/mini-pitch-test.component";
import { AiImportComponent } from "./components/ai-import/ai-import.component";
import { DashboardStatsCardComponent } from './components/dashboard/dashboard-stats-card/dashboard-stats-card.component';
import { DashboardHighlightCardComponent } from './components/dashboard/dashboard-highlight-card/dashboard-highlight-card.component';
import { FixtureCardComponent } from './components/fixtures/fixture-card/fixture-card.component';
import { PlayerStatsCardComponent } from './components/player-details/player-stats-card/player-stats-card.component';
import { TeamStatsCardComponent } from './components/team-details/team-stats-card/team-stats-card.component';
import { TeamActionButtonsComponent } from './components/team-details/team-action-buttons/team-action-buttons.component';
import { TeamSquadListComponent } from './components/team-details/team-squad-list/team-squad-list.component';
import { GameHeaderComponent } from './components/game-details/game-header/game-header.component';
import { GameActionsComponent } from './components/game-details/game-actions/game-actions.component';
import { PromotionalBannerComponent } from './components/promotional-banner/promotional-banner.component';
import { ChatComponent } from './components/chat/chat.component';
import { TransferRequestsComponent } from './components/transfer-requests/transfer-requests.component';
import { BracketsComponent } from './components/brackets/brackets.component';
import { PredictionVotingComponent } from './components/prediction-voting/prediction-voting.component';
import { GameCommentsComponent } from './components/game-comments/game-comments.component';
import { AdminCmsDashboardComponent } from './components/admin-cms-dashboard/admin-cms-dashboard.component';
import { AdminEndSeasonComponent } from './components/admin-end-season/admin-end-season.component';
import { AdminAddAchievementComponent } from './components/admin-add-achievement/admin-add-achievement.component';
import { AchievementHistoryComponent } from './components/achievement-history/achievement-history.component';
import { CmsPlayersTableComponent } from './components/admin-cms-dashboard/cms-players-table/cms-players-table.component';
import { CmsTeamsTableComponent } from './components/admin-cms-dashboard/cms-teams-table/cms-teams-table.component';
import { CmsGamesTableComponent } from './components/admin-cms-dashboard/cms-games-table/cms-games-table.component';
import { CmsLeaguesTableComponent } from './components/admin-cms-dashboard/cms-leagues-table/cms-leagues-table.component';
import { CmsNewsTableComponent } from './components/admin-cms-dashboard/cms-news-table/cms-news-table.component';
import { PredictionService } from './services/prediction.service';
import { CommentService } from './services/comment.service';


@NgModule({
    declarations: [
        AppComponent,
        DashboardComponent,
        NavbarComponent,
        FixturesComponent,
        ViewModeToggleComponent,
        FixtureNavigationComponent,
        GameEditingComponent,
        DateViewComponent,
        LeagueTableComponent,
        TopAssistsComponent,
        TopScorersComponent,
        GameDetailsComponent,
        PlayerDetailsComponent,
        TransferHistoryComponent,
        PlayerHeaderComponent,
        PlayerProfileCardComponent,
        PlayerQuickStatsComponent,
        PlayerDetailedAnalyticsComponent,
        PlayerComparisonComponent,
        PlayerSeasonHistoryComponent,
        TeamDetailsComponent,
        CreatePlayerComponent,
        CreateTeamComponent,
        ModifyGameComponent,
        TeamDetailsComponent,
        TeamDetailsOverallComponent,
        TeamDetailsOverallDataComponent,
        TeamDetailsOverallStatsComponent,
        TeamDetailsMatchesComponent,
        TeamDetailsSquadComponent,
        TeamDetailsStatsComponent,
        TeamDetailsHistoryComponent,
        AddFixtureComponent,
        TeamGamesComponent,
        SignUpComponent,
        LoginComponent,
        AssignPlayerToTeamComponent,
        LastGamesFormComponent,
        PlayerStatsByPositionComponent,
        DashboardTopscorersComponent,
        DashboardNextFixturesComponent,
        UserProfileComponent,
        AdminPlayerRequestsComponent,
        DashboardTopassistsComponent,
        TotwComponent,
        TeamVsTeamHistoryComponent,
        TotwCardComponent,
        TopAvgRatingByPositionComponent,
        RulesComponent,
        NewsComponent,
        NewsListComponent,
        NewsShareModalComponent,
        NewsEditModalComponent,
        GameFormationDisplayComponent,
        AddNewsComponent,
        PlayerSearchComponent,
        ThemeAwareDirective,
        DashboardStatsCardComponent,
        DashboardHighlightCardComponent,
        FixtureCardComponent,
        PlayerStatsCardComponent,
        TeamStatsCardComponent,
        TeamActionButtonsComponent,
        TeamSquadListComponent,
        GameHeaderComponent,
        AdminCmsDashboardComponent,
        AdminEndSeasonComponent,
        AdminAddAchievementComponent,
        AchievementHistoryComponent,
        CmsPlayersTableComponent,
        CmsTeamsTableComponent,
        CmsGamesTableComponent,
        CmsLeaguesTableComponent,
        CmsNewsTableComponent,
        GameActionsComponent,
        PromotionalBannerComponent,
        ChatComponent,
        TransferRequestsComponent,
        BracketsComponent,
        PredictionVotingComponent,
        GameCommentsComponent
    ],
    imports: [
        AppRoutingModule,
        BrowserModule,
        ReactiveFormsModule,
        FormsModule,
        ReactiveFormsModule,
        HttpClientModule,
        MatTabsModule,
        MatSnackBarModule,
        SharedModule,
        MatPaginatorModule,
        MatDialogModule,
        AgChartsModule,
        MiniPitchFormationComponent,
        MiniPitchTestComponent,
        AiImportComponent,

    ],
    exports: [],
    providers: [

        provideAnimationsAsync(),
        PlayerService,
        TeamService,
        FixtureService,
        LeagueService,

        NotificationService,
        DatePipe,
        ConfigurationService,
        NewsService,
        NewsImageGeneratorService,
        FormationTemplateService,
        AuthService,
        PredictionService,
        CommentService,
        {
            provide: HTTP_INTERCEPTORS,
            useClass: AuthInterceptor,
            multi: true
        }
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    bootstrap: [AppComponent]
})
export class AppModule { }