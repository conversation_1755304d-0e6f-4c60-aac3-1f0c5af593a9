<div class="game-comments">
  <!-- Header -->
  <div class="comments-header">
    <h3 class="comments-title">
      <i class="fas fa-comments"></i>
      Comments
      <span class="comment-count" *ngIf="totalCount > 0">({{ totalCount }})</span>
    </h3>
  </div>

  <!-- New Comment Form -->
  <div *ngIf="isAuthenticated" class="new-comment-form">
    <div class="comment-input-container">
      <textarea 
        [(ngModel)]="newCommentContent"
        placeholder="Share your thoughts about this match..."
        class="comment-input"
        rows="3"
        maxlength="1000"
        [disabled]="isSubmittingComment">
      </textarea>
      <div class="input-footer">
        <div class="character-count"
             [class.warning]="getRemainingCharacters(newCommentContent || '') < 100"
             [class.error]="getRemainingCharacters(newCommentContent || '') < 0">
          {{ getRemainingCharacters(newCommentContent || '') }} characters remaining
        </div>
        <button class="submit-btn"
                [disabled]="!(newCommentContent || '').trim() || isSubmittingComment || getRemainingCharacters(newCommentContent || '') < 0"
                (click)="submitComment()">
          <i class="fas fa-paper-plane" [class.fa-spin]="isSubmittingComment"></i>
          {{ isSubmittingComment ? 'Posting...' : 'Post Comment' }}
        </button>
      </div>
    </div>
  </div>

  <!-- Login Prompt -->
  <div *ngIf="!isAuthenticated" class="login-prompt">
    <i class="fas fa-sign-in-alt"></i>
    <span>Log in to join the conversation!</span>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <i class="fas fa-spinner fa-spin"></i>
    <span>Loading comments...</span>
  </div>

  <!-- Comments List -->
  <div *ngIf="!isLoading" class="comments-list" style="margin-top: 30px !important; clear: both !important; position: static !important;">



    <!-- No Comments -->
    <div *ngIf="(comments || []).length === 0" class="no-comments">
      <i class="fas fa-comment-slash"></i>
      <span>No comments yet. Be the first to share your thoughts!</span>
    </div>

    <!-- Comment Items -->
    <div *ngFor="let comment of comments" class="comment-item">
      
      <!-- Main Comment -->
      <div class="comment-content">
        <div class="comment-avatar">
          <img [src]="getProfileImage(comment)" [alt]="comment.user.name" class="avatar-image">
        </div>
        
        <div class="comment-body">
          <div class="comment-header">
            <span class="author-name">{{ comment.user.name }}</span>
            <span class="comment-time">{{ formatTimeAgo(comment.createdAt) }}</span>
            <span *ngIf="comment.isEdited" class="edited-indicator">(edited)</span>
          </div>
          
          <!-- Comment Text (View Mode) -->
          <div *ngIf="editingCommentId !== comment.id" class="comment-text">
            {{ comment.content }}
          </div>
          
          <!-- Comment Edit Form -->
          <div *ngIf="editingCommentId === comment.id" class="edit-form">
            <textarea 
              [(ngModel)]="editContent"
              class="edit-input"
              rows="3"
              maxlength="1000"
              [disabled]="isSubmittingEdit">
            </textarea>
            <div class="edit-actions">
              <div class="character-count" 
                   [class.warning]="getRemainingCharacters(editContent) < 100"
                   [class.error]="getRemainingCharacters(editContent) < 0">
                {{ getRemainingCharacters(editContent) }} characters remaining
              </div>
              <div class="action-buttons">
                <button class="cancel-btn" (click)="cancelEdit()" [disabled]="isSubmittingEdit">
                  Cancel
                </button>
                <button class="save-btn" 
                        (click)="submitEdit()" 
                        [disabled]="!editContent.trim() || isSubmittingEdit || getRemainingCharacters(editContent) < 0">
                  <i class="fas fa-check" [class.fa-spin]="isSubmittingEdit"></i>
                  {{ isSubmittingEdit ? 'Saving...' : 'Save' }}
                </button>
              </div>
            </div>
          </div>
          
          <!-- Comment Actions -->
          <div class="comment-actions">
            <button class="action-btn like-btn" 
                    [class.liked]="comment.isLikedByUser"
                    (click)="toggleLike(comment)"
                    [disabled]="!isAuthenticated">
              <i class="fas fa-heart"></i>
              <span *ngIf="comment.likesCount > 0">{{ comment.likesCount }}</span>
            </button>
            
            <button class="action-btn reply-btn" 
                    (click)="startReply(comment.id)"
                    [disabled]="!isAuthenticated">
              <i class="fas fa-reply"></i>
              Reply
            </button>
            
            <button *ngIf="canEditComment(comment)" 
                    class="action-btn edit-btn" 
                    (click)="startEdit(comment)">
              <i class="fas fa-edit"></i>
              Edit
            </button>
            
            <button *ngIf="canDeleteComment(comment)" 
                    class="action-btn delete-btn" 
                    (click)="deleteComment(comment.id)">
              <i class="fas fa-trash"></i>
              Delete
            </button>
          </div>
        </div>
      </div>

      <!-- Reply Form -->
      <div *ngIf="replyingToCommentId === comment.id" class="reply-form">
        <div class="reply-input-container">
          <textarea 
            [(ngModel)]="replyContent"
            placeholder="Write a reply..."
            class="reply-input"
            rows="2"
            maxlength="1000"
            [disabled]="isSubmittingReply">
          </textarea>
          <div class="reply-actions">
            <div class="character-count" 
                 [class.warning]="getRemainingCharacters(replyContent) < 100"
                 [class.error]="getRemainingCharacters(replyContent) < 0">
              {{ getRemainingCharacters(replyContent) }} characters remaining
            </div>
            <div class="action-buttons">
              <button class="cancel-btn" (click)="cancelReply()" [disabled]="isSubmittingReply">
                Cancel
              </button>
              <button class="submit-btn" 
                      (click)="submitReply()" 
                      [disabled]="!replyContent.trim() || isSubmittingReply || getRemainingCharacters(replyContent) < 0">
                <i class="fas fa-paper-plane" [class.fa-spin]="isSubmittingReply"></i>
                {{ isSubmittingReply ? 'Posting...' : 'Reply' }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Replies -->
      <div *ngIf="comment.replies && (comment.replies || []).length > 0" class="replies-container">
        <div *ngFor="let reply of comment.replies" class="reply-item">
          <div class="comment-content">
            <div class="comment-avatar">
              <img [src]="getProfileImage(reply)" [alt]="reply.user.name" class="avatar-image small">
            </div>
            
            <div class="comment-body">
              <div class="comment-header">
                <span class="author-name">{{ reply.user.name }}</span>
                <span class="comment-time">{{ formatTimeAgo(reply.createdAt) }}</span>
                <span *ngIf="reply.isEdited" class="edited-indicator">(edited)</span>
              </div>
              
              <!-- Reply Text (View Mode) -->
              <div *ngIf="editingCommentId !== reply.id" class="comment-text">
                {{ reply.content }}
              </div>
              
              <!-- Reply Edit Form -->
              <div *ngIf="editingCommentId === reply.id" class="edit-form">
                <textarea 
                  [(ngModel)]="editContent"
                  class="edit-input"
                  rows="2"
                  maxlength="1000"
                  [disabled]="isSubmittingEdit">
                </textarea>
                <div class="edit-actions">
                  <div class="character-count" 
                       [class.warning]="getRemainingCharacters(editContent) < 100"
                       [class.error]="getRemainingCharacters(editContent) < 0">
                    {{ getRemainingCharacters(editContent) }} characters remaining
                  </div>
                  <div class="action-buttons">
                    <button class="cancel-btn" (click)="cancelEdit()" [disabled]="isSubmittingEdit">
                      Cancel
                    </button>
                    <button class="save-btn" 
                            (click)="submitEdit()" 
                            [disabled]="!editContent.trim() || isSubmittingEdit || getRemainingCharacters(editContent) < 0">
                      <i class="fas fa-check" [class.fa-spin]="isSubmittingEdit"></i>
                      {{ isSubmittingEdit ? 'Saving...' : 'Save' }}
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- Reply Actions -->
              <div class="comment-actions">
                <button class="action-btn like-btn" 
                        [class.liked]="reply.isLikedByUser"
                        (click)="toggleLike(reply)"
                        [disabled]="!isAuthenticated">
                  <i class="fas fa-heart"></i>
                  <span *ngIf="reply.likesCount > 0">{{ reply.likesCount }}</span>
                </button>
                
                <button *ngIf="canEditComment(reply)" 
                        class="action-btn edit-btn" 
                        (click)="startEdit(reply)">
                  <i class="fas fa-edit"></i>
                  Edit
                </button>
                
                <button *ngIf="canDeleteComment(reply)" 
                        class="action-btn delete-btn" 
                        (click)="deleteComment(reply.id)">
                  <i class="fas fa-trash"></i>
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Load More Button -->
  <div *ngIf="hasMore && !isLoading" class="load-more-container">
    <button class="load-more-btn" 
            (click)="loadMoreComments()" 
            [disabled]="isLoadingMore">
      <i class="fas fa-chevron-down" [class.fa-spin]="isLoadingMore"></i>
      {{ isLoadingMore ? 'Loading...' : 'Load More Comments' }}
    </button>
  </div>
</div>
