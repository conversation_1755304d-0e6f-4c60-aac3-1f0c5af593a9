import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { BracketsService, PlayoffBracket, BracketStage, BracketMatch } from '../../../services/brackets.service';
import { PLAYOFF_STAGE } from '../../../shared/models/game.model';
import { NotificationService } from '../../../services/notification.service';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-playoff-bracket',
  templateUrl: './playoff-bracket.component.html',
  styleUrls: ['./playoff-bracket.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PlayoffBracketComponent implements OnInit, OnDestroy {
  @Input() viewMode: 'fixtures' | 'date' | 'playoffs' = 'playoffs';
  
  @Output() gameClicked = new EventEmitter<string>();

  // Playoff-related properties
  bracketData: PlayoffBracket | null = null;
  bracketViewMode: 'bracket' | 'list' = 'bracket';
  bracketLoading: boolean = false;
  selectedStage: PLAYOFF_STAGE | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private bracketsService: BracketsService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    if (this.viewMode === 'playoffs') {
      this.loadBracketData();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  async loadBracketData(): Promise<void> {
    if (this.viewMode !== 'playoffs') return;

    this.bracketLoading = true;
    try {
      // Load bracket data for the current league and season
      const LEAGUE_ID = '65ecb1eb2f272e434483a821'; // Current league ID
      const CURRENT_SEASON = 6; // Current season number
      this.bracketData = await this.bracketsService.getPlayoffBracket(LEAGUE_ID, CURRENT_SEASON);
      
    } catch (error) {
      console.error('Error loading bracket data:', error);
      this.notificationService.error('Failed to load playoff bracket data');
      this.bracketData = null;
    } finally {
      this.bracketLoading = false;
      this.cdr.detectChanges(); // Trigger change detection
    }
  }



  onBracketViewModeChange(mode: 'bracket' | 'list'): void {
    this.bracketViewMode = mode;
  }

  refreshBracket(): void {
    this.loadBracketData();
  }

  // Bracket helper methods
  hasPlayoffData(): boolean {
    return !!(this.bracketData && this.bracketData.stages && this.bracketData.stages.length > 0);
  }

  getCompletionPercentage(): number {
    if (!this.bracketData) return 0;
    return this.bracketsService.getBracketCompletionPercentage(this.bracketData);
  }

  getNextMatch(): BracketMatch | null {
    if (!this.bracketData) return null;
    return this.bracketsService.getNextMatch(this.bracketData);
  }

  getStageDisplayName(stage: PLAYOFF_STAGE): string {
    return this.bracketsService.getStageDisplayName(stage);
  }

  formatMatchDate(date: Date): string {
    return this.bracketsService.formatMatchDate(date);
  }

  hasChampionshipResults(): boolean {
    return !!(this.bracketData && (this.bracketData.champion || this.bracketData.runnerUp || this.bracketData.thirdPlace));
  }

  getChampionshipTitle(): string {
    return 'Championship Results';
  }

  getTeamImage(team: { imgUrl?: string }): string {
    return this.bracketsService.getTeamImage(team);
  }

  getStageWidth(stage: BracketStage): string {
    const stageCount = this.bracketData?.stages?.length || 1;
    return `${100 / stageCount}%`;
  }

  getStageIcon(stage: PLAYOFF_STAGE): string {
    const stageIcons: { [key: string]: string } = {
      'Play-in Round': 'fas fa-play',
      'Quarter-Final': 'fas fa-trophy',
      'Semi-Final': 'fas fa-medal',
      'Third Place': 'fas fa-award',
      'Final': 'fas fa-crown',
      'Relegation Playoff': 'fas fa-arrow-down'
    };
    return stageIcons[stage.toString()] || 'fas fa-futbol';
  }

  getMatchPosition(index: number, totalMatches: number): string {
    const spacing = 100 / (totalMatches + 1);
    return `${spacing * (index + 1)}%`;
  }

  isMatchCompleted(match: BracketMatch): boolean {
    return this.bracketsService.isMatchCompleted(match);
  }

  getMatchWinner(match: BracketMatch): { id: string; name: string; imgUrl?: string } | null {
    return this.bracketsService.getMatchWinner(match);
  }

  getMatchStatusIcon(match: BracketMatch): string {
    return this.bracketsService.getMatchStatusIcon(match);
  }

  getMatchStatusColor(match: BracketMatch): string {
    return this.bracketsService.getMatchStatusColor(match);
  }

  goToGameDetails(gameId: string): void {
    this.gameClicked.emit(gameId);
    this.router.navigate(['/game-details', gameId]);
  }

  shareMatch(match: BracketMatch): void {
    // Implement share functionality
    this.notificationService.info('Share functionality coming soon!');
  }

  onStageSelect(stage: PLAYOFF_STAGE | null): void {
    this.selectedStage = stage;
  }

  // Utility methods
  trackByStageId(_index: number, stage: BracketStage): string {
    return stage.stage.toString();
  }

  trackByMatchId(_index: number, match: BracketMatch): string {
    return match.id;
  }
}
