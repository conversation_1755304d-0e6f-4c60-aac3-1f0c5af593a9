/* === POPUP DIALOG STYLING === */

.popup-area {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

/* Dialog Header */
.popup-area > .d-flex:first-child {
    background: var(--surface-secondary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    min-height: 60px;

    i {
        font-size: var(--text-lg);
        color: var(--text-secondary);
        transition: color 0.2s ease;

        &:hover {
            color: var(--text-primary);
        }
    }
}

/* Edit Controls */
.popup-area > .d-flex:nth-child(2) {
    background: var(--surface-tertiary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--spacing-sm) var(--spacing-lg);
    min-height: 50px;

    .text-uppercase {
        font-size: var(--text-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-secondary);
        transition: all 0.2s ease;
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--radius-md);

        &:hover {
            color: var(--primary);
            background: var(--surface-hover);
        }

        i {
            margin-left: var(--spacing-xs);
        }
    }
}

/* Content Area */
.h-90 {
    flex: 1;
    overflow: auto;
    background: var(--bg-primary);
}

/* Global Dialog Panel Styling */
::ng-deep .game-details-dialog {
    .mat-mdc-dialog-container {
        padding: 0 !important;
        background: var(--bg-primary) !important;
        border-radius: var(--radius-lg) !important;
        box-shadow: var(--shadow-2xl) !important;
    }

    .mdc-dialog__surface {
        border-radius: var(--radius-lg) !important;
        background: var(--bg-primary) !important;
    }
}

/* Tab Container Styles */
.tab-container {
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    padding: 4px;
    gap: 4px;
    border: 1px solid var(--border-primary);
}

.tab-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: var(--text-sm);
    min-width: 120px;
    justify-content: center;

    &:hover {
        background: var(--surface-hover);
        color: var(--text-primary);
    }

    &.active {
        background: var(--primary);
        color: white;
        box-shadow: var(--shadow-sm);
    }

    i {
        font-size: var(--text-sm);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .popup-area > .d-flex:first-child {
        padding: var(--spacing-sm) var(--spacing-md);
        min-height: 50px;
    }

    .popup-area > .d-flex:nth-child(2) {
        padding: var(--spacing-xs) var(--spacing-md);
        min-height: 40px;

        .text-uppercase {
            font-size: var(--text-xs);
            padding: var(--spacing-xs);
        }
    }

    .tab-container {
        padding: 2px;
        gap: 2px;
    }

    .tab-button {
        padding: var(--spacing-xs) var(--spacing-sm);
        min-width: 100px;
        font-size: var(--text-xs);
    }
}