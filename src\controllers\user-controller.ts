import { Request, Response, NextFunction } from "express";
import { inject, injectable } from "tsyringe";
import { IUserController, IUserService, CreateUserRequest, LoginRequest, GoogleAuthRequest } from "../interfaces/user";
import logger from "../config/logger";
import { OAuth2Client } from 'google-auth-library';

@injectable()
export default class UserController implements IUserController {
  private googleClient: OAuth2Client;

  constructor(
    @inject("IUserService") private userService: IUserService
  ) {
    // Initialize Google OAuth client
    this.googleClient = new OAuth2Client(
      process.env.GOOGLE_CLIENT_ID || '465418039105-p141710mc4km6ap5bkm3ujp1ftfog8bb.apps.googleusercontent.com'
    );
  }

  async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userData: CreateUserRequest = req.body;

      // Validate required fields
      if (!userData.email || !userData.password || !userData.firstName || !userData.lastName) {
        res.status(400).json({ message: "All fields are required" });
        return;
      }

      const result = await this.userService.createUser(userData);

      res.status(201).json({
        message: "User registered successfully",
        user: result.user,
        token: result.token
      });
    } catch (error: any) {
      next(error);
    }
  }

  async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const loginData: LoginRequest = req.body;

      // Validate required fields
      if (!loginData.email || !loginData.password) {
        res.status(400).json({ message: "Email and password are required" });
        return;
      }

      const result = await this.userService.loginUser(loginData);

      res.json({
        message: "Login successful",
        user: result.user,
        token: result.token
      });
    } catch (error: any) {
      next(error);
    }
  }

  async googleAuth(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { idToken, accessToken } = req.body;

      if (!idToken && !accessToken) {
        res.status(400).json({ message: "Google ID token or access token is required" });
        return;
      }

      let googleData: GoogleAuthRequest;

      if (idToken) {
        // Verify the Google ID token
        const ticket = await this.googleClient.verifyIdToken({
          idToken,
          audience: process.env.GOOGLE_CLIENT_ID || '465418039105-p141710mc4km6ap5bkm3ujp1ftfog8bb.apps.googleusercontent.com'
        });

        const payload = ticket.getPayload();
        if (!payload) {
          res.status(400).json({ message: "Invalid Google token" });
          return;
        }

        googleData = {
          googleId: payload.sub,
          email: payload.email!,
          firstName: payload.given_name || '',
          lastName: payload.family_name || '',
          profilePicture: payload.picture
        };
      } else {
        // Handle access token case (for frontend social login libraries)
        // This is a fallback for different OAuth flows
        res.status(400).json({ message: "ID token verification required" });
        return;
      }

      const result = await this.userService.googleAuth(googleData);

      res.json({
        message: "Google authentication successful",
        user: result.user,
        token: result.token
      });
    } catch (error: any) {
      logger.error(`Google auth error: ${error.message}`);
      if (error.message.includes('Token used too early') || error.message.includes('Token expired')) {
        res.status(401).json({ message: "Google token expired or invalid" });
      } else {
        next(error);
      }
    }
  }

  async getProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      res.json({
        user: req.user
      });
    } catch (error: any) {
      next(error);
    }
  }

  async updateProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const updateData = req.body;
      const updatedUser = await this.userService.updateUser(req.user.id, updateData);

      if (!updatedUser) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      res.json({
        message: "Profile updated successfully",
        user: updatedUser
      });
    } catch (error: any) {
      next(error);
    }
  }

  async deleteAccount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const deleted = await this.userService.deleteUser(req.user.id);

      if (!deleted) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      res.json({
        message: "Account deleted successfully"
      });
    } catch (error: any) {
      next(error);
    }
  }

  async associatePlayer(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { playerId, playerEmail } = req.body;

      if (!playerId && !playerEmail) {
        res.status(400).json({ message: "Player ID or email is required" });
        return;
      }

      const updatedUser = await this.userService.associatePlayerWithUser(req.user.id, playerId, playerEmail);

      if (!updatedUser) {
        res.status(404).json({ message: "User or player not found" });
        return;
      }

      res.json({
        message: "Player associated successfully",
        user: updatedUser
      });
    } catch (error: any) {
      next(error);
    }
  }

  async removePlayer(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { playerId } = req.body;

      if (!playerId) {
        res.status(400).json({ message: "Player ID is required" });
        return;
      }

      const updatedUser = await this.userService.removePlayerFromUser(req.user.id, playerId);

      if (!updatedUser) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      res.json({
        message: "Player removed successfully",
        user: updatedUser
      });
    } catch (error: any) {
      next(error);
    }
  }

  async checkPlayerAssociation(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { playerId } = req.params;

      if (!playerId) {
        res.status(400).json({ message: "Player ID is required" });
        return;
      }

      const isAssociated = await this.userService.isPlayerAssociated(playerId);

      res.json({
        isAssociated
      });
    } catch (error: any) {
      next(error);
    }
  }

  async debugUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      res.json({
        message: "User debug info",
        user: {
          id: req.user.id,
          email: req.user.email,
          firstName: req.user.firstName,
          lastName: req.user.lastName,
          role: req.user.role,
          isEmailVerified: req.user.isEmailVerified,
          associatedPlayers: req.user.associatedPlayers
        }
      });
    } catch (error: any) {
      next(error);
    }
  }

  async verifyEmail(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const updatedUser = await this.userService.verifyEmail(req.user.id);

      if (!updatedUser) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      res.json({
        message: "Email verified successfully",
        user: updatedUser
      });
    } catch (error: any) {
      next(error);
    }
  }

  async googleCallback(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // This endpoint can be used for OAuth callback flows
      // For now, we'll redirect to the frontend with success/error status
      const { code, error, state } = req.query;

      const frontendUrl = process.env.FRONTEND_URL || 'https://d2pql7k53cjta2.cloudfront.net';

      if (error) {
        // Redirect to frontend with error
        res.redirect(`${frontendUrl}/login?error=${encodeURIComponent(error as string)}`);
        return;
      }

      if (code) {
        // For server-side OAuth flow (if needed in the future)
        // For now, redirect to frontend to handle the code
        res.redirect(`${frontendUrl}/login?code=${encodeURIComponent(code as string)}&state=${encodeURIComponent(state as string || '')}`);
        return;
      }

      res.redirect(`${frontendUrl}/login`);
    } catch (error: any) {
      logger.error(`Google callback error: ${error.message}`);
      const frontendUrl = process.env.FRONTEND_URL || 'https://d2pql7k53cjta2.cloudfront.net';
      res.redirect(`${frontendUrl}/login?error=callback_error`);
    }
  }

  async searchAvailablePlayers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { search, email } = req.query;

      const players = await this.userService.searchAvailablePlayers(
        search as string,
        email as string
      );

      res.json({
        players
      });
    } catch (error: any) {
      next(error);
    }
  }

  async searchAvailablePlayersPublic(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { search, email } = req.query;

      // Only allow search by name for public endpoint, not email
      if (email) {
        res.status(400).json({ message: "Email search not allowed for public endpoint" });
        return;
      }

      if (!search || (search as string).trim().length < 2) {
        res.status(400).json({ message: "Search term must be at least 2 characters" });
        return;
      }

      const players = await this.userService.searchAvailablePlayers(
        search as string,
        undefined
      );

      res.json({
        players
      });
    } catch (error: any) {
      next(error);
    }
  }

  async getAssociatedPlayers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const players = await this.userService.getUserAssociatedPlayers(req.user.id);

      res.json({
        players
      });
    } catch (error: any) {
      next(error);
    }
  }

  async checkPlayerOwnership(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { playerId } = req.params;

      if (!playerId) {
        res.status(400).json({ message: "Player ID is required" });
        return;
      }

      const isOwner = await this.userService.checkPlayerOwnership(req.user.id, playerId);

      res.json({
        isOwner
      });
    } catch (error: any) {
      next(error);
    }
  }
}
