import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-dashboard-stats-card',
  templateUrl: './dashboard-stats-card.component.html',
  styleUrl: './dashboard-stats-card.component.scss'
})
export class DashboardStatsCardComponent {
  @Input() icon!: string;
  @Input() title!: string;
  @Input() value!: string | number;
  @Input() subtitle?: string;
  @Input() trend?: 'up' | 'down' | 'neutral';
  @Input() trendValue?: string;
  @Input() color?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
}
