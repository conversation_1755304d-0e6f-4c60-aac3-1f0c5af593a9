import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-game-actions',
  templateUrl: './game-actions.component.html',
  styleUrl: './game-actions.component.scss'
})
export class GameActionsComponent {
  @Input() canEdit$!: Observable<boolean>;
  @Input() hasLiveStream: boolean = false;
  @Input() liveStreamUrl?: string;
  @Input() isGameTime: boolean = false;

  @Output() editGameClick = new EventEmitter<void>();
  @Output() liveStreamClick = new EventEmitter<void>();

  onEditGameClick(): void {
    this.editGameClick.emit();
  }

  onLiveStreamClick(): void {
    if (this.liveStreamUrl) {
      window.open(this.liveStreamUrl, '_blank', 'noopener,noreferrer');
    }
    this.liveStreamClick.emit();
  }
}
