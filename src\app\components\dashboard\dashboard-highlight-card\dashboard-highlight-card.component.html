<div class="highlight-card" [style.animation-delay]="animationDelay + 's'">
    <div class="card-header">
        <div class="header-content">
            <h3 class="card-title">
                <i class="fas" [class.fa-user]="data.type === 'player'" [class.fa-users]="data.type === 'team'"></i>
                {{ data.title }}
            </h3>
        </div>
        <div class="card-badge" *ngIf="data.badge">
            <i [class]="data.badge.icon" [style.color]="data.badge.color"></i>
        </div>
    </div>

    <div class="card-content">
        <div class="highlight-info">
            <div class="avatar-container">
                <img [src]="data.imageUrl || getDefaultImage()" 
                     [alt]="data.name"
                     class="avatar-image"
                     [class.player-avatar]="data.type === 'player'"
                     [class.team-avatar]="data.type === 'team'">
                <div class="avatar-overlay" *ngIf="data.badge">
                    <i [class]="data.badge.icon"></i>
                </div>
            </div>
            
            <div class="info-details">
                <h4 class="highlight-name">{{ data.name }}</h4>
                <div class="stats-badge">
                    {{ data.stats.secondary?.label || 'Performance' }}
                </div>
                <div class="primary-stat">
                    <span class="stat-value">{{ data.stats.primary.value }}</span>
                    <span class="stat-label">{{ data.stats.primary.label }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
