<div class="dashboard-topassists-container" *ngIf="topAssists; else noData">
    <div class="assists-list">
        <div class="assist-item"
             *ngFor="let assister of topAssists.slice(0, 5); let i = index"
             (click)="onPlayerClick(assister)"
             [class.first-place]="i === 0"
             [class.second-place]="i === 1"
             [class.third-place]="i === 2">

            <div class="rank-badge">
                <span class="rank-number">{{i + 1}}</span>
                <i class="fas fa-crown" *ngIf="i === 0"></i>
            </div>

            <div class="player-avatar">
                <img [src]="assister.playerImgUrl || 'assets/Icons/User.jpg'"
                     [alt]="assister.playerName">
            </div>

            <div class="player-details">
                <div class="player-name">{{assister.playerName}}</div>
                <div class="player-team">{{assister.teamName}}</div>
            </div>

            <div class="assists-stat">
                <span class="assists-number">{{assister.assists}}</span>
                <span class="assists-label">Assists</span>
            </div>
        </div>
    </div>
</div>

<ng-template #noData>
    <div class="loading-container">
        <pro-clubs-spinner></pro-clubs-spinner>
    </div>
</ng-template>