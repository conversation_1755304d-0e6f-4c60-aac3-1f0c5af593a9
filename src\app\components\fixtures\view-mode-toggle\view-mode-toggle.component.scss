.fixtures-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-color);

  .page-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;

    i {
      color: var(--primary);
      font-size: var(--font-size-lg);
    }
  }

  .view-toggle {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    box-shadow: var(--shadow-sm);

    .toggle-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      padding: var(--spacing-sm) var(--spacing-md);
      border: none;
      border-radius: var(--radius-md);
      background: transparent;
      color: var(--text-secondary);
      font-size: var(--font-size-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      white-space: nowrap;

      i {
        font-size: var(--font-size-sm);
      }

      &:hover {
        background: var(--surface-hover);
        color: var(--text-primary);
      }

      &.active {
        background: var(--primary);
        color: var(--text-on-primary);
        box-shadow: var(--shadow-sm);

        &:hover {
          background: var(--primary-hover);
        }
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        
        &:hover {
          background: transparent;
          color: var(--text-secondary);
        }
      }
    }
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .fixtures-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;

    .page-title {
      justify-content: center;
      font-size: var(--font-size-lg);
    }

    .view-toggle {
      justify-content: center;
      
      .toggle-btn {
        flex: 1;
        justify-content: center;
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
        
        span {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .fixtures-header {
    .view-toggle {
      .toggle-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        
        i {
          font-size: var(--font-size-xs);
        }
      }
    }
  }
}
