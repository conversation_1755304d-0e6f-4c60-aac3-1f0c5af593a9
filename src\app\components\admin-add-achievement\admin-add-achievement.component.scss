.admin-add-achievement-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  background: var(--background-color);
  color: var(--text-color);

  .header {
    margin-bottom: 30px;
    text-align: center;

    .back-btn {
      position: absolute;
      left: 20px;
      top: 20px;
      background: var(--secondary-color);
      color: var(--text-color);
      border: none;
      padding: 10px 15px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;

      &:hover {
        background: var(--primary-color);
        transform: translateY(-2px);
      }

      i {
        margin-right: 8px;
      }
    }

    h1 {
      color: var(--primary-color);
      margin-bottom: 10px;
      font-size: 2.5rem;

      i {
        margin-right: 15px;
        color: #ffd700;
      }
    }

    .subtitle {
      color: var(--text-secondary);
      font-size: 1.1rem;
      margin: 0;
    }
  }

  .form-container {
    background: var(--card-background);
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
  }

  .achievement-form {
    .form-group {
      margin-bottom: 25px;

      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: var(--text-color);
        font-size: 14px;
      }

      .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        background: var(--input-background);
        color: var(--text-color);
        font-size: 14px;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
        }

        &::placeholder {
          color: var(--text-secondary);
        }
      }

      .form-text {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 5px;
      }

      .error-message {
        color: var(--error-color);
        font-size: 12px;
        margin-top: 5px;
      }
    }

    .player-search-container {
      position: relative;

      .clear-btn {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
          background: var(--hover-color);
          color: var(--text-color);
        }
      }
    }

    .selected-player {
      display: flex;
      align-items: center;
      padding: 12px;
      background: var(--success-color-light);
      border: 2px solid var(--success-color);
      border-radius: 8px;
      margin-top: 10px;

      .player-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
        object-fit: cover;
      }

      .player-name {
        font-weight: 600;
        color: var(--text-color);
      }

      .player-team {
        color: var(--text-secondary);
        margin-left: 8px;
      }
    }

    .player-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: var(--card-background);
      border: 2px solid var(--border-color);
      border-top: none;
      border-radius: 0 0 8px 8px;
      max-height: 300px;
      overflow-y: auto;
      z-index: 1000;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

      .player-option {
        display: flex;
        align-items: center;
        padding: 12px;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
          background: var(--hover-color);
        }

        .player-avatar-small {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-right: 12px;
          object-fit: cover;
        }

        .player-info {
          display: flex;
          flex-direction: column;

          .player-name {
            font-weight: 500;
            color: var(--text-color);
          }

          .player-team {
            font-size: 12px;
            color: var(--text-secondary);
          }
        }
      }
    }

    .stats-section {
      background: var(--secondary-background);
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 25px;
      border: 1px solid var(--border-color);

      h3 {
        margin: 0 0 20px 0;
        color: var(--primary-color);
        font-size: 1.2rem;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;

        .form-group {
          margin-bottom: 0;
        }
      }
    }

    .form-actions {
      display: flex;
      gap: 15px;
      justify-content: flex-end;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid var(--border-color);

      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        &.btn-secondary {
          background: var(--secondary-color);
          color: var(--text-color);

          &:hover:not(:disabled) {
            background: var(--hover-color);
          }
        }

        &.btn-primary {
          background: var(--primary-color);
          color: white;

          &:hover:not(:disabled) {
            background: var(--primary-color-dark);
            transform: translateY(-2px);
          }
        }
      }
    }
  }

  .loading-container {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);

    i {
      font-size: 2rem;
      margin-bottom: 15px;
      color: var(--primary-color);
    }

    p {
      font-size: 1.1rem;
      margin: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .admin-add-achievement-container {
    padding: 15px;

    .header {
      .back-btn {
        position: static;
        margin-bottom: 20px;
      }

      h1 {
        font-size: 2rem;
      }
    }

    .form-container {
      padding: 20px;
    }

    .achievement-form {
      .stats-section {
        .stats-grid {
          grid-template-columns: 1fr;
        }
      }

      .form-actions {
        flex-direction: column;

        .btn {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
}
