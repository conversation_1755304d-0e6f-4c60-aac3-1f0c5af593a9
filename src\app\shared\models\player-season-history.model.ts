export interface PlayerSeasonHistoryData {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  currentSeason: {
    seasonId: string;
    seasonName: string;
    stats: {
      games: number;
      goals: number;
      assists: number;
      cleanSheets: number;
      playerOfTheMatch: number;
      avgRating: number;
    };
    team?: {
      id: string;
      name: string;
      imgUrl?: string;
    };
  };
  seasonHistory: Array<{
    seasonId: string;
    seasonName: string;
    stats: {
      games: number;
      goals: number;
      assists: number;
      cleanSheets: number;
      playerOfTheMatch: number;
      avgRating: number;
    };
    team?: {
      id: string;
      name: string;
      imgUrl?: string;
    };
  }>;
  careerTotals: {
    totalSeasons: number;
    totalGames: number;
    totalGoals: number;
    totalAssists: number;
    totalCleanSheets: number;
    totalPlayerOfTheMatch: number;
    careerAvgRating: number;
    bestSeason: {
      seasonId: string;
      seasonName: string;
      reason: string;
      value: number;
    };
  };
}
