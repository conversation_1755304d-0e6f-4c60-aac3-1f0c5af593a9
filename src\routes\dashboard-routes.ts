import { Router } from "express";
import DashboardController from "../controllers/dashboard-controller";
import { container } from "../config/container.config";
import { requireAdmin, authenticateToken } from "../middlewares/auth-middleware";

const router = Router();
const dashboardController = container.resolve(DashboardController);

router.get("/summary", (req, res, next) => dashboardController.getDashboardSummary(req, res, next));

// Admin-only cache management endpoints
router.post("/cache/warm", authenticateToken, requireAdmin, (req, res, next) => dashboardController.warmCache(req, res, next));
router.get("/cache/status", authenticateToken, requireAdmin, (req, res, next) => dashboardController.getCacheStatus(req, res, next));

export default router;
