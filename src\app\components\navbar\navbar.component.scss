/* === NAVBAR COMPONENT === */

.navbar {
    background: var(--surface-primary);
    border-bottom: 1px solid var(--border-primary);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    width: 100%;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 60px;
    padding: 0 var(--spacing-lg);

    @media (max-width: 768px) {
        padding: 0 var(--spacing-md);
        height: 56px;
        position: relative;
    }
}

/* === BRAND === */
.brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s ease;

    &:hover {
        transform: scale(1.02);
    }

    img {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-md);
    }

    span {
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
    }

    @media (max-width: 768px) {
        img {
            width: 32px;
            height: 32px;
        }

        span {
            font-size: var(--text-lg);
        }
    }
}

/* === MOBILE TOGGLE === */
.mobile-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    z-index: 1001;

    span {
        width: 24px;
        height: 3px;
        background: var(--text-primary);
        border-radius: var(--radius-sm);
        transition: all 0.3s ease;
    }

    @media (max-width: 768px) {
        display: flex;
        position: static; // Remove absolute positioning
        margin-left: auto; // Push to the right
    }
}

/* === NAVIGATION MENU === */
.nav-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: var(--spacing-xl);

    @media (max-width: 768px) {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--surface-primary);
        border-top: 1px solid var(--border-primary);
        flex-direction: column;
        padding: var(--spacing-md);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        flex: none;
        z-index: 1000;
        max-height: calc(100vh - 56px);
        overflow-y: auto;

        &.active {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }
    }
}

.nav-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    list-style: none;
    margin: 0;
    padding: 0;

    .nav-item {
        position: relative;

        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            transition: all 0.2s ease;

            &:hover, &.router-link-active {
                color: var(--primary-500);
                background: var(--primary-50);
            }

            i {
                font-size: var(--text-sm);
                width: 16px;
                text-align: center;
            }
        }
    }

    @media (max-width: 768px) {
        flex-direction: column;
        width: 100%;
        gap: var(--spacing-md);

        .nav-item {
            width: 100%;
        }
    }
}

/* === NAVIGATION DROPDOWN === */
.nav-dropdown {
    position: relative;

    .nav-dropdown-toggle {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: transparent;
        border: none;
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: var(--text-base);

        &:hover, &.active {
            color: var(--primary-500);
            background: var(--primary-50);
        }

        i {
            font-size: var(--text-sm);
            width: 16px;
            text-align: center;
        }

        .dropdown-arrow {
            margin-left: var(--spacing-xs);
            transition: transform 0.2s ease;
            font-size: var(--text-xs);

            &.rotated {
                transform: rotate(180deg);
            }
        }
    }

    .nav-dropdown-menu {
        position: absolute;
        top: calc(100% + var(--spacing-xs));
        left: 0;
        background: var(--surface-primary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        min-width: 220px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.2s ease;
        z-index: 1000;
        list-style: none;
        margin: 0;
        padding: var(--spacing-xs) 0;

        &.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        li {
            .nav-dropdown-link {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                padding: var(--spacing-sm) var(--spacing-md);
                color: var(--text-secondary);
                text-decoration: none;
                transition: all 0.2s ease;
                font-size: var(--text-sm);
                font-weight: var(--font-weight-medium);

                &:hover, &.router-link-active {
                    background: var(--surface-secondary);
                    color: var(--primary-500);
                }

                i {
                    width: 16px;
                    color: var(--text-tertiary);
                    font-size: var(--text-sm);
                    text-align: center;
                }

                &:hover i {
                    color: var(--primary-500);
                }
            }
        }
    }

    @media (max-width: 768px) {
        .nav-dropdown-menu {
            position: static;
            box-shadow: none;
            border: none;
            background: var(--surface-secondary);
            border-radius: var(--radius-md);
            margin-top: var(--spacing-xs);
            transform: none;
            opacity: 1;
            visibility: visible;
            display: none;

            &.show {
                display: block;
            }
        }
    }
}

/* === THEME TOGGLE === */
.theme-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;

    &:hover {
        background: var(--surface-tertiary);
        border-color: var(--primary-500);
        color: var(--primary-500);
        transform: translateY(-1px);
    }

    i {
        font-size: var(--text-sm);
        transition: transform 0.2s ease;
    }

    &:hover i {
        transform: rotate(15deg);
    }

    @media (max-width: 768px) {
        padding: var(--spacing-xs);
        gap: var(--spacing-xs);
        font-size: var(--text-xs);
        flex-shrink: 0;
        min-width: 32px;
        height: 32px;
        justify-content: center;

        span {
            display: none; // Always hide text on mobile
        }

        i {
            font-size: var(--text-sm);
        }
    }
}

/* === THEME SPECIFIC STYLES === */
[data-theme="dark"] {
    .nav-dropdown-menu {
        background: var(--surface-primary);
        border-color: var(--border-primary);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 4px 10px rgba(0, 0, 0, 0.2);
    }
}

[data-theme="light"] {
    .nav-dropdown-menu {
        background: var(--surface-primary);
        border-color: var(--border-primary);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .nav-link:hover, .nav-link.router-link-active {
        background: var(--primary-100);
    }

    .nav-dropdown-toggle:hover, .nav-dropdown-toggle.active {
        background: var(--primary-100);
    }
}

/* === NAV RIGHT CONTAINER === */
.nav-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    @media (max-width: 768px) {
        gap: var(--spacing-xs);
        margin-left: auto; // Push to the right
    }
}

/* === AUTH LINKS === */
.auth-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    @media (max-width: 768px) {
        display: none; // Hide auth links on mobile, show in mobile menu instead
    }

    a {
        color: var(--text-secondary);
        text-decoration: none;
        font-weight: var(--font-weight-medium);
        transition: color 0.2s ease;

        &:hover {
            color: var(--text-primary);
        }
    }
}

/* === MOBILE NAV ACTIONS === */
.nav-actions-mobile {
    display: none;

    @media (max-width: 768px) {
        display: flex !important;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        width: 100%;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) 0;
        border-top: 1px solid var(--border-primary);
        margin-top: var(--spacing-md);
        padding-top: var(--spacing-md);
        background: var(--surface-primary);
        position: relative;
        z-index: 10;
    }

    .auth-links {
        display: flex !important; // Override the desktop display: none
        align-items: center;
        gap: var(--spacing-lg);
        width: 100%;
        justify-content: center;

        a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
            font-size: var(--text-base);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            transition: all 0.2s ease;
            flex: 1;
            text-align: center;
            border: 1px solid var(--border-primary);
            background: var(--surface-secondary);

            &:hover {
                color: var(--text-primary);
                background: var(--surface-tertiary);
                border-color: var(--border-secondary);
            }

            &:first-child {
                background: var(--primary-500);
                color: white;
                border-color: var(--primary-500);

                &:hover {
                    background: var(--primary-600);
                    border-color: var(--primary-600);
                    color: white;
                }
            }
        }
    }
}



/* === USER PROFILE === */
.user-profile {
    position: relative;

    .dropdown {
        position: relative;
    }

    .user-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-sm) var(--spacing-md);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background: var(--surface-tertiary);
            border-color: var(--primary);
        }

        img {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            object-fit: cover;
        }

        span {
            color: var(--text-primary);
            font-weight: var(--font-weight-medium);
        }

        i {
            color: var(--text-tertiary);
            font-size: var(--text-xs);
            transition: transform 0.2s ease;
        }

        &:hover i {
            transform: rotate(180deg);
        }

        @media (max-width: 768px) {
            padding: var(--spacing-xs);
            gap: var(--spacing-xs);
            min-width: auto;

            img {
                width: 20px;
                height: 20px;
            }

            span {
                display: none; // Always hide username on mobile
            }

            i {
                display: none; // Hide dropdown arrow on mobile
            }
        }
    }

    .dropdown-menu {
        position: absolute;
        top: calc(100% + var(--spacing-xs));
        right: 0;
        background: var(--surface-primary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        min-width: 200px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.2s ease;
        z-index: 1000;

        &.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        @media (max-width: 768px) {
            min-width: 160px;
            right: -5px;
            font-size: var(--text-xs);
        }

        li {
            list-style: none;

            &.dropdown-header {
                padding: var(--spacing-sm) var(--spacing-md);
                font-size: var(--text-sm);
                font-weight: var(--font-weight-semibold);
                color: var(--text-primary);
                border-bottom: 1px solid var(--border-primary);
            }

            a {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                padding: var(--spacing-sm) var(--spacing-md);
                color: var(--text-secondary);
                text-decoration: none;
                transition: all 0.2s ease;

                &:hover {
                    background: var(--surface-secondary);
                    color: var(--text-primary);
                }

                i {
                    width: 16px;
                    color: var(--text-tertiary);
                }
            }

            &.linked-player-item {
                .linked-player-link {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: var(--spacing-md);
                    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
                    border-radius: var(--radius-md);
                    margin: var(--spacing-xs);
                    transition: all 0.2s ease;
                    cursor: pointer;
                    color: white;

                    &:hover {
                        background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
                        transform: translateY(-1px);
                        box-shadow: var(--shadow-sm);
                    }

                    .linked-player-info {
                        display: flex;
                        align-items: center;
                        gap: var(--spacing-sm);

                        .linked-player-avatar {
                            width: 32px;
                            height: 32px;
                            border-radius: var(--radius-full);
                            object-fit: cover;
                            border: 2px solid rgba(255, 255, 255, 0.3);
                        }

                        .linked-player-details {
                            display: flex;
                            flex-direction: column;

                            .linked-player-name {
                                font-size: var(--text-sm);
                                font-weight: var(--font-weight-semibold);
                                color: white;
                                line-height: 1.2;
                            }

                            .linked-player-position {
                                font-size: var(--text-xs);
                                color: rgba(255, 255, 255, 0.8);
                                line-height: 1.2;
                            }
                        }
                    }

                    i {
                        color: rgba(255, 255, 255, 0.9);
                        font-size: var(--text-sm);
                    }
                }
            }

            &.dropdown-divider {
                height: 1px;
                background: var(--border-primary);
                margin: var(--spacing-xs) 0;
                padding: 0;
            }
        }
    }
}

/* === THEME TOGGLE === */
.theme-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background: var(--surface-tertiary);
        border-color: var(--primary);
    }

    i {
        color: var(--text-secondary);
        transition: color 0.2s ease;
    }

    span {
        color: var(--text-secondary);
        font-size: var(--text-sm);
        font-weight: var(--font-weight-medium);
    }

    &:hover i,
    &:hover span {
        color: var(--primary);
    }
}
