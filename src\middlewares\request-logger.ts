import { Request, Response, NextFunction } from 'express';
import { LoggerUtils } from '../utils/logger-utils';

/**
 * Middleware to log all API requests with user information and response time
 */
export const requestLoggerMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  
  // Log the incoming request
  LoggerUtils.logApiRequest(req);
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any, cb?: () => void) {
    const responseTime = Date.now() - startTime;

    // Log the response with timing
    const context = LoggerUtils.getUserContext(req);
    const message = `API Response: ${req.method} ${req.originalUrl} - ${res.statusCode}`;
    LoggerUtils.info(message, context, {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      ip: req.ip
    });

    // Call the original end method and return the result
    return originalEnd.call(this, chunk, encoding, cb);
  };
  
  next();
};
