import { Component, Input, Output, EventEmitter } from '@angular/core';
import { GAME_STATUS } from '@pro-clubs-manager/shared-dtos';
import { ExtendedGameDTO } from '../../../shared/types/extended-game-dto';

@Component({
  selector: 'app-game-header',
  templateUrl: './game-header.component.html',
  styleUrl: './game-header.component.scss'
})
export class GameHeaderComponent {
  @Input() game!: ExtendedGameDTO;
  @Input() showBackButton: boolean = false;

  @Output() backClick = new EventEmitter<void>();
  @Output() homeTeamClick = new EventEmitter<string>();
  @Output() awayTeamClick = new EventEmitter<string>();

  readonly GAME_STATUS = GAME_STATUS;

  onBackClick(): void {
    this.backClick.emit();
  }

  onHomeTeamClick(): void {
    this.homeTeamClick.emit(this.game.homeTeam.id);
  }

  onAwayTeamClick(): void {
    this.awayTeamClick.emit(this.game.awayTeam.id);
  }

  getStatusClass(): string {
    return this.game.status.toLowerCase();
  }

  hasResult(): boolean {
    return this.game.result !== null && this.game.result !== undefined;
  }

  isScheduled(): boolean {
    return this.game.status === GAME_STATUS.SCHEDULED;
  }

  isCompleted(): boolean {
    return this.game.status === GAME_STATUS.PLAYED || this.game.status === GAME_STATUS.COMPLETED;
  }

  hasPenalties(): boolean {
    return this.hasResult() &&
           this.game.result?.penalties !== undefined &&
           this.game.result?.penalties !== null &&
           (this.game.result.penalties.homeTeamPenalties > 0 || this.game.result.penalties.awayTeamPenalties > 0);
  }
}
