import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { LeagueService } from '../../services/league.service';
import { ListOption } from '../../shared/models/list-option.model';
import { LEAGUE_ID } from '../../shared/constants/league.constants';

export interface TOTSPlayer {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  position: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  stats: {
    games: number;
    goals: number;
    assists: number;
    cleanSheets: number;
    avgRating: number;
    playerOfTheMatch: number;
    score: number;
  };
}

export interface TeamOfTheSeason {
  id: string;
  seasonNumber: number;
  formation: string;
  players: TOTSPlayer[];
  createdAt: Date;
}

@Component({
  selector: 'app-tots',
  templateUrl: './tots.component.html',
  styleUrls: ['./tots.component.scss']
})
export class TotsComponent implements OnInit {
  tots: TeamOfTheSeason | null = null;
  isLoading: boolean = false;
  availableSeasons: number[] = [];
  availableFormations: string[] = [];
  supportedFormations: string[] = [];
  
  selectedSeason: number | null = null;
  selectedFormation: string = '3-5-2';
  
  seasonOptions: ListOption[] = [];
  formationOptions: ListOption[] = [];
  
  // Formation layout for display
  formationLayout: { [formation: string]: string[][] } = {
    '3-5-2': [
      ['GK'],
      ['CB', 'CB', 'CB'],
      ['LM', 'CDM', 'CDM', 'RM'],
      ['CAM'],
      ['ST', 'ST']
    ],
    '4-3-3': [
      ['GK'],
      ['LM', 'CB', 'CB', 'RM'], // LB, CB, CB, RB
      ['CDM', 'CDM', 'CDM'],
      ['ST', 'ST', 'ST'] // LW, ST, RW
    ]
  };

  constructor(
    private leagueService: LeagueService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadInitialData();
  }

  async loadInitialData() {
    this.isLoading = true;
    
    try {
      // Load supported formations
      this.supportedFormations = await this.leagueService.getSupportedTOTSFormations();
      this.formationOptions = this.supportedFormations.map(formation => ({
        value: formation,
        displayText: formation
      }));
      
      // Load available seasons
      this.availableSeasons = await this.leagueService.getAvailableTOTSSeasons(LEAGUE_ID);
      this.seasonOptions = this.availableSeasons.map(season => ({
        value: season.toString(),
        displayText: `Season ${season}`
      }));
      
      // Set default to latest season if available
      if (this.availableSeasons.length > 0) {
        this.selectedSeason = this.availableSeasons[0]; // Most recent first
        await this.loadTOTS();
      }
      
    } catch (error) {
      console.error('Error loading initial data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  async onSeasonChange(season: ListOption) {
    if (!season) return;
    
    this.selectedSeason = parseInt(season.value);
    
    // Load available formations for this season
    try {
      this.availableFormations = await this.leagueService.getAvailableTOTSFormations(
        LEAGUE_ID, 
        this.selectedSeason
      );
      
      // Reset formation to first available or default
      if (this.availableFormations.length > 0) {
        this.selectedFormation = this.availableFormations.includes(this.selectedFormation) 
          ? this.selectedFormation 
          : this.availableFormations[0];
      }
      
      await this.loadTOTS();
    } catch (error) {
      console.error('Error loading formations for season:', error);
    }
  }

  async onFormationChange(formation: ListOption) {
    if (!formation) return;
    
    this.selectedFormation = formation.value;
    await this.loadTOTS();
  }

  async loadTOTS() {
    if (!this.selectedSeason) return;
    
    this.isLoading = true;
    
    try {
      this.tots = await this.leagueService.getTOTS(
        LEAGUE_ID,
        this.selectedSeason,
        this.selectedFormation
      );
    } catch (error) {
      console.error('Error loading TOTS:', error);
      this.tots = null;
    } finally {
      this.isLoading = false;
    }
  }

  getPlayersByPosition(position: string): TOTSPlayer[] {
    if (!this.tots) return [];
    
    return this.tots.players.filter(player => player.position === position);
  }

  getFormationRows(): string[][] {
    return this.formationLayout[this.selectedFormation] || [];
  }

  onPlayerClick(player: TOTSPlayer) {
    this.router.navigate(['/player', player.playerId]);
  }

  onTeamClick(player: TOTSPlayer) {
    this.router.navigate(['/team', player.teamId]);
  }

  async generateTOTS() {
    if (!this.selectedSeason) return;
    
    this.isLoading = true;
    
    try {
      await this.leagueService.generateTOTS(
        LEAGUE_ID,
        this.selectedSeason,
        this.selectedFormation
      );
      
      // Reload TOTS
      await this.loadTOTS();
    } catch (error) {
      console.error('Error generating TOTS:', error);
    } finally {
      this.isLoading = false;
    }
  }

  async regenerateTOTS() {
    if (!this.selectedSeason) return;
    
    this.isLoading = true;
    
    try {
      await this.leagueService.regenerateTOTS(
        LEAGUE_ID,
        this.selectedSeason,
        this.selectedFormation
      );
      
      // Reload TOTS
      await this.loadTOTS();
    } catch (error) {
      console.error('Error regenerating TOTS:', error);
    } finally {
      this.isLoading = false;
    }
  }

  getPositionDisplayName(position: string): string {
    const positionNames: { [key: string]: string } = {
      'GK': 'Goalkeeper',
      'CB': 'Centre Back',
      'LM': 'Left Mid / Left Back',
      'RM': 'Right Mid / Right Back',
      'CDM': 'Defensive Mid',
      'CAM': 'Attacking Mid',
      'ST': 'Striker / Winger'
    };
    
    return positionNames[position] || position;
  }
}
