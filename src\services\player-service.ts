import { ClientSession, Types } from "mongoose";
import { inject, injectable } from "tsyringe";
import logger from "../config/logger";
import { LoggerUtils } from "../utils/logger-utils";
import { IPlayerRepository, IPlayerService } from "../interfaces/player";
import { ITeamRepository } from "../interfaces/team";
import { IGameRepository } from "../interfaces/game";
import { ImageService } from "../interfaces/util-services/image-service.interface";
import { CacheService } from "../interfaces/util-services/cache-service.interface";
import { PlayerMapper } from "../mappers/player-mapper";
import { IPlayer, IPlayerSeason, ITransferHistoryEntry } from "../models/player/player";
import { PlayerComparisonData } from "../interfaces/player/player-comparison.interface";
import { PlayerSeasonHistoryData } from "../interfaces/player/player-season-history.interface";
import { PlayerDTO, CreatePlayerDataRequest } from "@pro-clubs-manager/shared-dtos";
import { PlayerGamePerformance } from "../models/game/game";
import Game from "../models/game/game";

const FREE_AGENTS_CACHE_KEY = "freeAgents";

@injectable()
export class PlayerService implements IPlayerService {
  private imageService: ImageService;
  private playerRepository: IPlayerRepository;
  private teamRepository: ITeamRepository;
  private gameRepository: IGameRepository;
  private cacheService: CacheService;

  constructor(
    @inject("IPlayerRepository") playerRepository: IPlayerRepository,
    @inject("ImageService") imageService: ImageService,
    @inject("ITeamRepository") teamRepository: ITeamRepository,
    @inject("IGameRepository") gameRepository: IGameRepository,
    @inject("CacheService") cacheService: CacheService
  ) {
    this.playerRepository = playerRepository;
    this.imageService = imageService;
    this.teamRepository = teamRepository;
    this.gameRepository = gameRepository;
    this.cacheService = cacheService;
  }
  async renamePlayer(id: string, newName: string): Promise<void> {
    logger.info(`PlayerService: renaming player with id ${id}`);

    await this.playerRepository.renamePlayer(id, newName);
  }

  async getPlayerById(id: string): Promise<PlayerDTO> {
    LoggerUtils.logDbOperation("GET", "players", undefined, { playerId: id });

    const player = await this.playerRepository.getPlayerById(id);

    // Calculate current season stats using game-by-game aggregation to handle mid-season transfers
    const currentSeasonStats = await this.calculateCurrentSeasonStats(player);

    // Debug logging for Itamar specifically
    if (id === '67f2bd3317ec7db8bcd4f087') {
      logger.info(`DEBUG - Itamar currentSeasonStats: ${JSON.stringify(currentSeasonStats)}`);
      logger.info(`DEBUG - Itamar player.currentSeason: ${JSON.stringify(player.currentSeason)}`);
    }

    const playerDto = await PlayerMapper.mapToDtoWithStats(player, currentSeasonStats);

    // Debug logging for Itamar specifically
    if (id === '67f2bd3317ec7db8bcd4f087') {
      logger.info(`DEBUG - Itamar final DTO stats: ${JSON.stringify(playerDto.stats)}`);
    }

    return playerDto;
  }

  async getPlayerByEmail(email: string, session?: ClientSession): Promise<PlayerDTO | null> {
    logger.info(`PlayerService: getting player with email ${email}`);

    const player = await this.playerRepository.getPlayerByEmail(email, session);

    if (!player) {
      return null;
    }

    return await PlayerMapper.mapToDto(player);
  }

  async getFreeAgents(session?: ClientSession): Promise<PlayerDTO[]> {
    logger.info(`PlayerService: getting free agents players`);

    // Try to get from cache first
    let freeAgents = await this.getFreeAgentsFromCache();
    if (!freeAgents) {
      logger.info(`calculating free agents from database`);
      const freeAgentsData = await this.playerRepository.getFreeAgents(session);
      freeAgents = await PlayerMapper.mapToDtos(freeAgentsData);

      // Cache for 6 hours
      await this.setFreeAgentsInCache(freeAgents);
    }

    return freeAgents;
  }

  async createPlayer(playerData: CreatePlayerDataRequest): Promise<PlayerDTO> {
    logger.info(`PlayerService: creating player with name ${playerData.name}`);

    if (!playerData.playablePositions) {
      playerData.playablePositions = [playerData.position];
    }

    const player = await this.playerRepository.createPlayer(playerData);
    return PlayerMapper.mapToDto(player);
  }

  async setPlayerImage(playerId: string, file: Express.Multer.File): Promise<string> {
    logger.info(`PlayerService: setting image for player with ${playerId}`);

    const player = await this.playerRepository.getPlayerById(playerId);

    if (player.imgUrl) {
      // remove current image from cloud
      await this.imageService.removeImage(player.imgUrl);
    }
    const imageUrl = await this.imageService.uploadImage(file);

    player.imgUrl = imageUrl;
    await player.save();

    return imageUrl;
  }

  async removePlayersFromTeam(playersIds: Types.ObjectId[], session: ClientSession): Promise<void> {
    logger.info(`PlayerService: removing ${playersIds.length} players from team`);
    const result = await this.playerRepository.removePlayersFromTeam(playersIds, session);

    // Clear free agents cache since players are now free agents
    await this.clearFreeAgentsCache();

    return result;
  }

  async updatePlayersGamePerformance(playersStats: PlayerGamePerformance[], session: ClientSession): Promise<void> {
    logger.info(`PlayerService: updating players game performance..`);
    const result = await this.playerRepository.updatePlayersGamePerformance(playersStats, session);

    // Note: League stats cache clearing is handled by the calling service to avoid circular dependencies

    return result;
  }

  async revertPlayersGamePerformance(playersStats: PlayerGamePerformance[], session: ClientSession): Promise<void> {
    logger.info(`PlayerService: reverting players game performance..`);
    const result = await this.playerRepository.revertPlayersGamePerformance(playersStats, session);

    // Note: League stats cache clearing is handled by the calling service to avoid circular dependencies

    return result;
  }

  async deletePlayer(player: IPlayer, session: ClientSession): Promise<void> {
    logger.info(`PlayerService: deleting player with id ${player.id}`);

    await this.playerRepository.deletePlayer(player.id, session);

    if (player.imgUrl) {
      await this.imageService.removeImage(player.imgUrl);
    }
  }

  async startNewSeason(teamId: Types.ObjectId, leagueId: Types.ObjectId, seasonNumber: number, session: ClientSession): Promise<void> {
    logger.info(`PlayerService: starting new season for players in team with id ${teamId}`);

    const players = await this.playerRepository.getPlayersByTeamId(teamId, session);
    const newSeason: IPlayerSeason = {
      league: leagueId,
      team: teamId,
      seasonNumber: seasonNumber,
      stats: {
        assists: 0,
        goals: 0,
        avgRating: 0,
        cleanSheets: 0,
        games: 0,
        playerOfTheMatch: 0,
      },
    };

    await Promise.all(
      players.map(async (player) => {
        if (player.currentSeason) {
          player.seasonsHistory.push(player.currentSeason);
        }
        player.currentSeason = newSeason;
        await player.save({ session });
      })
    );
  }

  async editPlayerAge(id: string, age: number): Promise<void> {
    logger.info(`PlayerService: setting player age for player id:  ${id}`);

    await this.playerRepository.editPlayerAge(id, age);
  }

  async editPlayerPosition(id: string, position: string): Promise<void> {
    logger.info(`PlayerService: setting player position for player id:  ${id}`);

    await this.playerRepository.editPlayerPosition(id, position);
  }

  async editPlayerPlayablePositions(id: string, playablePositions: string[]): Promise<void> {
    logger.info(`PlayerService: setting player playable positions for player id:  ${id}`);

    await this.playerRepository.editPlayerPlayablePositions(id, playablePositions);
  }

  async playerSearchByText(searchText: string, session?: ClientSession): Promise<PlayerDTO[]> {
    logger.info(`PlayerService: player search for text: ${searchText}`);

    const playerSearchResponse = await this.playerRepository.playerSearchByText(searchText, session);

    return await PlayerMapper.mapToDtos(playerSearchResponse);
  }


  async getAllPlayers(): Promise<PlayerDTO[]> {
    logger.info(`PlayerService: getting all players`);

    const allPlayers = await this.playerRepository.getAllPlayers();

    return await PlayerMapper.mapToDtos(allPlayers);
  }

  async getTransferHistoryByPlayerId(playerId: string): Promise<ITransferHistoryEntry[]> {
    logger.info(`PlayerService: getting transfer history for player ${playerId}`);

    const player = await this.playerRepository.getPlayerById(playerId);

    return player.transferHistory || [];
  }

  async comparePlayersById(player1Id: string, player2Id: string): Promise<PlayerComparisonData> {
    logger.info(`PlayerService: comparing players ${player1Id} and ${player2Id}`);

    const [player1, player2] = await Promise.all([
      this.playerRepository.getPlayerById(player1Id),
      this.playerRepository.getPlayerById(player2Id)
    ]);

    const player1Stats = PlayerMapper.calculatePlayerStats(player1);
    const player2Stats = PlayerMapper.calculatePlayerStats(player2);

    // Calculate per-game statistics
    const player1GoalsPerGame = player1Stats.games > 0 ? player1Stats.goals / player1Stats.games : 0;
    const player2GoalsPerGame = player2Stats.games > 0 ? player2Stats.goals / player2Stats.games : 0;

    const player1AssistsPerGame = player1Stats.games > 0 ? player1Stats.assists / player1Stats.games : 0;
    const player2AssistsPerGame = player2Stats.games > 0 ? player2Stats.assists / player2Stats.games : 0;

    const player1CleanSheetsPerGame = player1Stats.games > 0 ? player1Stats.cleanSheets / player1Stats.games : 0;
    const player2CleanSheetsPerGame = player2Stats.games > 0 ? player2Stats.cleanSheets / player2Stats.games : 0;

    const player1PotmPerGame = player1Stats.games > 0 ? player1Stats.playerOfTheMatch / player1Stats.games : 0;
    const player2PotmPerGame = player2Stats.games > 0 ? player2Stats.playerOfTheMatch / player2Stats.games : 0;

    // Get team information
    const player1Team = player1.team ? await this.teamRepository.getTeamById(player1.team.toString()) : null;
    const player2Team = player2.team ? await this.teamRepository.getTeamById(player2.team.toString()) : null;

    return {
      player1: {
        id: player1.id,
        name: player1.name,
        imgUrl: player1.imgUrl,
        age: player1.age,
        position: player1.position,
        team: player1Team ? {
          id: player1Team.id,
          name: player1Team.name,
          imgUrl: player1Team.imgUrl
        } : undefined,
        stats: player1Stats
      },
      player2: {
        id: player2.id,
        name: player2.name,
        imgUrl: player2.imgUrl,
        age: player2.age,
        position: player2.position,
        team: player2Team ? {
          id: player2Team.id,
          name: player2Team.name,
          imgUrl: player2Team.imgUrl
        } : undefined,
        stats: player2Stats
      },
      comparison: {
        gamesDifference: player1Stats.games - player2Stats.games,
        goalsDifference: player1Stats.goals - player2Stats.goals,
        assistsDifference: player1Stats.assists - player2Stats.assists,
        cleanSheetsDifference: player1Stats.cleanSheets - player2Stats.cleanSheets,
        potmDifference: player1Stats.playerOfTheMatch - player2Stats.playerOfTheMatch,
        avgRatingDifference: player1Stats.avgRating - player2Stats.avgRating,
        goalsPerGame: {
          player1: player1GoalsPerGame,
          player2: player2GoalsPerGame,
          difference: player1GoalsPerGame - player2GoalsPerGame
        },
        assistsPerGame: {
          player1: player1AssistsPerGame,
          player2: player2AssistsPerGame,
          difference: player1AssistsPerGame - player2AssistsPerGame
        },
        cleanSheetsPerGame: {
          player1: player1CleanSheetsPerGame,
          player2: player2CleanSheetsPerGame,
          difference: player1CleanSheetsPerGame - player2CleanSheetsPerGame
        },
        potmPerGame: {
          player1: player1PotmPerGame,
          player2: player2PotmPerGame,
          difference: player1PotmPerGame - player2PotmPerGame
        }
      }
    };
  }

  async getPlayerSeasonHistory(playerId: string): Promise<PlayerSeasonHistoryData> {
    logger.info(`PlayerService: getting season history for player ${playerId} using game-by-game calculation`);

    const player = await this.playerRepository.getPlayerById(playerId);

    // Get current team info
    const currentTeam = player.team ? await this.teamRepository.getTeamById(player.team.toString()) : null;

    // Get all games where this player participated and calculate stats by season
    const playerGames = await Game.aggregate([
      {
        $match: {
          $or: [
            { "homeTeamPlayersPerformance.playerId": new Types.ObjectId(playerId) },
            { "awayTeamPlayersPerformance.playerId": new Types.ObjectId(playerId) }
          ]
        }
      },
      {
        $project: {
          league: 1,
          seasonNumber: 1,
          players: {
            $concatArrays: [
              { $ifNull: ["$homeTeamPlayersPerformance", []] },
              { $ifNull: ["$awayTeamPlayersPerformance", []] }
            ]
          }
        }
      },
      { $unwind: "$players" },
      { $match: { "players.playerId": new Types.ObjectId(playerId) } },
      {
        $group: {
          _id: { league: "$league", seasonNumber: "$seasonNumber" },
          totalGames: { $sum: 1 },
          totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
          totalAssists: { $sum: { $ifNull: ["$players.assists", 0] } },
          totalCleanSheets: { $sum: { $cond: [{ $eq: ["$players.cleanSheet", true] }, 1, 0] } },
          totalPotm: { $sum: { $cond: [{ $eq: ["$players.playerOfTheMatch", true] }, 1, 0] } },
          avgRating: { $avg: "$players.rating" }
        }
      },
      { $sort: { "_id.seasonNumber": 1 } }
    ]);

    // Process season history
    const seasonHistory = [];
    let totalGames = 0;
    let totalGoals = 0;
    let totalAssists = 0;
    let totalCleanSheets = 0;
    let totalPotm = 0;
    let totalRatingSum = 0;
    let bestSeason = {
      seasonId: 'current',
      seasonName: 'Current Season',
      reason: 'Best Rating',
      value: 0
    };

    // Process game-calculated seasons
    for (const gameSeasonData of playerGames) {
      const seasonNumber = gameSeasonData._id.seasonNumber;
      const seasonStats = {
        games: gameSeasonData.totalGames,
        goals: gameSeasonData.totalGoals,
        assists: gameSeasonData.totalAssists,
        cleanSheets: gameSeasonData.totalCleanSheets,
        playerOfTheMatch: gameSeasonData.totalPotm,
        avgRating: gameSeasonData.avgRating || 0
      };

      // Find the corresponding season in player's history to get team info
      let seasonTeam = null;
      const playerSeason = player.seasonsHistory?.find(s => s.seasonNumber === seasonNumber) ||
                          (player.currentSeason?.seasonNumber === seasonNumber ? player.currentSeason : null);

      if (playerSeason?.team) {
        try {
          seasonTeam = await this.teamRepository.getTeamById(playerSeason.team.toString());
        } catch (error) {
          logger.warn(`Could not find team ${playerSeason.team} for season ${seasonNumber}`);
        }
      }

      // Determine if this is the current season
      const isCurrentSeason = player.currentSeason?.seasonNumber === seasonNumber;
      const seasonId = isCurrentSeason ? 'current' : `season-${seasonNumber}`;
      const seasonName = isCurrentSeason ? 'Current Season' : `Season ${seasonNumber}`;

      seasonHistory.push({
        seasonId,
        seasonName,
        stats: seasonStats,
        team: seasonTeam ? {
          id: seasonTeam.id,
          name: seasonTeam.name,
          imgUrl: seasonTeam.imgUrl
        } : undefined
      });

      // Add to totals
      totalGames += seasonStats.games;
      totalGoals += seasonStats.goals;
      totalAssists += seasonStats.assists;
      totalCleanSheets += seasonStats.cleanSheets;
      totalPotm += seasonStats.playerOfTheMatch;
      totalRatingSum += seasonStats.avgRating * seasonStats.games;

      // Check if this is the best season
      if (seasonStats.goals > bestSeason.value) {
        bestSeason = {
          seasonId,
          seasonName,
          reason: 'Most Goals',
          value: seasonStats.goals
        };
      } else if (seasonStats.avgRating > bestSeason.value && bestSeason.reason === 'Best Rating') {
        bestSeason = {
          seasonId,
          seasonName,
          reason: 'Best Rating',
          value: seasonStats.avgRating
        };
      }
    }

    const careerAvgRating = totalGames > 0 ? totalRatingSum / totalGames : 0;

    // Find current season data for display
    const currentSeasonData = seasonHistory.find(s => s.seasonId === 'current');
    const currentSeasonStats = currentSeasonData?.stats || {
      games: 0,
      goals: 0,
      assists: 0,
      cleanSheets: 0,
      playerOfTheMatch: 0,
      avgRating: 0
    };

    return {
      playerId: player.id,
      playerName: player.name,
      playerImgUrl: player.imgUrl,
      currentSeason: {
        seasonId: 'current',
        seasonName: 'Current Season',
        stats: currentSeasonStats,
        team: currentTeam ? {
          id: currentTeam.id,
          name: currentTeam.name,
          imgUrl: currentTeam.imgUrl
        } : undefined
      },
      seasonHistory: seasonHistory.filter(s => s.seasonId !== 'current').reverse(), // Most recent first, exclude current
      careerTotals: {
        totalSeasons: playerGames.length,
        totalGames,
        totalGoals,
        totalAssists,
        totalCleanSheets,
        totalPlayerOfTheMatch: totalPotm,
        careerAvgRating,
        bestSeason
      }
    };
  }

  async deletePlayerSeasonHistory(playerId: string, seasonNumber: number): Promise<void> {
    logger.info(`PlayerService: deleting season ${seasonNumber} history for player ${playerId}`);

    const player = await this.playerRepository.getPlayerById(playerId);

    if (!player.seasonsHistory || player.seasonsHistory.length === 0) {
      throw new Error("Player has no season history to delete");
    }

    // Find and remove the season
    const seasonIndex = player.seasonsHistory.findIndex(season => season.seasonNumber === seasonNumber);

    if (seasonIndex === -1) {
      throw new Error(`Season ${seasonNumber} not found in player's history`);
    }

    // Remove the season from history
    player.seasonsHistory.splice(seasonIndex, 1);

    // Save the updated player
    await player.save();

    logger.info(`Successfully deleted season ${seasonNumber} from player ${playerId} history`);
  }

  // Cache helper methods for free agents
  private async getFreeAgentsFromCache(): Promise<PlayerDTO[] | null> {
    const cachedData = await this.cacheService.get(FREE_AGENTS_CACHE_KEY);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }

  private async setFreeAgentsInCache(freeAgents: PlayerDTO[]): Promise<void> {
    // Cache for 6 hours
    await this.cacheService.set(FREE_AGENTS_CACHE_KEY, freeAgents, 6 * 60 * 60 * 1000);
  }

  // Method to clear free agents cache (useful when players are added/removed from teams)
  async clearFreeAgentsCache(): Promise<void> {
    await this.cacheService.delete(FREE_AGENTS_CACHE_KEY);
  }

  /**
   * Calculate current season stats using game-by-game aggregation
   * This ensures stats are preserved even when players transfer mid-season
   */
  private async calculateCurrentSeasonStats(player: IPlayer): Promise<{ goals: number; assists: number; games: number } | null> {
    if (!player.currentSeason) {
      return null;
    }

    try {
      const currentSeasonNumber = player.currentSeason.seasonNumber;
      const leagueId = player.currentSeason.league.toString();

      // Debug logging for Itamar specifically
      if (player.id === '67f2bd3317ec7db8bcd4f087') {
        logger.info(`DEBUG - Itamar calculateCurrentSeasonStats: leagueId=${leagueId}, seasonNumber=${currentSeasonNumber}`);
      }

      // Use the same aggregation method as the top scorers
      const result = await this.gameRepository.aggregatePlayerStatsForSeason(leagueId, currentSeasonNumber);

      // Debug logging for Itamar specifically
      if (player.id === '67f2bd3317ec7db8bcd4f087') {
        logger.info(`DEBUG - Itamar aggregation result length: ${result.length}`);
      }

      // Find this player's stats in the aggregation result
      const playerStats = result.find(stats => stats._id.toString() === player.id);

      // Debug logging for Itamar specifically
      if (player.id === '67f2bd3317ec7db8bcd4f087') {
        logger.info(`DEBUG - Itamar playerStats found: ${JSON.stringify(playerStats)}`);
      }

      if (playerStats) {
        const stats = {
          goals: playerStats.totalGoals,
          assists: playerStats.totalAssists,
          games: playerStats.totalGames
        };

        // Debug logging for Itamar specifically
        if (player.id === '67f2bd3317ec7db8bcd4f087') {
          logger.info(`DEBUG - Itamar returning stats: ${JSON.stringify(stats)}`);
        }

        return stats;
      }

      // If no stats found in aggregation, return zeros
      if (player.id === '67f2bd3317ec7db8bcd4f087') {
        logger.info(`DEBUG - Itamar no stats found, returning zeros`);
      }
      return { goals: 0, assists: 0, games: 0 };
    } catch (error) {
      logger.error(`Error calculating current season stats for player ${player.id}:`, error);
      // Fallback to the old method if aggregation fails
      return null;
    }
  }
}
