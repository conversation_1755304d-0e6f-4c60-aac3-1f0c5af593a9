"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
require("reflect-metadata");
const mock_game_repository_1 = require("../../../src/mocks/repositories/mock-game-repository");
const mock_player_repository_1 = require("../../../src/mocks/repositories/mock-player-repository");
const wrapper_services_1 = require("../../../src/services/wrapper-services");
describe("PlayerStatsService", () => {
    let playerStatsService;
    let mockGameRepository;
    let mockPlayerRepository;
    beforeAll(() => {
        mockGameRepository = new mock_game_repository_1.MockGameRepository();
        mockPlayerRepository = new mock_player_repository_1.MockPlayerRepository();
        playerStatsService = new wrapper_services_1.PlayerStatsService(mockPlayerRepository, mockGameRepository);
    });
    describe("getPlayerStatsByPosition", () => {
        let playerId;
        beforeAll(() => {
            playerId = new mongoose_1.Types.ObjectId();
            const leagueId = new mongoose_1.Types.ObjectId();
            const mockPlayer = {
                id: playerId.toString(),
                currentSeason: {
                    league: leagueId,
                    seasonNumber: 1,
                },
                _id: playerId,
            };
            jest.spyOn(mockPlayerRepository, "getPlayerById").mockResolvedValue(mockPlayer);
        });
        it("should return player stats grouped by position", () => __awaiter(void 0, void 0, void 0, function* () {
            const games = [
                {
                    homeTeamPlayersPerformance: [
                        {
                            playerId,
                            positionPlayed: "Forward",
                            goals: 2,
                            assists: 1,
                            rating: 8.0,
                            cleanSheet: true,
                            playerOfTheMatch: true,
                        },
                    ],
                    awayTeamPlayersPerformance: [],
                },
                {
                    homeTeamPlayersPerformance: [],
                    awayTeamPlayersPerformance: [
                        {
                            playerId,
                            positionPlayed: "Midfielder",
                            goals: 1,
                            assists: 0,
                            rating: 7.5,
                            cleanSheet: false,
                            playerOfTheMatch: false,
                        },
                    ],
                },
            ];
            jest.spyOn(mockGameRepository, "getPlayerPlayedSeasonGames").mockResolvedValue(games);
            const result = yield playerStatsService.getPlayerStatsByPosition(playerId.toString());
            expect(result.Forward.games).toBe(1);
            expect(result.Forward.goals).toBe(2);
            expect(result.Forward.assists).toBe(1);
            expect(result.Forward.cleanSheets).toBe(1);
            expect(result.Forward.playerOfTheMatch).toBe(1);
            expect(result.Forward.avgRating).toBe(8.0);
            expect(result.Midfielder.games).toBe(1);
            expect(result.Midfielder.goals).toBe(1);
            expect(result.Midfielder.assists).toBe(0);
            expect(result.Midfielder.cleanSheets).toBe(0);
            expect(result.Midfielder.playerOfTheMatch).toBe(0);
            expect(result.Midfielder.avgRating).toBe(7.5);
        }));
        it("should return an empty object if the player is not in an active season", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockPlayer = {
                id: playerId.toString(),
                currentSeason: undefined,
            };
            jest.spyOn(mockPlayerRepository, "getPlayerById").mockResolvedValue(mockPlayer);
            const result = yield playerStatsService.getPlayerStatsByPosition(playerId.toString());
            expect(result).toEqual({});
        }));
        it("should throw an error if player performance is not found", () => __awaiter(void 0, void 0, void 0, function* () {
            const games = [
                {
                    homeTeamPlayersPerformance: [],
                    awayTeamPlayersPerformance: [],
                },
            ];
            jest.spyOn(mockGameRepository, "getPlayerPlayedSeasonGames").mockResolvedValue(games);
            yield expect(playerStatsService.getPlayerStatsByPosition(playerId.toString())).rejects.toThrow(`Failed to fetch player ${playerId} stats by position `);
        }));
    });
    describe("getLastFiveGamesPerformance", () => {
        let playerId;
        beforeAll(() => {
            playerId = new mongoose_1.Types.ObjectId();
            const leagueId = new mongoose_1.Types.ObjectId();
            const mockPlayer = {
                id: playerId.toString(),
                currentSeason: {
                    league: leagueId,
                    seasonNumber: 1,
                },
                _id: playerId,
            };
            jest.spyOn(mockPlayerRepository, "getPlayerById").mockResolvedValue(mockPlayer);
        });
        it("should return the last five games performance with total goals and assists", () => __awaiter(void 0, void 0, void 0, function* () {
            const games = [
                {
                    id: new mongoose_1.Types.ObjectId().toString(),
                    league: { id: new mongoose_1.Types.ObjectId().toString(), name: "League 1" },
                    round: 1,
                    date: new Date(),
                    homeTeamPlayersPerformance: [
                        {
                            playerId,
                            goals: 1,
                            assists: 2,
                            rating: 8.0,
                            positionPlayed: "Forward",
                        },
                    ],
                    awayTeamPlayersPerformance: [],
                    result: { homeTeamGoals: 3, awayTeamGoals: 1 },
                    homeTeam: { id: new mongoose_1.Types.ObjectId().toString(), name: "Team A", imgUrl: "imgA.jpg" },
                    awayTeam: { id: new mongoose_1.Types.ObjectId().toString(), name: "Team B", imgUrl: "imgB.jpg" },
                },
                {
                    id: new mongoose_1.Types.ObjectId().toString(),
                    league: { id: new mongoose_1.Types.ObjectId().toString(), name: "League 1" },
                    round: 2,
                    date: new Date(),
                    homeTeamPlayersPerformance: [],
                    awayTeamPlayersPerformance: [
                        {
                            playerId,
                            goals: 0,
                            assists: 1,
                            rating: 7.5,
                            positionPlayed: "Midfielder",
                        },
                    ],
                    result: { homeTeamGoals: 2, awayTeamGoals: 2 },
                    homeTeam: { id: new mongoose_1.Types.ObjectId().toString(), name: "Team C", imgUrl: "imgC.jpg" },
                    awayTeam: { id: new mongoose_1.Types.ObjectId().toString(), name: "Team D", imgUrl: "imgD.jpg" },
                },
            ];
            jest.spyOn(mockGameRepository, "getPlayerLastGames").mockResolvedValue(games);
            const result = yield playerStatsService.getLastFiveGamesPerformance(playerId.toString());
            expect(result.lastGames.length).toBe(2);
            expect(result.totalGoals).toBe(1);
            expect(result.totalAssists).toBe(3);
            expect(result.lastGames[0].gameId).toBe(games[0].id);
            expect(result.lastGames[0].rating).toBe(8.0);
            expect(result.lastGames[0].goals).toBe(1);
            expect(result.lastGames[0].assists).toBe(2);
            expect(result.lastGames[0].homeTeam.name).toBe("Team A");
            expect(result.lastGames[0].awayTeam.name).toBe("Team B");
            expect(result.lastGames[1].gameId).toBe(games[1].id);
            expect(result.lastGames[1].rating).toBe(7.5);
            expect(result.lastGames[1].goals).toBe(0);
            expect(result.lastGames[1].assists).toBe(1);
            expect(result.lastGames[1].homeTeam.name).toBe("Team C");
            expect(result.lastGames[1].awayTeam.name).toBe("Team D");
        }));
        it("should return empty last games if player is not in an active season", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockPlayer = {
                id: playerId.toString(),
                currentSeason: undefined,
            };
            jest.spyOn(mockPlayerRepository, "getPlayerById").mockResolvedValue(mockPlayer);
            const result = yield playerStatsService.getLastFiveGamesPerformance(playerId.toString());
            expect(result.lastGames.length).toBe(0);
            expect(result.totalGoals).toBe(0);
            expect(result.totalAssists).toBe(0);
        }));
        it("should throw an error if player performance is not found in a game", () => __awaiter(void 0, void 0, void 0, function* () {
            const games = [
                {
                    id: new mongoose_1.Types.ObjectId().toString(),
                    homeTeamPlayersPerformance: [],
                    awayTeamPlayersPerformance: [],
                },
            ];
            jest.spyOn(mockGameRepository, "getPlayerLastGames").mockResolvedValue(games);
            yield expect(playerStatsService.getLastFiveGamesPerformance(playerId.toString())).rejects.toThrow(`failed to get last games of player with id ${playerId}`);
        }));
    });
});
