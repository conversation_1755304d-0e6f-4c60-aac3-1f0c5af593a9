<div class="create-team-container">
    <div class="create-team-card">
        <div class="create-team-header">
            <h1 class="create-team-title">
                <i class="fas fa-users"></i>
                Add Team
            </h1>
            <p class="create-team-subtitle">Create a new team for the league</p>
        </div>

        <form [formGroup]="addTeamFormGroup" (ngSubmit)="onSubmit()" class="create-team-form">
            <div class="form-group">
                <label class="form-label">
                    Team Name
                    <span class="required-indicator">*</span>
                </label>
                <input
                    [formControlName]="'name'"
                    placeholder="Enter team name"
                    class="form-input"
                    [class.error]="addTeamFormGroup.get('name')?.invalid && addTeamFormGroup.get('name')?.touched">
            </div>

            <div class="file-upload-section">
                <label class="file-upload-label">Team Logo</label>
                <div class="file-upload-button" (click)="fileInput.click()">
                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                    <div class="upload-text">Upload Team Logo</div>
                    <div class="upload-hint">Click to select an image file</div>
                </div>
                <input
                    type="file"
                    #fileInput
                    id="urlPhoto"
                    class="file-input"
                    (change)="onFileSelected($event)"
                    accept="image/*">
            </div>

            <button
                type="submit"
                class="submit-button"
                [disabled]="addTeamFormGroup.invalid">
                <i class="fas fa-plus"></i>
                Create Team
            </button>
        </form>
    </div>
</div>