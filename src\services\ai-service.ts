import { injectable } from "tsyringe";
import OpenAI from "openai";
import logger from "../config/logger";
import { BadRequestError } from "../errors";

export interface ExtractedPlayerData {
  name: string;
  position: string;
  goals: number;
  assists: number;
  rating: number;
  isPlayerOfMatch?: boolean;
}

export interface PlayerStatsExtractionResult {
  success: boolean;
  players: ExtractedPlayerData[];
  error?: string;
}

export interface PlayerMatchResult {
  extractedName: string;
  matchedPlayer?: {
    id: string;
    name: string;
    confidence: number;
  };
  position: string;
  goals: number;
  assists: number;
  rating: number;
  isPlayerOfMatch?: boolean;
}

@injectable()
export class AIService {
  private openai: OpenAI;

  constructor() {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error("OPENAI_API_KEY environment variable is required");
    }
    
    this.openai = new OpenAI({
      apiKey: apiKey,
    });
  }

  /**
   * Extract player stats from uploaded image using OpenAI Vision
   */
  async extractPlayerStatsFromImage(imageBuffer: Buffer, mimeType: string): Promise<PlayerStatsExtractionResult> {
    try {
      logger.info("Starting AI analysis of player stats image");

      // Convert buffer to base64
      const base64Image = imageBuffer.toString('base64');
      const dataUrl = `data:${mimeType};base64,${base64Image}`;

      const response = await this.openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `Analyze this FIFA Pro Clubs player statistics image and extract the following information for each player:

1. Player name (exactly as shown)
2. Position (GK, DEF, MID, ATT, or specific positions like LB, CB, RB, LM, CM, RM, LW, ST, RW, etc.)
3. Goals scored (integer number)
4. Assists made (integer number)
5. Rating (decimal number, e.g., 7.5, 8.2, 6.8)
6. Player of the match indicator (look for yellow ball, star, or special highlighting near a player)

Return ONLY a valid JSON object with this exact structure:
{
  "success": true,
  "players": [
    {
      "name": "Player Name",
      "position": "Position",
      "goals": 0,
      "assists": 0,
      "rating": 7.0,
      "isPlayerOfMatch": false
    }
  ]
}

Important rules:
- Extract ALL visible players from the stats table
- Use exact player names as they appear in the image
- Convert goals and assists to integer numbers (not strings)
- Convert rating to decimal number (e.g., 7.5, not 7 or "7.5")
- Set isPlayerOfMatch to true ONLY if you see a yellow ball or special indicator next to that player
- If you cannot read the image clearly, return: {"success": false, "error": "Could not analyze image"}
- Return ONLY the JSON, no other text or explanation`
              },
              {
                type: "image_url",
                image_url: {
                  url: dataUrl
                }
              }
            ]
          }
        ],
        max_tokens: 1500,
        temperature: 0.1
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error("No response content from OpenAI");
      }

      // Parse the JSON response
      let result: PlayerStatsExtractionResult;
      try {
        // Try to extract JSON from the response (in case it's wrapped in markdown or has extra text)
        let jsonContent = content.trim();

        // Remove markdown code blocks if present
        if (jsonContent.startsWith('```json')) {
          jsonContent = jsonContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (jsonContent.startsWith('```')) {
          jsonContent = jsonContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        // Try to find JSON object in the response
        const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonContent = jsonMatch[0];
        }

        result = JSON.parse(jsonContent);
      } catch (parseError) {
        logger.error("Failed to parse OpenAI response:", content);
        logger.error("Parse error:", parseError);
        return {
          success: false,
          players: [],
          error: "Failed to parse AI response"
        };
      }

      if (result.success && result.players && result.players.length > 0) {
        logger.info(`Successfully extracted stats for ${result.players.length} players`);
        return result;
      } else {
        return {
          success: false,
          players: [],
          error: result.error || "No players found in image"
        };
      }

    } catch (error: any) {
      logger.error("Error in AI image analysis:", error);
      return {
        success: false,
        players: [],
        error: error.message || "AI analysis failed"
      };
    }
  }

  /**
   * Normalize position codes according to user requirements
   */
  private normalizePosition(position: string): string {
    const normalizedPos = position.toUpperCase().trim();

    // Position mappings as requested
    const positionMap: { [key: string]: string } = {
      // Defensive midfield variations -> CDM
      'RDM': 'CDM',
      'LDM': 'CDM',

      // Central midfield variations -> CM
      'LCM': 'CM',
      'RCM': 'CM',

      // Attacking midfield variations -> CAM
      'LAM': 'CAM',
      'RAM': 'CAM',

      // Center back variations -> CB
      'RCB': 'CB',
      'LCB': 'CB'
    };

    return positionMap[normalizedPos] || normalizedPos;
  }

  /**
   * Match extracted player names with existing players in the database
   */
  matchPlayersWithDatabase(
    extractedPlayers: ExtractedPlayerData[],
    databasePlayers: Array<{id: string, name: string}>
  ): PlayerMatchResult[] {
    return extractedPlayers.map(extracted => {
      const match = this.findBestPlayerMatch(extracted.name, databasePlayers);

      return {
        extractedName: extracted.name,
        matchedPlayer: match,
        position: this.normalizePosition(extracted.position),
        goals: parseInt(extracted.goals.toString()) || 0,
        assists: parseInt(extracted.assists.toString()) || 0,
        rating: parseFloat(extracted.rating.toString()) || 0.0,
        isPlayerOfMatch: extracted.isPlayerOfMatch
      };
    });
  }

  /**
   * Find the best matching player using fuzzy string matching
   */
  private findBestPlayerMatch(
    extractedName: string, 
    databasePlayers: Array<{id: string, name: string}>
  ): {id: string, name: string, confidence: number} | undefined {
    let bestMatch: {id: string, name: string, confidence: number} | undefined;
    let highestScore = 0;

    for (const dbPlayer of databasePlayers) {
      const confidence = this.calculateNameSimilarity(extractedName, dbPlayer.name);
      
      // Only consider matches with confidence > 0.6 (60%)
      if (confidence > 0.6 && confidence > highestScore) {
        highestScore = confidence;
        bestMatch = {
          id: dbPlayer.id,
          name: dbPlayer.name,
          confidence: confidence
        };
      }
    }

    return bestMatch;
  }

  /**
   * Calculate similarity between two names using multiple algorithms
   */
  private calculateNameSimilarity(name1: string, name2: string): number {
    // Normalize names for comparison
    const normalize = (name: string) => name.toLowerCase().trim().replace(/[^\w\s]/g, '');
    const n1 = normalize(name1);
    const n2 = normalize(name2);

    // Exact match
    if (n1 === n2) return 1.0;

    // Check if one name contains the other
    if (n1.includes(n2) || n2.includes(n1)) return 0.9;

    // Levenshtein distance
    const levenshteinScore = 1 - (this.levenshteinDistance(n1, n2) / Math.max(n1.length, n2.length));
    
    // Word-based matching (for names like "John Smith" vs "J. Smith")
    const words1 = n1.split(/\s+/);
    const words2 = n2.split(/\s+/);
    let wordMatches = 0;
    
    for (const word1 of words1) {
      for (const word2 of words2) {
        if (word1 === word2 || word1.startsWith(word2) || word2.startsWith(word1)) {
          wordMatches++;
          break;
        }
      }
    }
    
    const wordScore = wordMatches / Math.max(words1.length, words2.length);
    
    // Return the best score from different algorithms
    return Math.max(levenshteinScore, wordScore);
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }
}
