<!-- Modern Team Games Component -->
<div class="team-games-container" *ngIf="!isLoading && teamGamesData">
    <!-- Stunning <PERSON><PERSON> with Gradient -->
    <div class="games-header">
        <div class="header-content">
            <div class="header-icon">
                <i class="fas fa-futbol"></i>
            </div>
            <div class="header-text">
                <h2 class="games-title">Team Matches</h2>
                <p class="games-subtitle">Season performance overview</p>
            </div>
        </div>
        <div class="games-stats">
            <div class="stat-item">
                <span class="stat-number">{{teamGamesData.length}}</span>
                <span class="stat-label">Total Games</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{getWins()}}</span>
                <span class="stat-label">Wins</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{getDraws()}}</span>
                <span class="stat-label">Draws</span>
            </div>
        </div>
    </div>

    <!-- Modern Games Grid -->
    <div class="games-grid">
        <div class="game-card"
             *ngFor="let game of teamGamesData; trackBy: trackByGameId"
             [class.playoff-game]="isPlayoffGame(game)"
             [class.completed]="game.status === GameStatus.PLAYED || game.status === GameStatus.COMPLETED"
             [class.scheduled]="game.status === GameStatus.SCHEDULED"
             (click)="onGameClick(game)">

            <!-- Game Status Badge -->
            <div class="game-status-badge" [ngClass]="getStatusClass(game.status)">
                <span class="status-text">{{getStatusText(game.status)}}</span>
                <div class="status-indicator"></div>
            </div>

            <!-- Playoff Badge -->
            <div class="playoff-badge" *ngIf="isPlayoffGame(game)">
                <i class="fas fa-crown"></i>
                <span>{{getPlayoffStage(game)}}</span>
            </div>

            <!-- Game Date -->
            <div class="game-date">
                <div class="date-primary">{{ game.date | date:'dd MMM' }}</div>
                <div class="date-secondary">{{ game.date | date:'HH:mm' }}</div>
            </div>

            <!-- Teams Section -->
            <div class="teams-container">
                <!-- Home Team -->
                <div class="team home-team">
                    <div class="team-logo-container">
                        <img class="team-logo"
                             [src]="game.homeTeam.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                             [alt]="game.homeTeam.name + ' logo'">
                        <div class="team-glow"></div>
                    </div>
                    <div class="team-info">
                        <span class="team-name">{{game.homeTeam.name}}</span>
                        <span class="team-label">Home</span>
                    </div>
                </div>

                <!-- Score Section -->
                <div class="score-container">
                    <!-- Display Mode -->
                    <div class="score-display" *ngIf="game.id !== currentEditedGameId">
                        <div class="score-main" *ngIf="game.status === GameStatus.PLAYED || game.status === GameStatus.COMPLETED">
                            <span class="score-number home">{{game.result!.homeTeamGoals}}</span>
                            <span class="score-separator">
                                <div class="separator-dot"></div>
                            </span>
                            <span class="score-number away">{{game.result!.awayTeamGoals}}</span>
                        </div>
                        <div class="score-vs" *ngIf="game.status === GameStatus.SCHEDULED">
                            <span class="vs-text">VS</span>
                            <div class="vs-line"></div>
                        </div>
                    </div>

                    <!-- Edit Mode -->
                    <div class="score-edit" *ngIf="game.id === currentEditedGameId">
                        <div class="edit-container">
                            <input type="number"
                                   class="score-input home-input"
                                   [(ngModel)]="homeTeamGoals"
                                   placeholder="0"
                                   min="0"
                                   max="99">
                            <div class="edit-separator">:</div>
                            <input type="number"
                                   class="score-input away-input"
                                   [(ngModel)]="awayTeamGoals"
                                   placeholder="0"
                                   min="0"
                                   max="99">
                        </div>
                        <div class="edit-actions">
                            <button class="save-btn" (click)="onSaveClick(game)" title="Save Score">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="cancel-btn" (click)="cancelEdit()" title="Cancel">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Away Team -->
                <div class="team away-team">
                    <div class="team-info">
                        <span class="team-name">{{game.awayTeam.name}}</span>
                        <span class="team-label">Away</span>
                    </div>
                    <div class="team-logo-container">
                        <img class="team-logo"
                             [src]="game.awayTeam.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                             [alt]="game.awayTeam.name + ' logo'">
                        <div class="team-glow"></div>
                    </div>
                </div>
            </div>

            <!-- Game Actions -->
            <div class="game-actions" *ngIf="canEditGame(game.id) | async">
                <button class="action-button edit-button"
                        *ngIf="currentEditedGameId !== game.id"
                        (click)="onEditGameResultClick(game); $event.stopPropagation()"
                        title="Edit Score">
                    <i class="fas fa-edit"></i>
                    <span>Edit</span>
                </button>
            </div>

            <!-- Interactive Hover Effect -->
            <div class="card-hover-effect"></div>
        </div>
    </div>
</div>

<!-- Beautiful Loading State -->
<div class="loading-container" *ngIf="isLoading">
    <div class="loading-content">
        <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
        </div>
        <h3 class="loading-title">Loading Matches</h3>
        <p class="loading-subtitle">Fetching team performance data...</p>
    </div>
</div>

<!-- Empty State -->
<div class="empty-state" *ngIf="!isLoading && (!teamGamesData || teamGamesData.length === 0)">
    <div class="empty-content">
        <div class="empty-icon">
            <i class="fas fa-calendar-times"></i>
        </div>
        <h3 class="empty-title">No Matches Found</h3>
        <p class="empty-subtitle">This team hasn't played any matches yet this season.</p>
    </div>
</div>