import { Request, Response, NextFunction } from "express";
import { inject, injectable } from "tsyringe";
import { IDashboardService } from "../services/dashboard-service";
import { ICacheWarmingService } from "../services/cache-warming-service";

export interface IDashboardController {
  getDashboardSummary(req: Request, res: Response, next: NextFunction): Promise<void>;
}

@injectable()
export default class DashboardController implements IDashboardController {
  constructor(
    @inject("IDashboardService") private dashboardService: IDashboardService,
    @inject("ICacheWarmingService") private cacheWarmingService: ICacheWarmingService
  ) {}

  async getDashboardSummary(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const summary = await this.dashboardService.getDashboardSummary();
      res.status(200).json(summary);
    } catch (error: any) {
      next(error);
    }
  }

  async warmCache(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      await this.cacheWarmingService.warmCacheNow();
      res.status(200).json({
        success: true,
        message: "Dashboard cache warmed successfully"
      });
    } catch (error: any) {
      next(error);
    }
  }

  async getCacheStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const status = this.cacheWarmingService.getStatus();
      res.status(200).json(status);
    } catch (error: any) {
      next(error);
    }
  }
}
