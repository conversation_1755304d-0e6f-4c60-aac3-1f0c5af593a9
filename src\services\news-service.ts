import { inject, injectable } from "tsyringe";
import logger from "../config/logger";
import { INewsService } from "../interfaces/news/news-service.interface";
import { INewsRepository } from "../interfaces/news/news-repository.interface";
import { CacheService } from "../interfaces/util-services/cache-service.interface";
import { INews } from "../models/news";
import { AddNewsRequestModel } from "../controllers/news-controller";

const NEWS_CACHE_KEY = "news:all";

@injectable()
export class NewsService implements INewsService {
    private newsRepository: INewsRepository;
    private cacheService: CacheService;

    constructor(
        @inject("INewsRepository") newsRepository: INewsRepository,
        @inject("CacheService") cacheService: CacheService
    ) {
        this.newsRepository = newsRepository;
        this.cacheService = cacheService;
    }

    async getAllNews(): Promise<INews[]> {
        logger.info(`NewsService: getting all news`);

        try {
            // Try to get from cache first
            const cachedNews = await this.getNewsFromCache();
            if (cachedNews) {
                logger.info(`NewsService: returning ${cachedNews.length} news items from cache`);
                return cachedNews;
            }

            // If not in cache, fetch from database
            logger.info(`NewsService: fetching news from database`);
            const allNews = await this.newsRepository.getAllNews();

            // Cache the results
            await this.setNewsInCache(allNews);

            return allNews;
        } catch (error) {
            logger.error("Error getting all news:", error);
            // Return empty array instead of throwing to prevent client errors
            return [];
        }
    }

    async addNews(newsData: AddNewsRequestModel): Promise<INews> {
        logger.info(`NewsService: adding news for title ${newsData.title}`);

        console.log('News service: transferData', newsData.transferData);
        const news = await this.newsRepository.addNews(newsData);

        // Clear cache when new news is added
        await this.clearNewsCache();

        return news;
    }

    async deleteNews(newsId: string): Promise<boolean> {
        logger.info(`NewsService: deleting news with id ${newsId}`);

        const isDeleted = await this.newsRepository.deleteNews(newsId);

        // Clear cache when news is deleted
        if (isDeleted) {
            await this.clearNewsCache();
        }

        console.log(`Service: ${isDeleted}`);
        return isDeleted;
    }

    async likeNews(newsId: string, userId: string): Promise<INews> {
        logger.info(`NewsService: liking news ${newsId} by user ${userId}`);

        const updatedNews = await this.newsRepository.likeNews(newsId, userId);

        // Clear cache when news is liked
        await this.clearNewsCache();

        return updatedNews;
    }

    async unlikeNews(newsId: string, userId: string): Promise<INews> {
        logger.info(`NewsService: unliking news ${newsId} by user ${userId}`);

        const updatedNews = await this.newsRepository.unlikeNews(newsId, userId);

        // Clear cache when news is unliked
        await this.clearNewsCache();

        return updatedNews;
    }

    async updateNews(newsId: string, newsData: Partial<AddNewsRequestModel>): Promise<INews> {
        logger.info(`NewsService: updating news ${newsId}`);

        const updatedNews = await this.newsRepository.updateNews(newsId, newsData);

        // Clear cache when news is updated
        await this.clearNewsCache();

        return updatedNews;
    }

    // Cache helper methods
    private async getNewsFromCache(): Promise<INews[] | null> {
        try {
            const cachedData = await this.cacheService.get(NEWS_CACHE_KEY);
            if (cachedData) {
                return JSON.parse(cachedData);
            }
            return null;
        } catch (error) {
            logger.error("Error getting news from cache:", error);
            return null;
        }
    }

    private async setNewsInCache(news: INews[]): Promise<void> {
        try {
            // Cache for 10 minutes
            await this.cacheService.set(NEWS_CACHE_KEY, news, 10 * 60);
        } catch (error) {
            logger.error("Error setting news in cache:", error);
        }
    }

    async clearNewsCache(): Promise<void> {
        try {
            await this.cacheService.delete(NEWS_CACHE_KEY);
        } catch (error) {
            logger.error("Error clearing news cache:", error);
        }
    }
}