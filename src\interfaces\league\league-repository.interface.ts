import { ClientSession, Types } from "mongoose";
import { ILeague } from "../../models/league";
import { TopScorer, TopAssister } from "@pro-clubs-manager/shared-dtos";
import { AllTimeTopAvgRatingByPosition, MostHattricks, MostCleanSheets, MostWinningPercentageTeam, MostWinningPercentagePlayer } from "../../repositories/game-repository";

export interface ILeagueRepository {
  getAllLeagues(): Promise<ILeague[]>;
  getLeagueById(id: string | Types.ObjectId, session?: ClientSession): Promise<ILeague>;

  isLeagueNameExists(name: string): Promise<boolean>;

  createLeague(name: string, imgUrl?: string): Promise<ILeague>;
  deleteLeague(id: string | Types.ObjectId, session?: ClientSession): Promise<void>;

  removeTeamFromLeague(leagueId: Types.ObjectId, teamId: Types.ObjectId, session?: ClientSession): Promise<void>;

  calculateLeagueTopScorers(leagueId: string | Types.ObjectId, limit: number, session?: ClientSession): Promise<TopScorer[]>;
  calculateLeagueTopAssisters(leagueId: string | Types.ObjectId, limit: number, session?: ClientSession): Promise<TopAssister[]>;
  calculateAllTimeTopScorers(leagueId: string | Types.ObjectId, limit: number, session?: ClientSession): Promise<TopScorer[]>;
  calculateAllTimeTopAssisters(leagueId: string | Types.ObjectId, limit: number, session?: ClientSession): Promise<TopAssister[]>;

  // New all-time statistics methods
  calculateAllTimeTopAvgRatingByPosition(leagueId: string, position: string, minimumGames: number, limit?: number, session?: ClientSession): Promise<AllTimeTopAvgRatingByPosition[]>;
  calculateMostHattricks(leagueId: string, limit?: number, session?: ClientSession): Promise<MostHattricks[]>;
  calculateMostCleanSheets(leagueId: string, limit?: number, session?: ClientSession): Promise<MostCleanSheets[]>;
  calculateMostWinningPercentageTeams(leagueId: string, minimumGames?: number, limit?: number, session?: ClientSession): Promise<MostWinningPercentageTeam[]>;
  calculateMostWinningPercentagePlayers(leagueId: string, minimumGames?: number, limit?: number, session?: ClientSession): Promise<MostWinningPercentagePlayer[]>;

  syncPlayerStatsWithGameData(leagueId: string, session?: ClientSession): Promise<{ updated: number; errors: string[] }>;
}
