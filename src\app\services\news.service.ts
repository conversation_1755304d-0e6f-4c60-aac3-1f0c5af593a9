import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { News } from '../components/news/news.model';
import { AddNewsRequestModel } from '../components/add-news/add-news.component';

@Injectable({
    providedIn: 'root'
})
export class NewsService {
    NEWS_CONTROLLER_URL = "news";

    constructor(private apiService: ApiService) { }

    async getAllNews(): Promise<Array<News>> {
        const response = await this.apiService.get<Array<News>>(`${this.NEWS_CONTROLLER_URL}/`);

        return response.data;
    }

    async addNews(addNewsReqeuestModel: AddNewsRequestModel): Promise<News> {
        const response = await this.apiService.post<News>(`${this.NEWS_CONTROLLER_URL}/addNews`, addNewsReqeuestModel);

        return response.data;
    }

    async deleteNews(newsId: string): Promise<boolean> {
        const response = await this.apiService.delete<boolean>(`${this.NEWS_CONTROLLER_URL}/${newsId}`);

        return response.data;
    }

    async likeNews(newsId: string): Promise<News> {
        const response = await this.apiService.post<News>(`${this.NEWS_CONTROLLER_URL}/${newsId}/like`, {});
        return response.data;
    }

    async unlikeNews(newsId: string): Promise<News> {
        const response = await this.apiService.delete<News>(`${this.NEWS_CONTROLLER_URL}/${newsId}/like`);
        return response.data;
    }

    async updateNews(newsId: string, newsData: Partial<AddNewsRequestModel>): Promise<News> {
        const response = await this.apiService.put<News>(`${this.NEWS_CONTROLLER_URL}/${newsId}`, newsData);
        return response.data;
    }
}