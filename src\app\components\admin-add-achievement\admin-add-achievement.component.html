<div class="admin-add-achievement-container">
  <div class="header">
    <button class="back-btn" (click)="goBack()">
      <i class="fas fa-arrow-left"></i>
      Back to CMS Dashboard
    </button>
    <h1>
      <i class="fas fa-trophy"></i>
      Add Player Achievement
    </h1>
    <p class="subtitle">Manually add achievements to players for specific seasons</p>
  </div>

  <div class="form-container" *ngIf="!isLoading">
    <form [formGroup]="achievementForm" (ngSubmit)="onSubmit()" class="achievement-form">
      
      <!-- Player Selection -->
      <div class="form-group">
        <label for="player">Player *</label>
        <div class="player-search-container">
          <input
            type="text"
            id="player"
            class="form-control"
            [(ngModel)]="searchTerm"
            (input)="onSearchChange()"
            placeholder="Search for a player..."
            [ngModelOptions]="{standalone: true}"
            autocomplete="off"
          >
          <button type="button" class="clear-btn" *ngIf="searchTerm" (click)="clearPlayerSelection()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <!-- Selected Player Display -->
        <div class="selected-player" *ngIf="getSelectedPlayer()">
          <img [src]="getSelectedPlayer()?.imgUrl || '/assets/default-player.png'" 
               [alt]="getSelectedPlayer()?.name" 
               class="player-avatar">
          <span class="player-name">{{ getSelectedPlayer()?.name }}</span>
          <span class="player-team" *ngIf="getSelectedPlayer()?.team">
            ({{ getSelectedPlayer()?.team }})
          </span>
        </div>

        <!-- Player Dropdown -->
        <div class="player-dropdown" *ngIf="filteredPlayers.length > 0 && !getSelectedPlayer()">
          <div class="player-option" 
               *ngFor="let player of filteredPlayers.slice(0, 10)" 
               (click)="onPlayerSelect(player)">
            <img [src]="player.imgUrl || '/assets/default-player.png'" 
                 [alt]="player.name" 
                 class="player-avatar-small">
            <div class="player-info">
              <span class="player-name">{{ player.name }}</span>
              <span class="player-team" *ngIf="player.team">
                {{ player.team }}
              </span>
            </div>
          </div>
        </div>

        <div class="error-message" *ngIf="achievementForm.get('playerId')?.invalid && achievementForm.get('playerId')?.touched">
          Please select a player
        </div>
      </div>

      <!-- Season Number -->
      <div class="form-group">
        <label for="seasonNumber">Season Number *</label>
        <input
          type="number"
          id="seasonNumber"
          formControlName="seasonNumber"
          class="form-control"
          placeholder="Enter season number (e.g., 1, 2, 3...)"
          min="1"
        >
        <div class="error-message" *ngIf="achievementForm.get('seasonNumber')?.invalid && achievementForm.get('seasonNumber')?.touched">
          Please enter a valid season number
        </div>
      </div>

      <!-- Achievement Type -->
      <div class="form-group">
        <label for="achievementType">Achievement Type *</label>
        <select id="achievementType" formControlName="achievementType" class="form-control">
          <option value="">Select achievement type</option>
          <option *ngFor="let type of achievementTypes" [value]="type">{{ type }}</option>
        </select>
        <div class="error-message" *ngIf="achievementForm.get('achievementType')?.invalid && achievementForm.get('achievementType')?.touched">
          Please select an achievement type
        </div>
      </div>

      <!-- Rank (Optional) -->
      <div class="form-group">
        <label for="rank">Rank (Optional)</label>
        <input
          type="number"
          id="rank"
          formControlName="rank"
          class="form-control"
          placeholder="Enter rank (1st, 2nd, 3rd...)"
          min="1"
        >
        <small class="form-text">For achievements like Top Scorer (1st, 2nd, 3rd place)</small>
      </div>

      <!-- Stats Section (shown for relevant achievement types) -->
      <div class="stats-section" *ngIf="isStatsRelevant(achievementForm.get('achievementType')?.value)">
        <h3>Statistics (Optional)</h3>
        <div class="stats-grid">
          <div class="form-group">
            <label for="goals">Goals</label>
            <input type="number" id="goals" formControlName="goals" class="form-control" min="0">
          </div>
          <div class="form-group">
            <label for="assists">Assists</label>
            <input type="number" id="assists" formControlName="assists" class="form-control" min="0">
          </div>
          <div class="form-group">
            <label for="cleanSheets">Clean Sheets</label>
            <input type="number" id="cleanSheets" formControlName="cleanSheets" class="form-control" min="0">
          </div>
          <div class="form-group">
            <label for="avgRating">Average Rating</label>
            <input type="number" id="avgRating" formControlName="avgRating" class="form-control" step="0.1" min="0" max="10">
          </div>
          <div class="form-group">
            <label for="games">Games Played</label>
            <input type="number" id="games" formControlName="games" class="form-control" min="0">
          </div>
          <div class="form-group">
            <label for="playerOfTheMatch">Player of the Match</label>
            <input type="number" id="playerOfTheMatch" formControlName="playerOfTheMatch" class="form-control" min="0">
          </div>
        </div>
      </div>

      <!-- Description -->
      <div class="form-group">
        <label for="description">Description (Optional)</label>
        <textarea
          id="description"
          formControlName="description"
          class="form-control"
          rows="3"
          placeholder="Add a custom description for this achievement..."
        ></textarea>
      </div>

      <!-- Submit Button -->
      <div class="form-actions">
        <button type="button" class="btn btn-secondary" (click)="goBack()" [disabled]="isSubmitting">
          Cancel
        </button>
        <button type="submit" class="btn btn-primary" [disabled]="isSubmitting || achievementForm.invalid">
          <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
          <i class="fas fa-plus" *ngIf="!isSubmitting"></i>
          {{ isSubmitting ? 'Adding Achievement...' : 'Add Achievement' }}
        </button>
      </div>
    </form>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <i class="fas fa-spinner fa-spin"></i>
    <p>Loading players...</p>
  </div>
</div>
