import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

export interface PlayerPosition {
  x: number;
  y: number;
  position: string;
  name: string;
  goals: number;
  assists: number;
  rating: number;
  isPlayerOfMatch?: boolean;
}

export interface AIImportData {
  players: PlayerPosition[];
  gameId?: string;
  teamId?: string;
}

@Component({
  selector: 'app-ai-import',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './ai-import.component.html',
  styleUrls: ['./ai-import.component.scss']
})
export class AiImportComponent implements OnInit {
  importData: AIImportData = {
    players: []
  };

  // Formation positions (4-3-3 default)
  formationPositions = [
    // Goalkeeper
    { x: 50, y: 90, position: 'GK', name: '', goals: 0, assists: 0, rating: 0 },
    // Defense
    { x: 20, y: 70, position: 'LB', name: '', goals: 0, assists: 0, rating: 0 },
    { x: 35, y: 75, position: 'CB', name: '', goals: 0, assists: 0, rating: 0 },
    { x: 65, y: 75, position: 'CB', name: '', goals: 0, assists: 0, rating: 0 },
    { x: 80, y: 70, position: 'RB', name: '', goals: 0, assists: 0, rating: 0 },
    // Midfield
    { x: 30, y: 50, position: 'CM', name: '', goals: 0, assists: 0, rating: 0 },
    { x: 50, y: 45, position: 'CM', name: '', goals: 0, assists: 0, rating: 0 },
    { x: 70, y: 50, position: 'CM', name: '', goals: 0, assists: 0, rating: 0 },
    // Attack
    { x: 25, y: 25, position: 'LW', name: '', goals: 0, assists: 0, rating: 0 },
    { x: 50, y: 20, position: 'ST', name: '', goals: 0, assists: 0, rating: 0 },
    { x: 75, y: 25, position: 'RW', name: '', goals: 0, assists: 0, rating: 0 }
  ];

  selectedPlayer: PlayerPosition | null = null;
  isModalOpen = false;

  constructor(public router: Router) {}

  ngOnInit(): void {
    this.importData.players = [...this.formationPositions];
  }

  openPlayerModal(player: PlayerPosition): void {
    this.selectedPlayer = { ...player };
    this.isModalOpen = true;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.selectedPlayer = null;
  }

  savePlayer(): void {
    if (this.selectedPlayer) {
      const index = this.importData.players.findIndex(p => 
        p.x === this.selectedPlayer!.x && p.y === this.selectedPlayer!.y
      );
      if (index !== -1) {
        this.importData.players[index] = { ...this.selectedPlayer };
      }
    }
    this.closeModal();
  }

  clearPlayer(player: PlayerPosition): void {
    const index = this.importData.players.findIndex(p => 
      p.x === player.x && p.y === player.y
    );
    if (index !== -1) {
      this.importData.players[index] = {
        ...player,
        name: '',
        goals: 0,
        assists: 0,
        rating: 0,
        isPlayerOfMatch: false
      };
    }
  }

  importFromAI(): void {
    // TODO: Implement AI import functionality
    console.log('AI Import functionality to be implemented');
  }

  submitData(): void {
    const filledPlayers = this.importData.players.filter(p => p.name.trim() !== '');
    if (filledPlayers.length === 0) {
      alert('Please add at least one player before submitting.');
      return;
    }

    // TODO: Submit data to backend
    console.log('Submitting data:', { ...this.importData, players: filledPlayers });
    
    // Navigate back or show success message
    this.router.navigate(['/dashboard']);
  }

  resetForm(): void {
    this.importData.players = [...this.formationPositions];
  }

  getPlayerDisplayText(player: PlayerPosition): string {
    if (!player.name) return player.position;
    return `${player.name} (${player.rating})`;
  }

  hasPlayerData(player: PlayerPosition): boolean {
    return player.name.trim() !== '';
  }

  getFilledPlayersCount(): number {
    return this.importData.players.filter(p => p.name.trim() !== '').length;
  }

  trackByPosition(index: number, player: PlayerPosition): string {
    return `${player.x}-${player.y}`;
  }

  getPlayerInitials(name: string): string {
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);
  }

  getGoalScorersCount(): number {
    return this.importData.players.filter(p => p.goals > 0).length;
  }

  getAssistMakersCount(): number {
    return this.importData.players.filter(p => p.assists > 0).length;
  }
}
