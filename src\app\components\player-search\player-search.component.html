<div class="player-search-container">
    <div class="player-search-header">
        <button class="back-button" (click)="onArrowBackClick()">
            <i class="fas fa-arrow-left"></i>
            <span>Back</span>
        </button>
        <h1 class="search-title">
            <i class="fas fa-search"></i>
            Player Search
        </h1>
    </div>

    <div class="search-section">
        <div class="search-input-group">
            <div class="search-input-wrapper">
                <i class="fas fa-user search-icon"></i>
                <input
                    type="text"
                    (keydown.enter)="onSearchClick()"
                    [maxLength]="25"
                    class="search-input"
                    [(ngModel)]="searchText"
                    placeholder="Enter player name (min 3 characters)" />
            </div>
            <button
                (click)="onSearchClick()"
                class="search-button"
                title="Enter at least 3 characters"
                [disabled]="searchText.length < 3">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </button>
        </div>
    </div>

    <div class="results-section" *ngIf="playerSearchResults && !isLoading">
        <div *ngIf="playerSearchResults.length > 0" class="results-content">
            <div class="results-header">
                <h2 class="results-title">
                    <i class="fas fa-list"></i>
                    Search Results
                    <span class="results-count">({{ playerSearchResults.length }})</span>
                </h2>
            </div>
            <div class="results-table">
                <pro-clubs-data-table
                    [tableData]="playerSearchResults"
                    [isClickable]="true"
                    [columnsToDisplay]="playerSearchResultsColumns"
                    (onRowClickEvent)="onPlayerClick($event)">
                </pro-clubs-data-table>
            </div>
        </div>

        <div *ngIf="playerSearchResults.length === 0" class="no-results">
            <div class="no-results-content">
                <i class="fas fa-user-slash no-results-icon"></i>
                <h3 class="no-results-title">No Players Found</h3>
                <p class="no-results-text">Try adjusting your search criteria</p>
            </div>
        </div>
    </div>
</div>

<pro-clubs-spinner *ngIf="isLoading"></pro-clubs-spinner>