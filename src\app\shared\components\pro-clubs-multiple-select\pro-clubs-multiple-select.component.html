<div class="pro-clubs-multiselect-container">
    <div class="multiselect-wrapper" [class.focused]="isFocused" [class.has-value]="hasSelectedValues()">
        <div class="select-trigger" (click)="toggleDropdown()">
            <div class="selected-values" *ngIf="hasSelectedValues(); else placeholderTemplate">
                <div class="value-chips">
                    <span class="value-chip" *ngFor="let option of getSelectedOptions(); let i = index">
                        <span class="chip-text">{{option.displayText}}</span>
                        <i class="fas fa-times chip-remove" (click)="removeOption(option, $event)"></i>
                    </span>
                </div>
            </div>

            <ng-template #placeholderTemplate>
                <span class="placeholder-text">{{placeholder}}</span>
            </ng-template>

            <div class="select-icon">
                <i class="fas fa-chevron-down" [class.rotated]="isDropdownOpen"></i>
            </div>
        </div>

        <div class="dropdown-panel" *ngIf="isDropdownOpen" (clickOutside)="closeDropdown()">
            <div class="dropdown-header" *ngIf="selectOptions.length > 5">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input
                        type="text"
                        class="search-input"
                        placeholder="Search options..."
                        [(ngModel)]="searchTerm"
                        (input)="filterOptions()">
                </div>
            </div>

            <div class="options-container">
                <div class="select-all-option" *ngIf="filteredSelectOptions.length > 1">
                    <label class="option-label">
                        <input
                            type="checkbox"
                            class="option-checkbox"
                            [checked]="isAllSelected()"
                            [indeterminate]="isIndeterminate()"
                            (change)="toggleSelectAll()">
                        <span class="checkbox-custom"></span>
                        <span class="option-text">Select All</span>
                    </label>
                </div>

                <div class="option-item"
                     *ngFor="let option of filteredSelectOptions"
                     (click)="toggleOption(option)">
                    <label class="option-label">
                        <input
                            type="checkbox"
                            class="option-checkbox"
                            [checked]="isSelected(option)"
                            (change)="toggleOption(option)">
                        <span class="checkbox-custom"></span>
                        <span class="option-text">{{option.displayText}}</span>
                    </label>
                </div>

                <div class="no-options" *ngIf="filteredSelectOptions.length === 0">
                    <i class="fas fa-search"></i>
                    <span>No options found</span>
                </div>
            </div>
        </div>
    </div>
</div>