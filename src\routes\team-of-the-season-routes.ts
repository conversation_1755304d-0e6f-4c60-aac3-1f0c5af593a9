import { Router } from "express";
import { container } from "../config/container.config";
import TeamOfTheSeasonController from "../controllers/team-of-the-season-controller";

const router = Router();
const totsController = container.resolve(TeamOfTheSeasonController);

// Get supported formations
router.get("/formations", (req, res, next) => totsController.getSupportedFormations(req, res, next));

// Get available seasons for a league
router.get("/league/:leagueId/seasons", (req, res, next) => totsController.getAvailableSeasons(req, res, next));

// Get available formations for a specific season
router.get("/league/:leagueId/season/:seasonNumber/formations", (req, res, next) => totsController.getAvailableFormations(req, res, next));

// Get all TOTS for a league
router.get("/league/:leagueId", (req, res, next) => totsController.getAllTeamsOfTheSeason(req, res, next));

// Get specific TOTS
router.get("/league/:leagueId/season/:seasonNumber/:formation?", (req, res, next) => totsController.getTeamOfTheSeason(req, res, next));

// Generate TOTS for a season
router.post("/league/:leagueId/season/:seasonNumber/generate", (req, res, next) => totsController.generateTeamOfTheSeason(req, res, next));

// Regenerate TOTS for a season
router.put("/league/:leagueId/season/:seasonNumber/regenerate", (req, res, next) => totsController.regenerateTeamOfTheSeason(req, res, next));

export default router;
