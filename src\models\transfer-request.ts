import mongoose, { Document, Schema } from 'mongoose';

export interface ITransferRequest extends Document {
  id: string;
  playerId: mongoose.Types.ObjectId;
  fromTeamId: mongoose.Types.ObjectId;
  toTeamId: mongoose.Types.ObjectId;
  requestedBy: mongoose.Types.ObjectId; // Captain who initiated the request
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  requestedAt: Date;
  processedAt?: Date;
  processedBy?: mongoose.Types.ObjectId; // Captain who approved/rejected
  reason?: string; // Reason for rejection or additional notes
  message?: string; // Message from requesting captain
  expiresAt: Date; // Auto-expire after certain time
}

const transferRequestSchema = new Schema<ITransferRequest>(
  {
    playerId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Player',
      required: true
    },
    fromTeamId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Team',
      required: true
    },
    toTeamId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Team',
      required: true
    },
    requestedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'cancelled'],
      default: 'pending'
    },
    requestedAt: {
      type: Date,
      default: Date.now
    },
    processedAt: {
      type: Date
    },
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: {
      type: String,
      maxlength: 500
    },
    message: {
      type: String,
      maxlength: 500
    },
    expiresAt: {
      type: Date,
      required: true,
      default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    id: true
  }
);

// Index for efficient querying
transferRequestSchema.index({ playerId: 1 });
transferRequestSchema.index({ fromTeamId: 1 });
transferRequestSchema.index({ toTeamId: 1 });
transferRequestSchema.index({ requestedBy: 1 });
transferRequestSchema.index({ status: 1 });
transferRequestSchema.index({ expiresAt: 1 });

// Compound index for team-specific queries
transferRequestSchema.index({ fromTeamId: 1, status: 1 });
transferRequestSchema.index({ toTeamId: 1, status: 1 });

// Virtual for populated player details
transferRequestSchema.virtual('player', {
  ref: 'Player',
  localField: 'playerId',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated team details
transferRequestSchema.virtual('fromTeam', {
  ref: 'Team',
  localField: 'fromTeamId',
  foreignField: '_id',
  justOne: true
});

transferRequestSchema.virtual('toTeam', {
  ref: 'Team',
  localField: 'toTeamId',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated user details
transferRequestSchema.virtual('requester', {
  ref: 'User',
  localField: 'requestedBy',
  foreignField: '_id',
  justOne: true
});

transferRequestSchema.virtual('processor', {
  ref: 'User',
  localField: 'processedBy',
  foreignField: '_id',
  justOne: true
});

const TransferRequest = mongoose.model<ITransferRequest>('TransferRequest', transferRequestSchema);

export default TransferRequest;
