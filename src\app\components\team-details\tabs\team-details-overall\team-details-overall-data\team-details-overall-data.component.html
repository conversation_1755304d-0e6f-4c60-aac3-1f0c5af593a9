<!-- Modern Team Management Panel -->
<div class="team-management-panel">

    <!-- Team Name Management -->
    <div class="management-section" *ngIf="editTeamMode">
        <div class="section-header">
            <h4 class="section-title">Edit Team Name</h4>
            <div class="section-actions">
                <button class="action-btn cancel-btn" (click)="onCancelEditTeamClick()">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
                <button class="action-btn save-btn" (click)="onSaveClick()">
                    <i class="fas fa-save"></i>
                    Save
                </button>
            </div>
        </div>
        <div class="section-content">
            <div class="team-name-edit">
                <input class="name-input" [(ngModel)]="editedTeamName" placeholder="Enter team name">
            </div>
        </div>
    </div>

    <!-- Team Logo Management -->
    <div class="management-section" *ngIf="editTeamMode">
        <div class="section-header">
            <h4 class="section-title">Change Team Logo</h4>
        </div>
        <div class="section-content">
            <div class="logo-management">
                <div class="current-logo">
                    <img [src]="chosenTeam!.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                         class="team-logo-preview"
                         [alt]="chosenTeam!.name + ' logo'">
                </div>
                <div class="logo-actions">
                    <button type="button" (click)="fileInput.click()" class="upload-btn">
                        <i class="fas fa-upload"></i>
                        Change Logo
                    </button>
                    <input type="file" #fileInput class="file-input" (change)="onFileSelected($event)" accept="image/*">
                </div>
            </div>
        </div>
    </div>

    <!-- Team Information Display -->
    <div class="management-section" *ngIf="!editTeamMode && !editCaptainMode">
        <div class="section-header">
            <h4 class="section-title">Team Information</h4>
        </div>
        <div class="section-content">
            <div class="team-info-display">
                <div class="team-logo-section">
                    <div class="team-logo-container">
                        <img [src]="chosenTeam!.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                             class="team-logo-display"
                             [alt]="chosenTeam!.name + ' logo'">
                    </div>
                    <div class="team-name-display">
                        <h3 class="team-name">{{chosenTeam!.name}}</h3>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-label">
                        <i class="fas fa-users"></i>
                        Squad Size
                    </div>
                    <div class="info-value">{{chosenTeam!.players.length || 0}} Players</div>
                </div>
                <div class="info-item">
                    <div class="info-label">
                        <i class="fas fa-star"></i>
                        Captain
                    </div>
                    <div class="info-value">{{chosenTeam!.captain?.name || 'No Captain'}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">
                        <i class="fas fa-calendar-alt"></i>
                        Founded
                    </div>
                    <div class="info-value">2024</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Team Actions -->
    <div class="management-section" *ngIf="!editTeamMode && !editCaptainMode">
        <div class="section-header">
            <h4 class="section-title">Team Settings</h4>
        </div>
        <div class="section-content">
            <app-team-action-buttons
                [team]="chosenTeam!"
                [canEdit]="(canEditThisTeam() | async) || false"
                [isEditMode]="editTeamMode"
                [isLoading]="false"
                (editClick)="onEditTeamClick()"
                (addPlayerClick)="onAddPlayerClick()"
                (deleteClick)="onDeleteTeamClick()">
            </app-team-action-buttons>

            <!-- Captain Change Button (separate from main actions) -->
            <div class="captain-actions" *ngIf="canSetCaptain() | async">
                <button class="action-btn captain-btn"
                        (click)="changeEditCaptainModeStatus(true)">
                    <i class="fas fa-user-crown"></i>
                    Change Captain
                </button>
            </div>
        </div>
    </div>
    <!-- Captain Management -->
    <div class="management-section" *ngIf="editCaptainMode">
        <div class="section-header">
            <h4 class="section-title">Change Team Captain</h4>
            <div class="section-actions">
                <button class="action-btn cancel-btn" (click)="changeEditCaptainModeStatus(false)">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
            </div>
        </div>
        <div class="section-content">
            <!-- Captain Selection -->
            <div class="captain-selection">
                <pro-clubs-auto-complete-select
                    [defaultOption]="selectedCaptain!.value"
                    [selectOptions]="playersOptions!"
                    (selectionChange)="onCaptainSelect($event)"
                    [placeholder]="'Choose Captain'">
                </pro-clubs-auto-complete-select>
                <button type="button" (click)="saveNewCaptain()" class="save-captain-btn">
                    <i class="fas fa-save"></i>
                    Save Captain
                </button>
            </div>
        </div>
    </div>
</div>