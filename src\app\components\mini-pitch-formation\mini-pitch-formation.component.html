<div class="vertical-pitch-container" [class.compact]="compactMode" [class.totw-mode]="totwMode">
  <!-- Team Tabs -->
  <div class="team-tabs" *ngIf="!totwMode">
    <button *ngFor="let team of teams; let i = index" class="team-tab" [class.active]="selectedTeamIndex === i"
      (click)="selectTeam(i)">
      <div class="tab-content">
        <span class="team-name">{{ team.teamName }}</span>
        <span class="formation-badge">{{ team.formation }}</span>
      </div>
    </button>
  </div>

  <!-- TOTW Header -->
  <div class="totw-header" *ngIf="totwMode && selectedTeam">
    <div class="totw-title">
      <i class="fas fa-trophy"></i>
      <span class="team-name">{{ selectedTeam.teamName }}</span>
      <span class="formation-badge">{{ selectedTeam.formation }}</span>
    </div>
  </div>

  <!-- Selected Team Pitch View -->
  <div class="pitch-view" *ngIf="selectedTeam">

    <!-- Vertical Pitch -->
    <div class="vertical-pitch">
      <!-- Pitch Background -->
      <div class="pitch-background" [class.totw-mode]="totwMode">
        <!-- Pitch Lines -->
        <div class="pitch-lines">
          <!-- Goal areas -->
          <div class="goal-area top"></div>
          <div class="goal-area bottom"></div>

          <!-- Penalty areas -->
          <div class="penalty-area top"></div>
          <div class="penalty-area bottom"></div>

          <!-- Center circle -->
          <div class="center-circle"></div>

          <!-- Center line -->
          <div class="center-line"></div>

          <!-- Corner arcs -->
          <div class="corner-arc top-left"></div>
          <div class="corner-arc top-right"></div>
          <div class="corner-arc bottom-left"></div>
          <div class="corner-arc bottom-right"></div>
        </div>

        <!-- Team Players -->
        <div class="team-players">
          <div *ngFor="let player of getPositionedPlayers(selectedTeam); trackBy: trackByPlayerId" class="player-marker d-flex flex-column align-items-center"
            [class.player-of-match]="player.isPlayerOfMatch" [style.left.%]="player.x" [style.top.%]="player.y">

            <div class="player-avatar">
              <img [src]="player.profileImage || 'assets/Icons/User.jpg'" [alt]="player.name || 'Player'"
                class="player-image" (error)="onImageError($event)">

              <!-- Team Badge -->
              <div class="team-badge" *ngIf="totwMode && player.teamImage">
                <img [src]="player.teamImage" [alt]="player.teamName || 'Team'"
                     class="team-badge-image" (error)="onImageError($event)">
              </div>

              <!-- Rating Circle -->
              <div class="rating-circle" *ngIf="hasRating(player)"
                [style.background-color]="getRatingColor(player.rating)">
                {{ player.rating?.toFixed(1) }}
              </div>
            </div>

            <div class="player-info" *ngIf="!compactMode">
              <span class="player-name">{{ getPlayerDisplayName(player) }}</span>
              <div class="player-stats" *ngIf="hasPlayerStats(player)">
                <span class="stat goals" *ngIf="player.goals && player.goals > 0">
                  ⚽{{ player.goals }}
                </span>
                <span class="stat assists" *ngIf="player.assists && player.assists > 0">
                  🅰️{{ player.assists }}
                </span>
              </div>
            </div>

            <!-- Player of the Match Star -->
            <div class="potm-star" *ngIf="player.isPlayerOfMatch">⭐</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Legend -->
    <div class="legend" *ngIf="compactMode || showPlayerStats">
      <div class="legend-item" *ngIf="showPlayerStats">
        <span class="legend-icon">⭐</span>
        <span>Player of the Match</span>
      </div>
      <div class="legend-item" *ngIf="showPlayerStats">
        <span class="legend-icon">⚽</span>
        <span>Goals</span>
      </div>
      <div class="legend-item" *ngIf="showPlayerStats">
        <span class="legend-icon">🅰️</span>
        <span>Assists</span>
      </div>
    </div>
  </div>
</div>