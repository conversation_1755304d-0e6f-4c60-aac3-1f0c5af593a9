import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { LeagueService } from '../../services/league.service';
import { NotificationService } from '../../services/notification.service';
import { MostHattricks } from '../../shared/models/all-time-statistics.model';

@Component({
  selector: 'app-most-hattricks',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './most-hattricks.component.html',
  styleUrls: ['./most-hattricks.component.scss']
})
export class MostHattricksComponent implements OnInit {
  mostHattricksData: MostHattricks[] = [];
  isLoading: boolean = false;
  selectedLeagueId: string = '';
  leagues: any[] = [];

  @Input() hideTitle: boolean = false;
  @Input() leagueId?: string;

  constructor(
    private router: Router,
    private leagueService: LeagueService,
    private notificationService: NotificationService
  ) { }

  async ngOnInit(): Promise<void> {
    await this.loadLeagues();
    if (this.leagueId) {
      this.selectedLeagueId = this.leagueId;
      await this.loadData();
    }
  }

  private async loadLeagues(): Promise<void> {
    try {
      this.leagues = await this.leagueService.getAllLeagues();
      if (this.leagues.length > 0 && !this.selectedLeagueId) {
        this.selectedLeagueId = this.leagues[0].id;
        await this.loadData();
      }
    } catch (error) {
      console.error('Error loading leagues:', error);
      this.notificationService.error('Failed to load leagues');
    }
  }

  async onLeagueChange(): Promise<void> {
    if (this.selectedLeagueId) {
      await this.loadData();
    }
  }

  private async loadData(): Promise<void> {
    if (!this.selectedLeagueId) return;

    this.isLoading = true;
    try {
      const response = await this.leagueService.getMostHattricks(this.selectedLeagueId);

      this.mostHattricksData = response.map(player => ({
        ...player,
        tableIcon: { 
          name: player.playerName, 
          imgUrl: player.playerImgUrl || '', 
          isTeam: false 
        },
        hattricsPerGame: parseFloat(player.hattricsPerGame.toFixed(3))
      }));
    } catch (error) {
      console.error('Error loading most hattricks data:', error);
      this.notificationService.error('Failed to load hattricks statistics');
      this.mostHattricksData = [];
    } finally {
      this.isLoading = false;
    }
  }

  onPlayerClick(player: MostHattricks): void {
    this.router.navigate(['/player', player.playerId]);
  }

  onTeamClick(teamId: string): void {
    this.router.navigate(['/team-details', teamId]);
  }

  getPlayerRank(index: number): string {
    return `#${index + 1}`;
  }

  getHattrickPercentage(player: MostHattricks): string {
    return `${(player.hattricsPerGame * 100).toFixed(1)}%`;
  }

  getFireIconsArray(hattricks: number): number[] {
    return Array(Math.min(hattricks, 5)).fill(0);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/User.jpg';
    }
  }
}
