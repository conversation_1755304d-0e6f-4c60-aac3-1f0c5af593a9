"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
require("reflect-metadata");
const mock_game_repository_1 = require("../../../src/mocks/repositories/mock-game-repository");
const mock_team_repository_1 = require("../../../src/mocks/repositories/mock-team-repository");
const team_stats_service_1 = require("../../../src/services/wrapper-services/team-stats-service");
describe("TeamStatsService", () => {
    let teamStatsService;
    let mockGameRepository;
    let mockTeamRepository;
    beforeAll(() => __awaiter(void 0, void 0, void 0, function* () {
        mockGameRepository = new mock_game_repository_1.MockGameRepository();
        mockTeamRepository = new mock_team_repository_1.MockTeamRepository();
        teamStatsService = new team_stats_service_1.TeamStatsService(mockGameRepository, mockTeamRepository);
    }));
    describe("getCurrentSeasonTeamPlayersStats", () => {
        let teamId;
        beforeAll(() => {
            teamId = new mongoose_1.Types.ObjectId();
            const leagueId = new mongoose_1.Types.ObjectId();
            const mockTeam = {
                id: teamId.toString(),
                name: "Team A",
                league: leagueId,
                players: [
                    {
                        id: new mongoose_1.Types.ObjectId().toString(),
                        name: "Player 1",
                        position: "Forward",
                        imgUrl: "img1.jpg",
                        currentSeason: { stats: { games: 10, goals: 15, assists: 5, avgRating: 8.5 } },
                    },
                    {
                        id: new mongoose_1.Types.ObjectId().toString(),
                        name: "Player 2",
                        position: "Midfielder",
                        imgUrl: "img2.jpg",
                        currentSeason: { stats: { games: 10, goals: 5, assists: 15, avgRating: 7.5 } },
                    },
                ],
                currentSeason: { league: leagueId, seasonNumber: 1, stats: {} },
            };
            jest.spyOn(mockTeamRepository, "getTeamWithPlayers").mockResolvedValue(mockTeam);
        });
        it("should return top players stats", () => __awaiter(void 0, void 0, void 0, function* () {
            const result = yield teamStatsService.getCurrentSeasonTeamPlayersStats(teamId.toString());
            expect(result.topScorers.length).toBe(2);
            expect(result.topAssisters.length).toBe(2);
            expect(result.topAvgRating.length).toBe(2);
        }));
        it("should return limited top players stats", () => __awaiter(void 0, void 0, void 0, function* () {
            const result = yield teamStatsService.getCurrentSeasonTeamPlayersStats(teamId.toString(), 1);
            const { topScorers, topAssisters, topAvgRating } = result;
            expect(topScorers[0].playerName).toBe("Player 1");
            expect(topAssisters[0].playerName).toBe("Player 2");
            expect(topScorers.length).toBe(1);
            expect(topAssisters.length).toBe(1);
            expect(topAvgRating.length).toBe(1);
        }));
        it("should throw an error if the team is not in an active season", () => __awaiter(void 0, void 0, void 0, function* () {
            const mockTeam = {
                currentSeason: undefined,
            };
            jest.spyOn(mockTeamRepository, "getTeamWithPlayers").mockResolvedValue(mockTeam);
            yield expect(teamStatsService.getCurrentSeasonTeamPlayersStats(teamId.toString())).rejects.toThrow(`Team with id ${teamId} is not in an active season`);
        }));
    });
    describe("getCurrentSeasonAdvancedTeamStats", () => {
        it("should throw error if team is not in an active season", () => __awaiter(void 0, void 0, void 0, function* () {
            const teamId = new mongoose_1.Types.ObjectId();
            const team = {
                currentSeason: undefined,
            };
            jest.spyOn(mockTeamRepository, "getTeamById").mockResolvedValue(team);
            yield expect(teamStatsService.getCurrentSeasonAdvancedTeamStats(teamId.toString())).rejects.toThrow(`Team with id ${teamId} is not currently in an active season`);
        }));
        it("should return all streaks", () => __awaiter(void 0, void 0, void 0, function* () {
            const teamId = new mongoose_1.Types.ObjectId();
            const leagueId = new mongoose_1.Types.ObjectId();
            const seasonNumber = 1;
            const team = {
                id: teamId.toString(),
                name: "Team A",
                currentSeason: {
                    league: leagueId,
                    seasonNumber,
                },
            };
            const games = [
                { homeTeam: teamId, awayTeam: new mongoose_1.Types.ObjectId(), result: { homeTeamGoals: 3, awayTeamGoals: 0 } },
                { homeTeam: teamId, awayTeam: new mongoose_1.Types.ObjectId(), result: { homeTeamGoals: 1, awayTeamGoals: 0 } },
                { homeTeam: teamId, awayTeam: new mongoose_1.Types.ObjectId(), result: { homeTeamGoals: 0, awayTeamGoals: 0 } },
                { homeTeam: teamId, awayTeam: new mongoose_1.Types.ObjectId(), result: { homeTeamGoals: 0, awayTeamGoals: 1 } },
            ];
            jest.spyOn(mockTeamRepository, "getTeamById").mockResolvedValue(team);
            jest.spyOn(mockGameRepository, "getPlayedLeagueSeasonTeamGames").mockResolvedValue(games);
            const result = yield teamStatsService.getCurrentSeasonAdvancedTeamStats(teamId.toString());
            expect(result.longestWinStreak).toBe(2);
            expect(result.longestLoseStreak).toBe(1);
            expect(result.longestUnbeatenStreak).toBe(3);
            expect(result.longestWithoutScoringStreak).toBe(2);
        }));
    });
});
