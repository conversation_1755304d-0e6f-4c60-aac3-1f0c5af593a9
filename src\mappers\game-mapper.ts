import { GameDTO, PlayerPerformanceDTO } from "@pro-clubs-manager/shared-dtos";
import { IGame } from "../models/game/game";

// Temporary extended GameDTO with playoff and broadcast properties
interface ExtendedGameDTO extends GameDTO {
  isPlayoff?: boolean;
  playoffStage?: string | null;
  broadcast?: {
    streamUrl: string;
    broadcastingTeam: string;
  };
}

type PopulatedTeam = {
  id: string;
  name: string;
  imgUrl?: string;
};

type PopulatedPlayerPerformance = {
  playerId: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  goals?: number;
  assists?: number;
  rating: number;
  playerOfTheMatch?: boolean;
  cleanSheet: boolean;
  positionPlayed: string;
};

export class GameMapper {
  static async mapToDto(game: IGame): Promise<ExtendedGameDTO> {
    if (!game) {
      throw new Error("Game object is null or undefined");
    }

    const { homeTeam, awayTeam, homeTeamPlayersPerformance, awayTeamPlayersPerformance } = await game.populate<{
      homeTeam: PopulatedTeam;
      awayTeam: PopulatedTeam;
      homeTeamPlayersPerformance: PopulatedPlayerPerformance[];
      awayTeamPlayersPerformance: PopulatedPlayerPerformance[];
    }>([
      {
        path: "homeTeam",
        select: "name imgUrl",
      },
      {
        path: "awayTeam",
        select: "name imgUrl",
      },
      {
        path: "homeTeamPlayersPerformance.playerId awayTeamPlayersPerformance.playerId",
        select: "id name imgUrl",
      },
    ]);

    // Check if teams are properly populated
    if (!homeTeam || !awayTeam) {
      throw new Error(`Game ${game.id} has null teams - homeTeam: ${!!homeTeam}, awayTeam: ${!!awayTeam}`);
    }

    return {
      id: game.id,
      fixtureId: game.fixture.toString(),
      round: game.round,
      date: game.date,
      status: game.status,
      isPlayoff: (game as any).isPlayoff || false,
      playoffStage: (game as any).playoffStage || null,
      result: game.result
        ? {
            homeTeamGoals: game.result.homeTeamGoals,
            awayTeamGoals: game.result.awayTeamGoals,
          }
        : undefined,
      homeTeam: {
        id: homeTeam.id,
        name: homeTeam.name,
        imgUrl: homeTeam.imgUrl,
        playersPerformance: this.mapPlayersPerformanceToDTO(homeTeamPlayersPerformance || []),
      },
      awayTeam: {
        id: awayTeam.id,
        name: awayTeam.name,
        imgUrl: awayTeam.imgUrl,
        playersPerformance: this.mapPlayersPerformanceToDTO(awayTeamPlayersPerformance || []),
      },
      technicalLoss: game.technicalLoss
        ? {
            teamId: game.technicalLoss!.teamId.toString(),
            reason: game.technicalLoss!.reason,
          }
        : undefined,
      broadcast: game.broadcast
        ? {
            streamUrl: game.broadcast.streamUrl,
            broadcastingTeam: game.broadcast.broadcastingTeam,
          }
        : undefined,
    };
  }

  static async mapToDtos(games: IGame[]): Promise<ExtendedGameDTO[]> {
    if (!games || !Array.isArray(games)) {
      throw new Error("Games array is null, undefined, or not an array");
    }

    // Filter out any null/undefined games before mapping
    const validGames = games.filter(game => game != null);

    if (validGames.length !== games.length) {
      console.warn(`Filtered out ${games.length - validGames.length} null/undefined games from array`);
    }

    return await Promise.all(validGames.map(async (game) => this.mapToDto(game)));
  }

  private static mapPlayersPerformanceToDTO(playersPerformance?: PopulatedPlayerPerformance[]): PlayerPerformanceDTO[] | undefined {
    return playersPerformance?.length
      ? playersPerformance!.map((playerPerformance) => ({
          playerId: playerPerformance.playerId?.id,
          name: playerPerformance.playerId?.name,
          imgUrl: playerPerformance.playerId?.imgUrl,
          goals: playerPerformance.goals,
          assists: playerPerformance.assists,
          rating: playerPerformance.rating,
          playerOfTheMatch: playerPerformance.playerOfTheMatch,
          positionPlayed: playerPerformance.positionPlayed,
        }))
      : undefined;
  }
}
