/* === MODERN RULES DESIGN === */

.rules-container {
    min-height: 100vh;
    background: var(--bg-primary);
    padding: var(--spacing-xl) 5px;
    font-family: var(--font-sans);
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;

    @media (max-width: 768px) {
        padding: var(--spacing-lg) 5px;
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md) 5px;
    }
}

.rules-content {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-2xl);
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary));
        border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-xl);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-lg);
    }
}

.rules-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-xl);
    border-bottom: 2px solid var(--border-primary);

    h1 {
        font-size: var(--text-4xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-md) 0;
        background: linear-gradient(135deg, var(--primary), var(--accent-primary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1.2;

        @media (max-width: 768px) {
            font-size: var(--text-3xl);
        }

        @media (max-width: 480px) {
            font-size: var(--text-2xl);
        }
    }

    p {
        font-size: var(--text-lg);
        color: var(--text-secondary);
        margin: 0;
        font-weight: var(--font-weight-medium);
        line-height: 1.6;

        @media (max-width: 768px) {
            font-size: var(--text-base);
        }
    }
}

.rules-section {
    margin-bottom: var(--spacing-2xl);

    &:last-child {
        margin-bottom: 0;
    }

    h2 {
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-lg) 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) 0;
        border-bottom: 1px solid var(--border-primary);

        @media (max-width: 768px) {
            font-size: var(--text-xl);
        }
    }
}

.rule-item {
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    transition: all 0.3s ease;
    position: relative;

    &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--border-secondary);
    }

    &:last-child {
        margin-bottom: 0;
    }

    p {
        margin: 0;
        font-size: var(--text-base);
        color: var(--text-primary);
        line-height: 1.6;

        strong {
            color: var(--primary);
            font-weight: var(--font-weight-semibold);
        }

        em {
            color: var(--text-secondary);
            font-style: italic;
        }
    }
}

.prizes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);

    .prize-card {
        background: linear-gradient(135deg, var(--primary-100), var(--accent-100));
        border: 2px solid var(--primary-200);
        border-radius: var(--radius-xl);
        padding: var(--spacing-lg);
        text-align: center;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
            pointer-events: none;
        }

        .prize-title {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            color: var(--primary-700);
            margin: 0 0 var(--spacing-sm) 0;
        }

        .prize-amount {
            font-size: var(--text-2xl);
            font-weight: var(--font-weight-black);
            color: var(--primary);
            margin: 0;
            font-family: var(--font-mono);
        }
    }
}

.emergency-rules {
    background: var(--warning-100);
    border: 2px solid var(--warning-300);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);

    h2 {
        color: var(--warning-700);
        border-bottom-color: var(--warning-300);
    }

    ul {
        margin: var(--spacing-md) 0 0 0;
        padding: 0;
        list-style: none;

        li {
            background: var(--warning-50);
            border: 1px solid var(--warning-200);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
            font-size: var(--text-base);
            color: var(--warning-800);
            line-height: 1.6;
            position: relative;
            padding-right: var(--spacing-xl);

            &::before {
                content: '⚠️';
                position: absolute;
                right: var(--spacing-md);
                top: 50%;
                transform: translateY(-50%);
                font-size: var(--text-lg);
            }

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}