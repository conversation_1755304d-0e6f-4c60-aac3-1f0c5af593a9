import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PlayerService } from '../../services/player.service';
import { PlayerSeasonHistoryData } from '../../shared/models/player-season-history.model';
import { NotificationService } from '../../services/notification.service';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-player-season-history',
  templateUrl: './player-season-history.component.html',
  styleUrls: ['./player-season-history.component.scss']
})
export class PlayerSeasonHistoryComponent implements OnInit {
  playerId!: string;
  seasonHistoryData?: PlayerSeasonHistoryData;
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // View options
  viewMode: 'table' | 'cards' = 'table';
  sortBy: 'season' | 'games' | 'goals' | 'assists' | 'rating' = 'season';
  sortDirection: 'asc' | 'desc' = 'desc';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private playerService: PlayerService,
    private notificationService: NotificationService,
    private authService: AuthService
  ) {}

  async ngOnInit(): Promise<void> {
    this.route.params.subscribe(params => {
      this.playerId = params['playerId'];
      if (this.playerId) {
        this.loadSeasonHistory();
      }
    });
  }

  async loadSeasonHistory(): Promise<void> {
    try {
      this.isLoading = true;
      this.hasError = false;
      this.seasonHistoryData = await this.playerService.getPlayerSeasonHistory(this.playerId);
    } catch (error) {
      console.error('Error loading season history:', error);
      this.hasError = true;
      this.errorMessage = 'Failed to load season history';
    } finally {
      this.isLoading = false;
    }
  }

  goBack(): void {
    history.back();
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'table' ? 'cards' : 'table';
  }

  setSortBy(field: 'season' | 'games' | 'goals' | 'assists' | 'rating'): void {
    if (this.sortBy === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortDirection = 'desc';
    }
  }

  getSortedSeasons() {
    if (!this.seasonHistoryData) return [];

    const allSeasons = [
      this.seasonHistoryData.currentSeason,
      ...this.seasonHistoryData.seasonHistory
    ];

    return allSeasons.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (this.sortBy) {
        case 'season':
          aValue = a.seasonName;
          bValue = b.seasonName;
          break;
        case 'games':
          aValue = a.stats.games;
          bValue = b.stats.games;
          break;
        case 'goals':
          aValue = a.stats.goals;
          bValue = b.stats.goals;
          break;
        case 'assists':
          aValue = a.stats.assists;
          bValue = b.stats.assists;
          break;
        case 'rating':
          aValue = a.stats.avgRating;
          bValue = b.stats.avgRating;
          break;
        default:
          return 0;
      }

      if (this.sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }

  getSortIcon(field: string): string {
    if (this.sortBy !== field) return 'fas fa-sort';
    return this.sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
  }

  getDefaultPlayerImage(): string {
    return 'assets/Icons/User.jpg';
  }

  getDefaultTeamImage(): string {
    return 'assets/Icons/TeamLogo.jpg';
  }

  getRatingClass(rating: number): string {
    if (rating >= 8.5) return 'rating-excellent';
    if (rating >= 7.5) return 'rating-good';
    if (rating >= 6.5) return 'rating-average';
    return 'rating-poor';
  }

  isCurrentSeason(seasonId: string): boolean {
    return seasonId === 'current';
  }

  getBestSeasonBadge(season: any): string | null {
    if (!this.seasonHistoryData) return null;
    
    const bestSeason = this.seasonHistoryData.careerTotals.bestSeason;
    if (bestSeason.seasonId === season.seasonId) {
      return bestSeason.reason;
    }
    return null;
  }

  formatStatValue(value: number, decimals: number = 0): string {
    return value.toFixed(decimals);
  }

  getGoalsPerGame(season: any): number {
    return season.stats.games > 0 ? season.stats.goals / season.stats.games : 0;
  }

  getAssistsPerGame(season: any): number {
    return season.stats.games > 0 ? season.stats.assists / season.stats.games : 0;
  }

  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  async deleteSeason(season: any): Promise<void> {
    if (season.seasonId === 'current') {
      this.notificationService.error('Cannot delete current season');
      return;
    }

    const confirmDelete = confirm(
      `⚠️ Are you sure you want to delete ${season.seasonName}?\n\n` +
      'This action cannot be undone and will permanently remove this season from the player\'s history.'
    );

    if (!confirmDelete) {
      return;
    }

    try {
      // Extract season number from seasonId (format: "season-X")
      const seasonNumber = parseInt(season.seasonId.replace('season-', ''));

      await this.playerService.deletePlayerSeasonHistory(this.playerId, seasonNumber);

      this.notificationService.success(`${season.seasonName} deleted successfully`);

      // Reload the season history
      await this.loadSeasonHistory();

    } catch (error) {
      console.error('Error deleting season:', error);
      this.notificationService.error('Failed to delete season');
    }
  }
}
