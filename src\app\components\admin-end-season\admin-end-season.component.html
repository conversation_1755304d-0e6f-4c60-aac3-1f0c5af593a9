<div class="admin-end-season-container">
  <div class="header">
    <button class="back-btn" (click)="goBack()">
      <i class="fas fa-arrow-left"></i>
      Back to Admin Dashboard
    </button>
    <h1>End Season</h1>
    <p class="subtitle">Record season achievements and end the current season</p>
  </div>

  <div class="content">
    <!-- League Selection -->
    <div class="selection-section">
      <div class="form-group">
        <label for="league-select">Select League:</label>
        <select 
          id="league-select" 
          [(ngModel)]="selectedLeagueId" 
          (change)="onLeagueChange()"
          class="form-control">
          <option value="">Choose a league...</option>
          <option *ngFor="let league of leagues" [value]="league.id">
            {{ league.name }}
          </option>
        </select>
      </div>

      <div class="form-group" *ngIf="selectedLeagueId">
        <label for="season-input">Season Number:</label>
        <input 
          id="season-input"
          type="number" 
          [(ngModel)]="selectedSeasonNumber" 
          (change)="loadPreview()"
          class="form-control"
          min="1">
      </div>

      <button 
        class="refresh-btn" 
        (click)="refreshPreview()" 
        [disabled]="isLoadingPreview || !selectedLeagueId">
        <i class="fas fa-sync-alt" [class.fa-spin]="isLoadingPreview"></i>
        Refresh Preview
      </button>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoadingPreview" class="loading-section">
      <div class="spinner"></div>
      <p>Loading season preview...</p>
    </div>

    <!-- Season Preview -->
    <div *ngIf="preview && !isLoadingPreview" class="preview-section">
      <div class="season-info">
        <h2>Season {{ preview.seasonNumber }} - {{ preview.leagueName }}</h2>
        
        <div class="status-cards">
          <div class="status-card" [class.complete]="preview.isSeasonComplete" [class.incomplete]="!preview.isSeasonComplete">
            <i class="fas" [class.fa-check-circle]="preview.isSeasonComplete" [class.fa-exclamation-triangle]="!preview.isSeasonComplete"></i>
            <div class="status-info">
              <h3>Season Status</h3>
              <p>{{ preview.isSeasonComplete ? 'Complete' : 'Incomplete' }}</p>
              <small>{{ preview.completedGames }}/{{ preview.totalGames }} games played</small>
            </div>
          </div>

          <div class="status-card" *ngIf="preview.warnings.length > 0">
            <i class="fas fa-exclamation-triangle"></i>
            <div class="status-info">
              <h3>Warnings</h3>
              <ul>
                <li *ngFor="let warning of preview.warnings">{{ warning }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Championship Results -->
      <div class="achievements-section" *ngIf="preview.champion || preview.finalist || preview.thirdPlace">
        <h3><i class="fas fa-trophy"></i> Championship Results</h3>
        <div class="championship-results">
          <div class="champion-card" *ngIf="preview.champion">
            <div class="rank-badge champion">1st</div>
            <img [src]="preview.champion.teamImgUrl || 'assets/Icons/Team.png'" 
                 (error)="onTeamImageError($event)" 
                 alt="Champion">
            <div class="team-info">
              <h4>{{ preview.champion.teamName }}</h4>
              <p>Champion</p>
            </div>
          </div>

          <div class="champion-card" *ngIf="preview.finalist">
            <div class="rank-badge finalist">2nd</div>
            <img [src]="preview.finalist.teamImgUrl || 'assets/Icons/Team.png'" 
                 (error)="onTeamImageError($event)" 
                 alt="Finalist">
            <div class="team-info">
              <h4>{{ preview.finalist.teamName }}</h4>
              <p>Finalist</p>
            </div>
          </div>

          <div class="champion-card" *ngIf="preview.thirdPlace">
            <div class="rank-badge third">3rd</div>
            <img [src]="preview.thirdPlace.teamImgUrl || 'assets/Icons/Team.png'" 
                 (error)="onTeamImageError($event)" 
                 alt="Third Place">
            <div class="team-info">
              <h4>{{ preview.thirdPlace.teamName }}</h4>
              <p>Third Place</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Scorers -->
      <div class="achievements-section" *ngIf="preview.topScorers.length > 0">
        <h3><i class="fas fa-futbol"></i> Top Scorers</h3>
        <div class="player-achievements">
          <div class="player-card"
               *ngFor="let scorer of preview.topScorers"
               [class.first-place]="scorer.rank === 1"
               [class.second-place]="scorer.rank === 2"
               [class.third-place]="scorer.rank === 3">
            <div class="rank-badge"
                 [class.gold]="scorer.rank === 1"
                 [class.silver]="scorer.rank === 2"
                 [class.bronze]="scorer.rank === 3">{{ getRankSuffix(scorer.rank) }}</div>
            <img [src]="scorer.playerImgUrl || 'assets/Icons/Player.png'"
                 (error)="onImageError($event)"
                 alt="Player">
            <div class="player-info">
              <h4>{{ scorer.playerName }}</h4>
              <p>{{ scorer.teamName }}</p>
              <div class="stat">{{ scorer.goals }} goals</div>
              <div class="stat-ratio">{{ getGoalsPerGameRatio(scorer) }} per game</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Assists -->
      <div class="achievements-section" *ngIf="preview.topAssists.length > 0">
        <h3><i class="fas fa-hands-helping"></i> Top Assists</h3>
        <div class="player-achievements">
          <div class="player-card"
               *ngFor="let assister of preview.topAssists"
               [class.first-place]="assister.rank === 1"
               [class.second-place]="assister.rank === 2"
               [class.third-place]="assister.rank === 3">
            <div class="rank-badge"
                 [class.gold]="assister.rank === 1"
                 [class.silver]="assister.rank === 2"
                 [class.bronze]="assister.rank === 3">{{ getRankSuffix(assister.rank) }}</div>
            <img [src]="assister.playerImgUrl || 'assets/Icons/Player.png'"
                 (error)="onImageError($event)"
                 alt="Player">
            <div class="player-info">
              <h4>{{ assister.playerName }}</h4>
              <p>{{ assister.teamName }}</p>
              <div class="stat">{{ assister.assists }} assists</div>
              <div class="stat-ratio">{{ getAssistsPerGameRatio(assister) }} per game</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Best by Position -->
      <div class="achievements-section" *ngIf="preview.bestByPosition.length > 0">
        <h3><i class="fas fa-star"></i> Best by Position</h3>
        <div class="player-achievements">
          <div class="player-card" *ngFor="let player of preview.bestByPosition">
            <div class="position-badge">{{ player.position }}</div>
            <img [src]="player.playerImgUrl || 'assets/Icons/Player.png'" 
                 (error)="onImageError($event)" 
                 alt="Player">
            <div class="player-info">
              <h4>{{ player.playerName }}</h4>
              <p>{{ player.teamName }}</p>
              <div class="stat">{{ player.avgRating.toFixed(2) }} avg rating</div>
              <div class="stat">{{ player.games }} games</div>
            </div>
          </div>
        </div>
      </div>

      <!-- End Season Button -->
      <div class="action-section">
        <button 
          class="end-season-btn" 
          (click)="showEndSeasonDialog()"
          [disabled]="isEndingSeason">
          <i class="fas fa-flag-checkered"></i>
          End Season & Record Achievements
        </button>
      </div>
    </div>

    <!-- No Preview State -->
    <div *ngIf="!preview && !isLoadingPreview && selectedLeagueId" class="no-preview">
      <i class="fas fa-info-circle"></i>
      <p>No preview available. Click "Refresh Preview" to load season data.</p>
    </div>
  </div>
</div>

<!-- Confirmation Dialog -->
<div class="modal-overlay" *ngIf="showConfirmDialog" (click)="cancelEndSeason()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>Confirm End Season</h3>
      <button class="close-btn" (click)="cancelEndSeason()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    
    <div class="modal-body">
      <p>Are you sure you want to end Season {{ preview?.seasonNumber }} for {{ preview?.leagueName }}?</p>
      
      <div class="warning" *ngIf="preview && !preview.isSeasonComplete">
        <i class="fas fa-exclamation-triangle"></i>
        <p>Warning: This season is not complete ({{ preview.remainingGames }} games remaining).</p>
      </div>

      <div class="confirmation-options">
        <label class="checkbox-label" *ngIf="preview && !preview.isSeasonComplete">
          <input type="checkbox" [(ngModel)]="forceEnd">
          <span class="checkmark"></span>
          Force end season anyway
        </label>
      </div>

      <p class="action-description">
        This action will:
      </p>
      <ul class="action-list">
        <li>Record all season achievements permanently</li>
        <li>Update player and team achievement histories</li>
        <li>Mark the season as ended</li>
        <li><strong>This action cannot be undone</strong></li>
      </ul>
    </div>
    
    <div class="modal-footer">
      <button class="cancel-btn" (click)="cancelEndSeason()" [disabled]="isEndingSeason">
        Cancel
      </button>
      <button 
        class="confirm-btn" 
        (click)="confirmEndSeason()" 
        [disabled]="isEndingSeason || (preview && !preview.isSeasonComplete && !forceEnd)">
        <i class="fas fa-spinner fa-spin" *ngIf="isEndingSeason"></i>
        <i class="fas fa-flag-checkered" *ngIf="!isEndingSeason"></i>
        {{ isEndingSeason ? 'Ending Season...' : 'End Season' }}
      </button>
    </div>
  </div>
</div>
