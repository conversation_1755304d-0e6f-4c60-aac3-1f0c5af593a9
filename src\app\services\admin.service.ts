import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

export interface PlayerAssociationRequest {
  id: string;
  userId: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  playerId: {
    id: string;
    name: string;
    position: string;
    team?: any;
    imgUrl?: string;
  };
  playerEmail?: string;
  status: 'pending' | 'approved' | 'rejected';
  requestedAt: Date;
  processedAt?: Date;
  processedBy?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  reason?: string;
  userMessage?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AdminService {

  constructor(private apiService: ApiService) {}

  /**
   * Get all pending player association requests
   */
  async getPendingAssociationRequests(): Promise<PlayerAssociationRequest[]> {
    try {
      const response = await this.apiService.get<{ requests: PlayerAssociationRequest[] }>('player-association-requests/pending');
      return response.data.requests;
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Approve a player association request
   */
  async approveAssociationRequest(requestId: string): Promise<PlayerAssociationRequest> {
    try {
      const response = await this.apiService.put<{ request: PlayerAssociationRequest }>(`player-association-requests/${requestId}/approve`, {});
      return response.data.request;
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Reject a player association request
   */
  async rejectAssociationRequest(requestId: string, reason?: string): Promise<PlayerAssociationRequest> {
    try {
      const response = await this.apiService.put<{ request: PlayerAssociationRequest }>(`player-association-requests/${requestId}/reject`, { reason });
      return response.data.request;
    } catch (error: any) {
      throw error;
    }
  }
}
