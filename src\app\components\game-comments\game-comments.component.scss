.game-comments {
  background: var(--surface-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;

  .comments-header {
    margin-bottom: var(--spacing-lg);

    .comments-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-size: var(--text-lg);
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      margin: 0;

      i {
        color: var(--primary-500);
      }

      .comment-count {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: var(--font-weight-normal);
      }
    }
  }

  .new-comment-form {
    margin-bottom: var(--spacing-lg);

    .comment-input-container {
      .comment-input {
        width: 100%;
        max-width: 100%;
        min-height: 80px;
        padding: var(--spacing-md);
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-md);
        background: var(--surface-secondary);
        color: var(--text-primary);
        font-family: inherit;
        font-size: var(--text-sm);
        resize: vertical;
        transition: border-color 0.3s ease;
        box-sizing: border-box;

        &:focus {
          outline: none;
          border-color: var(--primary-500);
        }

        &::placeholder {
          color: var(--text-tertiary);
        }
      }

      .input-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: var(--spacing-sm);

        .character-count {
          font-size: var(--text-xs);
          color: var(--text-secondary);

          &.warning {
            color: var(--warning-500);
          }

          &.error {
            color: var(--error-500);
          }
        }

        .submit-btn {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          padding: var(--spacing-sm) var(--spacing-md);
          background: var(--primary-500);
          color: white;
          border: none;
          border-radius: var(--radius-md);
          font-size: var(--text-sm);
          font-weight: var(--font-weight-medium);
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            background: var(--primary-600);
            transform: translateY(-1px);
          }

          &:disabled {
            background: var(--surface-tertiary);
            color: var(--text-tertiary);
            cursor: not-allowed;
            transform: none;
          }

          i.fa-spin {
            animation: spin 1s linear infinite;
          }
        }
      }
    }
  }

  .login-prompt {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--info-50);
    border: 1px solid var(--info-200);
    border-radius: var(--radius-md);
    color: var(--info-700);
    margin-bottom: var(--spacing-lg);

    i {
      color: var(--info-500);
    }
  }

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-secondary);

    i {
      color: var(--primary-500);
      font-size: var(--text-lg);
    }
  }

  .no-comments {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    font-style: italic;

    i {
      color: var(--text-tertiary);
    }
  }

  .comments-list {
    .comment-item {
      margin-bottom: var(--spacing-lg);
      border-bottom: 1px solid var(--border-secondary);
      padding-bottom: var(--spacing-lg);

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }

      .comment-content {
        display: flex;
        gap: var(--spacing-md);

        .comment-avatar {
          flex-shrink: 0;

          .avatar-image {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;

            &.small {
              width: 32px;
              height: 32px;
            }
          }
        }

        .comment-body {
          flex: 1;

          .comment-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xs);

            .author-name {
              font-weight: var(--font-weight-semibold);
              color: var(--text-primary);
            }

            .comment-time {
              font-size: var(--text-xs);
              color: var(--text-secondary);
            }

            .edited-indicator {
              font-size: var(--text-xs);
              color: var(--text-tertiary);
              font-style: italic;
            }
          }

          .comment-text {
            color: var(--text-primary);
            line-height: 1.5;
            margin-bottom: var(--spacing-sm);
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-width: 100%;
          }

          .edit-form {
            margin-bottom: var(--spacing-sm);

            .edit-input {
              width: 100%;
              min-height: 60px;
              padding: var(--spacing-sm);
              border: 2px solid var(--border-primary);
              border-radius: var(--radius-sm);
              background: var(--surface-secondary);
              color: var(--text-primary);
              font-family: inherit;
              font-size: var(--text-sm);
              resize: vertical;

              &:focus {
                outline: none;
                border-color: var(--primary-500);
              }
            }

            .edit-actions {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: var(--spacing-xs);

              .character-count {
                font-size: var(--text-xs);
                color: var(--text-secondary);

                &.warning {
                  color: var(--warning-500);
                }

                &.error {
                  color: var(--error-500);
                }
              }

              .action-buttons {
                display: flex;
                gap: var(--spacing-xs);

                .cancel-btn,
                .save-btn {
                  padding: var(--spacing-xs) var(--spacing-sm);
                  border: none;
                  border-radius: var(--radius-sm);
                  font-size: var(--text-xs);
                  cursor: pointer;
                  transition: all 0.2s ease;

                  &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                  }
                }

                .cancel-btn {
                  background: var(--surface-tertiary);
                  color: var(--text-secondary);

                  &:hover:not(:disabled) {
                    background: var(--surface-quaternary);
                  }
                }

                .save-btn {
                  display: flex;
                  align-items: center;
                  gap: var(--spacing-xs);
                  background: var(--success-500);
                  color: white;

                  &:hover:not(:disabled) {
                    background: var(--success-600);
                  }
                }
              }
            }
          }

          .comment-actions {
            display: flex;
            gap: var(--spacing-md);

            .action-btn {
              display: flex;
              align-items: center;
              gap: var(--spacing-xs);
              padding: var(--spacing-xs) var(--spacing-sm);
              background: none;
              border: none;
              color: var(--text-secondary);
              font-size: var(--text-xs);
              cursor: pointer;
              border-radius: var(--radius-sm);
              transition: all 0.2s ease;

              &:hover:not(:disabled) {
                background: var(--surface-secondary);
                color: var(--text-primary);
              }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }

              &.like-btn.liked {
                color: var(--error-500);

                &:hover {
                  color: var(--error-600);
                }
              }

              &.delete-btn:hover:not(:disabled) {
                color: var(--error-500);
              }
            }
          }
        }
      }

      .reply-form {
        margin-left: 54px;
        margin-top: var(--spacing-md);

        .reply-input-container {
          .reply-input {
            width: 100%;
            min-height: 50px;
            padding: var(--spacing-sm);
            border: 2px solid var(--border-primary);
            border-radius: var(--radius-sm);
            background: var(--surface-secondary);
            color: var(--text-primary);
            font-family: inherit;
            font-size: var(--text-sm);
            resize: vertical;

            &:focus {
              outline: none;
              border-color: var(--primary-500);
            }
          }

          .reply-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--spacing-xs);

            .character-count {
              font-size: var(--text-xs);
              color: var(--text-secondary);

              &.warning {
                color: var(--warning-500);
              }

              &.error {
                color: var(--error-500);
              }
            }

            .action-buttons {
              display: flex;
              gap: var(--spacing-xs);

              .cancel-btn,
              .submit-btn {
                padding: var(--spacing-xs) var(--spacing-sm);
                border: none;
                border-radius: var(--radius-sm);
                font-size: var(--text-xs);
                cursor: pointer;
                transition: all 0.2s ease;

                &:disabled {
                  opacity: 0.6;
                  cursor: not-allowed;
                }
              }

              .cancel-btn {
                background: var(--surface-tertiary);
                color: var(--text-secondary);

                &:hover:not(:disabled) {
                  background: var(--surface-quaternary);
                }
              }

              .submit-btn {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
                background: var(--primary-500);
                color: white;

                &:hover:not(:disabled) {
                  background: var(--primary-600);
                }
              }
            }
          }
        }
      }

      .replies-container {
        margin-left: 54px;
        margin-top: var(--spacing-md);
        border-left: 2px solid var(--border-secondary);
        padding-left: var(--spacing-md);

        .reply-item {
          margin-bottom: var(--spacing-md);

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .load-more-container {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-lg);

    .load-more-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-md) var(--spacing-lg);
      background: var(--surface-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-md);
      color: var(--text-primary);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        background: var(--surface-tertiary);
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Mobile responsive styles
@media (max-width: 768px), (max-device-width: 768px) {
  .game-comments {
    // Reset any inherited styles that might cause issues
    position: static !important;
    transform: none !important;
    left: auto !important;
    right: auto !important;
    top: auto !important;
    bottom: auto !important;
    padding: 12px !important;
    margin: 0 5px 20px 5px !important;
    border-radius: 8px !important;
    width: calc(100% - 10px) !important;
    max-width: calc(100% - 10px) !important;
    min-height: auto !important;
    height: auto !important;
    box-sizing: border-box !important;
    overflow: visible !important;
    display: block !important;
    float: none !important;
    clear: both !important;

    .comments-header {
      margin-bottom: var(--spacing-md);

      .comments-title {
        font-size: var(--text-base);
        flex-wrap: wrap;
      }
    }

    .new-comment-form {
      margin-bottom: 20px !important;
      position: relative !important;
      z-index: 1 !important;

      .comment-input-container {
        .comment-input {
          min-height: 60px !important;
          padding: 10px !important;
          font-size: 14px !important;
          width: 100% !important;
          box-sizing: border-box !important;
        }

        .input-footer {
          display: flex !important;
          flex-direction: column !important;
          align-items: stretch !important;
          gap: 8px !important;
          margin-top: 8px !important;

          .character-count {
            text-align: center !important;
            font-size: 12px !important;
          }

          .submit-btn {
            width: 100% !important;
            padding: 10px !important;
            font-size: 14px !important;
            text-align: center !important;
          }
        }
      }
    }

    .comments-list {
      display: block !important;
      width: 100% !important;
      overflow: visible !important;
      position: static !important;
      clear: both !important;
      margin-top: 20px !important;

      .comment-item {
        display: block !important;
        margin: 0 0 var(--spacing-md, 16px) 0 !important;
        padding: var(--spacing-md, 16px) !important;
        border: 1px solid var(--border-secondary, #e5e5e5) !important;
        border-radius: var(--radius-md, 8px) !important;
        background: var(--surface-primary, #ffffff) !important;
        width: 100% !important;
        box-sizing: border-box !important;
        position: static !important;
        float: none !important;
        clear: both !important;
        box-shadow: var(--shadow-sm, 0 1px 3px rgba(0,0,0,0.1)) !important;

        .comment-content {
          display: flex !important;
          flex-direction: row !important;
          gap: var(--spacing-sm, 12px) !important;
          width: 100% !important;
          align-items: flex-start !important;

          .comment-avatar {
            flex-shrink: 0 !important;

            .avatar-image {
              width: 36px !important;
              height: 36px !important;
              border-radius: 50% !important;
              object-fit: cover !important;
              border: 2px solid var(--border-primary, #e5e5e5) !important;
            }
          }

          .comment-body {
            flex: 1 !important;
            min-width: 0 !important;

            .comment-header {
              display: flex !important;
              flex-wrap: wrap !important;
              gap: var(--spacing-xs, 8px) !important;
              margin-bottom: var(--spacing-xs, 8px) !important;

              .author-name {
                font-size: var(--text-sm, 14px) !important;
                font-weight: var(--font-weight-semibold, 600) !important;
                color: var(--text-primary, #333) !important;
              }

              .comment-time {
                font-size: var(--text-xs, 12px) !important;
                color: var(--text-secondary, #666) !important;
              }

              .edited-indicator {
                font-size: var(--text-xs, 12px) !important;
                color: var(--text-tertiary, #999) !important;
                font-style: italic !important;
              }
            }

            .comment-text {
              display: block !important;
              font-size: var(--text-sm, 14px) !important;
              line-height: 1.5 !important;
              color: var(--text-primary, #333) !important;
              word-wrap: break-word !important;
              overflow-wrap: break-word !important;
              margin-bottom: var(--spacing-sm, 12px) !important;
            }

            .comment-actions {
              display: flex !important;
              flex-wrap: wrap !important;
              gap: var(--spacing-xs, 8px) !important;
              margin-top: var(--spacing-xs, 8px) !important;

              .action-btn {
                font-size: var(--text-xs, 12px) !important;
                padding: var(--spacing-xs, 6px) var(--spacing-sm, 10px) !important;
                border: 1px solid var(--border-primary, #ddd) !important;
                border-radius: var(--radius-sm, 4px) !important;
                background: var(--surface-secondary, #f8f9fa) !important;
                color: var(--text-secondary, #666) !important;
                cursor: pointer !important;
                transition: all 0.2s ease !important;

                &:hover {
                  background: var(--surface-tertiary, #e9ecef) !important;
                  color: var(--text-primary, #333) !important;
                }

                &.liked {
                  color: var(--primary-500, #007bff) !important;
                  background: var(--primary-50, #e3f2fd) !important;
                }
              }
            }
          }
        }

        // Reply styles for mobile
        .replies-section {
          margin-top: 10px !important;
          margin-left: 0 !important;
          padding-left: 15px !important;
          border-left: 2px solid #ddd !important;

          .reply-item {
            margin-bottom: 10px !important;

            .comment-content {
              .comment-avatar .avatar-image {
                width: 28px !important;
                height: 28px !important;
              }
            }
          }
        }

        // Reply form for mobile
        .reply-form {
          margin-top: 10px !important;
          margin-left: 0 !important;
          padding: 10px !important;
          background: #f9f9f9 !important;
          border-radius: 6px !important;

          .reply-input {
            min-height: 50px !important;
            font-size: 14px !important;
            width: 100% !important;
            box-sizing: border-box !important;
          }

          .reply-actions {
            display: flex !important;
            flex-direction: column !important;
            gap: 8px !important;
            margin-top: 8px !important;

            .reply-btn, .cancel-btn {
              width: 100% !important;
              padding: 8px !important;
              text-align: center !important;
            }
          }
        }
      }
    }

    .load-more-container {
      margin-top: var(--spacing-md);

      .load-more-btn {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--text-sm);
      }
    }
  }
}

// Additional mobile styles for very small screens
@media (max-width: 480px) {
  .game-comments {
    padding: var(--spacing-sm, 12px) !important;
    margin: 0 3px !important;

    .comments-list {
      .comment-item {
        padding: var(--spacing-sm, 12px) !important;
        margin-bottom: var(--spacing-sm, 12px) !important;

        .comment-content {
          gap: var(--spacing-xs, 8px) !important;

          .comment-avatar .avatar-image {
            width: 32px !important;
            height: 32px !important;
          }

          .comment-body {
            .comment-header {
              gap: var(--spacing-xs, 6px) !important;

              .author-name {
                font-size: var(--text-sm, 14px) !important;
              }

              .comment-time {
                font-size: var(--text-xs, 12px) !important;
              }
            }

            .comment-text {
              font-size: var(--text-sm, 14px) !important;
              line-height: 1.4 !important;
            }
          }
        }
      }
    }
  }
}
