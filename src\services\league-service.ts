import { Types } from "mongoose";
import { inject, injectable } from "tsyringe";
import { BadRequestError } from "../errors";
import logger from "../config/logger";
import { FixtureMapper } from "../mappers/fixture-mapper";
import LeagueMapper from "../mappers/league-mapper";
import { AddFixtureData } from "../models/fixture";
import { AddGameData } from "../models/game/game";
import { ILeague } from "../models/league";
import { IFixtureService } from "../interfaces/fixture";
import { ILeagueService, ILeagueRepository } from "../interfaces/league";
import { ITeamService } from "../interfaces/team";
import { CacheService } from "../interfaces/util-services/cache-service.interface";
import { transactionService } from "./util-services/transaction-service";
import { AddSingleFixtureData, FixtureDTO, LeagueDTO, LeagueTableRow, TopAssister, TopScorer } from "@pro-clubs-manager/shared-dtos";
import { PlayoffDetails } from "../models/game/game";
import { AllTimeTopAvgRatingByPosition, MostHattricks, MostCleanSheets, MostWinningPercentageTeam, MostWinningPercentagePlayer } from "../repositories/game-repository";

// Extended interface to support playoff details
interface ExtendedAddSingleFixtureData extends AddSingleFixtureData {
  isPlayoff?: boolean;
  playoffDetails?: PlayoffDetails;
  games: Array<{
    homeTeamId: string;
    awayTeamId: string;
    isPlayoff?: boolean;
    playoffStage?: string;
    matchNumber?: number;
  }>;
}
import { IGameRepository } from "../interfaces/game";
import { ITeamOfTheWeekService } from "../interfaces/wrapper-services/team-of-the-week-service.interface";

const LEAGUE_TABLE_CACHE_KEY = "leagueTable";
const TOP_SCORERS_CACHE_KEY = "topScorers";
const TOP_ASSISTS_CACHE_KEY = "topAssists";
const ALL_TIME_TOP_SCORERS_CACHE_KEY = "allTimeTopScorers";
const ALL_TIME_TOP_ASSISTS_CACHE_KEY = "allTimeTopAssists";
const ALL_TIME_TOP_AVG_RATING_CACHE_KEY = "allTimeTopAvgRating";
const MOST_HATTRICKS_CACHE_KEY = "mostHattricks";
const MOST_CLEAN_SHEETS_CACHE_KEY = "mostCleanSheets";
const MOST_WINNING_PERCENTAGE_TEAMS_CACHE_KEY = "mostWinningPercentageTeams";
const MOST_WINNING_PERCENTAGE_PLAYERS_CACHE_KEY = "mostWinningPercentagePlayers";
const FREE_AGENTS_CACHE_KEY = "freeAgents";
const TOTW_CACHE_KEY = "totw";

@injectable()
export class LeagueService implements ILeagueService {
  constructor(
    @inject("ILeagueRepository") private leagueRepository: ILeagueRepository,
    @inject("IGameRepository") private gameRepository: IGameRepository,
    @inject("ITeamService") private teamService: ITeamService,
    @inject("CacheService") private cacheService: CacheService,
    @inject("IFixtureService") private fixtureService: IFixtureService,
    @inject("ITeamOfTheWeekService") private teamOfTheWeekService: ITeamOfTheWeekService
  ) {}

  async startNewSeason(leagueId: string, startDateString: string, endDateString?: string): Promise<void> {
    logger.info(`LeagueService: Starting new season for league with id ${leagueId}`);

    const league = await this.leagueRepository.getLeagueById(leagueId);

    const startDate = new Date(startDateString);
    const endDate = endDateString ? new Date(endDateString) : undefined;

    const newSeasonNumber = league.currentSeason ? league.currentSeason.seasonNumber + 1 : 1;

    await transactionService.withTransaction(async (session) => {
      if (league.currentSeason) {
        league.seasonsHistory.push(league.currentSeason);
      }

      league.currentSeason = {
        seasonNumber: newSeasonNumber,
        winner: null,
        fixtures: [],
        startDate,
        endDate,
        teams: league.teams,
      };

      await league.save({ session });

      // start new season for all the teams in the league
      await this.teamService.startNewLeagueSeason(league._id as any, newSeasonNumber, session);
    });
  }

  async addLeague(name: string, imgUrl?: string): Promise<ILeague> {
    const isLeagueExists = await this.leagueRepository.isLeagueNameExists(name);
    if (isLeagueExists) {
      throw new BadRequestError(`League ${name} already exists`);
    }

    logger.info(`LeagueService: Adding league with name ${name}`);

    const league = await this.leagueRepository.createLeague(name, imgUrl);

    return league;
  }

  async createFixture(leagueId: string, addFixtureData: AddSingleFixtureData): Promise<FixtureDTO> {
    // Cast to extended type to access playoff properties
    const extendedData = addFixtureData as ExtendedAddSingleFixtureData;
    const { round, games, isPlayoff, playoffDetails } = extendedData;

    logger.info(`LeagueService: adding fixture ${round} for league ${leagueId}`);

    const league = await this.leagueRepository.getLeagueById(leagueId);

    // const isFixtureRoundExists = await Fixture.exists({ round, league: league._id });

    // if (isFixtureRoundExists) {
    //   throw new BadRequestError(`Fixture with round ${round} already exists`);
    // }

    const startDate = new Date(addFixtureData.startDate);
    const endDate = new Date(addFixtureData.endDate);

    const gamesData: AddGameData[] = games.map((game, index) => ({
      awayTeam: new Types.ObjectId(game.awayTeamId),
      homeTeam: new Types.ObjectId(game.homeTeamId),
      round,
      isPlayoff: game.isPlayoff || isPlayoff || false,
      playoffStage: game.playoffStage as any,
      matchNumber: game.matchNumber || (isPlayoff ? index + 1 : undefined),
    }));
    return await transactionService.withTransaction(async (session) => {
      const fixture = await this.fixtureService.generateFixture(
        {
          leagueId: league._id as any,
          seasonNumber: league.currentSeason.seasonNumber,
          round,
          gamesData,
          startDate,
          endDate,
          isPlayoff,
          playoffDetails
        },
        session
      );
      league.currentSeason.fixtures.push(fixture._id as any); // add fixture to the latest season

      const fixtureDto = await FixtureMapper.mapToDto(fixture);
      await league.save({ session });

      return fixtureDto;
    });
  }

  async generateLeagueFixtures(leagueId: string, leagueStartDate: string, fixturesPerWeek: number): Promise<FixtureDTO[]> {
    logger.info(`LeagueService: Generating fixtures for league with id ${leagueId}`);

    const league = await this.leagueRepository.getLeagueById(leagueId);

    if (league.teams.length < 2) {
      throw new BadRequestError(`League with id ${leagueId} must have at least 2 teams`);
    }

    const startDate = new Date(leagueStartDate);

    const fixturesData = this.generateFixturesData(league.teams, league._id as any, startDate, fixturesPerWeek);
    return await transactionService.withTransaction(async (session) => {
      const fixtures = [];

      for (const fixtureData of fixturesData) {
        const fixture = await this.fixtureService.generateFixture(fixtureData, session);
        fixtures.push(fixture);
      }
      league.currentSeason.fixtures = fixtures.map((fixture) => fixture._id as any);
      await league.save({ session });
      return await FixtureMapper.mapToDtos(fixtures);
    });
  }

  private generateFixturesData(teams: Types.ObjectId[], _leagueId: Types.ObjectId, leagueStartDate: Date, fixturesPerWeek: number): AddFixtureData[] {
    logger.info(`LeagueService: generating fixtures data`);

    // TODO: handle dummy team
    // TODO: use random to generate the fixtures better

    const fixturesCount = teams.length - 1;
    const gamesPerFixture = Math.ceil(teams.length / 2);
    const fixtures: AddFixtureData[] = [];

    let startDate: Date = leagueStartDate;
    let endDate: Date = new Date(leagueStartDate.getTime());
    endDate.setDate(endDate.getDate() + 7);

    let reverseOrder = false;
    // two league rounds
    for (let k = 0; k < 2; k++) {
      for (let round = k * fixturesCount; round < k * fixturesCount + fixturesCount; round++) {
        const fixtureGames: AddGameData[] = [];
        for (let j = 0; j < gamesPerFixture; j++) {
          const homeTeamIndex = reverseOrder ? teams.length - 1 - j : j;
          const awayTeamIndex = reverseOrder ? j : teams.length - 1 - j;
          fixtureGames.push({
            homeTeam: teams[homeTeamIndex],
            awayTeam: teams[awayTeamIndex],
          });
        }

        // fixtures.push({ leagueId, gamesData: fixtureGames, round: round + 1, startDate: new Date(startDate.getTime()), endDate: new Date(endDate.getTime()) });

        // updates date after we done all fixtures of the week
        if ((round + 1) % fixturesPerWeek === 0) {
          startDate.setDate(endDate.getDate() + 1);
          endDate.setDate(startDate.getDate() + 7);
        }
        teams.splice(1, 0, teams.pop()!);
      }
      reverseOrder = !reverseOrder;
    }
    return fixtures;
  }

  async deleteAllLeagueFixtures(leagueId: string): Promise<void> {
    logger.info(`LeagueService: removing all fixtures for league with id ${leagueId}`);

    const league = await this.leagueRepository.getLeagueById(leagueId);

    await transactionService.withTransaction(async (session) => {
      await this.fixtureService.deleteFixtures(league.currentSeason.fixtures, session);
      try {
        league.currentSeason.fixtures = [];
        await league.save({ session });
      } catch (e) {
        logger.error(e);
        throw new Error(`failed to remove fixtures for league with id ${leagueId}`);
      }
    });
  }

  async deleteLeague(id: string): Promise<void> {
    await this.leagueRepository.deleteLeague(id);

    await this.cacheService.delete(`${LEAGUE_TABLE_CACHE_KEY}:${id}`);
  }

  async getLeagueById(id: string): Promise<LeagueDTO> {
    logger.info(`LeagueService: getting league with id ${id}`);
    const league = await this.leagueRepository.getLeagueById(id);

    return await LeagueMapper.toDto(league);
  }

  async getAllLeagues(): Promise<LeagueDTO[]> {
    logger.info(`LeagueService: getting all leagues`);
    const leagues = await this.leagueRepository.getAllLeagues();
    return await LeagueMapper.toDtos(leagues);
  }

  async getLeagueTable(leagueId: string): Promise<LeagueTableRow[]> {
    // look for the table in cache
    let leagueTable = await this.getLeagueTableFromCache(leagueId);
    if (!leagueTable) {
      // Calculate the league table if it's not in the cache
      leagueTable = await this.calculateLeagueTable(leagueId);
      await this.setLeagueTableInCache(leagueId, leagueTable);
    }
    return leagueTable;
  }

  async getLeagueTeamOfTheWeek(leagueId: string, startDate: Date, endDate: Date): Promise<{}> {
    logger.info(`LeagueService: getting team of the week for league with id ${leagueId}`);

    // Create cache key based on league, start and end dates
    const cacheKey = `${TOTW_CACHE_KEY}:${leagueId}:${startDate.toISOString().split('T')[0]}:${endDate.toISOString().split('T')[0]}`;

    // Try to get from cache first
    const cachedTOTW = await this.getTOTWFromCache(cacheKey);
    if (cachedTOTW) {
      logger.info("LeagueService: returning cached TOTW");
      return cachedTOTW;
    }

    const league = await this.leagueRepository.getLeagueById(leagueId);

    const leagueGames = await this.gameRepository.getLeaguePlayedGamesByDate(
      { leagueId: league._id as any, seasonNumber: league.currentSeason.seasonNumber },
      startDate,
      endDate
    );

    if (!leagueGames.length) {
      throw new BadRequestError(`No games played for the week between ${startDate} and ${endDate} in league ${league.name}`);
    }

    const totw = await this.teamOfTheWeekService.getTeamOfTheWeek(leagueGames);

    // Cache for 6 hours
    await this.setTOTWInCache(cacheKey, totw);

    return totw;
  }

  private async setLeagueTableInCache(leagueId: string, leagueTable: LeagueTableRow[]): Promise<void> {
    // Cache for 30 minutes (league table changes more frequently)
    await this.cacheService.set(`${LEAGUE_TABLE_CACHE_KEY}:${leagueId}`, leagueTable, 30 * 60 * 1000);
  }

  private async getLeagueTableFromCache(leagueId: string): Promise<LeagueTableRow[] | null> {
    const leagueTable = await this.cacheService.get(`${LEAGUE_TABLE_CACHE_KEY}:${leagueId}`);
    if (!leagueTable) return null;

    return JSON.parse(leagueTable) as LeagueTableRow[];
  }

  async updateLeagueTable(leagueId: string) {
    const leagueTable = await this.calculateLeagueTable(leagueId);
    await this.setLeagueTableInCache(leagueId, leagueTable);
  }

  async getTeamPosition(teamId: string, leagueId: string): Promise<number> {
    const leagueTable = await this.getLeagueTable(leagueId);
    const position = leagueTable.findIndex((row) => row.teamId === teamId) + 1;
    return position;
  }

  private async calculateLeagueTable(leagueId: string): Promise<LeagueTableRow[]> {
    const tableRows = await this.teamService.getTeamsStatsByLeague(leagueId);

    this.sortTableRows(tableRows);

    return tableRows;
  }

  private sortTableRows(tableRows: LeagueTableRow[]) {
    // Sort the tableRows based on points, goal difference, etc.
    tableRows.sort((a, b) => {
      if (a.points !== b.points) {
        return b.points - a.points; // Higher points first
      }

      if (a.goalDifference !== b.goalDifference) {
        return b.goalDifference - a.goalDifference; // Higher goal difference first
      }

      return b.goalsScored - a.goalsScored; // Higher goals scored first
    });
  }

  async getTopScorers(leagueId: string, limit: number = 10): Promise<TopScorer[]> {
    logger.info(`LeagueService: getting top scorers for ${leagueId}`);
    let topScorers = await this.getTopScorersFromCache(leagueId);
    if (!topScorers) {
      logger.info(`calculating top scorers for league with id ${leagueId}`);
      topScorers = await this.leagueRepository.calculateLeagueTopScorers(leagueId, limit);

      await this.setTopScorersInCache(leagueId, topScorers);
    }

    return topScorers;
  }

  async getTopAssists(leagueId: string, limit: number = 50): Promise<TopAssister[]> {
    logger.info(`LeagueService: getting top assists for league with id ${leagueId}`);

    let topAssists = await this.getTopAssistsFromCache(leagueId);
    if (!topAssists) {
      logger.info(`calculating top assists for league with id ${leagueId}`);
      topAssists = await this.leagueRepository.calculateLeagueTopAssisters(leagueId, limit);
      await this.setTopAssistsInCache(leagueId, topAssists);
    }

    return topAssists.slice(0, limit); // Apply limit to cached results too
  }

  async getAllTimeTopScorers(leagueId: string, limit: number = 50): Promise<TopScorer[]> {
    logger.info(`LeagueService: getting all-time top scorers for ${leagueId}`);
    let allTimeTopScorers = await this.getAllTimeTopScorersFromCache(leagueId);
    if (!allTimeTopScorers) {
      logger.info(`calculating all-time top scorers for league with id ${leagueId}`);
      allTimeTopScorers = await this.leagueRepository.calculateAllTimeTopScorers(leagueId, limit);
      await this.setAllTimeTopScorersInCache(leagueId, allTimeTopScorers);
    }

    return allTimeTopScorers;
  }

  async getAllTimeTopAssisters(leagueId: string, limit: number = 50): Promise<TopAssister[]> {
    logger.info(`LeagueService: getting all-time top assisters for ${leagueId}`);
    let allTimeTopAssisters = await this.getAllTimeTopAssistersFromCache(leagueId);
    if (!allTimeTopAssisters) {
      logger.info(`calculating all-time top assisters for league with id ${leagueId}`);
      allTimeTopAssisters = await this.leagueRepository.calculateAllTimeTopAssisters(leagueId, limit);
      await this.setAllTimeTopAssistersInCache(leagueId, allTimeTopAssisters);
    }

    return allTimeTopAssisters;
  }

  private async getTopScorersFromCache(leagueId: string): Promise<TopScorer[] | null> {
    const cachedData = await this.cacheService.get(`${TOP_SCORERS_CACHE_KEY}:${leagueId}`);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }
  private async getTopAssistsFromCache(leagueId: string): Promise<TopAssister[] | null> {
    const cachedData = await this.cacheService.get(`${TOP_ASSISTS_CACHE_KEY}:${leagueId}`);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }

  async setTopAssistsInCache(leagueId: string, players: TopAssister[]) {
    // Cache for 12 hours (twice a day refresh)
    await this.cacheService.set(`${TOP_ASSISTS_CACHE_KEY}:${leagueId}`, players, 12 * 60 * 60 * 1000);
  }

  private async setTopScorersInCache(leagueId: string, players: TopScorer[]): Promise<void> {
    // Cache for 12 hours (twice a day refresh)
    await this.cacheService.set(`${TOP_SCORERS_CACHE_KEY}:${leagueId}`, players, 12 * 60 * 60 * 1000);
  }

  private async getAllTimeTopScorersFromCache(leagueId: string): Promise<TopScorer[] | null> {
    const cachedData = await this.cacheService.get(`${ALL_TIME_TOP_SCORERS_CACHE_KEY}:${leagueId}`);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }

  private async getAllTimeTopAssistersFromCache(leagueId: string): Promise<TopAssister[] | null> {
    const cachedData = await this.cacheService.get(`${ALL_TIME_TOP_ASSISTS_CACHE_KEY}:${leagueId}`);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }

  private async setAllTimeTopScorersInCache(leagueId: string, players: TopScorer[]): Promise<void> {
    // Cache for 24 hours (all-time stats change less frequently)
    await this.cacheService.set(`${ALL_TIME_TOP_SCORERS_CACHE_KEY}:${leagueId}`, players, 24 * 60 * 60 * 1000);
  }

  private async setAllTimeTopAssistersInCache(leagueId: string, players: TopAssister[]): Promise<void> {
    // Cache for 24 hours (all-time stats change less frequently)
    await this.cacheService.set(`${ALL_TIME_TOP_ASSISTS_CACHE_KEY}:${leagueId}`, players, 24 * 60 * 60 * 1000);
  }

  private async getTOTWFromCache(cacheKey: string): Promise<any | null> {
    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }

  private async setTOTWInCache(cacheKey: string, totw: any): Promise<void> {
    // Cache for 6 hours
    await this.cacheService.set(cacheKey, totw, 6 * 60 * 60 * 1000);
  }

  async getAllTimeTopAvgRatingByPosition(leagueId: string, position: string, minimumGames: number, limit: number = 50): Promise<AllTimeTopAvgRatingByPosition[]> {
    logger.info(`LeagueService: getting all-time top avg rating by position for ${leagueId}, position: ${position}`);
    let allTimeTopAvgRating = await this.getAllTimeTopAvgRatingFromCache(leagueId, position, minimumGames);
    if (!allTimeTopAvgRating) {
      logger.info(`calculating all-time top avg rating by position for league with id ${leagueId}`);
      allTimeTopAvgRating = await this.leagueRepository.calculateAllTimeTopAvgRatingByPosition(leagueId, position, minimumGames, limit);
      await this.setAllTimeTopAvgRatingInCache(leagueId, position, minimumGames, allTimeTopAvgRating);
    }

    return allTimeTopAvgRating;
  }

  async getMostHattricks(leagueId: string, limit: number = 50): Promise<MostHattricks[]> {
    logger.info(`LeagueService: getting most hattricks for ${leagueId}`);
    let mostHattricks = await this.getMostHattricksFromCache(leagueId);
    if (!mostHattricks) {
      logger.info(`calculating most hattricks for league with id ${leagueId}`);
      mostHattricks = await this.leagueRepository.calculateMostHattricks(leagueId, limit);
      await this.setMostHattricksInCache(leagueId, mostHattricks);
    }

    return mostHattricks;
  }

  async getMostCleanSheets(leagueId: string, limit: number = 50): Promise<MostCleanSheets[]> {
    logger.info(`LeagueService: getting most clean sheets for ${leagueId}`);
    let mostCleanSheets = await this.getMostCleanSheetsFromCache(leagueId);
    if (!mostCleanSheets) {
      logger.info(`calculating most clean sheets for league with id ${leagueId}`);
      mostCleanSheets = await this.leagueRepository.calculateMostCleanSheets(leagueId, limit);
      await this.setMostCleanSheetsInCache(leagueId, mostCleanSheets);
    }

    return mostCleanSheets;
  }

  // Cache methods for new statistics
  private async getAllTimeTopAvgRatingFromCache(leagueId: string, position: string, minimumGames: number): Promise<AllTimeTopAvgRatingByPosition[] | null> {
    const cacheKey = `${ALL_TIME_TOP_AVG_RATING_CACHE_KEY}:${leagueId}:${position}:${minimumGames}`;
    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }

  private async setAllTimeTopAvgRatingInCache(leagueId: string, position: string, minimumGames: number, data: AllTimeTopAvgRatingByPosition[]): Promise<void> {
    const cacheKey = `${ALL_TIME_TOP_AVG_RATING_CACHE_KEY}:${leagueId}:${position}:${minimumGames}`;
    // Cache for 24 hours (daily refresh)
    await this.cacheService.set(cacheKey, data, 24 * 60 * 60 * 1000);
  }

  private async getMostHattricksFromCache(leagueId: string): Promise<MostHattricks[] | null> {
    const cachedData = await this.cacheService.get(`${MOST_HATTRICKS_CACHE_KEY}:${leagueId}`);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }

  private async setMostHattricksInCache(leagueId: string, data: MostHattricks[]): Promise<void> {
    // Cache for 24 hours (daily refresh)
    await this.cacheService.set(`${MOST_HATTRICKS_CACHE_KEY}:${leagueId}`, data, 24 * 60 * 60 * 1000);
  }

  private async getMostCleanSheetsFromCache(leagueId: string): Promise<MostCleanSheets[] | null> {
    const cachedData = await this.cacheService.get(`${MOST_CLEAN_SHEETS_CACHE_KEY}:${leagueId}`);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }

  private async setMostCleanSheetsInCache(leagueId: string, data: MostCleanSheets[]): Promise<void> {
    // Cache for 24 hours (daily refresh)
    await this.cacheService.set(`${MOST_CLEAN_SHEETS_CACHE_KEY}:${leagueId}`, data, 24 * 60 * 60 * 1000);
  }

  async getMostWinningPercentageTeams(leagueId: string, minimumGames: number = 10, limit: number = 50): Promise<MostWinningPercentageTeam[]> {
    logger.info(`LeagueService: getting most winning percentage teams for ${leagueId}`);
    let mostWinningPercentageTeams = await this.getMostWinningPercentageTeamsFromCache(leagueId, minimumGames);
    if (!mostWinningPercentageTeams) {
      logger.info(`calculating most winning percentage teams for league with id ${leagueId}`);
      mostWinningPercentageTeams = await this.leagueRepository.calculateMostWinningPercentageTeams(leagueId, minimumGames, limit);
      await this.setMostWinningPercentageTeamsInCache(leagueId, minimumGames, mostWinningPercentageTeams);
    }

    return mostWinningPercentageTeams;
  }

  async getMostWinningPercentagePlayers(leagueId: string, minimumGames: number = 10, limit: number = 50): Promise<MostWinningPercentagePlayer[]> {
    logger.info(`LeagueService: getting most winning percentage players for ${leagueId}`);
    let mostWinningPercentagePlayers = await this.getMostWinningPercentagePlayersFromCache(leagueId, minimumGames);
    if (!mostWinningPercentagePlayers) {
      logger.info(`calculating most winning percentage players for league with id ${leagueId}`);
      mostWinningPercentagePlayers = await this.leagueRepository.calculateMostWinningPercentagePlayers(leagueId, minimumGames, limit);
      await this.setMostWinningPercentagePlayersInCache(leagueId, minimumGames, mostWinningPercentagePlayers);
    }

    return mostWinningPercentagePlayers;
  }

  // Cache methods for winning percentage statistics
  private async getMostWinningPercentageTeamsFromCache(leagueId: string, minimumGames: number): Promise<MostWinningPercentageTeam[] | null> {
    const cacheKey = `${MOST_WINNING_PERCENTAGE_TEAMS_CACHE_KEY}:${leagueId}:${minimumGames}`;
    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }

  private async setMostWinningPercentageTeamsInCache(leagueId: string, minimumGames: number, data: MostWinningPercentageTeam[]): Promise<void> {
    const cacheKey = `${MOST_WINNING_PERCENTAGE_TEAMS_CACHE_KEY}:${leagueId}:${minimumGames}`;
    // Cache for 24 hours (daily refresh)
    await this.cacheService.set(cacheKey, data, 24 * 60 * 60 * 1000);
  }

  private async getMostWinningPercentagePlayersFromCache(leagueId: string, minimumGames: number): Promise<MostWinningPercentagePlayer[] | null> {
    const cacheKey = `${MOST_WINNING_PERCENTAGE_PLAYERS_CACHE_KEY}:${leagueId}:${minimumGames}`;
    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  }

  private async setMostWinningPercentagePlayersInCache(leagueId: string, minimumGames: number, data: MostWinningPercentagePlayer[]): Promise<void> {
    const cacheKey = `${MOST_WINNING_PERCENTAGE_PLAYERS_CACHE_KEY}:${leagueId}:${minimumGames}`;
    // Cache for 24 hours (daily refresh)
    await this.cacheService.set(cacheKey, data, 24 * 60 * 60 * 1000);
  }

  // Methods to clear caches when stats are updated
  async clearLeagueTableCache(leagueId: string): Promise<void> {
    await this.cacheService.delete(`${LEAGUE_TABLE_CACHE_KEY}:${leagueId}`);
  }

  // Methods to clear caches when stats are updated
  async clearTopScorersCache(leagueId: string): Promise<void> {
    await this.cacheService.delete(`${TOP_SCORERS_CACHE_KEY}:${leagueId}`);
  }

  async clearTopAssistsCache(leagueId: string): Promise<void> {
    await this.cacheService.delete(`${TOP_ASSISTS_CACHE_KEY}:${leagueId}`);
  }

  async clearAllTimeTopScorersCache(leagueId: string): Promise<void> {
    await this.cacheService.delete(`${ALL_TIME_TOP_SCORERS_CACHE_KEY}:${leagueId}`);
  }

  async clearAllTimeTopAssistersCache(leagueId: string): Promise<void> {
    await this.cacheService.delete(`${ALL_TIME_TOP_ASSISTS_CACHE_KEY}:${leagueId}`);
  }

  async clearMostHattricksCache(leagueId: string): Promise<void> {
    await this.cacheService.delete(`${MOST_HATTRICKS_CACHE_KEY}:${leagueId}`);
  }

  async clearMostCleanSheetsCache(leagueId: string): Promise<void> {
    await this.cacheService.delete(`${MOST_CLEAN_SHEETS_CACHE_KEY}:${leagueId}`);
  }

  async clearAllTimeTopAvgRatingCache(leagueId: string, position: string, minimumGames: number): Promise<void> {
    const cacheKey = `${ALL_TIME_TOP_AVG_RATING_CACHE_KEY}:${leagueId}:${position}:${minimumGames}`;
    await this.cacheService.delete(cacheKey);
  }

    // Methods to clear caches when stats are updated
  async clearFreeAgentsCache(leagueId: string): Promise<void> {
    await this.cacheService.delete(`${FREE_AGENTS_CACHE_KEY}:${leagueId}`);
  }

  async clearLeagueStatsCache(leagueId: string): Promise<void> {
    // Clear all league-related caches when stats are updated
    await Promise.all([
      this.clearTopScorersCache(leagueId),
      this.clearTopAssistsCache(leagueId),
      this.clearAllTimeTopScorersCache(leagueId),
      this.clearAllTimeTopAssistersCache(leagueId),
      this.clearMostHattricksCache(leagueId),
      this.clearMostCleanSheetsCache(leagueId),
      this.clearLeagueTableCache(leagueId)
    ]);
  }

  async syncPlayerStatsWithGameData(leagueId: string): Promise<{ updated: number; errors: string[] }> {
    return await this.leagueRepository.syncPlayerStatsWithGameData(leagueId);
  }
}
