/* === COMPREHENSIVE UTILITY CLASSES === */

/* === SPACING UTILITIES === */
.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 0.75rem !important; }
.p-4 { padding: 1rem !important; }
.p-5 { padding: 1.25rem !important; }
.p-6 { padding: 1.5rem !important; }
.p-8 { padding: 2rem !important; }
.p-10 { padding: 2.5rem !important; }
.p-12 { padding: 3rem !important; }
.p-16 { padding: 4rem !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !important; }
.px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
.px-3 { padding-left: 0.75rem !important; padding-right: 0.75rem !important; }
.px-4 { padding-left: 1rem !important; padding-right: 1rem !important; }
.px-6 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }
.px-8 { padding-left: 2rem !important; padding-right: 2rem !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
.py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }
.py-3 { padding-top: 0.75rem !important; padding-bottom: 0.75rem !important; }
.py-4 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
.py-6 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.py-8 { padding-top: 2rem !important; padding-bottom: 2rem !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 0.75rem !important; }
.m-4 { margin: 1rem !important; }
.m-5 { margin: 1.25rem !important; }
.m-6 { margin: 1.5rem !important; }
.m-8 { margin: 2rem !important; }
.m-auto { margin: auto !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: 0.25rem !important; margin-right: 0.25rem !important; }
.mx-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !important; }
.mx-3 { margin-left: 0.75rem !important; margin-right: 0.75rem !important; }
.mx-4 { margin-left: 1rem !important; margin-right: 1rem !important; }
.mx-6 { margin-left: 1.5rem !important; margin-right: 1.5rem !important; }
.mx-8 { margin-left: 2rem !important; margin-right: 2rem !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: 0.25rem !important; margin-bottom: 0.25rem !important; }
.my-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !important; }
.my-3 { margin-top: 0.75rem !important; margin-bottom: 0.75rem !important; }
.my-4 { margin-top: 1rem !important; margin-bottom: 1rem !important; }
.my-6 { margin-top: 1.5rem !important; margin-bottom: 1.5rem !important; }
.my-8 { margin-top: 2rem !important; margin-bottom: 2rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 0.75rem !important; }
.mt-4 { margin-top: 1rem !important; }
.mt-6 { margin-top: 1.5rem !important; }
.mt-8 { margin-top: 2rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 0.75rem !important; }
.mb-4 { margin-bottom: 1rem !important; }
.mb-6 { margin-bottom: 1.5rem !important; }
.mb-8 { margin-bottom: 2rem !important; }

/* === TYPOGRAPHY UTILITIES === */
.text-xs { font-size: 0.75rem !important; line-height: 1rem !important; }
.text-sm { font-size: 0.875rem !important; line-height: 1.25rem !important; }
.text-base { font-size: 1rem !important; line-height: 1.5rem !important; }
.text-lg { font-size: 1.125rem !important; line-height: 1.75rem !important; }
.text-xl { font-size: 1.25rem !important; line-height: 1.75rem !important; }
.text-2xl { font-size: 1.5rem !important; line-height: 2rem !important; }
.text-3xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }
.text-4xl { font-size: 2.25rem !important; line-height: 2.5rem !important; }
.text-5xl { font-size: 3rem !important; line-height: 1 !important; }

.font-thin { font-weight: 100 !important; }
.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }
.font-extrabold { font-weight: 800 !important; }
.font-black { font-weight: 900 !important; }

.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.uppercase { text-transform: uppercase !important; }
.lowercase { text-transform: lowercase !important; }
.capitalize { text-transform: capitalize !important; }

.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-tertiary { color: var(--text-tertiary) !important; }
.text-accent { color: var(--accent-primary) !important; }
.text-success { color: var(--success-500) !important; }
.text-warning { color: var(--warning-500) !important; }
.text-error { color: var(--error-500) !important; }
.text-info { color: var(--info-500) !important; }
.text-white { color: white !important; }

/* === LAYOUT UTILITIES === */
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.grid { display: grid !important; }
.block { display: block !important; }
.inline-block { display: inline-block !important; }
.hidden { display: none !important; }

.flex-row { flex-direction: row !important; }
.flex-col { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.items-start { align-items: flex-start !important; }
.items-center { align-items: center !important; }
.items-end { align-items: flex-end !important; }
.items-stretch { align-items: stretch !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-evenly { justify-content: space-evenly !important; }

.gap-0 { gap: 0 !important; }
.gap-1 { gap: 0.25rem !important; }
.gap-2 { gap: 0.5rem !important; }
.gap-3 { gap: 0.75rem !important; }
.gap-4 { gap: 1rem !important; }
.gap-6 { gap: 1.5rem !important; }
.gap-8 { gap: 2rem !important; }

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)) !important; }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }

/* === BACKGROUND UTILITIES === */
.bg-primary { background-color: var(--surface-primary) !important; }
.bg-secondary { background-color: var(--surface-secondary) !important; }
.bg-tertiary { background-color: var(--surface-tertiary) !important; }
.bg-accent { background-color: var(--accent-primary) !important; }
.bg-success { background-color: var(--success-500) !important; }
.bg-warning { background-color: var(--warning-500) !important; }
.bg-error { background-color: var(--error-500) !important; }
.bg-info { background-color: var(--info-500) !important; }
.bg-transparent { background-color: transparent !important; }

/* === BORDER UTILITIES === */
.border { border: 1px solid var(--border-primary) !important; }
.border-2 { border: 2px solid var(--border-primary) !important; }
.border-0 { border: 0 !important; }
.border-t { border-top: 1px solid var(--border-primary) !important; }
.border-b { border-bottom: 1px solid var(--border-primary) !important; }
.border-l { border-left: 1px solid var(--border-primary) !important; }
.border-r { border-right: 1px solid var(--border-primary) !important; }

.border-primary { border-color: var(--border-primary) !important; }
.border-secondary { border-color: var(--border-secondary) !important; }
.border-accent { border-color: var(--accent-primary) !important; }
.border-success { border-color: var(--success-500) !important; }
.border-warning { border-color: var(--warning-500) !important; }
.border-error { border-color: var(--error-500) !important; }

.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: 0.125rem !important; }
.rounded { border-radius: 0.25rem !important; }
.rounded-md { border-radius: 0.375rem !important; }
.rounded-lg { border-radius: 0.5rem !important; }
.rounded-xl { border-radius: 0.75rem !important; }
.rounded-2xl { border-radius: 1rem !important; }
.rounded-3xl { border-radius: 1.5rem !important; }
.rounded-full { border-radius: 9999px !important; }

/* === SHADOW UTILITIES === */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important; }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important; }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important; }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important; }

/* === POSITION UTILITIES === */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }

/* === WIDTH & HEIGHT UTILITIES === */
.w-auto { width: auto !important; }
.w-full { width: 100% !important; }
.w-screen { width: 100vw !important; }
.w-fit { width: fit-content !important; }

.h-auto { height: auto !important; }
.h-full { height: 100% !important; }
.h-screen { height: 100vh !important; }
.h-fit { height: fit-content !important; }

.min-h-0 { min-height: 0 !important; }
.min-h-full { min-height: 100% !important; }
.min-h-screen { min-height: 100vh !important; }

.max-w-xs { max-width: 20rem !important; }
.max-w-sm { max-width: 24rem !important; }
.max-w-md { max-width: 28rem !important; }
.max-w-lg { max-width: 32rem !important; }
.max-w-xl { max-width: 36rem !important; }
.max-w-2xl { max-width: 42rem !important; }
.max-w-4xl { max-width: 56rem !important; }
.max-w-full { max-width: 100% !important; }

/* === OVERFLOW UTILITIES === */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }

/* === CURSOR UTILITIES === */
.cursor-auto { cursor: auto !important; }
.cursor-default { cursor: default !important; }
.cursor-pointer { cursor: pointer !important; }
.cursor-wait { cursor: wait !important; }
.cursor-text { cursor: text !important; }
.cursor-move { cursor: move !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

/* === TRANSITION UTILITIES === */
.transition-none { transition: none !important; }
.transition-all { transition: all 0.15s ease-in-out !important; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out !important; }
.transition-opacity { transition: opacity 0.15s ease-in-out !important; }
.transition-shadow { transition: box-shadow 0.15s ease-in-out !important; }
.transition-transform { transition: transform 0.15s ease-in-out !important; }

.duration-75 { transition-duration: 75ms !important; }
.duration-100 { transition-duration: 100ms !important; }
.duration-150 { transition-duration: 150ms !important; }
.duration-200 { transition-duration: 200ms !important; }
.duration-300 { transition-duration: 300ms !important; }
.duration-500 { transition-duration: 500ms !important; }
.duration-700 { transition-duration: 700ms !important; }
.duration-1000 { transition-duration: 1000ms !important; }

.ease-linear { transition-timing-function: linear !important; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1) !important; }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1) !important; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; }

/* === TRANSFORM UTILITIES === */
.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)) !important; }
.transform-none { transform: none !important; }

.scale-0 { --tw-scale-x: 0; --tw-scale-y: 0; }
.scale-50 { --tw-scale-x: 0.5; --tw-scale-y: 0.5; }
.scale-75 { --tw-scale-x: 0.75; --tw-scale-y: 0.75; }
.scale-90 { --tw-scale-x: 0.9; --tw-scale-y: 0.9; }
.scale-95 { --tw-scale-x: 0.95; --tw-scale-y: 0.95; }
.scale-100 { --tw-scale-x: 1; --tw-scale-y: 1; }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }
.scale-110 { --tw-scale-x: 1.1; --tw-scale-y: 1.1; }
.scale-125 { --tw-scale-x: 1.25; --tw-scale-y: 1.25; }
.scale-150 { --tw-scale-x: 1.5; --tw-scale-y: 1.5; }

.rotate-0 { --tw-rotate: 0deg; }
.rotate-1 { --tw-rotate: 1deg; }
.rotate-2 { --tw-rotate: 2deg; }
.rotate-3 { --tw-rotate: 3deg; }
.rotate-6 { --tw-rotate: 6deg; }
.rotate-12 { --tw-rotate: 12deg; }
.rotate-45 { --tw-rotate: 45deg; }
.rotate-90 { --tw-rotate: 90deg; }
.rotate-180 { --tw-rotate: 180deg; }

/* === OPACITY UTILITIES === */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* === COMPONENT UTILITIES === */
.card {
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-primary);
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-300);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.btn-primary {
    background: var(--primary-500);
    color: var(--text-inverse);
    border-color: var(--primary-500);

    &:hover:not(:disabled) {
        background: var(--primary-600);
        border-color: var(--primary-600);
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
    }
}

.btn-secondary {
    background: var(--surface-secondary);
    color: var(--text-primary);
    border-color: var(--border-primary);

    &:hover:not(:disabled) {
        background: var(--surface-hover);
        border-color: var(--primary-300);
    }
}

.btn-accent {
    background: var(--accent-primary);
    color: var(--text-inverse);
    border-color: var(--accent-primary);

    &:hover:not(:disabled) {
        background: var(--accent-600);
        border-color: var(--accent-600);
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
    }
}

.btn-success {
    background: var(--success-500);
    color: var(--text-inverse);
    border-color: var(--success-500);

    &:hover:not(:disabled) {
        background: var(--success-600);
        border-color: var(--success-600);
    }
}

.btn-warning {
    background: var(--warning-500);
    color: var(--text-inverse);
    border-color: var(--warning-500);

    &:hover:not(:disabled) {
        background: var(--warning-600);
        border-color: var(--warning-600);
    }
}

.btn-error {
    background: var(--error-500);
    color: var(--text-inverse);
    border-color: var(--error-500);

    &:hover:not(:disabled) {
        background: var(--error-600);
        border-color: var(--error-600);
    }
}

.btn-ghost {
    background: transparent;
    color: var(--text-primary);
    border-color: transparent;

    &:hover:not(:disabled) {
        background: var(--surface-hover);
        color: var(--primary-500);
    }
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.input {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    &::placeholder {
        color: var(--text-tertiary);
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: var(--surface-secondary);
    }
}

.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-600);
    border: 1px solid rgba(99, 102, 241, 0.2);
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-600);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-600);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-600);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.badge-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-600);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.avatar {
    display: inline-block;
    border-radius: var(--radius-full);
    object-fit: cover;
    border: 2px solid var(--border-primary);
}

.avatar-sm {
    width: 2rem;
    height: 2rem;
}

.avatar-md {
    width: 3rem;
    height: 3rem;
}

.avatar-lg {
    width: 4rem;
    height: 4rem;
}

.avatar-xl {
    width: 6rem;
    height: 6rem;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid var(--border-primary);
    border-radius: 50%;
    border-top-color: var(--primary-500);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-500), var(--accent-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
}

/* === RESPONSIVE UTILITIES === */
@media (max-width: 640px) {
    .sm\:hidden { display: none !important; }
    .sm\:block { display: block !important; }
    .sm\:flex { display: flex !important; }
    .sm\:grid { display: grid !important; }
    .sm\:text-sm { font-size: 0.875rem !important; }
    .sm\:text-base { font-size: 1rem !important; }
    .sm\:p-2 { padding: 0.5rem !important; }
    .sm\:p-4 { padding: 1rem !important; }
    .sm\:m-2 { margin: 0.5rem !important; }
    .sm\:m-4 { margin: 1rem !important; }
}

@media (max-width: 768px) {
    .md\:hidden { display: none !important; }
    .md\:block { display: block !important; }
    .md\:flex { display: flex !important; }
    .md\:grid { display: grid !important; }
    .md\:text-lg { font-size: 1.125rem !important; }
    .md\:text-xl { font-size: 1.25rem !important; }
    .md\:p-4 { padding: 1rem !important; }
    .md\:p-6 { padding: 1.5rem !important; }
    .md\:m-4 { margin: 1rem !important; }
    .md\:m-6 { margin: 1.5rem !important; }
}

@media (max-width: 1024px) {
    .lg\:hidden { display: none !important; }
    .lg\:block { display: block !important; }
    .lg\:flex { display: flex !important; }
    .lg\:grid { display: grid !important; }
    .lg\:text-xl { font-size: 1.25rem !important; }
    .lg\:text-2xl { font-size: 1.5rem !important; }
    .lg\:p-6 { padding: 1.5rem !important; }
    .lg\:p-8 { padding: 2rem !important; }
    .lg\:m-6 { margin: 1.5rem !important; }
    .lg\:m-8 { margin: 2rem !important; }
}
