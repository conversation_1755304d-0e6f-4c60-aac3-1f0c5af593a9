<!-- Date View -->
<div class="date-view">
    <!-- Loading indicator for past dates -->
    <div class="date-loading past-loading" *ngIf="isLoadingPastDates">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Loading past matches...</span>
        </div>
    </div>

    <!-- Load More Past Button -->
    <div class="load-more-section" *ngIf="!isLoadingPastDates">
        <button class="load-more-btn past-btn" (click)="loadMorePastDates()">
            <i class="fas fa-chevron-up"></i>
            <span>Load Earlier Matches</span>
        </button>
    </div>

    <!-- Date Groups -->
    <div class="date-groups">
        <div class="date-group"
             *ngFor="let date of sortedDates; trackBy: trackByDate; let i = index"
             [class]="getDateDisplayClass(date)"
             [style.animation-delay]="(i * 0.1) + 's'">
            
            <!-- Date Header -->
            <div class="date-header">
                <div class="date-info">
                    <h3 class="date-title">{{ formatDateHeader(date) }}</h3>
                    <span class="date-subtitle">{{ date | date:'EEEE, MMMM d, y' }}</span>
                </div>
                <div class="games-count">
                    <span class="count-badge">{{ (gamesByDate[date] || []).length }} matches</span>
                </div>
            </div>

            <!-- Games for this date -->
            <div class="date-games">
                <app-fixture-card
                    *ngFor="let game of gamesByDate[date] || []; trackBy: trackByGameId"
                    [game]="game"
                    [canEdit$]="canEditGame(game.id)"
                    [isCurrentlyEditing]="false"
                    [isCurrentlyEditingTime]="false"
                    [homeTeamGoals]="game.result?.homeTeamGoals || 0"
                    [awayTeamGoals]="game.result?.awayTeamGoals || 0"
                    [isAdmin]="false"
                    (gameDetailsClick)="onGameClick($event)">
                </app-fixture-card>
            </div>

            <!-- Empty state for date with no games -->
            <div class="date-empty" *ngIf="!gamesByDate[date] || gamesByDate[date].length === 0">
                <i class="fas fa-calendar-times"></i>
                <span>No matches scheduled</span>
            </div>
        </div>
    </div>

    <!-- Load More Future Button -->
    <div class="load-more-section" *ngIf="!isLoadingFutureDates">
        <button class="load-more-btn future-btn" (click)="loadMoreFutureDates()">
            <i class="fas fa-chevron-down"></i>
            <span>Load Later Matches</span>
        </button>
    </div>

    <!-- Loading indicator for future dates -->
    <div class="date-loading future-loading" *ngIf="isLoadingFutureDates">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Loading upcoming matches...</span>
        </div>
    </div>

    <!-- Empty state when no dates available -->
    <div class="no-dates-state" *ngIf="sortedDates.length === 0">
        <i class="fas fa-calendar-times"></i>
        <h3>No Scheduled Matches</h3>
        <p>There are no matches scheduled at the moment.</p>
    </div>
</div>
