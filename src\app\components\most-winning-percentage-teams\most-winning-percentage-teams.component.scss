.most-winning-percentage-teams-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .header-section {
    text-align: center;
    margin-bottom: 30px;

    .header-content {
      .header-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;

        i {
          color: #FFD700;
          animation: trophy-shine 3s infinite ease-in-out;
        }
      }

      .header-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
        color: var(--text-color);
      }
    }
  }

  .controls-section, .filters-section {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--card-background);
    border-radius: 12px;
    border: 1px solid var(--border-color);

    .control-group, .filter-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-width: 200px;

      .control-label, .filter-label {
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--text-color);

        i {
          color: var(--accent-color);
        }
      }

      .control-select, .filter-input {
        padding: 12px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        background: var(--input-background);
        color: var(--text-color);
        font-size: 1rem;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: var(--accent-color);
          box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb), 0.1);
        }
      }
    }
  }

  .rankings-section {
    .rankings-header {
      margin-bottom: 25px;

      .rankings-title {
        font-size: 1.8rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 0 0 5px 0;

        i {
          color: var(--accent-color);
        }
      }

      .rankings-subtitle {
        opacity: 0.9;
        margin: 0;
        color: var(--text-color);
      }
    }

    .teams-list {
      display: flex;
      flex-direction: column;
      gap: 15px;

      .team-row {
        display: grid;
        grid-template-columns: 80px 1fr 300px 120px;
        gap: 20px;
        align-items: center;
        padding: 20px;
        background: var(--card-background);
        border: 2px solid var(--border-color);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
          border-color: #FFD700;
        }

        &.first-place {
          border-color: #FFD700;
          background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), var(--card-background));
        }

        &.second-place {
          border-color: #C0C0C0;
          background: linear-gradient(135deg, rgba(192, 192, 192, 0.1), var(--card-background));
        }

        &.third-place {
          border-color: #CD7F32;
          background: linear-gradient(135deg, rgba(205, 127, 50, 0.1), var(--card-background));
        }

        .rank-section {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .rank {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--accent-color);
          }

          .crown-icon {
            position: absolute;
            top: -10px;
            right: -5px;
            color: #FFD700;
            font-size: 1.2rem;
          }
        }

        .team-info {
          display: flex;
          align-items: center;
          gap: 15px;

          .team-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #FFD700;
          }

          .team-details {
            .team-name {
              font-size: 1.2rem;
              font-weight: bold;
              margin: 0 0 5px 0;
              color: var(--text-color);
            }

            .team-record {
              font-size: 0.9rem;
              opacity: 0.95;
              margin: 0;
              color: #FFD700;
              font-weight: 500;
            }
          }
        }

        .stats-section {
          display: flex;
          align-items: center;
          gap: 20px;

          .primary-stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 15px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);

            .stat-value {
              font-size: 1.4rem;
              font-weight: bold;
            }

            .stat-label {
              font-size: 0.8rem;
              opacity: 1;
              color: white;
              font-weight: 500;
            }
          }

          .secondary-stats {
            display: flex;
            gap: 15px;

            .stat-item {
              display: flex;
              flex-direction: column;
              align-items: center;

              .stat-value {
                font-size: 1.1rem;
                font-weight: bold;
                color: var(--text-color);
              }

              .stat-label {
                font-size: 0.8rem;
                opacity: 0.9;
                color: var(--text-color);
                font-weight: 500;
              }
            }
          }
        }

        .winning-indicator {
          display: flex;
          justify-content: center;
          align-items: center;

          .trophy-icons {
            display: flex;
            align-items: center;
            gap: 5px;

            i {
              color: #FFD700;
              font-size: 1.2rem;
              animation: trophy-shine 3s infinite ease-in-out;
              animation-delay: calc(var(--i) * 0.3s);
            }

            .more-indicator {
              font-size: 0.9rem;
              font-weight: bold;
              color: #FFD700;
              margin-left: 5px;
            }
          }
        }
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-color);
    opacity: 0.9;

    i {
      font-size: 4rem;
      margin-bottom: 20px;
      color: #FFD700;
    }

    h3 {
      font-size: 1.5rem;
      margin: 0 0 10px 0;
    }

    p {
      margin: 0;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;

    .spinner {
      text-align: center;

      i {
        font-size: 2rem;
        color: var(--accent-color);
        margin-bottom: 10px;
      }

      p {
        margin: 0;
        color: var(--text-color);
      }
    }
  }
}

@keyframes trophy-shine {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5));
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
    filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.8));
  }
}

// Responsive Design
@media (max-width: 768px) {
  .most-winning-percentage-teams-container {
    padding: 15px;

    .header-section .header-content .header-title {
      font-size: 2rem;
    }

    .controls-section, .filters-section {
      flex-direction: column;
      gap: 15px;

      .control-group, .filter-group {
        min-width: auto;
      }
    }

    .rankings-section .teams-list .team-row {
      grid-template-columns: 1fr;
      gap: 15px;
      text-align: center;

      .stats-section {
        justify-content: center;
      }

      .winning-indicator {
        order: -1;
      }
    }
  }
}
