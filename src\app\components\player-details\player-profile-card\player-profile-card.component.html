<!-- Player Profile Card -->
<aside class="player-profile-card">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="player-avatar-section">
            <div class="avatar-container" [class.uploading]="isUploadingImage">
                <img [src]="getCurrentImageUrl()"
                     class="player-avatar"
                     [class.preview]="imagePreviewUrl"
                     [alt]="player.name + ' avatar'">

                <!-- Upload Progress Overlay -->
                <div class="upload-progress-overlay" *ngIf="isUploadingImage">
                    <div class="progress-circle">
                        <svg class="progress-ring" width="60" height="60">
                            <circle class="progress-ring-circle"
                                    stroke="var(--primary-500)"
                                    stroke-width="4"
                                    fill="transparent"
                                    r="26"
                                    cx="30"
                                    cy="30"
                                    [style.stroke-dasharray]="163.36"
                                    [style.stroke-dashoffset]="163.36 - (163.36 * uploadProgress) / 100">
                            </circle>
                        </svg>
                        <div class="progress-text">{{ uploadProgress.toFixed(0) }}%</div>
                    </div>
                    <div class="upload-status">Uploading...</div>
                </div>

                <!-- Upload Success Feedback -->
                <div class="upload-success-overlay" *ngIf="uploadProgress === 100 && !isUploadingImage">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="success-text">Upload Complete!</div>
                </div>

                <!-- Upload Overlay -->
                <div class="avatar-overlay" *ngIf="editPlayerMode && !isUploadingImage">
                    <label for="imageUpload" class="upload-label">
                        <i class="fas fa-camera"></i>
                        <span>Change Photo</span>
                    </label>
                    <input type="file" id="imageUpload" class="file-input" accept="image/*"
                           (change)="onImageUpload($event)">
                </div>

                <!-- Image Preview Badge -->
                <div class="preview-badge" *ngIf="imagePreviewUrl && !isUploadingImage">
                    <i class="fas fa-eye"></i>
                    <span>Preview</span>
                </div>
            </div>
        </div>

        <div class="player-name-section">
            <div class="player-name-container">
                <h1 class="player-name" *ngIf="!editPlayerMode">{{player.name}}</h1>
                <div class="verified-icon" *ngIf="isPlayerAssociated && !editPlayerMode" title="Verified Player">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
            <div class="name-edit-container" *ngIf="editPlayerMode">
                <input type="text"
                       class="name-input"
                       #nameInput
                       [value]="editedPlayerName || player.name"
                       (input)="onNameChange(nameInput.value)"
                       [placeholder]="player.name"
                       maxlength="50">
            </div>
            <div class="player-subtitle">
                <span class="position-badge">{{player.position}}</span>
            </div>
        </div>
    </div>

    <!-- Team Section -->
    <div class="team-section" *ngIf="player.team">
        <h3 class="section-title">
            <i class="fas fa-users"></i>
            <span>Current Team</span>
        </h3>
        <div class="team-card" (click)="onTeamNavigate()">
            <div class="team-logo-container">
                <img [src]="player.team.imgUrl || getDefaultTeamImage()"
                     [alt]="player.team.name + ' logo'"
                     class="team-logo">
            </div>
            <div class="team-info">
                <span class="team-name">{{player.team.name}}</span>
                <span class="team-role">Player</span>
            </div>
            <div class="team-arrow">
                <i class="fas fa-chevron-right"></i>
            </div>
        </div>
    </div>

    <!-- Free Agent Section -->
    <div class="free-agent-section" *ngIf="!player.team">
        <div class="free-agent-card">
            <div class="free-agent-icon">
                <i class="fas fa-user-clock"></i>
            </div>
            <div class="free-agent-info">
                <span class="free-agent-title">Free Agent</span>
                <span class="free-agent-subtitle">Available for signing</span>
            </div>
        </div>
    </div>

    <!-- Player Info Grid -->
    <div class="player-info-grid">
        <!-- Age -->
        <div class="info-item">
            <div class="info-icon">
                <i class="fas fa-birthday-cake"></i>
            </div>
            <div class="info-content">
                <span class="info-label">Age</span>
                <div *ngIf="!editPlayerMode" class="info-value">{{player.age}}</div>
                <div *ngIf="editPlayerMode" class="age-edit">
                    <input type="number"
                           class="age-input"
                           [value]="editedPlayerAge || player.age"
                           (input)="onAgeChange($event)"
                           min="16"
                           max="50"
                           placeholder="Age">
                </div>
            </div>
        </div>

        <!-- Position -->
        <div class="info-item">
            <div class="info-icon">
                <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="info-content">
                <span class="info-label">Position</span>
                <div *ngIf="!editPlayerMode" class="info-value">{{player.position}}</div>
                <div *ngIf="editPlayerMode" class="position-select">
                    <pro-clubs-auto-complete-select
                        [selectOptions]="positionOptions"
                        [defaultOption]="editedPlayerPosition"
                        (selectionChange)="onPositionChange($event)">
                    </pro-clubs-auto-complete-select>
                </div>
            </div>
        </div>

        <!-- Playable Positions -->
        <div class="info-item">
            <div class="info-icon">
                <i class="fas fa-list"></i>
            </div>
            <div class="info-content">
                <span class="info-label">Playable Positions</span>
                <div *ngIf="!editPlayerMode" class="info-value">
                    <span *ngFor="let position of player.playablePositions; let last = last">
                        {{position}}<span *ngIf="!last">, </span>
                    </span>
                </div>
                <div *ngIf="editPlayerMode" class="playable-positions-select">
                    <pro-clubs-multiple-select
                        [selectOptions]="positionOptions"
                        [defaultSelectedOptions]="getSelectedPlayablePositions()"
                        (selectionChange)="onPlayablePositionsChange($event)"
                        placeholder="Select playable positions">
                    </pro-clubs-multiple-select>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="profile-actions">
        <button class="compare-btn" (click)="onComparePlayer()">
            <i class="fas fa-balance-scale"></i>
            <span>Compare Player</span>
        </button>
        <button class="history-btn" (click)="onViewSeasonHistory()">
            <i class="fas fa-history"></i>
            <span>Season History</span>
        </button>
        <button class="danger-btn" *ngIf="player.team && canRemoveFromTeam" (click)="onRemoveFromTeam()">
            <i class="fas fa-user-minus"></i>
            <span>Remove from Team</span>
        </button>
    </div>
</aside>
