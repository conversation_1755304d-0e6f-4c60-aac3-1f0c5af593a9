import mongoose, { Types } from 'mongoose';
import Player from '../src/models/player/player';
import Team from '../src/models/team';
import League from '../src/models/league';
import { ACHIEVEMENT_TYPE } from '../src/models/season-achievement';

// Connect to MongoDB
async function connectToDatabase() {
  try {
    // Load environment variables
    require('dotenv').config();

    const runMode = process.env.RUN_MODE || 'dev';
    const dbName = runMode === 'prod' ? process.env.MONGODB_DB_PROD : process.env.MONGODB_DB_DEV;
    const mongoUri = `mongodb+srv://${process.env.MONGO_DB_USERNAME}:${process.env.MONGO_DB_PASSWORD}@${process.env.MONGODB_CLUSTER}/${dbName}?retryWrites=true&w=majority`;

    console.log(`Connecting to MongoDB (${runMode} mode)...`);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

// Team achievements to add
const teamAchievements = [
  {
    teamId: '678907060ac8f44728a5e0dc',
    achievements: [
      { seasonNumber: 4, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 4 Champion' },
      { seasonNumber: 5, achievementType: ACHIEVEMENT_TYPE.FINALIST, rank: 2, description: 'Season 5 Runner-up' }
    ]
  },
  {
    teamId: '66058b5119a6c5698f4ba74b',
    achievements: [
      { seasonNumber: 1, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 1 Champion' },
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 2 Champion' },
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 3 Champion' },
      { seasonNumber: 4, achievementType: ACHIEVEMENT_TYPE.FINALIST, rank: 2, description: 'Season 4 Runner-up' },
      { seasonNumber: 5, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 5 Champion' }
    ]
  }
];

// Player achievements to add
const playerAchievements = [
  {
    playerId: '6654f375f9eac0fe69961bfd',
    achievements: [
      { seasonNumber: 1, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 1 Champion' },
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 2 Champion' },
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 3 Champion' },
      { seasonNumber: 5, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 5 Champion' },
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.TOP_SCORER, rank: 1, description: 'Season 2: 1st in top scorers' },
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.TOP_SCORER, rank: 2, description: 'Season 3: 2nd in top scorers' }
    ]
  },
  {
    playerId: '6645318cc3216c6e45855647',
    achievements: [
      { seasonNumber: 5, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 5 Champion' }
    ]
  },
  {
    playerId: '6630bc565592af6add24a005',
    achievements: [
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.FINALIST, rank: 2, description: 'Season 2 Runner-up' }
    ]
  },
  {
    playerId: '6681b9d7bc79b0c69168b5b8',
    achievements: [
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.FINALIST, rank: 2, description: 'Season 2 Runner-up' }
    ]
  },
  {
    playerId: '6617944dd6085929ce96da8c',
    achievements: [
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.TOP_SCORER, rank: 2, description: 'Season 2: 2nd in top scorers' }
    ]
  },
  {
    playerId: '660a712a0381fc57697ec9fe',
    achievements: [
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.TOP_SCORER, rank: 3, description: 'Season 3: 3rd in top scorers' }
    ]
  },
  {
    playerId: '660bbdbb22901e293800b2f3',
    achievements: [
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER, rank: 1, description: 'Season 2: 1st in top assists' },
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER, rank: 3, description: 'Season 3: 3rd in top assists' }
    ]
  },
  {
    playerId: '6624e5ae2e2d3dd16735c1fa',
    achievements: [
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.TOP_SCORER, rank: 1, description: 'Season 3: 1st in top scorers' }
    ]
  }
];

async function addTeamAchievements() {
  console.log('Adding team achievements...');

  for (const teamData of teamAchievements) {
    try {
      const team = await Team.findById(teamData.teamId).populate('league');
      if (!team) {
        console.error(`Team with ID ${teamData.teamId} not found`);
        continue;
      }

      console.log(`Processing team: ${team.name}`);

      // Get league information
      let leagueId: Types.ObjectId;
      let leagueName = 'Pro Clubs League';

      if (team.league) {
        if (typeof team.league === 'object' && 'name' in team.league) {
          // League is populated
          leagueId = team.league._id as Types.ObjectId;
          leagueName = (team.league as any).name;
        } else {
          // League is just an ObjectId
          leagueId = team.league as Types.ObjectId;
        }
      } else {
        // If no league is set, try to find the first league
        const firstLeague = await League.findOne();
        if (firstLeague) {
          leagueId = firstLeague._id as Types.ObjectId;
          leagueName = firstLeague.name;
        } else {
          leagueId = new Types.ObjectId();
        }
      }
      
      for (const achievement of teamData.achievements) {
        // Check if achievement already exists
        const existingAchievement = team.achievementHistory.find((a: any) => 
          a.seasonNumber === achievement.seasonNumber && 
          a.achievementType === achievement.achievementType
        );
        
        if (existingAchievement) {
          console.log(`  - Achievement already exists: ${achievement.description}`);
          continue;
        }
        
        team.achievementHistory.push({
          seasonNumber: achievement.seasonNumber,
          league: leagueId,
          leagueName: leagueName,
          achievementType: achievement.achievementType,
          rank: achievement.rank,
          stats: {},
          description: achievement.description,
          achievedDate: new Date()
        });
        
        console.log(`  + Added: ${achievement.description}`);
      }
      
      await team.save();
      console.log(`✓ Saved achievements for team: ${team.name}`);
      
    } catch (error) {
      console.error(`Error processing team ${teamData.teamId}:`, error);
    }
  }
}

async function addPlayerAchievements() {
  console.log('\nAdding player achievements...');
  
  for (const playerData of playerAchievements) {
    try {
      const player = await Player.findById(playerData.playerId);
      if (!player) {
        console.error(`Player with ID ${playerData.playerId} not found`);
        continue;
      }
      
      console.log(`Processing player: ${player.name}`);
      
      // Get player's current team and league information
      let teamId: Types.ObjectId;
      let teamName = 'Unknown Team';
      let leagueId: Types.ObjectId;
      let leagueName = 'Pro Clubs League';

      if (player.currentSeason?.team) {
        teamId = player.currentSeason.team as Types.ObjectId;
        const team = await Team.findById(teamId).populate('league');
        if (team) {
          teamName = team.name;
          if (team.league) {
            if (typeof team.league === 'object' && 'name' in team.league) {
              // League is populated
              leagueId = team.league._id as Types.ObjectId;
              leagueName = (team.league as any).name;
            } else {
              // League is just an ObjectId
              leagueId = team.league as Types.ObjectId;
            }
          } else {
            leagueId = new Types.ObjectId();
          }
        } else {
          teamId = new Types.ObjectId();
          leagueId = new Types.ObjectId();
        }
      } else {
        teamId = new Types.ObjectId();
        // If no team found, get the first available league
        const firstLeague = await League.findOne();
        if (firstLeague) {
          leagueId = firstLeague._id as Types.ObjectId;
          leagueName = firstLeague.name;
        } else {
          leagueId = new Types.ObjectId();
        }
      }
      
      for (const achievement of playerData.achievements) {
        // Check if achievement already exists
        const existingAchievement = player.achievementHistory.find((a: any) => 
          a.seasonNumber === achievement.seasonNumber && 
          a.achievementType === achievement.achievementType
        );
        
        if (existingAchievement) {
          console.log(`  - Achievement already exists: ${achievement.description}`);
          continue;
        }
        
        player.achievementHistory.push({
          seasonNumber: achievement.seasonNumber,
          league: leagueId,
          leagueName: leagueName,
          achievementType: achievement.achievementType,
          rank: achievement.rank,
          teamId: teamId,
          teamName: teamName,
          stats: {},
          description: achievement.description,
          achievedDate: new Date()
        });
        
        console.log(`  + Added: ${achievement.description}`);
      }
      
      await player.save();
      console.log(`✓ Saved achievements for player: ${player.name}`);
      
    } catch (error) {
      console.error(`Error processing player ${playerData.playerId}:`, error);
    }
  }
}

async function main() {
  try {
    await connectToDatabase();
    
    await addTeamAchievements();
    await addPlayerAchievements();
    
    console.log('\n✅ All achievements have been added successfully!');
    
  } catch (error) {
    console.error('Error in main process:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the script
main().catch(console.error);
