import mongoose from 'mongoose';
import Team from '../src/models/team';
import Player from '../src/models/player/player';

// Connect to MongoDB
async function connectToDatabase() {
  try {
    // Load environment variables
    require('dotenv').config();
    
    const runMode = process.env.RUN_MODE || 'dev';
    const dbName = runMode === 'prod' ? process.env.MONGODB_DB_PROD : process.env.MONGODB_DB_DEV;
    const mongoUri = `mongodb+srv://${process.env.MONGO_DB_USERNAME}:${process.env.MONGO_DB_PASSWORD}@${process.env.MONGODB_CLUSTER}/${dbName}?retryWrites=true&w=majority`;
    
    console.log(`Connecting to MongoDB (${runMode} mode)...`);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

async function verifyAchievements() {
  await connectToDatabase();

  console.log('\n=== VERIFYING TEAM ACHIEVEMENTS ===');
  
  // Check UTOPIA XI
  const utopiaTeam = await Team.findById('678907060ac8f44728a5e0dc');
  if (utopiaTeam) {
    console.log(`\nTeam: ${utopiaTeam.name}`);
    console.log(`Achievement History Length: ${utopiaTeam.achievementHistory.length}`);
    utopiaTeam.achievementHistory.forEach((achievement, index) => {
      console.log(`  ${index + 1}. Season ${achievement.seasonNumber}: ${achievement.description} (${achievement.achievementType})`);
    });
  } else {
    console.log('UTOPIA XI team not found!');
  }

  // Check Guns N Roses
  const gunsTeam = await Team.findById('66058b5119a6c5698f4ba74b');
  if (gunsTeam) {
    console.log(`\nTeam: ${gunsTeam.name}`);
    console.log(`Achievement History Length: ${gunsTeam.achievementHistory.length}`);
    gunsTeam.achievementHistory.forEach((achievement, index) => {
      console.log(`  ${index + 1}. Season ${achievement.seasonNumber}: ${achievement.description} (${achievement.achievementType})`);
    });
  } else {
    console.log('Guns N Roses team not found!');
  }

  console.log('\n=== VERIFYING PLAYER ACHIEVEMENTS ===');
  
  // Check a few players
  const playerIds = [
    '6654f375f9eac0fe69961bfd', // Dor Ohayon
    '6617944dd6085929ce96da8c', // Araujo
    '660bbdbb22901e293800b2f3'  // De Drori
  ];

  for (const playerId of playerIds) {
    const player = await Player.findById(playerId);
    if (player) {
      console.log(`\nPlayer: ${player.name}`);
      console.log(`Achievement History Length: ${player.achievementHistory.length}`);
      player.achievementHistory.forEach((achievement, index) => {
        console.log(`  ${index + 1}. Season ${achievement.seasonNumber}: ${achievement.description} (${achievement.achievementType})`);
      });
    } else {
      console.log(`Player ${playerId} not found!`);
    }
  }

  await mongoose.disconnect();
  console.log('\nDatabase connection closed');
}

verifyAchievements().catch(console.error);
