.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 800px;
  background: var(--background-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chat-header {
  background: var(--primary-color);
  color: white;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;

      i {
        margin-right: 0.5rem;
        color: var(--accent-color);
      }
    }

    .refresh-btn {
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 0.5rem;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.1);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .spinning {
        animation: spin 1s linear infinite;
      }
    }
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--background-secondary);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
  }
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;

  .load-more-btn {
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: var(--primary-color);
      color: white;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    i {
      margin-right: 0.5rem;
    }
  }
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message-wrapper {
  display: flex;
  flex-direction: column;

  &.own-message {
    align-items: flex-end;

    .message-content {
      flex-direction: row-reverse;
    }

    .message-body {
      background: var(--primary-color);
      color: white;
      border-radius: 18px 18px 4px 18px;
    }
  }

  &.admin-message:not(.own-message) .message-body {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-color-light));
    border-left: 4px solid var(--accent-color);
  }
}

.reply-reference {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: var(--background-secondary);
  border-radius: 8px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  border-left: 3px solid var(--primary-color);

  i {
    color: var(--primary-color);
  }

  .reply-author {
    font-weight: 600;
    color: var(--primary-color);
  }

  .reply-content {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.message-content {
  display: flex;
  gap: 0.75rem;
  max-width: 80%;
}

.message-avatar {
  position: relative;
  flex-shrink: 0;

  img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
  }

  .admin-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--warning-color);
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.6rem;
  }
}

.message-body {
  background: var(--background-secondary);
  border-radius: 18px 18px 18px 4px;
  padding: 0.75rem 1rem;
  position: relative;
  min-width: 100px;
  transition: all 0.2s ease;

  &:hover {
    .message-actions {
      opacity: 1;
    }
  }
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;

  .author-name {
    font-weight: 600;
    color: var(--primary-color);

    .admin-label {
      background: var(--warning-color);
      color: white;
      padding: 0.125rem 0.375rem;
      border-radius: 10px;
      font-size: 0.75rem;
      margin-left: 0.5rem;
    }
  }

  .message-time {
    color: var(--text-secondary);
    font-size: 0.75rem;
  }
}

.message-text {
  word-wrap: break-word;
  line-height: 1.4;
  position: relative;

  .edited-indicator {
    color: var(--text-secondary);
    font-size: 0.75rem;
    margin-left: 0.5rem;
    opacity: 0.7;
  }
}

.own-message-time {
  text-align: right;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
}

.message-reactions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.5rem;

  .reaction-btn {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--primary-color);
      color: white;
    }

    &.user-reacted {
      background: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }
  }
}

.message-actions {
  position: absolute;
  top: -10px;
  right: 10px;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  background: var(--background-color);
  border-radius: 8px;
  padding: 0.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .action-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75rem;
    transition: all 0.2s ease;

    &:hover {
      background: var(--background-secondary);
      color: var(--primary-color);
    }

    &.delete-btn:hover {
      color: var(--danger-color);
    }
  }
}

.emoji-picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .emoji-picker-content {
    background: var(--background-color);
    border-radius: 12px;
    padding: 1rem;
    max-width: 300px;
    width: 90%;

    .emoji-picker-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      font-weight: 600;

      .close-emoji-btn {
        background: transparent;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;

        &:hover {
          background: var(--background-secondary);
        }
      }
    }

    .emoji-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 0.5rem;

      .emoji-option {
        background: var(--background-secondary);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 1.25rem;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--primary-color);
          transform: scale(1.1);
        }
      }
    }
  }
}

.reply-indicator,
.edit-indicator {
  background: var(--background-secondary);
  border-left: 4px solid var(--primary-color);
  padding: 0.75rem 1rem;
  margin: 0 1rem;

  .indicator-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);

    i {
      color: var(--primary-color);
    }

    .cancel-btn {
      background: transparent;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      padding: 0.25rem;
      border-radius: 4px;
      margin-left: auto;

      &:hover {
        background: var(--background-color);
        color: var(--danger-color);
      }
    }
  }
}

.message-input-container {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background: var(--background-color);

  .input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    background: var(--background-secondary);
    border-radius: 24px;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    transition: border-color 0.2s ease;

    &:focus-within {
      border-color: var(--primary-color);
    }

    .message-input {
      flex: 1;
      background: transparent;
      border: none;
      outline: none;
      resize: none;
      font-family: inherit;
      font-size: 0.875rem;
      line-height: 1.4;
      color: var(--text-color);
      max-height: 120px;

      &::placeholder {
        color: var(--text-secondary);
      }

      &:disabled {
        opacity: 0.6;
      }
    }

    .input-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .character-count {
        font-size: 0.75rem;
        color: var(--text-secondary);
      }

      .send-btn {
        background: var(--primary-color);
        border: none;
        color: white;
        padding: 0.5rem;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover:not(:disabled) {
          background: var(--primary-color-dark);
          transform: scale(1.05);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Mobile responsive
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
    border-radius: 0;
  }

  .message-content {
    max-width: 90%;
  }

  .message-actions {
    position: static;
    opacity: 1;
    margin-top: 0.5rem;
    justify-content: center;
  }

  .emoji-picker-content {
    width: 95%;
    
    .emoji-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}
