import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { PlayerDTO } from '@pro-clubs-manager/shared-dtos';
import { PlayerService } from '../../../services/player.service';
import { NotificationService } from '../../../services/notification.service';

@Component({
  selector: 'app-cms-players-table',
  templateUrl: './cms-players-table.component.html',
  styleUrl: './cms-players-table.component.scss'
})
export class CmsPlayersTableComponent {
  @Input() players: PlayerDTO[] = [];
  @Output() playerDeleted = new EventEmitter<void>();
  @Output() playerUpdated = new EventEmitter<void>();

  deletingPlayerIds = new Set<string>();

  constructor(
    private playerService: PlayerService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  async deletePlayer(player: PlayerDTO): Promise<void> {
    const confirmDelete = confirm(
      `⚠️ Are you sure you want to delete "${player.name}"?\n\n` +
      'This action cannot be undone and will permanently remove:\n' +
      '• Player profile and statistics\n' +
      '• All game performances\n' +
      '• Transfer history\n' +
      '• Associated user account links'
    );

    if (!confirmDelete) {
      return;
    }

    try {
      this.deletingPlayerIds.add(player.id);
      await this.playerService.deletePlayer(player.id);
      this.notificationService.success(`${player.name} deleted successfully`);
      this.playerDeleted.emit();
    } catch (error: any) {
      console.error('Error deleting player:', error);
      this.notificationService.error(error.message || 'Failed to delete player');
    } finally {
      this.deletingPlayerIds.delete(player.id);
    }
  }

  viewPlayerDetails(playerId: string): void {
    this.router.navigate(['/player-details', { id: playerId }]);
  }

  editPlayer(playerId: string): void {
    // Navigate to player edit page or open edit modal
    // For now, navigate to player details where they can edit
    this.viewPlayerDetails(playerId);
  }

  getPlayerTeamName(player: PlayerDTO): string {
    return player.team?.name || 'Free Agent';
  }

  getPlayerPosition(player: PlayerDTO): string {
    return player.position || 'N/A';
  }

  getPlayerStats(player: PlayerDTO): string {
    if (!player.stats) {
      return 'No stats';
    }

    const goals = player.stats.goals || 0;
    const assists = player.stats.assists || 0;
    const games = player.stats.games || 0;

    return `${goals}G ${assists}A (${games} games)`;
  }

  isPlayerDeleting(playerId: string): boolean {
    return this.deletingPlayerIds.has(playerId);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/User.jpg';
    }
  }
}
