<div class="admin-requests-container">
  <div class="admin-header">
    <h1 class="admin-title">Player Association Requests</h1>
    <p class="admin-subtitle">Review and manage pending player association requests</p>
  </div>

  <div class="requests-content" *ngIf="!isLoading">
    <div class="requests-stats">
      <div class="stat-card">
        <div class="stat-number">{{ pendingRequests.length }}</div>
        <div class="stat-label">Pending Requests</div>
      </div>
    </div>

    <div class="requests-list" *ngIf="pendingRequests.length > 0; else noRequests">
      <div class="request-card" *ngFor="let request of pendingRequests">
        <div class="request-header">
          <div class="user-info">
            <div class="user-avatar">
              <i class="fas fa-user"></i>
            </div>
            <div class="user-details">
              <h3 class="user-name">{{ request.userId.firstName }} {{ request.userId.lastName }}</h3>
              <p class="user-email">{{ request.userId.email }}</p>
            </div>
          </div>
          <div class="request-date">
            <span class="date-label">Requested</span>
            <span class="date-value">{{ formatDate(request.requestedAt) }}</span>
          </div>
        </div>

        <div class="request-body">
          <div class="player-info">
            <div class="player-avatar">
              <img [src]="request.playerId.imgUrl || 'assets/default-player.png'"
                   [alt]="request.playerId.name"
                   (error)="onImageError($event)">
            </div>
            <div class="player-details">
              <h4 class="player-name">{{ request.playerId.name }}</h4>
              <p class="player-position">{{ request.playerId.position }}</p>
              <p class="player-team" *ngIf="request.playerId.team">
                Current Team: {{ request.playerId.team.name }}
              </p>
              <p class="player-team" *ngIf="!request.playerId.team">
                Free Agent
              </p>
            </div>
          </div>

          <div class="request-message" *ngIf="request.userMessage">
            <h5>User Message:</h5>
            <p>{{ request.userMessage }}</p>
          </div>

          <div class="request-email" *ngIf="request.playerEmail">
            <p><strong>Player Email:</strong> {{ request.playerEmail }}</p>
          </div>
        </div>

        <div class="request-actions">
          <button class="btn btn-success" 
                  (click)="approveRequest(request)"
                  [disabled]="isProcessing(request.id)">
            <i class="fas fa-check" *ngIf="!isProcessing(request.id)"></i>
            <i class="fas fa-spinner fa-spin" *ngIf="isProcessing(request.id)"></i>
            Approve
          </button>
          <button class="btn btn-danger" 
                  (click)="rejectRequest(request)"
                  [disabled]="isProcessing(request.id)">
            <i class="fas fa-times" *ngIf="!isProcessing(request.id)"></i>
            <i class="fas fa-spinner fa-spin" *ngIf="isProcessing(request.id)"></i>
            Reject
          </button>
        </div>
      </div>
    </div>

    <ng-template #noRequests>
      <div class="no-requests">
        <div class="no-requests-icon">
          <i class="fas fa-inbox"></i>
        </div>
        <h3>No Pending Requests</h3>
        <p>All player association requests have been processed.</p>
      </div>
    </ng-template>
  </div>

  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-spinner">
      <i class="fas fa-spinner fa-spin"></i>
    </div>
    <p>Loading pending requests...</p>
  </div>
</div>
