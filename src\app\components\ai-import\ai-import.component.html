<div class="ai-import-container">
  <!-- Header -->
  <div class="header-section">
    <div class="header-content">
      <h1 class="page-title">
        <i class="fas fa-robot"></i>
        AI Player Stats Import
      </h1>
      <p class="page-subtitle">Import player statistics from AI analysis or manually input data</p>
    </div>
    <div class="header-actions">
      <button class="ai-import-btn" (click)="importFromAI()">
        <i class="fas fa-magic"></i>
        Import from AI
      </button>
      <button class="reset-btn" (click)="resetForm()">
        <i class="fas fa-undo"></i>
        Reset
      </button>
    </div>
  </div>

  <!-- Stats Summary -->
  <div class="stats-summary">
    <div class="stat-card">
      <i class="fas fa-users"></i>
      <div class="stat-info">
        <span class="stat-value">{{ getFilledPlayersCount() }}</span>
        <span class="stat-label">Players Added</span>
      </div>
    </div>
    <div class="stat-card">
      <i class="fas fa-futbol"></i>
      <div class="stat-info">
        <span class="stat-value">{{ getGoalScorersCount() }}</span>
        <span class="stat-label">Goal Scorers</span>
      </div>
    </div>
    <div class="stat-card">
      <i class="fas fa-hands-helping"></i>
      <div class="stat-info">
        <span class="stat-value">{{ getAssistMakersCount() }}</span>
        <span class="stat-label">Assist Makers</span>
      </div>
    </div>
  </div>

  <!-- Formation Pitch -->
  <div class="pitch-container">
    <div class="pitch">
      <div class="pitch-lines">
        <!-- Goal areas -->
        <div class="goal-area top"></div>
        <div class="goal-area bottom"></div>
        <!-- Center circle -->
        <div class="center-circle"></div>
        <!-- Center line -->
        <div class="center-line"></div>
      </div>

      <!-- Player positions -->
      <div class="player-positions">
        <div 
          *ngFor="let player of importData.players; trackBy: trackByPosition"
          class="player-slot"
          [class.filled]="hasPlayerData(player)"
          [class.player-of-match]="player.isPlayerOfMatch"
          [style.left.%]="player.x"
          [style.top.%]="player.y"
          (click)="openPlayerModal(player)">
          
          <div class="player-avatar">
            <i class="fas fa-user" *ngIf="!hasPlayerData(player)"></i>
            <img *ngIf="hasPlayerData(player)" 
                 [src]="'assets/images/default-player.png'" 
                 [alt]="player.name"
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'">
            <div class="player-initials" *ngIf="hasPlayerData(player)">
              {{ getPlayerInitials(player.name) }}
            </div>
          </div>
          
          <div class="player-info">
            <span class="player-name">{{ getPlayerDisplayText(player) }}</span>
            <div class="player-stats" *ngIf="hasPlayerData(player)">
              <span class="stat" *ngIf="player.goals > 0">⚽{{ player.goals }}</span>
              <span class="stat" *ngIf="player.assists > 0">🅰️{{ player.assists }}</span>
            </div>
          </div>

          <button class="clear-btn" 
                  *ngIf="hasPlayerData(player)"
                  (click)="clearPlayer(player); $event.stopPropagation()">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="action-buttons">
    <button class="cancel-btn" (click)="router.navigate(['/dashboard'])">
      <i class="fas fa-arrow-left"></i>
      Cancel
    </button>
    <button class="submit-btn" 
            (click)="submitData()"
            [disabled]="getFilledPlayersCount() === 0">
      <i class="fas fa-check"></i>
      Submit Data ({{ getFilledPlayersCount() }} players)
    </button>
  </div>
</div>

<!-- Player Edit Modal -->
<div class="modal-overlay" *ngIf="isModalOpen" (click)="closeModal()">
  <div class="modal-content" (click)="$event.stopPropagation()" *ngIf="selectedPlayer">
    <div class="modal-header">
      <h3>Edit Player - {{ selectedPlayer.position }}</h3>
      <button class="close-btn" (click)="closeModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="modal-body">
      <div class="form-group">
        <label for="playerName">Player Name</label>
        <input 
          id="playerName"
          type="text" 
          [(ngModel)]="selectedPlayer.name" 
          placeholder="Enter player name"
          class="form-input">
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="goals">Goals</label>
          <input 
            id="goals"
            type="number" 
            [(ngModel)]="selectedPlayer.goals" 
            min="0"
            class="form-input">
        </div>
        <div class="form-group">
          <label for="assists">Assists</label>
          <input 
            id="assists"
            type="number" 
            [(ngModel)]="selectedPlayer.assists" 
            min="0"
            class="form-input">
        </div>
      </div>

      <div class="form-group">
        <label for="rating">Rating</label>
        <input 
          id="rating"
          type="number" 
          [(ngModel)]="selectedPlayer.rating" 
          min="0" 
          max="10" 
          step="0.1"
          class="form-input">
      </div>

      <div class="form-group">
        <label class="checkbox-label">
          <input 
            type="checkbox" 
            [(ngModel)]="selectedPlayer.isPlayerOfMatch">
          <span class="checkmark"></span>
          Player of the Match
        </label>
      </div>
    </div>

    <div class="modal-footer">
      <button class="cancel-btn" (click)="closeModal()">Cancel</button>
      <button class="save-btn" (click)="savePlayer()">Save Player</button>
    </div>
  </div>
</div>
