.most-hattricks-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .header-section {
    text-align: center;
    margin-bottom: 30px;

    .header-content {
      .header-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;

        i {
          color: #FF6B35;
          animation: flicker 2s infinite alternate;
        }
      }

      .header-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
        color: var(--text-color);
      }
    }
  }

  .controls-section {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--card-background);
    border-radius: 12px;
    border: 1px solid var(--border-color);

    .control-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-width: 200px;

      .control-label {
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--text-color);

        i {
          color: var(--accent-color);
        }
      }

      .control-select {
        padding: 12px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        background: var(--input-background);
        color: var(--text-color);
        font-size: 1rem;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: var(--accent-color);
          box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb), 0.1);
        }
      }
    }
  }

  .rankings-section {
    .rankings-header {
      margin-bottom: 25px;

      .rankings-title {
        font-size: 1.8rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 0 0 5px 0;

        i {
          color: var(--accent-color);
        }
      }

      .rankings-subtitle {
        opacity: 0.9;
        margin: 0;
        color: var(--text-color);
      }
    }

    .players-list {
      display: flex;
      flex-direction: column;
      gap: 15px;

      .player-row {
        display: grid;
        grid-template-columns: 80px 1fr 200px 300px 120px;
        gap: 20px;
        align-items: center;
        padding: 20px;
        background: var(--card-background);
        border: 2px solid var(--border-color);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
          border-color: #FF6B35;
        }

        &.first-place {
          border-color: #FFD700;
          background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), var(--card-background));
        }

        &.second-place {
          border-color: #C0C0C0;
          background: linear-gradient(135deg, rgba(192, 192, 192, 0.1), var(--card-background));
        }

        &.third-place {
          border-color: #CD7F32;
          background: linear-gradient(135deg, rgba(205, 127, 50, 0.1), var(--card-background));
        }

        .rank-section {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .rank {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--accent-color);
          }

          .crown-icon {
            position: absolute;
            top: -10px;
            right: -5px;
            color: #FFD700;
            font-size: 1.2rem;
          }
        }

        .player-info {
          display: flex;
          align-items: center;
          gap: 15px;

          .player-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #FF6B35;
          }

          .player-details {
            .player-name {
              font-size: 1.2rem;
              font-weight: bold;
              margin: 0 0 5px 0;
              color: var(--text-color);
            }

            .player-position {
              font-size: 0.9rem;
              opacity: 0.95;
              margin: 0;
              color: #FF6B35;
              font-weight: 500;
            }
          }
        }

        .team-info {
          .team-name {
            font-weight: 600;
            color: var(--text-color);
            cursor: pointer;
            transition: color 0.3s ease;

            &:hover {
              color: var(--accent-color);
            }
          }
        }

        .stats-section {
          display: flex;
          align-items: center;
          gap: 20px;

          .primary-stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 15px;
            background: linear-gradient(135deg, #FF6B35, #FF8E53);
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);

            .stat-value {
              font-size: 1.4rem;
              font-weight: bold;
            }

            .stat-label {
              font-size: 0.8rem;
              opacity: 1;
              color: white;
              font-weight: 500;
            }
          }

          .secondary-stats {
            display: flex;
            gap: 15px;

            .stat-item {
              display: flex;
              flex-direction: column;
              align-items: center;

              .stat-value {
                font-size: 1.1rem;
                font-weight: bold;
                color: var(--text-color);
              }

              .stat-label {
                font-size: 0.8rem;
                opacity: 0.9;
                color: var(--text-color);
                font-weight: 500;
              }
            }
          }
        }

        .hattrick-indicator {
          display: flex;
          justify-content: center;
          align-items: center;

          .fire-icons {
            display: flex;
            align-items: center;
            gap: 5px;

            i {
              color: #FF6B35;
              font-size: 1.2rem;
              animation: flicker 2s infinite alternate;
              animation-delay: calc(var(--i) * 0.2s);
            }

            .more-indicator {
              font-size: 0.9rem;
              font-weight: bold;
              color: #FF6B35;
              margin-left: 5px;
            }
          }
        }
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-color);
    opacity: 0.9;

    i {
      font-size: 4rem;
      margin-bottom: 20px;
      color: #FF6B35;
    }

    h3 {
      font-size: 1.5rem;
      margin: 0 0 10px 0;
    }

    p {
      margin: 0;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }
}

@keyframes flicker {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .most-hattricks-container {
    padding: 15px;

    .header-section .header-content .header-title {
      font-size: 2rem;
    }

    .controls-section {
      flex-direction: column;
      gap: 15px;

      .control-group {
        min-width: auto;
      }
    }

    .rankings-section .players-list .player-row {
      grid-template-columns: 1fr;
      gap: 15px;
      text-align: center;

      .stats-section {
        justify-content: center;
      }

      .hattrick-indicator {
        order: -1;
      }
    }
  }
}
