/* === FRESH DASHBOARD TOP ASSISTS === */

.dashboard-topassists-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: transparent;
    font-family: var(--font-sans);
}

.assists-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    height: 100%;
    overflow-y: auto;
    padding: var(--spacing-xs);
}

.assist-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--surface-secondary);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
        transform: translateX(4px);
        box-shadow: var(--shadow-md);
        border-color: var(--border-primary);
        background: var(--surface-hover);
    }

    &.first-place {
        border-color: #fbbf24;
        background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), var(--surface-secondary));
        border: 2px solid #fbbf24;

        .rank-badge {
            background: #fbbf24;
            color: white;
            box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);

            .fas {
                color: rgba(255, 255, 255, 0.8);
                margin-left: var(--spacing-xs);
            }
        }

        .assists-number {
            color: #fbbf24;
            font-weight: var(--font-weight-bold);
        }

        .player-name {
            color: white !important;
            font-weight: var(--font-weight-bold);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .player-team {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }
    }

    &.second-place {
        border-color: #60a5fa;
        background: linear-gradient(135deg, rgba(96, 165, 250, 0.15), var(--surface-secondary));
        border: 2px solid #60a5fa;

        .rank-badge {
            background: #60a5fa;
            color: white;
            box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
        }

        .assists-number {
            color: #60a5fa;
            font-weight: var(--font-weight-bold);
        }

        .player-name {
            color: white !important;
            font-weight: var(--font-weight-bold);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .player-team {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }
    }

    &.third-place {
        border-color: #ea580c;
        background: linear-gradient(135deg, rgba(234, 88, 12, 0.15), var(--surface-secondary));
        border: 2px solid #ea580c;

        .rank-badge {
            background: #ea580c;
            color: white;
            box-shadow: 0 2px 8px rgba(234, 88, 12, 0.3);
        }

        .assists-number {
            color: #ea580c;
            font-weight: var(--font-weight-bold);
        }

        .player-name {
            color: white !important;
            font-weight: var(--font-weight-bold);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .player-team {
            color: rgba(255, 255, 255, 0.9) !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }
    }
}

.rank-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    background: var(--surface-tertiary);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-bold);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    flex-shrink: 0;

    .rank-number {
        line-height: 1;
    }
}

.player-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    overflow: hidden;
    flex-shrink: 0;
    border: 2px solid var(--border-primary);

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.player-details {
    flex: 1;
    min-width: 0;

    .player-name {
        font-size: var(--text-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
        line-height: 1.2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .player-team {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.assists-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 50px;
    flex-shrink: 0;

    .assists-number {
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--info-500);
        line-height: 1;
        margin-bottom: var(--spacing-xs);
    }

    .assists-label {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: var(--font-weight-medium);
    }
}

.loading-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
}