import { Router } from "express";
import TeamController from "../controllers/team-controller";
import upload from "../config/multer-config";
import { container } from "../config/container.config";
import { authenticateToken, requireAdmin, requireTeamEditPermission } from "../middlewares/auth-middleware";

const router = Router();
const teamController = container.resolve(TeamController);

// Admin only routes
router.post("/", authenticateToken, requireAdmin, upload.single("file"), (req, res, next) => teamController.createTeam(req, res, next));
router.put("/:id/addPlayer", authenticateToken, requireTeamEditPermission, (req, res, next) => teamController.addPlayerToTeam(req, res, next));
router.put("/:id/removePlayer", authenticateToken, requireTeamEditPermission, (req, res, next) => teamController.removePlayerFromTeam(req, res, next));
router.delete("/:id/removeAllPlayersFromTeam", authenticateToken, requireAdmin, (req, res, next) => teamController.removeAllPlayersFromTeam(req, res, next));
router.patch("/:id/setImage", authenticateToken, requireTeamEditPermission, upload.single("file"), (req, res, next) => teamController.setTeamImage(req, res, next));
router.patch("/:id/setCaptain", authenticateToken, requireAdmin, (req, res, next) => teamController.setTeamCaptain(req, res, next));
router.put("/:id/rename", authenticateToken, requireTeamEditPermission, (req, res, next) => teamController.renameTeam(req, res, next));

router.get("/", (req, res, next) => teamController.getAllTeams(req, res, next));
router.get("/:id/getTeamsByLeagueId", (req, res, next) => teamController.getTeamsByLeagueId(req, res, next));
router.get("/:id/advancedStats", (req, res, next) => teamController.getAdvancedTeamStats(req, res, next));
router.get("/:id/players", (req, res, next) => teamController.getTeamPlayers(req, res, next));
router.get("/:id/playerStats", (req, res, next) => teamController.getTeamPlayersStats(req, res, next));
router.get("/:id", (req, res, next) => teamController.getTeamById(req, res, next));

// router.delete("/:id", teamController.deleteTeam);

export default router;
