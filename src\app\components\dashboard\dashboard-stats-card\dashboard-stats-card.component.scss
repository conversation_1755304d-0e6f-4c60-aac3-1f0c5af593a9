.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-secondary);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
        transition: left 0.6s ease;
    }

    &:hover {
        transform: translateY(-6px) scale(1.02);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-300);
        
        &::before {
            left: 100%;
        }
        
        .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }
    }
    
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }

    .stat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%);
        border-radius: var(--radius-xl);
        color: var(--primary-600);
        font-size: var(--text-xl);
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);
        flex-shrink: 0;

        @media (max-width: 480px) {
            width: 48px;
            height: 48px;
            font-size: var(--text-lg);
        }
    }

    .stat-content {
        flex: 1;
        min-width: 0;

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-xs);
        }

        .stat-title {
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: var(--text-xs);
            font-weight: var(--font-weight-semibold);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-md);

            &--up {
                color: var(--success-600);
                background: var(--success-50);
            }

            &--down {
                color: var(--danger-600);
                background: var(--danger-50);
            }

            &--neutral {
                color: var(--text-secondary);
                background: var(--surface-tertiary);
            }
        }

        .stat-value {
            font-size: var(--text-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            line-height: 1.2;
            margin-bottom: var(--spacing-xs);

            @media (max-width: 480px) {
                font-size: var(--text-xl);
            }
        }

        .stat-subtitle {
            font-size: var(--text-xs);
            color: var(--text-tertiary);
            font-weight: var(--font-weight-medium);
        }
    }

    // Color variants
    &--success .stat-icon {
        background: linear-gradient(135deg, var(--success-100) 0%, var(--success-200) 100%);
        color: var(--success-600);
    }

    &--warning .stat-icon {
        background: linear-gradient(135deg, var(--warning-100) 0%, var(--warning-200) 100%);
        color: var(--warning-600);
    }

    &--danger .stat-icon {
        background: linear-gradient(135deg, var(--danger-100) 0%, var(--danger-200) 100%);
        color: var(--danger-600);
    }

    &--info .stat-icon {
        background: linear-gradient(135deg, var(--info-100) 0%, var(--info-200) 100%);
        color: var(--info-600);
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md);
        gap: var(--spacing-sm);
    }
}
