import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';

export type ViewMode = 'fixtures' | 'date' | 'playoffs';

@Component({
  selector: 'app-view-mode-toggle',
  templateUrl: './view-mode-toggle.component.html',
  styleUrls: ['./view-mode-toggle.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ViewModeToggleComponent {
  @Input() currentViewMode: ViewMode = 'fixtures';
  @Input() hideTitle: boolean = false;
  
  @Output() viewModeChanged = new EventEmitter<ViewMode>();

  onViewModeChange(mode: ViewMode): void {
    if (mode !== this.currentViewMode) {
      this.viewModeChanged.emit(mode);
    }
  }

  isActive(mode: ViewMode): boolean {
    return this.currentViewMode === mode;
  }
}
