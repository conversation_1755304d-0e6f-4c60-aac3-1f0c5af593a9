<div class="player-stats-container" *ngIf="playerStats && !isEmpty()">
    <!-- Header Section -->
    <div class="stats-header">
        <div class="header-content">
            <h3 class="stats-title">
                <i class="fas fa-map-marked-alt"></i>
                Performance by Position
            </h3>
            <p class="stats-subtitle">Breakdown of stats across different positions</p>
        </div>
        <div class="positions-count">
            {{getPlayerStatsAsArray()?.length}} Positions
        </div>
    </div>

    <!-- Chart Section -->
    <div class="chart-section" *ngIf="playerStatsChartOptions && getPlayerStatsAsArray()?.length! > 1">
        <div class="chart-header">
            <h4 class="chart-title">
                <i class="fas fa-chart-bar"></i>
                Rating Comparison by Position
            </h4>
        </div>
        <div class="chart-container">
            <ag-charts-angular
                [options]="playerStatsChartOptions"
                class="position-chart">
            </ag-charts-angular>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="position-card" *ngFor="let playerStats of getPlayerStatsAsArray()">
            <div class="position-header">
                <div class="position-icon">
                    <i [class]="getPositionIcon(playerStats.key)"></i>
                </div>
                <div class="position-info">
                    <h4 class="position-name">{{playerStats.key}}</h4>
                    <span class="games-count">{{playerStats.value.games}} games</span>
                </div>
            </div>

            <div class="position-stats">
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-value">{{playerStats.value.avgRating.toFixed(2)}}</span>
                        <span class="stat-label">Rating</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-futbol"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-value">{{playerStats.value.goals}}</span>
                        <span class="stat-label">Goals</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-value">{{playerStats.value.assists}}</span>
                        <span class="stat-label">Assists</span>
                    </div>
                </div>

                <div class="stat-item" *ngIf="playerStats.value.playerOfTheMatch > 0">
                    <div class="stat-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-value">{{playerStats.value.playerOfTheMatch}}</span>
                        <span class="stat-label">POTM</span>
                    </div>
                </div>
            </div>

            <!-- Performance Bar -->
            <div class="performance-bar">
                <div class="performance-fill"
                     [style.width.%]="getPerformancePercentage(playerStats.value.avgRating)"
                     [class]="getPerformanceClass(playerStats.value.avgRating)">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading State -->
<div class="loading-container" *ngIf="!playerStats || !playerStatsChartOptions">
    <pro-clubs-spinner></pro-clubs-spinner>
</div>