.action-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    animation: slideInFromBottom 0.6s ease-out 0.8s both;

    @media (max-width: 480px) {
        flex-direction: column;
        align-items: stretch;
    }
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);

        &::before {
            left: 100%;
        }
    }

    &:active {
        transform: translateY(0);
    }

    i {
        font-size: var(--text-lg);
    }

    &.edit-btn {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-600) 100%);
        color: white;
        border: 1px solid var(--primary-400);

        &:hover {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            border-color: var(--primary-500);
        }
    }

    &.stream-btn {
        background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
        color: white;
        border: 1px solid var(--danger-400);

        &:hover {
            background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-700) 100%);
            border-color: var(--danger-500);
        }

        &:disabled {
            background: var(--surface-tertiary);
            color: var(--text-secondary);
            border-color: var(--border-secondary);
            cursor: not-allowed;
            transform: none;

            &:hover {
                transform: none;
                box-shadow: none;
            }
        }
    }

    @media (max-width: 768px) {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--text-sm);
    }

    @media (max-width: 480px) {
        justify-content: center;
        padding: var(--spacing-md);
    }
}

@keyframes slideInFromBottom {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
