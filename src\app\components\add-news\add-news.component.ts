import { Component } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ListOption } from '../../shared/models/list-option.model';
import { NewsService } from '../../services/news.service';
import { NotificationService } from '../../services/notification.service';
import { PlayerService } from '../../services/player.service';
import { PlayerDTO } from '@pro-clubs-manager/shared-dtos';
import { IFreeAgentData } from '../news/news.model';
import { ITransferHistoryEntry } from '../../shared/models/transfer-history.model';

// Extended PlayerDTO with transfer history (matches server-side PlayerDTOWithTransferHistory)
interface PlayerDTOWithTransferHistory extends PlayerDTO {
  transferHistory?: ITransferHistoryEntry[];
}

export type AddNewsRequestModel = {
  title: string;
  content: string;
  createdBy: string;
  createdAt: Date;
  type: string;
  freeAgentData?: IFreeAgentData;
}

@Component({
  selector: 'add-news',
  templateUrl: './add-news.component.html',
  styleUrl: './add-news.component.scss'
})
export class AddNewsComponent {
  addNewsFormGroup: FormGroup = new FormGroup({});

  formControls = [
    { control: new FormControl('', Validators.required), field: 'title', displayText: 'Title', type: 'text-input', maxLength: 50 },
    { control: new FormControl('', Validators.required), field: 'content', displayText: 'Content', type: 'large-text-input', maxLength: 500 },
    { control: new FormControl(''), field: 'type', displayText: 'Type', type: 'select' },
    { control: new FormControl(''), field: 'freeAgentPlayer', displayText: 'Free Agent Player', type: 'select' }
  ];

  newsType: ListOption[] = [{ value: 'General', displayText: 'General' }, { value: 'Transfer', displayText: 'Transfer' }, { value: 'FreeAgent', displayText: 'Free Agent' }];
  freeAgents: PlayerDTOWithTransferHistory[] = [];
  freeAgentOptions: ListOption[] = [];
  selectedNewsType: string = '';
  selectedFreeAgent: PlayerDTOWithTransferHistory | null = null;

  constructor(
    private newsService: NewsService,
    private notificationService: NotificationService,
    private playerService: PlayerService
  ) { }

  ngOnInit() {
    this.loadFormControl();
    this.loadFreeAgents();
  }

  loadFormControl() {
    let group: any = {};
    this.formControls.forEach(item => {
      group[item.field] = item.control;
    });

    this.addNewsFormGroup = new FormGroup(group);
  }

  clearForm() {
    this.addNewsFormGroup.reset();
  }

  isRequiredForm(control: FormControl) {
    if (control.errors) {
      return control.errors['required'];
    }

    return false;
  }

  shouldShowField(fieldName: string): boolean {
    if (fieldName === 'title' && this.selectedNewsType === 'FreeAgent') {
      return false; // Hide title field for free agent news
    }
    return true;
  }

  getContentPlaceholder(fieldName: string): string {
    if (fieldName === 'content' && this.selectedNewsType === 'FreeAgent') {
      return 'Enter additional details (optional) - e.g., contact info, preferred position, etc.';
    }
    return 'Enter content';
  }

  async loadFreeAgents() {
    try {
      this.freeAgents = await this.playerService.getFreeAgents() as PlayerDTOWithTransferHistory[];
      this.freeAgentOptions = this.freeAgents.map(player => ({
        value: player.id,
        displayText: `${player.name} - ${player.position} (Age ${player.age})`
      }));
    } catch (error) {
      console.error('Error loading free agents:', error);
      this.notificationService.error('Failed to load free agents');
    }
  }

  onSelectionChange($newsType: ListOption) {
    if (!$newsType) return;

    this.selectedNewsType = $newsType.value;
    this.addNewsFormGroup.get('type')?.setValue($newsType.value);

    // Update form validation based on news type
    this.updateFormValidation();
  }

  private updateFormValidation() {
    const titleControl = this.addNewsFormGroup.get('title');
    const contentControl = this.addNewsFormGroup.get('content');

    if (this.selectedNewsType === 'FreeAgent') {
      // For free agent news, title is auto-generated and content is optional
      titleControl?.clearValidators();
      contentControl?.clearValidators();
    } else {
      // For other news types, both title and content are required
      titleControl?.setValidators([Validators.required]);
      contentControl?.setValidators([Validators.required]);
    }

    titleControl?.updateValueAndValidity();
    contentControl?.updateValueAndValidity();
  }

  onFreeAgentSelectionChange($freeAgent: ListOption) {
    if (!$freeAgent) return;

    this.selectedFreeAgent = this.freeAgents.find(player => player.id === $freeAgent.value) || null;
    this.addNewsFormGroup.get('freeAgentPlayer')?.setValue($freeAgent.value);
  }

  async onSubmit() {
    if (this.addNewsFormGroup.valid) {
      const convertedForm = this.convertFormToModel();
      convertedForm.createdAt = new Date();
      convertedForm.createdBy = 'Avi Vaknin';

      // Handle free agent data
      if (this.selectedNewsType === 'FreeAgent' && this.selectedFreeAgent) {
        convertedForm.freeAgentData = await this.generateFreeAgentData(this.selectedFreeAgent);
        convertedForm.title = `${this.selectedFreeAgent.name} looking for new team!`;
        // Use user-provided content if available, otherwise use default
        if (!convertedForm.content || convertedForm.content.trim() === '') {
          convertedForm.content = `${this.selectedFreeAgent.name} (${this.selectedFreeAgent.position}, Age ${this.selectedFreeAgent.age}) is now available as a free agent and looking for a new team!`;
        }
      }

      console.log(convertedForm);

      try {
        await this.newsService.addNews(convertedForm);
        this.notificationService.success(`News added successfully`);
        this.clearForm();
      } catch (error) {
        console.error('Error adding news:', error);
        this.notificationService.error('Failed to add news. Please try again.');
      }
    }
  };

  private async generateFreeAgentData(player: PlayerDTOWithTransferHistory): Promise<IFreeAgentData> {
    // Get player's transfer history to find previous teams
    const previousTeams = player.transferHistory
      ?.filter((transfer: ITransferHistoryEntry) => transfer.fromTeam) // Only transfers where player left a team
      .map((transfer: ITransferHistoryEntry) => ({
        id: transfer.fromTeam!.id,
        name: transfer.fromTeam!.name,
        imgUrl: transfer.fromTeam!.imgUrl,
        seasonNumber: transfer.seasonNumber
      }))
      .slice(-3) || []; // Get last 3 teams

    return {
      playerDetails: {
        id: player.id,
        name: player.name,
        imgUrl: player.imgUrl,
        position: player.position,
        age: player.age
      },
      previousTeams
    };
  }

    // when the user presses on submit, converting the form group into model before passing it to the server
    convertFormToModel(): AddNewsRequestModel {
      const convertedForm: AddNewsRequestModel = this.addNewsFormGroup.value;

      return convertedForm;
    }
}