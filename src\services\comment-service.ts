import { ClientSession, Types } from "mongoose";
import { inject, injectable } from "tsyringe";
import logger from "../config/logger";
import { BadRequestError, UnauthorizedError } from "../errors";
import { ICommentRepository, CommentWithReplies } from "../repositories/comment-repository";
import { IGameRepository } from "../interfaces/game";
import { transactionService } from "./util-services/transaction-service";

export interface CommentDTO {
  id: string;
  gameId: string;
  userId: string;
  content: string;
  parentCommentId?: string;
  likesCount: number;
  isLikedByUser: boolean;
  isEdited: boolean;
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: string;
    name: string;
    profileImage?: string;
  };
  replies?: CommentDTO[];
}

export interface CommentsResponse {
  comments: CommentDTO[];
  totalCount: number;
  hasMore: boolean;
}

export interface ICommentService {
  createComment(gameId: string, userId: string, content: string, parentCommentId?: string): Promise<CommentDTO>;
  updateComment(commentId: string, userId: string, content: string): Promise<CommentDTO>;
  deleteComment(commentId: string, userId: string): Promise<void>;
  getCommentsByGame(gameId: string, userId?: string, page?: number, limit?: number): Promise<CommentsResponse>;
  likeComment(commentId: string, userId: string): Promise<CommentDTO>;
  unlikeComment(commentId: string, userId: string): Promise<CommentDTO>;
  getUserComments(userId: string, limit?: number): Promise<CommentDTO[]>;
}

@injectable()
export class CommentService implements ICommentService {
  
  constructor(
    @inject("ICommentRepository") private commentRepository: ICommentRepository,
    @inject("IGameRepository") private gameRepository: IGameRepository
  ) {}

  async createComment(
    gameId: string, 
    userId: string, 
    content: string, 
    parentCommentId?: string
  ): Promise<CommentDTO> {
    logger.info(`CommentService: creating comment for game ${gameId} by user ${userId}`);

    // Validate content
    if (!content || content.trim().length === 0) {
      throw new BadRequestError("Comment content cannot be empty");
    }

    if (content.length > 1000) {
      throw new BadRequestError("Comment content cannot exceed 1000 characters");
    }

    // Validate game exists
    await this.gameRepository.getGameById(gameId);

    // If replying to a comment, validate parent exists and is not already a reply
    if (parentCommentId) {
      const parentComment = await this.commentRepository.getCommentById(parentCommentId);
      
      if (parentComment.parentCommentId) {
        throw new BadRequestError("Cannot reply to a reply. Only one level of nesting is allowed.");
      }
      
      if (parentComment.gameId.toString() !== gameId) {
        throw new BadRequestError("Parent comment does not belong to this game");
      }
    }

    return await transactionService.withTransaction(async (session) => {
      const comment = await this.commentRepository.createComment(
        new Types.ObjectId(gameId),
        new Types.ObjectId(userId),
        content.trim(),
        parentCommentId ? new Types.ObjectId(parentCommentId) : undefined,
        session
      );

      // Populate user data for response (within the same transaction)
      const populatedComment = await this.commentRepository.getCommentById(comment._id.toString(), session);

      return this.mapToDTO(populatedComment, userId);
    });
  }

  async updateComment(commentId: string, userId: string, content: string): Promise<CommentDTO> {
    logger.info(`CommentService: updating comment ${commentId} by user ${userId}`);

    // Validate content
    if (!content || content.trim().length === 0) {
      throw new BadRequestError("Comment content cannot be empty");
    }

    if (content.length > 1000) {
      throw new BadRequestError("Comment content cannot exceed 1000 characters");
    }

    const existingComment = await this.commentRepository.getCommentById(commentId);
    
    // Verify user owns this comment
    if (!existingComment.userId._id.equals(userId)) {
      throw new UnauthorizedError("You can only edit your own comments");
    }

    return await transactionService.withTransaction(async (session) => {
      const comment = await this.commentRepository.updateComment(
        commentId,
        content.trim(),
        session
      );

      const populatedComment = await this.commentRepository.getCommentById(comment._id.toString(), session);

      return this.mapToDTO(populatedComment, userId);
    });
  }

  async deleteComment(commentId: string, userId: string): Promise<void> {
    logger.info(`CommentService: deleting comment ${commentId} by user ${userId}`);

    const comment = await this.commentRepository.getCommentById(commentId);
    
    // Verify user owns this comment
    if (!comment.userId._id.equals(userId)) {
      throw new UnauthorizedError("You can only delete your own comments");
    }

    await transactionService.withTransaction(async (session) => {
      await this.commentRepository.deleteComment(commentId, session);
    });
  }

  async getCommentsByGame(
    gameId: string, 
    userId?: string, 
    page: number = 1, 
    limit: number = 20
  ): Promise<CommentsResponse> {
    logger.info(`CommentService: getting comments for game ${gameId}, page ${page}`);

    // Validate game exists
    await this.gameRepository.getGameById(gameId);

    const result = await this.commentRepository.getCommentsByGame(gameId, page, limit);
    
    const comments = result.comments.map(comment => this.mapToDTO(comment, userId));

    return {
      comments,
      totalCount: result.totalCount,
      hasMore: result.hasMore
    };
  }

  async likeComment(commentId: string, userId: string): Promise<CommentDTO> {
    logger.info(`CommentService: user ${userId} liking comment ${commentId}`);

    return await transactionService.withTransaction(async (session) => {
      const comment = await this.commentRepository.likeComment(commentId, userId, session);
      const populatedComment = await this.commentRepository.getCommentById(comment._id.toString(), session);

      return this.mapToDTO(populatedComment, userId);
    });
  }

  async unlikeComment(commentId: string, userId: string): Promise<CommentDTO> {
    logger.info(`CommentService: user ${userId} unliking comment ${commentId}`);

    return await transactionService.withTransaction(async (session) => {
      const comment = await this.commentRepository.unlikeComment(commentId, userId, session);
      const populatedComment = await this.commentRepository.getCommentById(comment._id.toString(), session);

      return this.mapToDTO(populatedComment, userId);
    });
  }

  async getUserComments(userId: string, limit: number = 20): Promise<CommentDTO[]> {
    logger.info(`CommentService: getting comments for user ${userId}`);

    const comments = await this.commentRepository.getUserComments(userId, limit);
    
    return comments.map(comment => this.mapToDTO(comment, userId));
  }

  private mapToDTO(comment: any, currentUserId?: string): CommentDTO {
    const isLikedByUser = currentUserId ? 
      comment.likes?.some((like: any) => like.toString() === currentUserId) || false : 
      false;

    return {
      id: comment._id.toString(),
      gameId: comment.gameId.toString(),
      userId: comment.userId._id?.toString() || comment.userId.toString(),
      content: comment.content,
      parentCommentId: comment.parentCommentId?.toString(),
      likesCount: comment.likes?.length || comment.likesCount || 0,
      isLikedByUser,
      isEdited: comment.isEdited || false,
      createdAt: comment.createdAt,
      updatedAt: comment.updatedAt,
      user: {
        id: comment.userId._id?.toString() || comment.userId.toString(),
        name: comment.userId.firstName && comment.userId.lastName
          ? `${comment.userId.firstName} ${comment.userId.lastName}`
          : 'Unknown User',
        profileImage: comment.userId.profilePicture
      },
      replies: comment.replies?.map((reply: any) => this.mapToDTO(reply, currentUserId))
    };
  }
}
