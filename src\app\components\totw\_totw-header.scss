/* === HEADER SECTION === */
.totw-header {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-md) var(--spacing-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    min-height: 48px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--warning-400), var(--warning-600));
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
    position: relative;
    z-index: 2;

    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
}

/* === TITLE SECTION === */
.title-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    flex: 1;

    @media (max-width: 768px) {
        align-items: center;
        text-align: center;
    }
}

.main-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;

    i {
        font-size: var(--text-2xl);
        color: var(--warning-500);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    h1 {
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

        @media (max-width: 768px) {
            font-size: var(--text-xl);
        }
    }

    @media (max-width: 768px) {
        justify-content: center;
        gap: var(--spacing-sm);

        i {
            font-size: var(--text-xl);
        }
    }
}

.week-badge {
    background: linear-gradient(135deg, var(--warning-400), var(--warning-600));
    color: var(--text-on-primary);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-bold);
    box-shadow: 0 2px 8px rgba(var(--warning-rgb), 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

    @media (max-width: 768px) {
        font-size: var(--text-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

.date-range {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);

    i {
        color: var(--primary-500);
        font-size: var(--text-base);
    }

    @media (max-width: 768px) {
        justify-content: center;
        font-size: var(--text-xs);
    }
}

/* === CONTROLS SECTION === */
.controls-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex-shrink: 0;

    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-md);
        width: 100%;
    }
}

.week-selector {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    min-width: 200px;

    @media (max-width: 768px) {
        width: 100%;
        min-width: unset;
    }
}

.selector-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin: 0;

    i {
        color: var(--primary-500);
        font-size: var(--text-sm);
    }
}

.navigation-buttons {
    display: flex;
    gap: var(--spacing-sm);

    @media (max-width: 768px) {
        width: 100%;
        justify-content: center;
    }
}

.nav-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
    justify-content: center;

    &:hover:not(:disabled) {
        background: var(--surface-tertiary);
        border-color: var(--primary-400);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:disabled,
    &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: var(--surface-disabled);
        color: var(--text-disabled);
        border-color: var(--border-disabled);

        &:hover {
            transform: none;
            box-shadow: none;
        }
    }

    i {
        font-size: var(--text-sm);
    }

    @media (max-width: 768px) {
        padding: var(--spacing-sm);
        min-width: 80px;
        font-size: var(--text-xs);
    }
}

.prev-button {
    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--info-50), var(--info-100));
        border-color: var(--info-400);
        color: var(--info-700);
    }
}

.next-button {
    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--success-50), var(--success-100));
        border-color: var(--success-400);
        color: var(--success-700);
    }
}
