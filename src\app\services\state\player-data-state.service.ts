import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { PlayerDTO, PlayerLastGamesForm } from '@pro-clubs-manager/shared-dtos';

export interface PlayerDataState {
  players: Map<string, PlayerDTO>;
  playerForms: Map<string, PlayerLastGamesForm>;
  lastPlayerUpdate: Map<string, Date>;
  lastFormUpdate: Map<string, Date>;
  loadingPlayers: Set<string>;
  loadingForms: Set<string>;
}

@Injectable({
  providedIn: 'root'
})
export class PlayerDataStateService {
  private readonly initialState: PlayerDataState = {
    players: new Map(),
    playerForms: new Map(),
    lastPlayerUpdate: new Map(),
    lastFormUpdate: new Map(),
    loadingPlayers: new Set(),
    loadingForms: new Set()
  };

  private stateSubject = new BehaviorSubject<PlayerDataState>(this.initialState);
  public state$ = this.stateSubject.asObservable();

  // Cache duration in milliseconds
  private readonly PLAYER_CACHE_DURATION = 15 * 60 * 1000; // 15 minutes
  private readonly FORM_CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

  constructor() {
    this.loadStateFromStorage();
  }

  get currentState(): PlayerDataState {
    return this.stateSubject.value;
  }

  getPlayer(playerId: string): PlayerDTO | null {
    return this.currentState.players.get(playerId) || null;
  }

  getPlayerForm(playerId: string): PlayerLastGamesForm | null {
    return this.currentState.playerForms.get(playerId) || null;
  }

  updatePlayer(playerId: string, player: PlayerDTO): void {
    const newState = { ...this.currentState };
    newState.players = new Map(newState.players);
    newState.lastPlayerUpdate = new Map(newState.lastPlayerUpdate);
    newState.loadingPlayers = new Set(newState.loadingPlayers);
    
    newState.players.set(playerId, player);
    newState.lastPlayerUpdate.set(playerId, new Date());
    newState.loadingPlayers.delete(playerId);
    
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  updatePlayerForm(playerId: string, form: PlayerLastGamesForm): void {
    const newState = { ...this.currentState };
    newState.playerForms = new Map(newState.playerForms);
    newState.lastFormUpdate = new Map(newState.lastFormUpdate);
    newState.loadingForms = new Set(newState.loadingForms);
    
    newState.playerForms.set(playerId, form);
    newState.lastFormUpdate.set(playerId, new Date());
    newState.loadingForms.delete(playerId);
    
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  setPlayerLoading(playerId: string, isLoading: boolean): void {
    const newState = { ...this.currentState };
    newState.loadingPlayers = new Set(newState.loadingPlayers);
    
    if (isLoading) {
      newState.loadingPlayers.add(playerId);
    } else {
      newState.loadingPlayers.delete(playerId);
    }
    
    this.stateSubject.next(newState);
  }

  setFormLoading(playerId: string, isLoading: boolean): void {
    const newState = { ...this.currentState };
    newState.loadingForms = new Set(newState.loadingForms);
    
    if (isLoading) {
      newState.loadingForms.add(playerId);
    } else {
      newState.loadingForms.delete(playerId);
    }
    
    this.stateSubject.next(newState);
  }

  isPlayerStale(playerId: string): boolean {
    const lastUpdate = this.currentState.lastPlayerUpdate.get(playerId);
    if (!lastUpdate) return true;
    return Date.now() - lastUpdate.getTime() > this.PLAYER_CACHE_DURATION;
  }

  isFormStale(playerId: string): boolean {
    const lastUpdate = this.currentState.lastFormUpdate.get(playerId);
    if (!lastUpdate) return true;
    return Date.now() - lastUpdate.getTime() > this.FORM_CACHE_DURATION;
  }

  isPlayerLoading(playerId: string): boolean {
    return this.currentState.loadingPlayers.has(playerId);
  }

  isFormLoading(playerId: string): boolean {
    return this.currentState.loadingForms.has(playerId);
  }

  removePlayer(playerId: string): void {
    const newState = { ...this.currentState };
    newState.players = new Map(newState.players);
    newState.playerForms = new Map(newState.playerForms);
    newState.lastPlayerUpdate = new Map(newState.lastPlayerUpdate);
    newState.lastFormUpdate = new Map(newState.lastFormUpdate);
    newState.loadingPlayers = new Set(newState.loadingPlayers);
    newState.loadingForms = new Set(newState.loadingForms);
    
    newState.players.delete(playerId);
    newState.playerForms.delete(playerId);
    newState.lastPlayerUpdate.delete(playerId);
    newState.lastFormUpdate.delete(playerId);
    newState.loadingPlayers.delete(playerId);
    newState.loadingForms.delete(playerId);
    
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  clearCache(): void {
    this.stateSubject.next(this.initialState);
    this.clearStateFromStorage();
  }

  private saveStateToStorage(state: PlayerDataState): void {
    try {
      const stateToSave = {
        players: Array.from(state.players.entries()),
        playerForms: Array.from(state.playerForms.entries()),
        lastPlayerUpdate: Array.from(state.lastPlayerUpdate.entries()).map(([key, date]) => [key, date.toISOString()]),
        lastFormUpdate: Array.from(state.lastFormUpdate.entries()).map(([key, date]) => [key, date.toISOString()]),
        // Don't save loading states
      };
      localStorage.setItem('player-data-state', JSON.stringify(stateToSave));
    } catch (error) {
      console.warn('Failed to save player data state to localStorage:', error);
    }
  }

  private loadStateFromStorage(): void {
    try {
      const savedState = localStorage.getItem('player-data-state');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        const restoredState: PlayerDataState = {
          ...this.initialState,
          players: new Map<string, PlayerDTO>(parsedState.players || []),
          playerForms: new Map<string, PlayerLastGamesForm>(parsedState.playerForms || []),
          lastPlayerUpdate: new Map<string, Date>((parsedState.lastPlayerUpdate || []).map(([key, dateStr]: [string, string]) => [key, new Date(dateStr)])),
          lastFormUpdate: new Map<string, Date>((parsedState.lastFormUpdate || []).map(([key, dateStr]: [string, string]) => [key, new Date(dateStr)])),
          // Reset loading states on app start
          loadingPlayers: new Set<string>(),
          loadingForms: new Set<string>()
        };
        this.stateSubject.next(restoredState);
      }
    } catch (error) {
      console.warn('Failed to load player data state from localStorage:', error);
    }
  }

  private clearStateFromStorage(): void {
    try {
      localStorage.removeItem('player-data-state');
    } catch (error) {
      console.warn('Failed to clear player data state from localStorage:', error);
    }
  }
}
