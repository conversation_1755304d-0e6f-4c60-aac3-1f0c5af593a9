.fixture-card {
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: fadeInUp 0.6s ease-out;
    position: relative;
    width: 100%;
    margin: 0 auto;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: var(--shadow-2xl);
        border-color: var(--primary-400);

        &::before {
            opacity: 1;
        }
    }

    &.playoff-match {
        border-color: var(--warning-400);
        background: linear-gradient(135deg, var(--surface-primary) 0%, rgba(251, 191, 36, 0.08) 100%);

        &::before {
            background: linear-gradient(90deg, var(--warning-400), var(--warning-600));
            opacity: 0.7;
        }
    }

    .match-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-md) var(--spacing-lg);
        background: var(--surface-secondary);
        border-bottom: 1px solid var(--border-primary);

        .match-status {
            display: flex;
            gap: var(--spacing-sm);

            .status-badge {
                padding: var(--spacing-xs) var(--spacing-sm);
                border-radius: var(--radius-md);
                font-size: var(--text-xs);
                font-weight: var(--font-weight-semibold);
                text-transform: uppercase;
                letter-spacing: 0.5px;
                background: var(--surface-tertiary);
                color: var(--text-secondary);
            }

            .playoff-badge {
                padding: var(--spacing-xs) var(--spacing-sm);
                border-radius: var(--radius-md);
                font-size: var(--text-xs);
                font-weight: var(--font-weight-bold);
                background: var(--warning-100);
                color: var(--warning-700);
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .match-number-badge {
                padding: var(--spacing-xs) var(--spacing-sm);
                border-radius: var(--radius-md);
                font-size: var(--text-xs);
                font-weight: var(--font-weight-bold);
                background: var(--info-100);
                color: var(--info-700);
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-left: var(--spacing-xs);
            }
        }

        .match-date {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
        }
    }

    .teams-section {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        align-items: center;
        gap: var(--spacing-lg);
        padding: var(--spacing-xl);

        .team {
            .team-info {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                flex-direction: column;

                .team-logo {
                    width: 40px;
                    height: 40px;
                    border-radius: var(--radius-full);
                    object-fit: cover;
                    border: 2px solid var(--border-primary);
                    flex-shrink: 0;
                }

                .team-name {
                    font-size: var(--text-base);
                    font-weight: var(--font-weight-semibold);
                    color: var(--text-primary);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            &.away-team .team-info {
                flex-direction: column;
            }
        }

        .score-section {
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 80px;

            .score-display {
                .score {
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-sm);
                    font-size: var(--text-2xl);
                    font-weight: var(--font-weight-bold);
                    color: var(--text-primary);

                    .score-separator {
                        color: var(--text-secondary);
                        font-weight: var(--font-weight-normal);
                    }
                }

                .no-score {
                    .vs-text {
                        font-size: var(--text-lg);
                        font-weight: var(--font-weight-semibold);
                        color: var(--text-secondary);
                        padding: var(--spacing-sm) var(--spacing-md);
                        background: var(--surface-tertiary);
                        border-radius: var(--radius-md);
                    }
                }
            }

            .score-edit {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);

                .score-input {
                    width: 50px;
                    padding: var(--spacing-sm);
                    border: 2px solid var(--border-primary);
                    border-radius: var(--radius-md);
                    background: var(--surface-secondary);
                    color: var(--text-primary);
                    font-size: var(--text-lg);
                    font-weight: var(--font-weight-bold);
                    text-align: center;
                    transition: all 0.2s ease;

                    &:focus {
                        outline: none;
                        border-color: var(--primary-500);
                        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
                    }
                }

                .score-separator {
                    color: var(--text-secondary);
                    font-size: var(--text-lg);
                    font-weight: var(--font-weight-semibold);
                }
            }
        }
    }

    .actions-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-md) var(--spacing-lg);
        background: var(--surface-secondary);
        border-top: 1px solid var(--border-primary);

        .action-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;

            &.details-btn {
                background: var(--primary-100);
                color: var(--primary-700);

                &:hover {
                    background: var(--primary-200);
                    transform: translateY(-1px);
                }
            }

            &.edit-btn {
                background: var(--warning-100);
                color: var(--warning-700);

                &:hover {
                    background: var(--warning-200);
                    transform: translateY(-1px);
                }
            }

            &.clock-btn {
                background: var(--info-100);
                color: var(--info-700);

                &:hover {
                    background: var(--info-200);
                    transform: translateY(-1px);
                }
            }

            &.save-btn {
                background: var(--success-100);
                color: var(--success-700);

                &:hover {
                    background: var(--success-200);
                    transform: translateY(-1px);
                }
            }

            &.cancel-btn {
                background: var(--danger-100);
                color: var(--danger-700);

                &:hover {
                    background: var(--danger-200);
                    transform: translateY(-1px);
                }
            }

            &.stream-btn {
                background: var(--danger-500);
                color: white;

                &:hover {
                    background: var(--danger-600);
                    transform: translateY(-1px);
                }
            }
        }

        .edit-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
    }

    // Status-specific styling
    &.status-scheduled {
        .match-header .status-badge {
            background: var(--info-100) !important;
            color: var(--info-700) !important;
        }
    }

    &.status-played {
        .match-header .status-badge {
            background: var(--success-100) !important;
            color: var(--success-700) !important;
        }
    }

    &.status-completed {
        .match-header .status-badge {
            background: var(--primary-100) !important;
            color: var(--primary-700) !important;
        }
    }

    @media (max-width: 768px) {
        .teams-section {
            padding: var(--spacing-lg);
            gap: var(--spacing-md);

            .team .team-info {
                .team-logo {
                    width: 32px;
                    height: 32px;
                }

                .team-name {
                    font-size: var(--text-sm);
                }
            }

            .score-section {
                .score-display .score {
                    font-size: var(--text-xl);
                }

                .score-edit .score-input {
                    width: 40px;
                    font-size: var(--text-base);
                }
            }
        }

        .actions-section {
            flex-wrap: wrap;
            gap: var(--spacing-sm);

            .action-btn {
                font-size: var(--text-xs);
                padding: var(--spacing-xs) var(--spacing-sm);
            }
        }
    }
}

/* === MOBILE SPECIFIC STYLES === */
@media (max-width: 768px) {
    .fixture-card {
        margin: 0 auto !important;
        max-width: calc(100vw - 2rem) !important; // Ensure it doesn't exceed viewport width
        width: calc(100vw - 2rem) !important;
        box-sizing: border-box !important;
        left: 0 !important;
        right: 0 !important;
        position: relative;

        &:hover {
            transform: translateY(-4px) scale(1.01);
            box-shadow: var(--shadow-xl);
        }

        .match-header {
            padding: var(--spacing-sm) var(--spacing-md);
        }

        .match-content {
            padding: var(--spacing-md);
        }

        .teams-container {
            gap: var(--spacing-xs);
            min-width: 0;
            overflow: hidden;

            .team {
                min-width: 0; // Allow flex item to shrink below content size
                flex: 1; // Allow teams to take equal space

                .team-info {
                    min-width: 0; // Allow flex item to shrink

                    .team-name {
                        font-size: var(--text-sm);
                        word-break: break-word;
                        overflow-wrap: break-word;
                        hyphens: auto;
                        line-height: 1.2;
                        max-width: 100%;
                        white-space: normal; // Allow text wrapping
                    }
                }

                .team-logo {
                    width: 35px;
                    height: 35px;
                    flex-shrink: 0;
                    margin-bottom: var(--spacing-xs);
                }
            }

            .score-section {
                .score {
                    font-size: var(--text-lg);
                }
            }
        }

        .match-actions {
            padding: var(--spacing-sm) var(--spacing-md);

            .action-btn {
                padding: var(--spacing-xs) var(--spacing-sm);
                font-size: var(--text-xs);
            }
        }
    }
}

/* === TIME EDIT SECTION === */
.time-edit-section {
    background: var(--surface-tertiary);
    border-top: 1px solid var(--border-primary);
    padding: var(--spacing-md);

    .time-edit-inputs {
        display: flex;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-md);

        .time-input-group {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);

            label {
                font-size: var(--text-xs);
                font-weight: var(--font-weight-semibold);
                color: var(--text-secondary);
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .time-input {
                padding: var(--spacing-sm);
                border: 1px solid var(--border-primary);
                border-radius: var(--radius-md);
                background: var(--surface-primary);
                color: var(--text-primary);
                font-size: var(--text-sm);
                transition: all 0.2s ease;

                &:focus {
                    outline: none;
                    border-color: var(--primary);
                    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
                }
            }
        }
    }

    .time-edit-actions {
        display: flex;
        gap: var(--spacing-sm);
        justify-content: flex-end;

        .action-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 80px;
            justify-content: center;

            i {
                font-size: var(--text-xs);
            }

            &.save-btn {
                background: var(--success-500);
                color: white;

                &:hover {
                    background: var(--success-600);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(var(--success-rgb), 0.3);
                }

                &:active {
                    transform: translateY(0);
                    box-shadow: 0 2px 4px rgba(var(--success-rgb), 0.3);
                }
            }

            &.cancel-btn {
                background: var(--surface-primary);
                color: var(--text-secondary);
                border: 1px solid var(--border-primary);

                &:hover {
                    background: var(--surface-secondary);
                    color: var(--text-primary);
                    border-color: var(--border-secondary);
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                &:active {
                    transform: translateY(0);
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                }
            }
        }
    }
}

/* === UPDATE STATE STYLES === */
.fixture-card {
    &.updating {
        border-color: var(--warning);
        box-shadow: 0 0 0 2px rgba(var(--warning-rgb), 0.2);

        .update-state-indicator {
            background: var(--warning);
            color: var(--text-on-warning);
        }
    }

    &.update-success {
        border-color: var(--success);
        box-shadow: 0 0 0 2px rgba(var(--success-rgb), 0.2);

        .update-state-indicator {
            background: var(--success);
            color: var(--text-on-success);
        }
    }

    &.update-error {
        border-color: var(--error);
        box-shadow: 0 0 0 2px rgba(var(--error-rgb), 0.2);

        .update-state-indicator {
            background: var(--error);
            color: var(--text-on-error);
        }
    }
}

.update-state-indicator {
    position: absolute;
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    z-index: 10;
    transition: all 0.3s ease;

    .update-text {
        font-size: var(--font-size-xs);
        white-space: nowrap;
    }

    i {
        font-size: var(--font-size-sm);
    }
}