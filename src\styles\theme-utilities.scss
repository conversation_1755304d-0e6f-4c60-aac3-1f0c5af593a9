/* === THEME UTILITY CLASSES === */

/* Theme-aware background classes */
.theme-bg-primary {
    background: var(--bg-primary);
}

.theme-bg-secondary {
    background: var(--bg-secondary);
}

.theme-bg-tertiary {
    background: var(--bg-tertiary);
}

/* Colorful surface classes for light theme */
.theme-surface-success {
    background: var(--surface-success, var(--surface-secondary));
    border: 1px solid var(--border-success, var(--border-primary));
}

.theme-surface-warning {
    background: var(--surface-warning, var(--surface-secondary));
    border: 1px solid var(--border-warning, var(--border-primary));
}

.theme-surface-error {
    background: var(--surface-error, var(--surface-secondary));
    border: 1px solid var(--border-error, var(--border-primary));
}

.theme-surface-info {
    background: var(--surface-info, var(--surface-secondary));
    border: 1px solid var(--border-info, var(--border-primary));
}

.theme-surface-accent {
    background: var(--surface-accent, var(--surface-secondary));
    border: 1px solid var(--border-accent, var(--border-primary));
}

.theme-surface-primary {
    background-color: var(--surface-primary);
    border: 1px solid var(--border-primary);
}

.theme-surface-secondary {
    background-color: var(--surface-secondary);
    border: 1px solid var(--border-primary);
}

.theme-surface-elevated {
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
}

/* Theme-aware text classes */
.theme-text-primary {
    color: var(--text-primary);
}

.theme-text-secondary {
    color: var(--text-secondary);
}

.theme-text-tertiary {
    color: var(--text-tertiary);
}

.theme-text-inverse {
    color: var(--text-inverse);
}

.theme-text-muted {
    color: var(--text-muted);
}

/* Theme-aware border classes */
.theme-border-primary {
    border-color: var(--border-primary);
}

.theme-border-secondary {
    border-color: var(--border-secondary);
}

.theme-border-focus {
    border-color: var(--border-focus);
}

/* Theme-aware button classes */
.theme-btn-primary {
    background-color: var(--button-primary-bg);
    color: var(--text-inverse);
    border: 1px solid var(--button-primary-bg);
    
    &:hover {
        background-color: var(--button-primary-hover);
        border-color: var(--button-primary-hover);
    }
}

.theme-btn-secondary {
    background-color: var(--button-secondary-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    
    &:hover {
        background-color: var(--button-secondary-hover);
    }
}

/* Theme-aware card classes */
.theme-card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    box-shadow: var(--card-shadow);
    border-radius: var(--radius-lg);
    
    &:hover {
        box-shadow: var(--shadow-xl);
        transform: translateY(-2px);
    }
}

.theme-card-compact {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    box-shadow: var(--shadow-sm);
    border-radius: var(--radius-md);
}

/* Theme-aware input classes */
.theme-input {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    color: var(--text-primary);
    
    &:focus {
        border-color: var(--input-focus);
        outline: none;
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }
    
    &::placeholder {
        color: var(--text-muted);
    }
}

/* Theme-specific visibility classes */
.light-only {
    display: block;
}

.dark-only {
    display: none;
}

[data-theme="dark"] {
    .light-only {
        display: none;
    }
    
    .dark-only {
        display: block;
    }
}

[data-theme="light"] {
    .light-only {
        display: block;
    }
    
    .dark-only {
        display: none;
    }
}

/* Theme transition classes */
.theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.theme-transition-fast {
    transition: background-color 0.15s ease, color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
}

.theme-transition-slow {
    transition: background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease, box-shadow 0.5s ease;
}

/* Theme-aware shadow classes */
.theme-shadow-sm {
    box-shadow: var(--shadow-sm);
}

.theme-shadow-md {
    box-shadow: var(--shadow-md);
}

.theme-shadow-lg {
    box-shadow: var(--shadow-lg);
}

.theme-shadow-xl {
    box-shadow: var(--shadow-xl);
}

/* Theme-aware hover effects */
.theme-hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }
}

.theme-hover-glow {
    transition: box-shadow 0.2s ease;
    
    &:hover {
        box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.3);
    }
}

/* Theme-aware status colors */
.theme-success {
    color: var(--success-600);
}

.theme-warning {
    color: var(--warning-600);
}

.theme-error {
    color: var(--error-600);
}

.theme-info {
    color: var(--info-600);
}

/* Theme-aware background status colors */
.theme-bg-success {
    background-color: var(--success-50);
    color: var(--success-700);
    border: 1px solid var(--success-200);
}

.theme-bg-warning {
    background-color: var(--warning-50);
    color: var(--warning-700);
    border: 1px solid var(--warning-200);
}

.theme-bg-error {
    background-color: var(--error-50);
    color: var(--error-700);
    border: 1px solid var(--error-200);
}

.theme-bg-info {
    background-color: var(--info-50);
    color: var(--info-700);
    border: 1px solid var(--info-200);
}

/* Dark theme adjustments for status colors */
[data-theme="dark"] {
    .theme-bg-success {
        background-color: rgba(16, 185, 129, 0.1);
        color: var(--success-400);
        border-color: rgba(16, 185, 129, 0.2);
    }
    
    .theme-bg-warning {
        background-color: rgba(245, 158, 11, 0.1);
        color: var(--warning-400);
        border-color: rgba(245, 158, 11, 0.2);
    }
    
    .theme-bg-error {
        background-color: rgba(239, 68, 68, 0.1);
        color: var(--error-400);
        border-color: rgba(239, 68, 68, 0.2);
    }
    
    .theme-bg-info {
        background-color: rgba(59, 130, 246, 0.1);
        color: var(--info-400);
        border-color: rgba(59, 130, 246, 0.2);
    }
}
