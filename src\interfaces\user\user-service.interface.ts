import { IUser } from "../../models/user";

export interface CreateUserRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface GoogleAuthRequest {
  googleId: string;
  email: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
}

export interface AuthResponse {
  user: IUser;
  token: string;
}

export interface IUserService {
  createUser(userData: CreateUserRequest): Promise<AuthResponse>;
  loginUser(loginData: LoginRequest): Promise<AuthResponse>;
  googleAuth(googleData: GoogleAuthRequest): Promise<AuthResponse>;
  getUserById(userId: string): Promise<IUser | null>;
  getUserByEmail(email: string): Promise<IUser | null>;
  updateUser(userId: string, updateData: Partial<IUser>): Promise<IUser | null>;
  deleteUser(userId: string): Promise<boolean>;
  associatePlayerWithUser(userId: string, playerId?: string, playerEmail?: string): Promise<IUser | null>;
  removePlayerFromUser(userId: string, playerId: string): Promise<IUser | null>;
  verifyEmail(userId: string): Promise<IUser | null>;
  searchAvailablePlayers(search?: string, email?: string): Promise<any[]>;
  getUserAssociatedPlayers(userId: string): Promise<any[]>;
  checkPlayerOwnership(userId: string, playerId: string): Promise<boolean>;
  isPlayerAssociated(playerId: string): Promise<boolean>;
}
