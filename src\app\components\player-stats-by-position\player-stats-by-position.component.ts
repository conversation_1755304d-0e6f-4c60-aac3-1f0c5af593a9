import { Component, Input } from '@angular/core';
import { PlayerService } from '../../services/player.service';
import { IPlayerStats } from '../../shared/models/player.model';
import { AgChartOptions } from 'ag-charts-community';

@Component({
  selector: 'player-stats-by-position',
  templateUrl: './player-stats-by-position.component.html',
  styleUrl: './player-stats-by-position.component.scss'
})
export class PlayerStatsByPositionComponent {

  playerStats: { [position: string]: IPlayerStats } | undefined = undefined;
  playerStatsChartOptions: AgChartOptions = {};

  @Input() playerId: string = '';

  constructor(private playerService: PlayerService) { }

  ngOnInit() {
    this.loadPlayerStatsByPosition();
  }

  async loadPlayerStatsByPosition() {
    this.playerStats = await this.playerService.getPlayerStatsByPosition(this.playerId);

    this.loadGraphData();
  };

  getPlayerStatsAsArray() {
    if (this.playerStats)
      return Object.entries(this.playerStats!).map(([key, value]) => ({ key, value }));

    return null;
  }

  loadGraphData() {
    const chartData = Object.entries(this.playerStats!).map(([key, value]) => ({
      position: key,
      rating: value.avgRating,
      games: value.games,
      goals: value.goals,
      assists: value.assists,
      potm: value.playerOfTheMatch
    }));

    this.playerStatsChartOptions = {
      data: chartData,
      background: { visible: false },
      theme: 'ag-default-dark',
      height: 240,
      series: [{
        type: "bar",
        xKey: "position",
        yKey: "rating",
        yName: "Average Rating",
        fill: '#6366f1',
        stroke: '#4f46e5',
        strokeWidth: 1,
        tooltip: {
          renderer: (tooltipParams: any) => {
            const data = tooltipParams.datum;
            return {
              content: `Rating: ${data.rating.toFixed(2)}<br>Games: ${data.games}<br>Goals: ${data.goals}<br>Assists: ${data.assists}<br>POTM: ${data.potm}`,
              title: `Position: ${data.position}`
            };
          },
        }
      }],
      axes: [
        {
          type: 'category',
          position: 'bottom'
        },
        {
          type: 'number',
          position: 'left',
          min: 0,
          max: 10
        }
      ],
      legend: {
        enabled: false
      }
    };
  };

  isEmpty(): boolean {
    if (!this.playerStats) {
      return true;
    }
    return Object.keys(this.playerStats).length === 0;
  }

  getPositionIcon(position: string): string {
    const positionIcons: { [key: string]: string } = {
      'ST': 'fas fa-futbol',
      'LM': 'fas fa-arrow-left',
      'RM': 'fas fa-arrow-right',
      'CAM': 'fas fa-magic',
      'CDM': 'fas fa-shield-alt',
      'CB': 'fas fa-user-shield',
      'GK': 'fas fa-hand-paper',
      'LW': 'fas fa-arrow-left',
      'RW': 'fas fa-arrow-right',
      'CM': 'fas fa-circle',
      'LB': 'fas fa-arrow-down',
      'RB': 'fas fa-arrow-down'
    };
    return positionIcons[position] || 'fas fa-user';
  }

  getPerformancePercentage(rating: number): number {
    // Convert rating (0-10) to percentage (0-100)
    // Assuming 10 is the maximum rating
    return Math.min((rating / 10) * 100, 100);
  }

  getPerformanceClass(rating: number): string {
    if (rating >= 8.5) return 'excellent';
    if (rating >= 7.5) return 'good';
    if (rating >= 6.5) return 'average';
    return 'poor';
  }

}