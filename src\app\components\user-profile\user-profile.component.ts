import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AuthService, User } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';

@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss']
})
export class UserProfileComponent implements OnInit {
  user: User | null = null;
  profileForm: FormGroup;
  associatedPlayers: any[] = [];
  availablePlayers: any[] = [];
  searchForm: FormGroup;
  isLoading = false;
  isSearching = false;
  showPlayerSearch = false;
  showEditProfile = false;
  associationRequests: any[] = [];

  constructor(
    private authService: AuthService,
    private notificationService: NotificationService
  ) {
    this.profileForm = new FormGroup({
      firstName: new FormControl('', Validators.required),
      lastName: new FormControl('', Validators.required),
      email: new FormControl('', [Validators.required, Validators.email])
    });

    this.searchForm = new FormGroup({
      searchTerm: new FormControl(''),
      playerEmail: new FormControl('', Validators.email)
    });
  }

  ngOnInit(): void {
    this.loadUserProfile();
    this.loadAssociatedPlayers();
    this.loadAssociationRequests();
  }

  async refreshAssociatedPlayers(): Promise<void> {
    await this.loadAssociatedPlayers();
    this.notificationService.success('Associated players refreshed');
  }

  async loadUserProfile(): Promise<void> {
    try {
      this.user = await this.authService.getProfile();
      this.profileForm.patchValue({
        firstName: this.user.firstName,
        lastName: this.user.lastName,
        email: this.user.email
      });
    } catch (error: any) {
      this.notificationService.error('Failed to load profile');
    }
  }

  async loadAssociatedPlayers(): Promise<void> {
    try {
      this.associatedPlayers = await this.authService.getAssociatedPlayers();
      console.log('Associated players loaded:', this.associatedPlayers);
    } catch (error: any) {
      console.error('Error loading associated players:', error);
      this.notificationService.error('Failed to load associated players');
      // Initialize as empty array to prevent undefined errors
      this.associatedPlayers = [];
    }
  }

  async updateProfile(): Promise<void> {
    if (this.profileForm.valid && !this.isLoading) {
      this.isLoading = true;
      
      try {
        const updateData = this.profileForm.value;
        await this.authService.updateProfile(updateData);
        this.notificationService.success('Profile updated successfully');
        this.user = this.authService.getCurrentUser();
      } catch (error: any) {
        this.notificationService.error(error.message || 'Failed to update profile');
      } finally {
        this.isLoading = false;
      }
    }
  }

  async searchPlayers(): Promise<void> {
    if (this.isSearching) return;
    
    this.isSearching = true;
    
    try {
      const searchTerm = this.searchForm.get('searchTerm')?.value;
      const playerEmail = this.searchForm.get('playerEmail')?.value;
      
      this.availablePlayers = await this.authService.searchAvailablePlayers(
        searchTerm || undefined,
        playerEmail || undefined
      );
      
      if (this.availablePlayers.length === 0) {
        this.notificationService.info('No available players found');
      } else {
        // Auto-scroll to search results on mobile
        setTimeout(() => {
          const searchResults = document.querySelector('.search-results');
          if (searchResults) {
            searchResults.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            });
          }
        }, 100);
      }
    } catch (error: any) {
      this.notificationService.error('Failed to search players');
    } finally {
      this.isSearching = false;
    }
  }

  async associatePlayer(player: any): Promise<void> {
    // Check if user already has a player associated
    if (this.associatedPlayers.length >= 1) {
      this.notificationService.error('You can only associate with one player. Remove your current player first.');
      return;
    }

    try {
      await this.authService.requestPlayerAssociation(player.id);
      this.notificationService.success(`Player association request submitted for ${player.name}! An admin will review your request.`);
      await this.loadAssociatedPlayers();
      this.availablePlayers = this.availablePlayers.filter(p => p.id !== player.id);
    } catch (error: any) {
      this.notificationService.error(error.message || 'Failed to submit player association request');
    }
  }

  async associatePlayerByEmail(): Promise<void> {
    // Check if user already has a player associated
    if (this.associatedPlayers.length >= 1) {
      this.notificationService.error('You can only associate with one player. Remove your current player first.');
      return;
    }

    const playerEmail = this.searchForm.get('playerEmail')?.value;
    if (!playerEmail) {
      this.notificationService.error('Please enter a player email');
      return;
    }

    try {
      await this.authService.requestPlayerAssociation(undefined, playerEmail);
      this.notificationService.success(`Player association request submitted for email ${playerEmail}! An admin will review your request.`);
      await this.loadAssociatedPlayers();
      this.searchForm.get('playerEmail')?.setValue('');
    } catch (error: any) {
      this.notificationService.error(error.message || 'Failed to submit player association request');
    }
  }

  async removePlayer(player: any): Promise<void> {
    if (confirm(`Are you sure you want to remove association with ${player.name}?`)) {
      try {
        await this.authService.removePlayer(player.id);
        this.notificationService.success(`Removed association with ${player.name}`);
        await this.loadAssociatedPlayers();
      } catch (error: any) {
        this.notificationService.error(error.message || 'Failed to remove player');
      }
    }
  }

  togglePlayerSearch(): void {
    // Check if user already has a player associated
    if (this.associatedPlayers.length >= 1 && !this.showPlayerSearch) {
      this.notificationService.error('You can only associate with one player. Remove your current player first.');
      return;
    }

    this.showPlayerSearch = !this.showPlayerSearch;
    if (this.showPlayerSearch) {
      this.availablePlayers = [];
      this.searchForm.reset();

      // Auto-scroll to search section on mobile
      setTimeout(() => {
        const searchSection = document.querySelector('.search-section');
        if (searchSection) {
          searchSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          });
        }
      }, 100);
    }
  }

  async loadAssociationRequests(): Promise<void> {
    try {
      this.associationRequests = await this.authService.getUserAssociationRequests();
    } catch (error: any) {
      console.error('Error loading association requests:', error);
    }
  }

  async cancelRequest(requestId: string): Promise<void> {
    try {
      await this.authService.cancelAssociationRequest(requestId);
      this.notificationService.success('Request cancelled successfully');
      await this.loadAssociationRequests();
    } catch (error: any) {
      this.notificationService.error(error.message || 'Failed to cancel request');
    }
  }

  toggleEditProfile(): void {
    this.showEditProfile = !this.showEditProfile;
  }

  getRequestStatusClass(status: string): string {
    switch (status) {
      case 'pending': return 'status-pending';
      case 'approved': return 'status-approved';
      case 'rejected': return 'status-rejected';
      default: return '';
    }
  }

  logout(): void {
    this.authService.logout();
  }
}
