import mongoose from 'mongoose';
import { container } from '../src/config/container.config';
import { ISeasonAchievementService } from '../src/services/wrapper-services/season-achievement-service';

// Connect to MongoDB
async function connectToDatabase() {
  try {
    // Load environment variables
    require('dotenv').config();
    
    const runMode = process.env.RUN_MODE || 'dev';
    const dbName = runMode === 'prod' ? process.env.MONGODB_DB_PROD : process.env.MONGODB_DB_DEV;
    const mongoUri = `mongodb+srv://${process.env.MONGO_DB_USERNAME}:${process.env.MONGO_DB_PASSWORD}@${process.env.MONGODB_CLUSTER}/${dbName}?retryWrites=true&w=majority`;
    
    console.log(`Connecting to MongoDB (${runMode} mode)...`);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

async function testService() {
  await connectToDatabase();

  console.log('\n=== TESTING SEASON ACHIEVEMENT SERVICE ===');
  
  try {
    // Import container config to initialize dependencies
    require('../src/config/container.config');
    
    const seasonAchievementService = container.resolve<ISeasonAchievementService>("ISeasonAchievementService");
    
    console.log('\n--- Testing UTOPIA XI Team Achievements ---');
    const utopiaHistory = await seasonAchievementService.getTeamAchievementHistory('678907060ac8f44728a5e0dc');
    console.log('UTOPIA XI Achievement History:');
    console.log(JSON.stringify(utopiaHistory, null, 2));
    
    console.log('\n--- Testing Guns N Roses Team Achievements ---');
    const gunsHistory = await seasonAchievementService.getTeamAchievementHistory('66058b5119a6c5698f4ba74b');
    console.log('Guns N Roses Achievement History:');
    console.log(JSON.stringify(gunsHistory, null, 2));
    
    console.log('\n--- Testing Player Achievements ---');
    const playerHistory = await seasonAchievementService.getPlayerAchievementHistory('6654f375f9eac0fe69961bfd');
    console.log('Dor Ohayon Achievement History:');
    console.log(JSON.stringify(playerHistory, null, 2));
    
  } catch (error) {
    console.error('Service Test Error:', error);
  }

  await mongoose.disconnect();
  console.log('\nDatabase connection closed');
}

testService().catch(console.error);
