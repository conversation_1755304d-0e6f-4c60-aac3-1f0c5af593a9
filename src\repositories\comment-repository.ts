import { ClientSession, Types } from "mongoose";
import { injectable } from "tsyringe";
import logger from "../config/logger";
import { NotFoundError } from "../errors";
import { IComment, Comment } from "../models/comment/comment";

export interface CommentWithReplies extends IComment {
  replies?: IComment[];
  likesCount: number;
}

export interface ICommentRepository {
  createComment(gameId: Types.ObjectId, userId: Types.ObjectId, content: string, parentCommentId?: Types.ObjectId, session?: ClientSession): Promise<IComment>;
  updateComment(commentId: string, content: string, session?: ClientSession): Promise<IComment>;
  deleteComment(commentId: string, session?: ClientSession): Promise<void>;
  getCommentById(commentId: string, session?: ClientSession): Promise<IComment>;
  getCommentsByGame(gameId: string, page?: number, limit?: number): Promise<{ comments: CommentWithReplies[], totalCount: number, hasMore: boolean }>;
  likeComment(commentId: string, userId: string, session?: ClientSession): Promise<IComment>;
  unlikeComment(commentId: string, userId: string, session?: ClientSession): Promise<IComment>;
  getUserComments(userId: string, limit?: number): Promise<IComment[]>;
  getCommentReplies(parentCommentId: string): Promise<IComment[]>;
}

@injectable()
export class CommentRepository implements ICommentRepository {
  
  async createComment(
    gameId: Types.ObjectId,
    userId: Types.ObjectId,
    content: string,
    parentCommentId?: Types.ObjectId,
    session?: ClientSession
  ): Promise<IComment> {
    logger.info(`CommentRepository: creating comment for game ${gameId} by user ${userId}`);
    
    const comment = new Comment({
      gameId,
      userId,
      content,
      parentCommentId: parentCommentId || null
    });
    
    await comment.save({ session });
    return comment;
  }

  async updateComment(commentId: string, content: string, session?: ClientSession): Promise<IComment> {
    logger.info(`CommentRepository: updating comment ${commentId}`);
    
    const comment = await Comment.findByIdAndUpdate(
      commentId,
      { 
        content,
        isEdited: true
      },
      { new: true, session }
    );
    
    if (!comment) {
      throw new NotFoundError(`Comment with id ${commentId} not found`);
    }
    
    return comment;
  }

  async deleteComment(commentId: string, session?: ClientSession): Promise<void> {
    logger.info(`CommentRepository: deleting comment ${commentId}`);
    
    // Also delete all replies to this comment
    await Comment.deleteMany({ parentCommentId: commentId }, { session });
    
    const result = await Comment.findByIdAndDelete(commentId, { session });
    
    if (!result) {
      throw new NotFoundError(`Comment with id ${commentId} not found`);
    }
  }

  async getCommentById(commentId: string, session?: ClientSession): Promise<IComment> {
    logger.info(`CommentRepository: getting comment ${commentId}`);

    const comment = await Comment.findById(commentId, null, { session })
      .populate('userId', 'firstName lastName profilePicture')
      .populate('gameId');

    if (!comment) {
      throw new NotFoundError(`Comment with id ${commentId} not found`);
    }

    return comment;
  }

  async getCommentsByGame(
    gameId: string, 
    page: number = 1, 
    limit: number = 20
  ): Promise<{ comments: CommentWithReplies[], totalCount: number, hasMore: boolean }> {
    logger.info(`CommentRepository: getting comments for game ${gameId}, page ${page}`);
    
    const skip = (page - 1) * limit;
    
    // Get top-level comments (no parent)
    const comments = await Comment.find({
      gameId: new Types.ObjectId(gameId),
      parentCommentId: null
    })
    .populate('userId', 'firstName lastName profilePicture')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .lean();

    // Get replies for each comment
    const commentsWithReplies: CommentWithReplies[] = [];
    
    for (const comment of comments) {
      const replies = await Comment.find({
        parentCommentId: comment._id
      })
      .populate('userId', 'firstName lastName profilePicture')
      .sort({ createdAt: 1 })
      .lean();

      commentsWithReplies.push({
        ...comment,
        replies,
        likesCount: comment.likes?.length || 0
      } as any);
    }

    // Get total count for pagination
    const totalCount = await Comment.countDocuments({
      gameId: new Types.ObjectId(gameId),
      parentCommentId: null
    });

    const hasMore = skip + limit < totalCount;

    return {
      comments: commentsWithReplies,
      totalCount,
      hasMore
    };
  }

  async likeComment(commentId: string, userId: string, session?: ClientSession): Promise<IComment> {
    logger.info(`CommentRepository: user ${userId} liking comment ${commentId}`);
    
    const comment = await Comment.findByIdAndUpdate(
      commentId,
      { $addToSet: { likes: new Types.ObjectId(userId) } },
      { new: true, session }
    );
    
    if (!comment) {
      throw new NotFoundError(`Comment with id ${commentId} not found`);
    }
    
    return comment;
  }

  async unlikeComment(commentId: string, userId: string, session?: ClientSession): Promise<IComment> {
    logger.info(`CommentRepository: user ${userId} unliking comment ${commentId}`);
    
    const comment = await Comment.findByIdAndUpdate(
      commentId,
      { $pull: { likes: new Types.ObjectId(userId) } },
      { new: true, session }
    );
    
    if (!comment) {
      throw new NotFoundError(`Comment with id ${commentId} not found`);
    }
    
    return comment;
  }

  async getUserComments(userId: string, limit: number = 20): Promise<IComment[]> {
    logger.info(`CommentRepository: getting comments for user ${userId}`);
    
    const comments = await Comment.find({
      userId: new Types.ObjectId(userId)
    })
    .populate('gameId')
    .sort({ createdAt: -1 })
    .limit(limit);
    
    return comments;
  }

  async getCommentReplies(parentCommentId: string): Promise<IComment[]> {
    logger.info(`CommentRepository: getting replies for comment ${parentCommentId}`);
    
    const replies = await Comment.find({
      parentCommentId: new Types.ObjectId(parentCommentId)
    })
    .populate('userId', 'firstName lastName profilePicture')
    .sort({ createdAt: 1 });
    
    return replies;
  }
}
