/* === VERTICAL PITCH FORMATION COMPONENT === */

.vertical-pitch-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  background: var(--surface-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);
  overflow: hidden;

  // TOTW Mode - Larger and more prominent
  &.totw-mode {
    max-width: 1200px;

    .vertical-pitch {
      height: 900px;
      max-width: 800px;

      @media (max-width: 1200px) {
        height: 800px;
        max-width: 700px;
      }

      @media (max-width: 768px) {
        height: 700px;
        max-width: 90vw;
      }

      @media (max-width: 480px) {
        height: 600px;
        max-width: 95vw;
      }
    }

    .player-marker {
      .player-avatar {
        width: 80px;
        height: 80px;

        @media (max-width: 1200px) {
          width: 70px;
          height: 70px;
        }

        @media (max-width: 768px) {
          width: 55px;
          height: 55px;
        }

        @media (max-width: 480px) {
          width: 45px;
          height: 45px;
        }

        .rating-circle {
          width: 32px;
          height: 32px;
          font-size: 14px;
          font-weight: bold;
          top: -8px;
          right: -8px;
          border-width: 3px;

          @media (max-width: 1200px) {
            width: 28px;
            height: 28px;
            font-size: 12px;
            top: -6px;
            right: -6px;
          }

          @media (max-width: 768px) {
            width: 22px;
            height: 22px;
            font-size: 10px;
            top: -4px;
            right: -4px;
            border-width: 2px;
          }

          @media (max-width: 480px) {
            width: 18px;
            height: 18px;
            font-size: 8px;
            top: -3px;
            right: -3px;
          }
        }
      }

      .player-name {
        font-size: var(--text-base);
        font-weight: var(--font-weight-bold);
        margin-top: var(--spacing-sm);

        @media (max-width: 1200px) {
          font-size: var(--text-sm);
        }

        @media (max-width: 768px) {
          font-size: var(--text-xs);
        }
      }

      .player-stats {
        font-size: var(--text-sm);
        margin-top: var(--spacing-xs);

        @media (max-width: 1200px) {
          font-size: var(--text-xs);
        }

        @media (max-width: 768px) {
          font-size: 10px;
        }
      }
    }
  }

  &.compact {
    max-width: 600px;

    .vertical-pitch {
      height: 500px;
    }

    .player-marker {
      .player-avatar {
        width: 35px;
        height: 35px;

        @media (max-width: 768px) {
          width: 28px; // Even smaller in compact mobile mode
          height: 28px;
        }

        @media (max-width: 480px) {
          width: 24px; // Very small for compact small mobile
          height: 24px;
        }
      }
    }
  }
}

/* === TEAM TABS === */
.team-tabs {
  display: flex;
  width: 100%;
  background: var(--surface-secondary);
  border-bottom: 1px solid var(--border-primary);

  .team-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;

    &:hover {
      background: var(--surface-tertiary);
      color: var(--text-primary);
    }

    &.active {
      color: var(--text-primary);
      border-bottom-color: var(--primary-500);
      background: var(--surface-primary);
      box-shadow: inset 0 -3px 0 var(--primary-500);
    }

    .tab-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-xs);

      .team-name {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: inherit;
        margin: 0;
        text-align: center;
      }

      .formation-badge {
        font-size: var(--text-sm);
        font-weight: var(--font-weight-medium);
        color: var(--text-secondary);
        background: var(--surface-tertiary);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-full);
        border: 1px solid var(--border-primary);
        transition: all 0.2s ease;
      }
    }

    &.active .tab-content .formation-badge {
      background: var(--primary-100);
      color: var(--primary-700);
      border-color: var(--primary-300);
    }

    @media (max-width: 768px) {
      padding: var(--spacing-md);

      .tab-content {
        .team-name {
          font-size: var(--text-base);
        }

        .formation-badge {
          font-size: var(--text-xs);
          padding: 2px 8px;
        }
      }
    }
  }
}

/* === TOTW HEADER === */
.totw-header {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #000;
  padding: var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid #FFD700;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);

  .totw-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-lg);
    font-weight: var(--font-weight-bold);
    font-size: var(--text-2xl);

    i {
      font-size: var(--text-3xl);
      color: #000;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .team-name {
      font-size: var(--text-2xl);
      font-weight: var(--font-weight-bold);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .formation-badge {
      background: rgba(0, 0, 0, 0.2);
      color: #000;
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-lg);
      font-size: var(--text-lg);
      font-weight: var(--font-weight-bold);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
  }

  @media (max-width: 1200px) {
    padding: var(--spacing-lg);

    .totw-title {
      gap: var(--spacing-md);
      font-size: var(--text-xl);

      i {
        font-size: var(--text-2xl);
      }

      .team-name {
        font-size: var(--text-xl);
      }

      .formation-badge {
        font-size: var(--text-base);
        padding: var(--spacing-sm);
      }
    }
  }

  @media (max-width: 768px) {
    padding: var(--spacing-md);

    .totw-title {
      gap: var(--spacing-sm);
      font-size: var(--text-lg);

      i {
        font-size: var(--text-xl);
      }

      .team-name {
        font-size: var(--text-lg);
      }

      .formation-badge {
        font-size: var(--text-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
      }
    }
  }
}

/* === PITCH VIEW === */
.pitch-view {
  padding: var(--spacing-lg);
  background: var(--surface-primary);

  @media (max-width: 768px) {
    padding: var(--spacing-md) var(--spacing-sm); // Less padding on mobile
  }

  @media (max-width: 480px) {
    padding: var(--spacing-sm) var(--spacing-xs); // Minimal padding on small mobile
  }
}

/* === TEAM INFO === */
.team-info {
  text-align: center;
  margin-bottom: var(--spacing-lg);

  .team-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
  }

  .formation-display {
    font-size: var(--text-base);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }

  @media (max-width: 768px) {
    margin-bottom: var(--spacing-md);

    .team-title {
      font-size: var(--text-xl);
    }

    .formation-display {
      font-size: var(--text-sm);
    }
  }
}

/* === VERTICAL PITCH === */
.vertical-pitch {
  position: relative;
  width: 100%;
  max-width: 600px;
  height: 700px;
  margin: 0 auto;
  background: var(--surface-primary);

  @media (max-width: 768px) {
    height: 600px;
    max-width: 90vw; // Use viewport width for better mobile experience
    min-width: 320px; // Minimum width to prevent too small pitch
  }

  @media (max-width: 480px) {
    height: 550px;
    max-width: 95vw; // Even wider on small screens
    min-width: 300px;
  }
}

.pitch-background {
  position: relative;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(ellipse at center, #4a8c3a 0%, #2d5a27 70%),
    linear-gradient(45deg, #2d5a27 0%, #3d6b32 25%, #4a7c3f 50%, #3d6b32 75%, #2d5a27 100%);
  overflow: hidden;
  border-radius: var(--radius-xl);
  border: 3px solid #1a3d1a;
  box-shadow:
    inset 0 0 50px rgba(0, 0, 0, 0.3),
    0 8px 32px rgba(0, 0, 0, 0.4);

  // TOTW Mode - Black pitch with gold lines
  &.totw-mode {
    background:
      radial-gradient(ellipse at center, #1a1a1a 0%, #000000 70%),
      linear-gradient(45deg, #000000 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #000000 100%);
    border: 3px solid #FFD700;
    box-shadow:
      inset 0 0 50px rgba(255, 215, 0, 0.2),
      0 8px 32px rgba(255, 215, 0, 0.3);

    // TOTW grass texture with gold highlights
    &::before {
      background-image:
        // Gold grass blade pattern
        repeating-linear-gradient(
          0deg,
          transparent 0px,
          rgba(255, 215, 0, 0.05) 1px,
          transparent 2px,
          rgba(255, 215, 0, 0.02) 3px,
          transparent 4px
        ),
        // Cross pattern for realistic grass
        repeating-linear-gradient(
          90deg,
          transparent 0px,
          rgba(255, 215, 0, 0.03) 1px,
          transparent 2px,
          rgba(255, 215, 0, 0.01) 3px,
          transparent 4px
        ),
        // Subtle gold noise texture
        radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.03) 0%, transparent 50%);
    }

    // TOTW stadium lighting effect with gold
    &::after {
      background:
        radial-gradient(ellipse at 30% 20%, rgba(255, 215, 0, 0.15) 0%, transparent 40%),
        radial-gradient(ellipse at 70% 20%, rgba(255, 215, 0, 0.12) 0%, transparent 40%),
        radial-gradient(ellipse at 50% 80%, rgba(255, 215, 0, 0.08) 0%, transparent 30%);
    }
  }

  // Modern grass texture with multiple layers
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      // Grass blade pattern
      repeating-linear-gradient(
        0deg,
        transparent 0px,
        rgba(255, 255, 255, 0.03) 1px,
        transparent 2px,
        rgba(255, 255, 255, 0.01) 3px,
        transparent 4px
      ),
      // Cross pattern for realistic grass
      repeating-linear-gradient(
        90deg,
        transparent 0px,
        rgba(255, 255, 255, 0.02) 1px,
        transparent 2px,
        rgba(255, 255, 255, 0.01) 3px,
        transparent 4px
      ),
      // Subtle noise texture
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0.8;
  }

  // Add stadium lighting effect
  &::after {
    content: '';
    position: absolute;
    top: -20%;
    left: -20%;
    right: -20%;
    bottom: -20%;
    background:
      radial-gradient(ellipse at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 40%),
      radial-gradient(ellipse at 70% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 40%),
      radial-gradient(ellipse at 50% 80%, rgba(255, 255, 255, 0.06) 0%, transparent 30%);
    pointer-events: none;
    animation: stadiumLighting 8s ease-in-out infinite;
  }
}

@keyframes stadiumLighting {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
}

/* === PITCH LINES === */
.pitch-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;

  // TOTW Mode - Gold lines
  .totw-mode & {
    .goal-area,
    .penalty-area,
    .center-circle,
    .center-line,
    .corner-arc {
      border-color: #FFD700 !important;
    }
  }

  .goal-area {
    position: absolute;
    left: 35%;
    width: 30%;
    height: 8%;
    border: 3px solid rgba(255, 255, 255, 0.95);
    box-shadow:
      0 0 10px rgba(255, 255, 255, 0.3),
      inset 0 0 10px rgba(255, 255, 255, 0.1);

    &.top {
      top: 0;
      border-bottom: none;
      border-top-left-radius: var(--radius-md);
      border-top-right-radius: var(--radius-md);
    }

    &.bottom {
      bottom: 0;
      border-top: none;
      border-bottom-left-radius: var(--radius-md);
      border-bottom-right-radius: var(--radius-md);
    }
  }

  .penalty-area {
    position: absolute;
    left: 20%;
    width: 60%;
    height: 18%;
    border: 3px solid rgba(255, 255, 255, 0.85);
    box-shadow:
      0 0 8px rgba(255, 255, 255, 0.2),
      inset 0 0 8px rgba(255, 255, 255, 0.05);

    &.top {
      top: 0;
      border-bottom: none;
      border-top-left-radius: var(--radius-sm);
      border-top-right-radius: var(--radius-sm);
    }

    &.bottom {
      bottom: 0;
      border-top: none;
      border-bottom-left-radius: var(--radius-sm);
      border-bottom-right-radius: var(--radius-sm);
    }
  }

  .center-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    border: 3px solid rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow:
      0 0 15px rgba(255, 255, 255, 0.4),
      inset 0 0 15px rgba(255, 255, 255, 0.1);

    @media (max-width: 768px) {
      width: 80px;
      height: 80px;
    }

    @media (max-width: 480px) {
      width: 60px;
      height: 60px;
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 8px;
      height: 8px;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
    }
  }

  .center-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.9) 50%,
      transparent 100%);
    transform: translateY(-50%);
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  }

  .corner-arc {
    position: absolute;
    width: 24px;
    height: 24px;
    border: 3px solid rgba(255, 255, 255, 0.85);
    box-shadow: 0 0 6px rgba(255, 255, 255, 0.2);

    &.top-left {
      top: 0;
      left: 0;
      border-right: none;
      border-bottom: none;
      border-top-left-radius: 24px;
    }

    &.top-right {
      top: 0;
      right: 0;
      border-left: none;
      border-bottom: none;
      border-top-right-radius: 24px;
    }

    &.bottom-left {
      bottom: 0;
      left: 0;
      border-right: none;
      border-top: none;
      border-bottom-left-radius: 24px;
    }

    &.bottom-right {
      bottom: 0;
      right: 0;
      border-left: none;
      border-top: none;
      border-bottom-right-radius: 24px;
    }
  }
}

/* === PLAYER MARKERS === */
.team-players {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.player-marker {
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;

  &:hover {
    transform: translate(-50%, -50%) scale(1.15);
    z-index: 20;

    .player-avatar {
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(var(--primary-rgb), 0.6);
    }

    .player-info {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &.player-of-match {
    .player-avatar {
      box-shadow: 0 0 20px var(--warning-400), 0 2px 8px rgba(0, 0, 0, 0.3);
      border-color: var(--warning-400);
      animation: pulse-glow 2s infinite;
    }
  }

  // Single team styling with modern gradient
  .player-avatar {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border: 3px solid var(--primary-400);
    color: white;
    box-shadow:
      0 4px 12px rgba(var(--primary-rgb), 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px var(--warning-400), 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 30px var(--warning-400), 0 4px 12px rgba(0, 0, 0, 0.4);
  }
}

.player-avatar {
  width: 55px; // Desktop size
  height: 55px;
  border-radius: 50%;
  border: 3px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-xs);
  position: relative;
  overflow: visible; // Changed to visible for rating circle
  background: var(--surface-secondary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); // Added shadow for better visibility

  @media (max-width: 768px) {
    width: 38px; // Smaller for mobile
    height: 38px;
    border-width: 2px; // Thinner border on mobile
  }

  @media (max-width: 480px) {
    width: 32px; // Even smaller for small mobile
    height: 32px;
    border-width: 2px;
  }

  .player-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }

  .team-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    border: 2px solid var(--surface-primary);
    background: var(--surface-primary);
    overflow: hidden;
    z-index: 10;

    @media (max-width: 768px) {
      width: 14px;
      height: 14px;
      border-width: 1px;
    }

    @media (max-width: 480px) {
      width: 12px;
      height: 12px;
      border-width: 1px;
    }

    .team-badge-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
    }
  }

  .player-initials {
    font-size: var(--text-sm);
    font-weight: var(--font-weight-bold);
    display: none;

    &.show {
      display: flex;
    }

    @media (max-width: 480px) {
      font-size: var(--text-xs);
    }
  }

  .rating-circle {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    z-index: 10;

    @media (max-width: 768px) {
      width: 16px; // Smaller rating circle for mobile
      height: 16px;
      font-size: 8px;
      top: -2px;
      right: -2px;
      border-width: 1px; // Thinner border
    }

    @media (max-width: 480px) {
      width: 14px; // Even smaller for small mobile
      height: 14px;
      font-size: 7px;
      top: -2px;
      right: -2px;
    }
  }
}

.player-info {
  text-align: center;
  min-width: 70px;
  opacity: 0.9;
  transform: translateY(2px);
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    min-width: 50px; // Smaller min-width on mobile
  }

  @media (max-width: 480px) {
    min-width: 40px; // Even smaller on small mobile
  }

  .player-name {
    display: block;
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
    color: white;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9);
    margin-bottom: var(--spacing-xs);
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background: rgba(0, 0, 0, 0.6);
    padding: 2px 6px;
    border-radius: var(--radius-sm);

    @media (max-width: 768px) {
      font-size: 10px;
      max-width: 60px;
      padding: 1px 4px;
    }

    @media (max-width: 480px) {
      font-size: 9px;
      max-width: 50px;
      padding: 1px 3px;
    }
  }

  .player-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xs);
    flex-wrap: wrap;

    .stat {
      font-size: 10px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 3px 6px;
      border-radius: var(--radius-sm);
      white-space: nowrap;
      border: 1px solid rgba(255, 255, 255, 0.2);

      @media (max-width: 480px) {
        font-size: 9px;
        padding: 2px 4px;
      }
    }
  }
}

.potm-star {
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 16px;
  z-index: 21;
  filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.8));

  @media (max-width: 480px) {
    font-size: 14px;
    top: -6px;
    right: -6px;
  }
}

/* === LEGEND === */
.legend {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--surface-secondary);
  border-top: 1px solid var(--border-primary);

  @media (max-width: 768px) {
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
  }
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--text-sm);
  color: var(--text-secondary);

  .legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid;

    &.home {
      background: var(--primary);
      border-color: var(--primary);
    }

    &.away {
      background: var(--danger);
      border-color: var(--danger);
    }
  }

  .legend-icon {
    font-size: var(--text-base);
  }
}

/* === MOBILE FORMATION VIEW === */
.mobile-formation {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.mobile-team {
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  border: 1px solid var(--border-primary);

  &.home-team-mobile {
    border-left: 4px solid var(--primary);
  }

  &.away-team-mobile {
    border-left: 4px solid var(--danger);
  }
}

.team-header-mobile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-primary);

  h4 {
    margin: 0;
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
  }

  .formation-badge {
    background: var(--surface-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
  }
}

.players-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.player-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--surface-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background: var(--surface-tertiary);
    border-color: var(--border-primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.player-of-match {
    background: linear-gradient(135deg, var(--warning-50), var(--surface-primary));
    border-color: var(--warning-300);
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.3);
  }
}

.position-badge {
  min-width: 40px;
  height: 40px;
  background: var(--surface-tertiary);
  border: 2px solid var(--border-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  .position-text {
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
  }
}

.player-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: visible;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 2px solid var(--border-primary);
  }

  .rating-circle {
    position: absolute;
    top: -3px;
    right: -3px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 9px;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    z-index: 10;
  }
}

.player-details {
  flex: 1;
  min-width: 0;

  .player-name {
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .player-number {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }
}

.player-stats {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;

  .stat-item {
    display: flex;
    align-items: center;
    gap: 2px;
    font-size: var(--text-sm);
    color: var(--text-secondary);
    background: var(--surface-tertiary);
    padding: 2px 6px;
    border-radius: var(--radius-sm);

    i {
      font-size: 10px;
    }
  }
}

.potm-star {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  color: var(--warning-500);
  font-size: 16px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));

  i {
    animation: pulse-star 2s infinite;
  }
}

@keyframes pulse-star {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* === LEGEND === */
.legend {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);

  .legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--text-sm);
    color: var(--text-secondary);

    .legend-icon {
      font-size: var(--text-base);
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-sm);

    .legend-item {
      font-size: var(--text-xs);
    }
  }
}
