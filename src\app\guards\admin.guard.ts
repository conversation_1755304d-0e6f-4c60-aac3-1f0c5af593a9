import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {
  
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    return this.authService.currentUser$.pipe(
      take(1),
      map(user => {
        console.log('Admin Guard - User check:', { user: user, role: user?.role });
        if (user && user.role === 'admin') {
          console.log('Admin Guard - Access granted');
          return true;
        } else {
          console.log('Admin Guard - Access denied, redirecting to dashboard');
          this.router.navigate(['/dashboard']);
          return false;
        }
      })
    );
  }
}
