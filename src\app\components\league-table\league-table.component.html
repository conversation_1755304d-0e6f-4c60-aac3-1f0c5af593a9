<div class="league-table-container animate-fade-in" *ngIf="leagueTable && leagueTable.length > 0 && !isLoading">
    <!-- Header Section -->
    <div class="table-header animate-slide-in-down" *ngIf="!hideTitle">
        <div class="header-content">
            <h1 class="header-title">
                <i class="fas fa-trophy"></i>
                League Table
            </h1>
        </div>
        <div class="header-actions">
            <button class="refresh-btn"
                    (click)="refreshData()"
                    [disabled]="isLoading"
                    title="Refresh league table">
                <i class="fas fa-sync-alt" [class.spinning]="isLoading"></i>
                Refresh
            </button>
            <button class="force-refresh-btn"
                    *ngIf="isAdmin()"
                    (click)="forceRefreshFromServer()"
                    [disabled]="isLoading"
                    title="Force refresh from server (Admin only)">
                <i class="fas fa-redo-alt" [class.spinning]="isLoading"></i>
                Force Refresh
            </button>
        </div>
    </div>

    <!-- Top 3 Podium (Only for full view) -->
    <div class="podium-section" *ngIf="!hideTitle && leagueTable && leagueTable.length >= 3">
        <div class="podium-container">
            <!-- 2nd Place -->
            <div class="podium-team second-place" *ngIf="leagueTable[1]" (click)="onTeamClick(leagueTable[1])">
                <div class="podium-rank">2</div>
                <img class="podium-logo"
                     [src]="leagueTable[1].tableIcon?.imgUrl || 'assets/Icons/Team.jpg'"
                     [alt]="leagueTable[1].tableIcon?.name">
                <div class="podium-info">
                    <h3 class="podium-name">{{leagueTable[1].tableIcon?.name}}</h3>
                    <div class="podium-stats">
                        <span class="points-count">{{leagueTable[1].points}}</span>
                        <span class="points-label">Points</span>
                    </div>
                </div>
            </div>

            <!-- 1st Place -->
            <div class="podium-team first-place" *ngIf="leagueTable[0]" (click)="onTeamClick(leagueTable[0])">
                <div class="podium-rank">1</div>
                <div class="crown-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <img class="podium-logo"
                     [src]="leagueTable[0].tableIcon?.imgUrl || 'assets/Icons/Team.jpg'"
                     [alt]="leagueTable[0].tableIcon?.name">
                <div class="podium-info">
                    <h3 class="podium-name">{{leagueTable[0].tableIcon?.name}}</h3>
                    <div class="podium-stats">
                        <span class="points-count">{{leagueTable[0].points}}</span>
                        <span class="points-label">Points</span>
                    </div>
                </div>
            </div>

            <!-- 3rd Place -->
            <div class="podium-team third-place" *ngIf="leagueTable[2]" (click)="onTeamClick(leagueTable[2])">
                <div class="podium-rank">3</div>
                <img class="podium-logo"
                     [src]="leagueTable[2].tableIcon?.imgUrl || 'assets/Icons/Team.jpg'"
                     [alt]="leagueTable[2].tableIcon?.name">
                <div class="podium-info">
                    <h3 class="podium-name">{{leagueTable[2].tableIcon?.name}}</h3>
                    <div class="podium-stats">
                        <span class="points-count">{{leagueTable[2].points}}</span>
                        <span class="points-label">Points</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaderboard Table -->
    <div class="leaderboard-section">
        <div class="leaderboard-header" *ngIf="!hideTitle">
            <h2 class="leaderboard-title">
                <i class="fas fa-list-ol"></i>
                Complete Standings
            </h2>
        </div>

        <div class="leaderboard-table">
            <div class="table-header" *ngIf="hideTitle">
                <h3 class="table-title">League Table</h3>
            </div>

            <div class="table-content">
                <div class="table-row header-row" *ngIf="!hideTitle">
                    <div class="rank-cell">Rank</div>
                    <div class="team-cell">Team</div>
                    <div class="games-cell">GP</div>
                    <div class="wins-cell" [class.hide-mobile]="true">W</div>
                    <div class="draws-cell" [class.hide-mobile]="true">D</div>
                    <div class="losses-cell" [class.hide-mobile]="true">L</div>
                    <div class="gf-cell" [class.hide-mobile]="true">GF</div>
                    <div class="ga-cell" [class.hide-mobile]="true">GA</div>
                    <div class="gd-cell">GD</div>
                    <div class="points-cell">PTS</div>
                </div>

                <div class="table-row team-row"
                     *ngFor="let team of leagueTable; let i = index; trackBy: trackByTeamId"
                     (click)="onTeamClick(team)"
                     [class.top-three]="i < 3 && !hideTitle"
                     [class.playoff-zone]="i < 6"
                     [class.playin-zone]="i >= 6 && i <= 9">
                    <div class="rank-cell">
                        <span class="rank-number" [class.gold]="i === 0" [class.silver]="i === 1" [class.bronze]="i === 2">
                            {{i + 1}}
                        </span>
                    </div>
                    <div class="team-cell">
                        <img class="team-logo"
                             [src]="team.tableIcon?.imgUrl || 'assets/Icons/Team.jpg'"
                             [alt]="team.tableIcon?.name">
                        <div class="team-info">
                            <span class="team-name">{{team.tableIcon?.name}}</span>
                            <div class="team-form" *ngIf="team.lastForm && team.lastForm.length > 0">
                                <span *ngFor="let result of team.lastForm"
                                      class="form-indicator"
                                      [class.win]="result === 'W'"
                                      [class.draw]="result === 'D'"
                                      [class.loss]="result === 'L'"
                                      [title]="getFormResultTitle(result)">
                                    {{result}}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="games-cell">
                        <span class="games-number">{{team.gamesPlayed}}</span>
                    </div>
                    <div class="wins-cell" [class.hide-mobile]="true">
                        <span class="wins-number">{{team.gamesWon}}</span>
                    </div>
                    <div class="draws-cell" [class.hide-mobile]="true">
                        <span class="draws-number">{{team.draws}}</span>
                    </div>
                    <div class="losses-cell" [class.hide-mobile]="true">
                        <span class="losses-number">{{team.gamesLost}}</span>
                    </div>
                    <div class="gf-cell" [class.hide-mobile]="true">
                        <span class="gf-number">{{team.goalsScored}}</span>
                    </div>
                    <div class="ga-cell" [class.hide-mobile]="true">
                        <span class="ga-number">{{team.goalsConceded}}</span>
                    </div>
                    <div class="gd-cell">
                        <span class="gd-number"
                              [class.positive]="team.goalDifference > 0"
                              [class.negative]="team.goalDifference < 0">
                            {{team.goalDifference > 0 ? '+' : ''}}{{team.goalDifference}}
                        </span>
                    </div>
                    <div class="points-cell">
                        <span class="points-number">{{team.points}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="loading-container" *ngIf="isLoading">
    <pro-clubs-spinner></pro-clubs-spinner>
</div>

<!-- No Data State -->
<div class="no-data-container" *ngIf="!isLoading && (!leagueTable || leagueTable.length === 0)">
    <div class="no-data-card">
        <i class="fas fa-trophy no-data-icon"></i>
        <h3 class="no-data-title">No League Data</h3>
        <p class="no-data-message">League table data is currently unavailable.</p>
    </div>
</div>