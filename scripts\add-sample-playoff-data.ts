import mongoose from 'mongoose';
import { config } from 'dotenv';
import { GAME_STATUS } from '@pro-clubs-manager/shared-dtos';
import '../src/models/game/game';
import '../src/models/team';
import '../src/models/fixture';

// Load environment variables
config();

const PLAYOFF_STAGE = {
  PLAY_IN_ROUND: "Play-in Round",
  QUARTER_FINAL: "Quarter-Final",
  SEMI_FINAL: "Semi-Final",
  FINAL: "Final",
  THIRD_PLACE: "3rd Place Match",
  PROMOTION_PLAYOFF: "Promotion Playoff",
  RELEGATION_PLAYOFF: "Relegation Playoff"
};

// Use the same database configuration as the main app
const dbName = process.env.RUN_MODE === "prod" ? process.env.MONGODB_DB_PROD : process.env.MONGODB_DB_DEV;
const MONGODB_URI = `mongodb+srv://${process.env.MONGO_DB_USERNAME}:${process.env.MONGO_DB_PASSWORD}@${process.env.MONGODB_CLUSTER}/${dbName}?retryWrites=true&w=majority`;

const LEAGUE_ID = '6734b0b8e5b3b3b3b3b3b3b3'; // Default league ID from brackets component
const SEASON_NUMBER = 6; // Temporarily use season 7 to bypass cache

async function addSamplePlayoffData() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get models
    const Team = mongoose.model('Team');
    const Game = mongoose.model('Game');
    const Fixture = mongoose.model('Fixture');

    // Get actual teams from database
    const teams = await Team.find().limit(8);
    
    if (teams.length < 8) {
      console.log(`Only found ${teams.length} teams, need at least 8 for playoff bracket`);
      console.log('Available teams:', teams.map(t => t.name));
      return;
    }

    console.log('Using teams:', teams.map(t => t.name));

    // Check if playoff fixture already exists
    let playoffFixture = await Fixture.findOne({ 
      round: 100, 
      leagueId: LEAGUE_ID 
    });

    if (!playoffFixture) {
      // Create a playoff fixture
      playoffFixture = new Fixture({
        round: 100, // High number to distinguish from regular season
        leagueId: LEAGUE_ID,
        league: LEAGUE_ID, // Add league field
        seasonNumber: SEASON_NUMBER, // Add seasonNumber field
        startDate: new Date('2024-12-01'),
        endDate: new Date('2024-12-31')
      });
      await playoffFixture.save();
      console.log('Created playoff fixture');
    } else {
      console.log('Using existing playoff fixture');
    }

    // Remove existing playoff games for this season to avoid duplicates
    await Game.deleteMany({
      league: LEAGUE_ID,
      seasonNumber: SEASON_NUMBER,
      isPlayoff: true
    });
    console.log('Removed existing playoff games');

    const playoffGames = [];

    // Quarter-Finals (4 games)
    const quarterFinalGames = [
      { home: teams[0], away: teams[7], stage: PLAYOFF_STAGE.QUARTER_FINAL, round: 1 },
      { home: teams[1], away: teams[6], stage: PLAYOFF_STAGE.QUARTER_FINAL, round: 2 },
      { home: teams[2], away: teams[5], stage: PLAYOFF_STAGE.QUARTER_FINAL, round: 3 },
      { home: teams[3], away: teams[4], stage: PLAYOFF_STAGE.QUARTER_FINAL, round: 4 }
    ];

    // Semi-Finals (2 games) - winners from QF
    const semiFinalGames = [
      { home: teams[0], away: teams[1], stage: PLAYOFF_STAGE.SEMI_FINAL, round: 5 },
      { home: teams[2], away: teams[3], stage: PLAYOFF_STAGE.SEMI_FINAL, round: 6 }
    ];

    // Final and 3rd Place
    const finalGames = [
      { home: teams[0], away: teams[2], stage: PLAYOFF_STAGE.FINAL, round: 7 },
      { home: teams[1], away: teams[3], stage: PLAYOFF_STAGE.THIRD_PLACE, round: 8 }
    ];

    const allGames = [...quarterFinalGames, ...semiFinalGames, ...finalGames];

    for (let i = 0; i < allGames.length; i++) {
      const gameData = allGames[i];
      
      const game = new Game({
        fixture: playoffFixture._id,
        round: gameData.round,
        league: LEAGUE_ID,
        seasonNumber: SEASON_NUMBER,
        homeTeam: gameData.home._id,
        awayTeam: gameData.away._id,
        date: new Date(Date.now() + (i * 24 * 60 * 60 * 1000)), // Spread games over days
        status: GAME_STATUS.SCHEDULED,
        isPlayoff: true,
        playoffStage: gameData.stage
      });

      // Add some completed results for demonstration
      if (i < 6) { // Complete first 6 games (all QF and SF)
        game.status = GAME_STATUS.COMPLETED;
        game.result = {
          homeTeamGoals: Math.floor(Math.random() * 4) + 1,
          awayTeamGoals: Math.floor(Math.random() * 3)
        };
      }

      await game.save();
      playoffGames.push(game);
      
      console.log(`Created ${gameData.stage} game: ${gameData.home.name} vs ${gameData.away.name} (${game.status})`);
    }

    console.log(`\nSuccessfully created ${playoffGames.length} playoff games for Season ${SEASON_NUMBER}`);
    console.log('You can now view the brackets at http://localhost:4200/brackets');

  } catch (error) {
    console.error('Error adding sample playoff data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
addSamplePlayoffData();
