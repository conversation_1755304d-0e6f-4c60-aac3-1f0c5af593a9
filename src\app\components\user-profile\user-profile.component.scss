// === MAIN CONTAINER ===
.user-profile-container {
  margin: 0 auto;
  padding: var(--spacing-xl) 5px;
  background: var(--background-primary);
  background-image:
    radial-gradient(at 40% 20%, hsla(228, 100%, 74%, 0.03) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 0.03) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 0.03) 0px, transparent 50%);
  color: var(--text-primary);
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;

  @media (max-width: 768px) {
    padding: var(--spacing-md) 5px;
  }

  @media (max-width: 480px) {
    padding: var(--spacing-sm) 5px;
  }
}

// === HERO HEADER ===
.hero-header {
  background: var(--surface-primary);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  }

  .hero-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
      gap: var(--spacing-lg);
    }
  }

  .user-avatar {
    position: relative;
    flex-shrink: 0;

    .avatar-image {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      object-fit: cover;
      border: 4px solid var(--primary);
      box-shadow: var(--shadow-md);
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .avatar-overlay {
      position: absolute;
      bottom: 8px;
      right: 8px;
      width: 32px;
      height: 32px;
      background: var(--primary);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--primary-hover);
        transform: scale(1.1);
      }
    }
  }

  .user-info {
    flex: 1;

    .user-name {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 var(--spacing-sm) 0;
      background: linear-gradient(135deg, var(--primary), var(--accent-primary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .user-email {
      font-size: 1.1rem;
      color: var(--text-secondary);
      margin: 0 0 var(--spacing-lg) 0;
    }

    .user-stats {
      display: flex;
      gap: var(--spacing-xl);

      @media (max-width: 768px) {
        justify-content: center;
      }

      .stat-item {
        text-align: center;

        .stat-number {
          display: block;
          font-size: 2rem;
          font-weight: 700;
          color: var(--primary);
          line-height: 1;
        }

        .stat-label {
          font-size: 0.9rem;
          color: var(--text-secondary);
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
      }
    }
  }

  .hero-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;

    @media (max-width: 768px) {
      justify-content: center;
      flex-wrap: wrap;
    }

    @media (max-width: 480px) {
      flex-direction: column;
      width: 100%;

      .btn {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

// === SECTION CARDS ===
.section-card {
  background: var(--surface-primary);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  }

  .card-header {
    margin-bottom: var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: var(--spacing-md);

    h2 {
      margin: 0;
      font-size: 1.75rem;
      font-weight: 700;
      background: linear-gradient(135deg, var(--primary), var(--accent-primary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      i {
        color: var(--primary);
        -webkit-text-fill-color: var(--primary);
      }
    }

    .card-subtitle {
      color: var(--text-secondary);
      margin: var(--spacing-xs) 0 0 0;
      font-size: 0.95rem;
    }

    .btn-close {
      background: var(--surface-secondary);
      border: 1px solid var(--border-primary);
      color: var(--text-secondary);
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--danger);
        color: white;
        border-color: var(--danger);
      }
    }

    .player-limit-info {
      display: flex;
      align-items: center;

      .limit-badge {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
        background: rgba(var(--primary-rgb), 0.1);
        color: var(--primary);
        border: 1px solid rgba(var(--primary-rgb), 0.2);
        border-radius: var(--radius-full);
        font-size: 0.8rem;
        font-weight: 600;

        i {
          font-size: 0.75rem;
        }
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: var(--spacing-md);

      .player-limit-info {
        justify-content: center;
      }
    }
  }
}

// === BUTTONS ===
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &.btn-primary {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow-md);

    &:hover:not(:disabled) {
      background: var(--primary-hover);
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
  }

  &.btn-secondary {
    background: var(--surface-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);

    &:hover:not(:disabled) {
      background: var(--surface-hover);
      border-color: var(--primary);
    }
  }

  &.btn-success {
    background: var(--success);
    color: white;
    box-shadow: var(--shadow-md);

    &:hover:not(:disabled) {
      background: var(--success-hover);
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
  }

  &.btn-danger {
    background: var(--danger);
    color: white;
    box-shadow: var(--shadow-md);

    &:hover:not(:disabled) {
      background: var(--danger-hover);
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
  }

  &.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
  }

  &.btn-block {
    width: 100%;
    justify-content: center;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }

  i.fa-spinner {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 0.8;
  }
}

// === FORMS ===
.profile-form {
  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .full-width {
      grid-column: 1 / -1;
    }
  }

  .form-group {
    margin-bottom: var(--spacing-lg);

    label {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      margin-bottom: var(--spacing-sm);
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.95rem;

      i {
        color: var(--primary);
        width: 16px;
      }
    }

    .form-input {
      width: 100%;
      padding: var(--spacing-md);
      border: 2px solid var(--border-primary);
      border-radius: var(--radius-md);
      background: var(--surface-secondary);
      color: var(--text-primary);
      font-size: 1rem;
      transition: all 0.3s ease;

      &:focus {
        outline: none;
        border-color: var(--primary);
        background: var(--surface-primary);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
      }

      &.error {
        border-color: var(--danger);
        background: rgba(var(--danger-rgb), 0.05);
      }

      &::placeholder {
        color: var(--text-tertiary);
      }
    }

    .error-message {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      color: var(--danger);
      font-size: 0.875rem;
      margin-top: var(--spacing-xs);

      i {
        font-size: 0.75rem;
      }
    }
  }

  .form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);

    @media (max-width: 768px) {
      flex-direction: column;

      .btn {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

// === EMPTY STATE ===
.empty-state {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-xl);
  color: var(--text-secondary);

  .empty-icon {
    font-size: 4rem;
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-lg);
    opacity: 0.6;
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
  }

  p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0 0 var(--spacing-lg) 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  .limit-notice {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: rgba(var(--warning-rgb), 0.1);
    border: 1px solid rgba(var(--warning-rgb), 0.2);
    border-radius: var(--radius-md);
    color: var(--warning);
    font-size: 0.95rem;
    margin-bottom: var(--spacing-xl);

    i {
      font-size: 1rem;
    }

    strong {
      color: var(--warning);
    }
  }
}

// === SINGLE PLAYER CONTAINER ===
.single-player-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);

  .player-card {
    max-width: 500px;
    width: 100%;
  }
}

// === PLAYERS GRID (for search results) ===
.players-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-lg);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

// === PLAYER CARDS ===
.player-card {
  background: var(--surface-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  }

  &.search-result {
    border: 2px dashed var(--border-primary);

    &:hover {
      border-color: var(--success);
      border-style: solid;
    }
  }

  &.featured-player {
    border: 2px solid var(--primary);
    background: linear-gradient(135deg, var(--surface-secondary), var(--surface-primary));
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.05), transparent);
      pointer-events: none;
      border-radius: var(--radius-lg);
    }

    &:hover {
      border-color: var(--primary);
      box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.1), var(--shadow-xl);
    }
  }

  .player-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    position: relative;

    .player-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid var(--primary);
      box-shadow: var(--shadow-md);
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .player-badge {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-full);
      font-size: 0.75rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.05em;

      &.position-gk {
        background: rgba(255, 193, 7, 0.2);
        color: #ffc107;
        border: 1px solid rgba(255, 193, 7, 0.3);
      }

      &.position-def {
        background: rgba(40, 167, 69, 0.2);
        color: #28a745;
        border: 1px solid rgba(40, 167, 69, 0.3);
      }

      &.position-mid {
        background: rgba(0, 123, 255, 0.2);
        color: #007bff;
        border: 1px solid rgba(0, 123, 255, 0.3);
      }

      &.position-att {
        background: rgba(220, 53, 69, 0.2);
        color: #dc3545;
        border: 1px solid rgba(220, 53, 69, 0.3);
      }
    }

    .featured-badge {
      position: absolute;
      top: -8px;
      right: -8px;
      background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
      color: white;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-full);
      font-size: 0.7rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      box-shadow: var(--shadow-md);
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      border: 1px solid rgba(255, 255, 255, 0.2);

      i {
        font-size: 0.6rem;
        animation: sparkle 2s ease-in-out infinite;
      }

      /* Ensure good contrast in both themes */
      [data-theme="light"] & {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        color: white;
      }

      [data-theme="dark"] & {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        color: white;
      }
    }
  }

  .player-details {
    margin-bottom: var(--spacing-lg);

    .player-name {
      margin: 0 0 var(--spacing-sm) 0;
      color: var(--text-primary);
      font-size: 1.25rem;
      font-weight: 600;
    }

    .player-meta {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);

      .meta-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--text-secondary);
        font-size: 0.9rem;

        i {
          width: 16px;
          color: var(--primary);
        }

        &.free-agent {
          color: var(--warning);
        }
      }
    }
  }

  .player-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;

    .btn {
      flex: 1;
      min-width: 120px;
    }

    @media (max-width: 768px) {
      flex-direction: column;

      .btn {
        width: 100%;
        min-width: auto;
        justify-content: center;
      }
    }
  }
}

// === SEARCH SECTION ===
.search-section {
  animation: slideInFromTop 0.5s ease-out;
}

.search-results {
  animation: slideInFromBottom 0.4s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-form {
  .search-methods {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
  }

  .search-method {
    .method-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);

      i {
        font-size: 1.5rem;
        color: var(--primary);
      }

      h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
      }
    }

    .input-group {
      display: flex;
      align-items: stretch;
      border-radius: var(--radius-md);
      overflow: hidden;
      border: 2px solid var(--border-primary);
      transition: border-color 0.3s ease;

      &:focus-within {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
      }

      .input-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        background: var(--surface-secondary);
        color: var(--text-secondary);
        border-right: 1px solid var(--border-primary);
      }

      .form-input {
        flex: 1;
        border: none;
        border-radius: 0;
        background: var(--surface-primary);

        &:focus {
          box-shadow: none;
          border: none;
        }
      }

      .btn {
        border-radius: 0;
        border-left: 1px solid var(--border-primary);
      }

      @media (max-width: 768px) {
        flex-direction: column;
        border-radius: var(--radius-md);

        .input-icon {
          border-right: none;
          border-bottom: 1px solid var(--border-primary);
        }

        .btn {
          border-left: none;
          border-top: 1px solid var(--border-primary);
          border-radius: 0;
          width: 100%;
          justify-content: center;
        }
      }
    }
  }

  .divider {
    text-align: center;
    position: relative;
    margin: var(--spacing-2xl) 0;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: var(--border-primary);
    }

    span {
      background: var(--surface-primary);
      padding: 0 var(--spacing-lg);
      color: var(--text-secondary);
      font-weight: 600;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
}

.search-results {
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-primary);

  .results-header {
    margin-bottom: var(--spacing-lg);

    h3 {
      margin: 0 0 var(--spacing-sm) 0;
      color: var(--text-primary);
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      i {
        color: var(--primary);
      }
    }

    p {
      margin: 0;
      color: var(--text-secondary);
      font-size: 0.95rem;
    }
  }
}

// === COMPREHENSIVE MOBILE STYLES ===
@media (max-width: 768px) {
  .user-profile-container {
    padding: var(--spacing-md) 5px;
  }

  .section-card {
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
  }

  .hero-header {
    padding: var(--spacing-lg);

    .user-info {
      .user-name {
        font-size: 2rem;
      }

      .user-stats {
        gap: var(--spacing-lg);
      }
    }
  }

  .single-player-container {
    .player-card {
      max-width: none;
    }
  }

  .players-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .empty-state {
    padding: var(--spacing-2xl) var(--spacing-md);

    .empty-icon {
      font-size: 3rem;
    }

    h3 {
      font-size: 1.25rem;
    }

    p {
      font-size: 1rem;
    }
  }
}

@media (max-width: 480px) {
  .user-profile-container {
    padding: var(--spacing-sm) 5px;
  }

  .section-card {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
  }

  .hero-header {
    padding: var(--spacing-md);

    .user-info {
      .user-name {
        font-size: 1.75rem;
      }

      .user-stats {
        flex-direction: column;
        gap: var(--spacing-md);

        .stat-item {
          .stat-number {
            font-size: 1.5rem;
          }
        }
      }
    }
  }

  .player-card {
    padding: var(--spacing-lg);

    .player-header {
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: var(--spacing-md);

      .featured-badge {
        position: static;
        margin-top: var(--spacing-sm);
      }
    }

    .player-details {
      text-align: center;

      .player-meta {
        justify-content: center;
      }
    }
  }

  .request-card {
    padding: var(--spacing-md);
  }
}

// === ASSOCIATION REQUESTS ===
.requests-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.request-card {
  background: var(--surface-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  }
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-lg);

  .player-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;

    .request-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid var(--primary);
      box-shadow: var(--shadow-sm);
    }

    .player-details {
      flex: 1;

      h3 {
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
        font-weight: 600;
        font-size: 1.1rem;
      }

      .player-meta {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        flex-wrap: wrap;

        .position-badge {
          padding: var(--spacing-xs) var(--spacing-sm);
          background: var(--primary);
          color: white;
          border-radius: var(--radius-full);
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
        }

        .team-name {
          color: var(--text-secondary);
          font-size: 0.9rem;

          &.free-agent {
            color: var(--warning);
            font-style: italic;
          }
        }
      }
    }
  }

  .request-status {
    text-align: right;
    flex-shrink: 0;

    .status-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-xs);
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-full);
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;

      &.status-pending {
        background: rgba(59, 130, 246, 0.15);
        color: #3b82f6;
        border: 1px solid rgba(59, 130, 246, 0.3);
      }

      &.status-approved {
        background: rgba(16, 185, 129, 0.15);
        color: #10b981;
        border: 1px solid rgba(16, 185, 129, 0.3);
      }

      &.status-rejected {
        background: rgba(239, 68, 68, 0.15);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.3);
      }
    }

    .request-date {
      color: var(--text-tertiary);
      font-size: 0.8rem;
      margin: var(--spacing-sm) 0 0 0;
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);

    .player-info {
      .player-details {
        .player-meta {
          flex-direction: column;
          align-items: flex-start;
          gap: var(--spacing-xs);
        }
      }
    }

    .request-status {
      text-align: left;

      .status-badge {
        align-self: flex-start;
      }
    }
  }
}

.request-message {
  background: var(--surface-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);

  .message-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: var(--primary);
    font-weight: 600;
    font-size: 0.9rem;
  }

  p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
    line-height: 1.6;
    font-style: italic;
  }
}

.request-reason {
  background: rgba(239, 68, 68, 0.08);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);

  .reason-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: #ef4444;
    font-weight: 600;
    font-size: 0.9rem;
  }

  p {
    color: #ef4444;
    margin: 0;
    font-size: 0.95rem;
    line-height: 1.6;
  }
}

.request-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-secondary);
}
