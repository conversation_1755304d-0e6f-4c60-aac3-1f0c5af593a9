<div class="cms-table-container">
    <div class="table-header">
        <h3>
            <i class="fas fa-shield-alt"></i>
            Teams Management
        </h3>
        <div class="table-actions">
            <button class="action-btn create-btn" routerLink="/create-team">
                <i class="fas fa-plus-circle"></i>
                Add Team
            </button>
        </div>
    </div>

    <div class="table-wrapper">
        <table class="cms-table">
            <thead>
                <tr>
                    <th>Team</th>
                    <th>Captain</th>
                    <th>Players</th>
                    <th>League</th>
                    <th>Stats</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let team of teams" class="table-row">
                    <td class="team-cell">
                        <div class="team-info">
                            <img [src]="team.imgUrl || 'assets/Icons/Team.png'" 
                                 [alt]="team.name"
                                 (error)="onImageError($event)"
                                 class="team-logo">
                            <div class="team-details">
                                <span class="team-name">{{ team.name }}</span>
                                <span class="team-id">ID: {{ team.id }}</span>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="captain-name" [class.no-captain]="!team.captain">
                            {{ getTeamCaptainName(team) }}
                        </span>
                    </td>
                    <td>
                        <div class="player-count">
                            <i class="fas fa-users"></i>
                            <span>{{ getTeamPlayerCount(team) }}</span>
                        </div>
                    </td>
                    <td>
                        <span class="league-name" [class.no-league]="!team.leagueId">
                            {{ getTeamLeague(team) }}
                        </span>
                    </td>
                    <td>
                        <span class="stats-text">{{ getTeamStats(team) }}</span>
                    </td>
                    <td class="actions-cell">
                        <div class="action-buttons">
                            <button class="action-btn view-btn" 
                                    (click)="viewTeamDetails(team.id)"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn edit-btn" 
                                    (click)="editTeam(team.id)"
                                    title="Edit Team">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-btn" 
                                    (click)="deleteTeam(team)"
                                    [disabled]="isTeamDeleting(team.id)"
                                    title="Delete Team">
                                <i class="fas fa-trash" *ngIf="!isTeamDeleting(team.id)"></i>
                                <i class="fas fa-spinner fa-spin" *ngIf="isTeamDeleting(team.id)"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="teams.length === 0">
            <div class="empty-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h4>No Teams Found</h4>
            <p>No teams match your current search criteria.</p>
            <button class="action-btn create-btn" routerLink="/create-team">
                <i class="fas fa-plus-circle"></i>
                Create First Team
            </button>
        </div>
    </div>
</div>
