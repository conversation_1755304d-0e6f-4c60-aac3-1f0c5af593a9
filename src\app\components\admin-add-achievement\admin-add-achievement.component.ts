import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { PlayerService } from '../../services/player.service';
import { SeasonAchievementService, ACHIEVEMENT_TYPE } from '../../services/season-achievement.service';
import { PlayerDTO } from '@pro-clubs-manager/shared-dtos';

@Component({
  selector: 'app-admin-add-achievement',
  templateUrl: './admin-add-achievement.component.html',
  styleUrls: ['./admin-add-achievement.component.scss']
})
export class AdminAddAchievementComponent implements OnInit {
  achievementForm: FormGroup;
  players: PlayerDTO[] = [];
  filteredPlayers: PlayerDTO[] = [];
  achievementTypes = Object.values(ACHIEVEMENT_TYPE);
  isLoading = false;
  isSubmitting = false;
  searchTerm = '';

  constructor(
    private fb: FormBuilder,
    private playerService: PlayerService,
    private seasonAchievementService: SeasonAchievementService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {
    this.achievementForm = this.fb.group({
      playerId: ['', Validators.required],
      seasonNumber: ['', [Validators.required, Validators.min(1)]],
      achievementType: ['', Validators.required],
      rank: [''],
      goals: [''],
      assists: [''],
      cleanSheets: [''],
      avgRating: [''],
      games: [''],
      playerOfTheMatch: [''],
      description: ['']
    });
  }

  async ngOnInit() {
    console.log('AdminAddAchievementComponent initialized');
    await this.loadPlayers();
  }

  async loadPlayers() {
    try {
      this.isLoading = true;
      this.players = await this.playerService.getAllPlayers();
      this.filteredPlayers = [...this.players];
    } catch (error) {
      console.error('Error loading players:', error);
      this.snackBar.open('Error loading players', 'Close', { duration: 3000 });
    } finally {
      this.isLoading = false;
    }
  }

  onSearchChange() {
    if (!this.searchTerm.trim()) {
      this.filteredPlayers = [...this.players];
      return;
    }

    const searchLower = this.searchTerm.toLowerCase();
    this.filteredPlayers = this.players.filter(player =>
      player.name.toLowerCase().includes(searchLower)
    );
  }

  onPlayerSelect(player: PlayerDTO) {
    this.achievementForm.patchValue({ playerId: player.id });
    this.searchTerm = player.name;
    this.filteredPlayers = [];
  }

  clearPlayerSelection() {
    this.achievementForm.patchValue({ playerId: '' });
    this.searchTerm = '';
    this.filteredPlayers = [...this.players];
  }

  getSelectedPlayer(): PlayerDTO | null {
    const playerId = this.achievementForm.get('playerId')?.value;
    return this.players.find(p => p.id === playerId) || null;
  }

  isStatsRelevant(achievementType: string): boolean {
    const statsRelevantTypes = [
      ACHIEVEMENT_TYPE.TOP_SCORER,
      ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER,
      ACHIEVEMENT_TYPE.BEST_GOALKEEPER,
      ACHIEVEMENT_TYPE.MOST_CLEAN_SHEETS
    ];
    return statsRelevantTypes.includes(achievementType as ACHIEVEMENT_TYPE);
  }

  async onSubmit() {
    if (this.achievementForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    try {
      this.isSubmitting = true;
      const formValue = this.achievementForm.value;

      const achievementData = {
        playerId: formValue.playerId,
        seasonNumber: formValue.seasonNumber,
        achievementType: formValue.achievementType,
        rank: formValue.rank || undefined,
        stats: {
          goals: formValue.goals || undefined,
          assists: formValue.assists || undefined,
          cleanSheets: formValue.cleanSheets || undefined,
          avgRating: formValue.avgRating || undefined,
          games: formValue.games || undefined,
          playerOfTheMatch: formValue.playerOfTheMatch || undefined
        },
        description: formValue.description || undefined
      };

      // Remove empty stats
      Object.keys(achievementData.stats).forEach(key => {
        if (achievementData.stats[key as keyof typeof achievementData.stats] === undefined) {
          delete achievementData.stats[key as keyof typeof achievementData.stats];
        }
      });

      const result = await this.seasonAchievementService.addManualPlayerAchievement(achievementData);

      this.snackBar.open(result.message, 'Close', { 
        duration: 5000,
        panelClass: ['success-snackbar']
      });

      // Reset form
      this.achievementForm.reset();
      this.clearPlayerSelection();

    } catch (error: any) {
      console.error('Error adding achievement:', error);
      const errorMessage = error.error?.message || error.message || 'Error adding achievement';
      this.snackBar.open(errorMessage, 'Close', { 
        duration: 5000,
        panelClass: ['error-snackbar']
      });
    } finally {
      this.isSubmitting = false;
    }
  }

  private markFormGroupTouched() {
    Object.keys(this.achievementForm.controls).forEach(key => {
      this.achievementForm.get(key)?.markAsTouched();
    });
  }

  goBack() {
    this.router.navigate(['/admin/cms-dashboard']);
  }
}
