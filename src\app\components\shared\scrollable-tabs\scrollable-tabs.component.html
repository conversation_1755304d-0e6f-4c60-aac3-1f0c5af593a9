<!-- Scrollable Tabs Component -->
<div class="scrollable-tabs" [class]="'variant-' + variant">
    <!-- Left Arrow -->
    <button 
        class="scroll-arrow left-arrow"
        [class.visible]="showLeftArrow && showArrows"
        (click)="scrollLeft()"
        [disabled]="!showLeftArrow"
        type="button"
        aria-label="Scroll tabs left">
        <i class="fas fa-chevron-left"></i>
    </button>

    <!-- Tabs Container -->
    <div class="tabs-container" #tabsContainer>
        <div class="tabs-list" #tabsList>
            <button
                *ngFor="let tab of tabs; trackBy: trackByTabId"
                [class]="getTabClasses(tab)"
                [attr.data-tab-id]="tab.id"
                (click)="onTabClick(tab)"
                [disabled]="tab.disabled"
                type="button"
                [attr.aria-selected]="tab.id === activeTabId"
                role="tab">
                
                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Icon -->
                    <i *ngIf="tab.icon" [class]="tab.icon" class="tab-icon"></i>
                    
                    <!-- Label -->
                    <span class="tab-label">{{ tab.label }}</span>
                    
                    <!-- Count Badge -->
                    <span 
                        *ngIf="showCounts && tab.count !== undefined && tab.count !== null" 
                        class="tab-count">
                        {{ tab.count }}
                    </span>
                </div>

                <!-- Active Indicator -->
                <div class="active-indicator" *ngIf="tab.id === activeTabId"></div>
            </button>
        </div>
    </div>

    <!-- Right Arrow -->
    <button 
        class="scroll-arrow right-arrow"
        [class.visible]="showRightArrow && showArrows"
        (click)="scrollRight()"
        [disabled]="!showRightArrow"
        type="button"
        aria-label="Scroll tabs right">
        <i class="fas fa-chevron-right"></i>
    </button>
</div>
