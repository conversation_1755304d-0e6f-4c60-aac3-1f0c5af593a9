/* === MODERN TEAM HISTORY DESIGN === */

.team-history-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    min-height: 100vh;
    font-family: var(--font-sans);

    @media (max-width: 768px) {
        padding: var(--spacing-md);
        gap: var(--spacing-lg);
    }
}

.history-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);

    .history-title {
        font-size: var(--text-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);

        i {
            color: var(--warning-500);
        }

        @media (max-width: 768px) {
            font-size: var(--text-2xl);
        }
    }

    .history-subtitle {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
        font-weight: var(--font-weight-medium);
    }
}

/* === ACHIEVEMENTS SECTION === */
.achievements-section {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    overflow: hidden;

    .section-header {
        margin-bottom: var(--spacing-lg);

        .section-title {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            i {
                color: var(--warning-500);
            }
        }
    }

    .achievements-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
    }

    .achievement-card {
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-xl);
        padding: var(--spacing-lg);
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-md);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            transition: all 0.3s ease;
        }

        &:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--border-secondary);
        }

        &.championship {
            &::before {
                background: linear-gradient(180deg, var(--warning-400), var(--warning-600));
            }

            .achievement-icon {
                background: linear-gradient(135deg, var(--warning-400), var(--warning-600));
            }

            .achievement-badge {
                background: linear-gradient(135deg, var(--warning-50), var(--warning-100));
                color: var(--warning-700);
                border-color: var(--warning-200);
            }
        }

        &.placement {
            &::before {
                background: linear-gradient(180deg, var(--info-400), var(--info-600));
            }

            .achievement-icon {
                background: linear-gradient(135deg, var(--info-400), var(--info-600));
            }

            .achievement-badge {
                background: linear-gradient(135deg, var(--info-50), var(--info-100));
                color: var(--info-700);
                border-color: var(--info-200);
            }
        }

        .achievement-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-inverse);
            font-size: var(--text-lg);
            flex-shrink: 0;
            box-shadow: var(--shadow-md);
        }

        .achievement-content {
            flex: 1;

            .achievement-title {
                font-size: var(--text-lg);
                font-weight: var(--font-weight-semibold);
                color: var(--text-primary);
                margin: 0 0 var(--spacing-xs) 0;
            }

            .achievement-description {
                font-size: var(--text-sm);
                color: var(--text-secondary);
                margin: 0 0 var(--spacing-sm) 0;
                line-height: 1.5;
            }

            .achievement-badge {
                display: inline-flex;
                align-items: center;
                gap: var(--spacing-xs);
                padding: var(--spacing-xs) var(--spacing-sm);
                border-radius: var(--radius-md);
                font-size: var(--text-xs);
                font-weight: var(--font-weight-semibold);
                border: 1px solid;

                i {
                    font-size: var(--text-xs);
                }
            }
        }
    }
}

/* === TIMELINE SECTION === */
.timeline-section {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    overflow: hidden;

    .section-header {
        margin-bottom: var(--spacing-lg);

        .section-title {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            i {
                color: var(--primary);
            }
        }
    }

    .timeline {
        position: relative;
        padding-left: var(--spacing-xl);

        &::before {
            content: '';
            position: absolute;
            left: 16px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(180deg, var(--border-primary), var(--border-secondary));
        }

        .timeline-item {
            position: relative;
            padding-bottom: var(--spacing-xl);

            &:last-child {
                padding-bottom: 0;
            }

            .timeline-marker {
                position: absolute;
                left: -24px;
                top: 8px;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                border: 3px solid var(--surface-primary);
                box-shadow: var(--shadow-sm);
                z-index: 2;

                &.championship-marker {
                    background: linear-gradient(135deg, var(--warning-400), var(--warning-600));
                }

                &.placement-marker {
                    background: linear-gradient(135deg, var(--info-400), var(--info-600));
                }
            }

            .timeline-content {
                background: var(--surface-secondary);
                border: 1px solid var(--border-primary);
                border-radius: var(--radius-lg);
                padding: var(--spacing-lg);
                transition: all 0.3s ease;

                &:hover {
                    border-color: var(--border-secondary);
                    transform: translateX(4px);
                    box-shadow: var(--shadow-md);
                }

                .timeline-date {
                    font-size: var(--text-xs);
                    font-weight: var(--font-weight-semibold);
                    color: var(--primary);
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    margin-bottom: var(--spacing-xs);
                }

                .timeline-title {
                    font-size: var(--text-base);
                    font-weight: var(--font-weight-semibold);
                    color: var(--text-primary);
                    margin-bottom: var(--spacing-xs);
                }

                .timeline-description {
                    font-size: var(--text-sm);
                    color: var(--text-secondary);
                    line-height: 1.5;
                    margin: 0;
                }
            }
        }
    }
}