import { Column, DataType } from "../../shared/models/column.model";

export const TOP_AVG_RATING_BY_POSITION_COLUMNS: Column[] = [
    {
        fieldName: 'index',
        displayText: '#'
    },
    {
        fieldName: 'tableIcon',
        displayText: 'Player Name',
        dataType: DataType.TEXT_WITH_ICON
    },
    {
        fieldName: 'position',
        displayText: 'Position',
        hideInMobile: true
    },
    {
        fieldName: 'teamName',
        displayText: 'Team Name',
        hideInMobile: true
    },
    {
        fieldName: 'games',
        displayText: 'Games'
    },
    {
        fieldName: 'goals',
        displayText: 'Goals',
        hideInMobile: true
    },
    {
        fieldName: 'assists',
        displayText: 'Assists',
        hideInMobile: true
    },
    {
        fieldName: 'cleanSheets',
        displayText: 'Clean Sheets',
        hideInMobile: true
    },
    {
        fieldName: 'avgRating',
        displayText: 'AVG'
    }
];