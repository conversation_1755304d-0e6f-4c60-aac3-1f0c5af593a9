import { connect, disconnect, Types } from 'mongoose';
import Player from '../src/models/player/player';
import Game from '../src/models/game/game';

// Simple logger for the script
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args)
};

interface SeasonStats {
  seasonNumber: number;
  leagueId: string;
  totalGames: number;
  totalGoals: number;
  totalAssists: number;
  totalCleanSheets: number;
  totalPotm: number;
  avgRating: number;
}

async function calculatePlayerSeasonStats(playerId: string): Promise<SeasonStats[]> {
  const playerGames = await Game.aggregate([
    {
      $match: {
        $or: [
          { "homeTeamPlayersPerformance.playerId": new Types.ObjectId(playerId) },
          { "awayTeamPlayersPerformance.playerId": new Types.ObjectId(playerId) }
        ]
      }
    },
    {
      $project: {
        league: 1,
        seasonNumber: 1,
        players: {
          $concatArrays: [
            { $ifNull: ["$homeTeamPlayersPerformance", []] },
            { $ifNull: ["$awayTeamPlayersPerformance", []] }
          ]
        }
      }
    },
    { $unwind: "$players" },
    { $match: { "players.playerId": new Types.ObjectId(playerId) } },
    {
      $group: {
        _id: { league: "$league", seasonNumber: "$seasonNumber" },
        totalGames: { $sum: 1 },
        totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
        totalAssists: { $sum: { $ifNull: ["$players.assists", 0] } },
        totalCleanSheets: { $sum: { $cond: [{ $eq: ["$players.cleanSheet", true] }, 1, 0] } },
        totalPotm: { $sum: { $cond: [{ $eq: ["$players.playerOfTheMatch", true] }, 1, 0] } },
        avgRating: { $avg: "$players.rating" }
      }
    },
    { $sort: { "_id.seasonNumber": 1 } }
  ]);

  return playerGames.map(season => ({
    seasonNumber: season._id.seasonNumber,
    leagueId: season._id.league.toString(),
    totalGames: season.totalGames,
    totalGoals: season.totalGoals,
    totalAssists: season.totalAssists,
    totalCleanSheets: season.totalCleanSheets,
    totalPotm: season.totalPotm,
    avgRating: season.avgRating || 0
  }));
}

async function fixPlayerSeasonHistory() {
  try {
    // Load environment variables
    require('dotenv').config();

    // Connect to MongoDB using the same pattern as the main app
    const runMode = process.env.RUN_MODE || 'dev';
    const dbName = runMode === 'prod' ? process.env.MONGODB_DB_PROD : process.env.MONGODB_DB_DEV;
    const mongoUri = `mongodb+srv://${process.env.MONGO_DB_USERNAME}:${process.env.MONGO_DB_PASSWORD}@${process.env.MONGODB_CLUSTER}/${dbName}?retryWrites=true&w=majority`;

    logger.info(`Connecting to MongoDB (${runMode} mode)...`);
    await connect(mongoUri);
    logger.info('Connected to MongoDB for season history fix');

    // Get all players
    const players = await Player.find({}).select('_id name');
    logger.info(`Found ${players.length} players to process`);

    let processedCount = 0;
    let updatedCount = 0;

    for (const player of players) {
      try {
        logger.info(`Processing player ${processedCount + 1}/${players.length}: ${player.name} (${player._id})`);
        
        // Calculate actual season stats from games
        const actualSeasonStats = await calculatePlayerSeasonStats(player._id.toString());
        
        if (actualSeasonStats.length === 0) {
          logger.info(`  No game data found for player ${player.name}`);
          processedCount++;
          continue;
        }

        // Get current player data
        const fullPlayer = await Player.findById(player._id);
        if (!fullPlayer) {
          logger.warn(`  Player ${player._id} not found`);
          processedCount++;
          continue;
        }

        // Update seasonsHistory with actual game data
        const updatedSeasonsHistory = [];
        
        for (const seasonStats of actualSeasonStats) {
          // Find existing season in player's history to preserve team info
          const existingSeason = fullPlayer.seasonsHistory?.find(s => s.seasonNumber === seasonStats.seasonNumber);
          const isCurrentSeason = fullPlayer.currentSeason?.seasonNumber === seasonStats.seasonNumber;
          
          // Skip current season (it will be handled separately)
          if (isCurrentSeason) {
            continue;
          }

          updatedSeasonsHistory.push({
            seasonNumber: seasonStats.seasonNumber,
            league: new Types.ObjectId(seasonStats.leagueId),
            team: existingSeason?.team || null,
            stats: {
              games: seasonStats.totalGames,
              goals: seasonStats.totalGoals,
              assists: seasonStats.totalAssists,
              cleanSheets: seasonStats.totalCleanSheets,
              playerOfTheMatch: seasonStats.totalPotm,
              avgRating: seasonStats.avgRating
            }
          });
        }

        // Update current season stats if it exists
        let updatedCurrentSeason = fullPlayer.currentSeason;
        if (fullPlayer.currentSeason) {
          const currentSeasonStats = actualSeasonStats.find(s => s.seasonNumber === fullPlayer.currentSeason!.seasonNumber);
          if (currentSeasonStats) {
            updatedCurrentSeason = {
              ...fullPlayer.currentSeason,
              stats: {
                games: currentSeasonStats.totalGames,
                goals: currentSeasonStats.totalGoals,
                assists: currentSeasonStats.totalAssists,
                cleanSheets: currentSeasonStats.totalCleanSheets,
                playerOfTheMatch: currentSeasonStats.totalPotm,
                avgRating: currentSeasonStats.avgRating
              }
            };
          }
        }

        // Update the player
        await Player.findByIdAndUpdate(player._id, {
          seasonsHistory: updatedSeasonsHistory,
          currentSeason: updatedCurrentSeason
        });

        logger.info(`  ✅ Updated ${player.name} - Fixed ${updatedSeasonsHistory.length} seasons`);
        
        // Log detailed stats for Dor Ohayon
        if (player._id.toString() === '6654f375f9eac0fe69961bfd') {
          logger.info(`  🎯 DOR OHAYON DETAILED UPDATE:`);
          actualSeasonStats.forEach(season => {
            logger.info(`    Season ${season.seasonNumber}: ${season.totalGoals} goals, ${season.totalGames} games`);
          });
        }
        
        updatedCount++;
        processedCount++;

      } catch (error) {
        logger.error(`Error processing player ${player.name} (${player._id}):`, error);
        processedCount++;
      }
    }

    logger.info(`✅ Season history fix completed!`);
    logger.info(`📊 Processed: ${processedCount} players`);
    logger.info(`🔄 Updated: ${updatedCount} players`);

  } catch (error) {
    logger.error('Error in fixPlayerSeasonHistory:', error);
  } finally {
    await disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

// Run the script if called directly
if (require.main === module) {
  fixPlayerSeasonHistory()
    .then(() => {
      logger.info('Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Script failed:', error);
      process.exit(1);
    });
}

export { fixPlayerSeasonHistory };
