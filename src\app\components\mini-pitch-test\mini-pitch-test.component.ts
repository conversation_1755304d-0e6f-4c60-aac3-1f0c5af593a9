import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MiniPitchFormationComponent, TeamFormation } from '../mini-pitch-formation/mini-pitch-formation.component';

@Component({
  selector: 'app-mini-pitch-test',
  standalone: true,
  imports: [CommonModule, MiniPitchFormationComponent],
  template: `
    <div class="test-container">
      <h1>Mini Pitch Formation Test</h1>
      <p>Testing the improved mini pitch formation component with better spacing, default profile images, and wider pitch.</p>
      
      <div class="test-section">
        <h2>Test 1: Teams with Real Player Names</h2>
        <app-mini-pitch-formation
          [homeTeam]="testTeam1"
          [awayTeam]="testTeam2"
          [showPlayerStats]="true"
          [showPlayerNames]="true"
          [compactMode]="false">
        </app-mini-pitch-formation>
      </div>

      <div class="test-section">
        <h2>Test 2: Teams with Mixed Profile Images (some missing)</h2>
        <app-mini-pitch-formation
          [homeTeam]="testTeam3"
          [awayTeam]="testTeam4"
          [showPlayerStats]="true"
          [showPlayerNames]="true"
          [compactMode]="false">
        </app-mini-pitch-formation>
      </div>

      <div class="test-section">
        <h2>Test 3: Compact Mode</h2>
        <app-mini-pitch-formation
          [homeTeam]="testTeam1"
          [awayTeam]="testTeam2"
          [showPlayerStats]="false"
          [showPlayerNames]="false"
          [compactMode]="true">
        </app-mini-pitch-formation>
      </div>
    </div>
  `,
  styles: [`
    .test-container {
      padding: 2rem;
      max-width: 1400px;
      margin: 0 auto;
      background: var(--surface-primary);
      min-height: 100vh;
    }

    h1 {
      color: var(--text-primary);
      text-align: center;
      margin-bottom: 1rem;
    }

    p {
      color: var(--text-secondary);
      text-align: center;
      margin-bottom: 2rem;
    }

    .test-section {
      margin-bottom: 3rem;
      padding: 2rem;
      background: var(--surface-secondary);
      border-radius: var(--radius-lg);
      border: 1px solid var(--border-primary);
    }

    h2 {
      color: var(--text-primary);
      margin-bottom: 1.5rem;
      text-align: center;
    }
  `]
})
export class MiniPitchTestComponent {
  testTeam1: TeamFormation = {
    teamId: 'team-1',
    teamName: 'SADIS TEAM',
    formation: '4-3-3',
    players: [
      { id: '1', name: 'Haba', position: 'GK', jerseyNumber: 1, profileImage: 'assets/Icons/User.jpg', rating: 7.2 },
      { id: '2', name: 'Itzko', position: 'LB', jerseyNumber: 2, goals: 1, rating: 8.1 },
      { id: '3', name: 'Antolini', position: 'CB', jerseyNumber: 3, profileImage: 'assets/Icons/User.jpg', rating: 7.8 },
      { id: '4', name: 'Shaul', position: 'CB', jerseyNumber: 4, rating: 6.9 },
      { id: '5', name: 'Boccoli', position: 'RB', jerseyNumber: 5, assists: 2, rating: 8.5 },
      { id: '6', name: 'Berry', position: 'CDM', jerseyNumber: 6, profileImage: 'assets/Icons/User.jpg', rating: 7.6 },
      { id: '7', name: 'Hadad', position: 'CM', jerseyNumber: 7, goals: 1, assists: 1, rating: 9.2 },
      { id: '8', name: 'Tuchman', position: 'CM', jerseyNumber: 8, goals: 1, rating: 8.0 },
      { id: '9', name: 'Patis', position: 'LW', jerseyNumber: 9, rating: 6.5 },
      { id: '10', name: 'Aharony', position: 'ST', jerseyNumber: 10, isPlayerOfMatch: true, rating: 9.5 },
      { id: '11', name: 'Cohen', position: 'RW', jerseyNumber: 11, goals: 1, rating: 7.4 }
    ]
  };

  testTeam2: TeamFormation = {
    teamId: 'team-2',
    teamName: 'SADISTEAM',
    formation: '3-5-2',
    players: [
      { id: '1', name: 'Sade', position: 'GK', jerseyNumber: 1, rating: 7.1 },
      { id: '2', name: 'Snaba', position: 'CB1', jerseyNumber: 2, profileImage: 'assets/Icons/User.jpg', rating: 6.8 },
      { id: '3', name: 'Baruch', position: 'CB2', jerseyNumber: 3, rating: 7.5 },
      { id: '4', name: 'Aviad', position: 'CB3', jerseyNumber: 4, profileImage: 'assets/Icons/User.jpg', rating: 8.2 },
      { id: '5', name: 'Sharabi', position: 'CDM1', jerseyNumber: 5, rating: 7.9 },
      { id: '6', name: 'Camacho', position: 'CDM2', jerseyNumber: 6, rating: 6.3 },
      { id: '7', name: 'Yishay', position: 'LM', jerseyNumber: 7, profileImage: 'assets/Icons/User.jpg', rating: 8.7 },
      { id: '8', name: 'Elbahri', position: 'RM', jerseyNumber: 8, rating: 7.3 },
      { id: '9', name: 'Meir', position: 'CAM', jerseyNumber: 9, rating: 9.1 },
      { id: '10', name: 'Player 10', position: 'ST1', jerseyNumber: 10, rating: 8.4 },
      { id: '11', name: 'Player 11', position: 'ST2', jerseyNumber: 11, rating: 6.7 }
    ]
  };

  testTeam3: TeamFormation = {
    teamId: 'team-3',
    teamName: 'Mixed Images Team',
    formation: '4-4-2',
    players: [
      { id: '1', name: 'Keeper One', position: 'GK', jerseyNumber: 1, profileImage: 'assets/Icons/User.jpg' },
      { id: '2', name: 'Defender One', position: 'LB', jerseyNumber: 2 }, // No image
      { id: '3', name: 'Defender Two', position: 'CB', jerseyNumber: 3, profileImage: 'assets/Icons/User.jpg' },
      { id: '4', name: 'Defender Three', position: 'CB', jerseyNumber: 4 }, // No image
      { id: '5', name: 'Defender Four', position: 'RB', jerseyNumber: 5, profileImage: 'assets/Icons/User.jpg' },
      { id: '6', name: 'Mid One', position: 'LM', jerseyNumber: 6 }, // No image
      { id: '7', name: 'Mid Two', position: 'CM', jerseyNumber: 7, profileImage: 'assets/Icons/User.jpg' },
      { id: '8', name: 'Mid Three', position: 'CM', jerseyNumber: 8 }, // No image
      { id: '9', name: 'Mid Four', position: 'RM', jerseyNumber: 9, profileImage: 'assets/Icons/User.jpg' },
      { id: '10', name: 'Forward One', position: 'ST', jerseyNumber: 10, goals: 2 }, // No image
      { id: '11', name: 'Forward Two', position: 'ST', jerseyNumber: 11, assists: 1, profileImage: 'assets/Icons/User.jpg' }
    ]
  };

  testTeam4: TeamFormation = {
    teamId: 'team-4',
    teamName: 'Default Images Team',
    formation: '3-4-1-2',
    players: [
      { id: '1', name: 'GK', position: 'GK', jerseyNumber: 1 },
      { id: '2', name: 'CB1', position: 'CB', jerseyNumber: 2 },
      { id: '3', name: 'CB2', position: 'CB', jerseyNumber: 3 },
      { id: '4', name: 'CB3', position: 'CB', jerseyNumber: 4 },
      { id: '5', name: 'CDM', position: 'CDM', jerseyNumber: 5 },
      { id: '6', name: 'LM', position: 'LM', jerseyNumber: 6 },
      { id: '7', name: 'CM1', position: 'CM', jerseyNumber: 7 },
      { id: '8', name: 'CM2', position: 'CM', jerseyNumber: 8 },
      { id: '9', name: 'RM', position: 'RM', jerseyNumber: 9 },
      { id: '10', name: 'ST1', position: 'ST', jerseyNumber: 10 },
      { id: '11', name: 'ST2', position: 'ST', jerseyNumber: 11 }
    ]
  };
}
