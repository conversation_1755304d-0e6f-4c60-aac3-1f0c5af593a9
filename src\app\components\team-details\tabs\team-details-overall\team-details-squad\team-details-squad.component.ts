import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { TeamService } from '../../../../../services/team.service';
import { PlayerDTOShort } from '../../../../../shared/models/player.model';
import { TeamDTO } from '@pro-clubs-manager/shared-dtos';
import { NotificationService } from '../../../../../services/notification.service';
import { AuthService } from '../../../../../services/auth.service';
import { PermissionsService } from '../../../../../services/permissions.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'team-details-squad',
  templateUrl: './team-details-squad.component.html',
  styleUrl: './team-details-squad.component.scss'
})
export class TeamDetailsSquadComponent {
  @Input() chosenTeam: TeamDTO | null = null;
  @Output() onTeamUpdateEvent = new EventEmitter<void>();

  allPlayers: PlayerDTOShort[] = [];
  goalkeepers: PlayerDTOShort[] = [];
  defenders: PlayerDTOShort[] = [];
  midfielders: PlayerDTOShort[] = [];
  attackers: PlayerDTOShort[] = [];
  isRemovingAllPlayers = false;

  // iOS-style jiggle mode
  isJiggleMode = false;
  removingPlayerIds = new Set<string>();

  // Permission observables
  canManagePlayers$: Observable<boolean> = new Observable();

  constructor(
    private router: Router,
    private teamService: TeamService,
    private notificationService: NotificationService,
    private authService: AuthService,
    private permissionsService: PermissionsService
  ) { }

  ngOnInit() {
    this.loadPlayersData();
    this.initializePermissions();
  }

  initializePermissions() {
    if (this.chosenTeam) {
      // Check if user can edit this specific team (admin or team captain)
      this.canManagePlayers$ = this.permissionsService.canEditTeam(this.chosenTeam.id);
    }
  }

  async loadPlayersData() {
    this.allPlayers = this.chosenTeam!.players!;

    this.getTeamAttackers();
    this.getTeamMidfielders();
    this.getTeamDefenders();
    this.getTeamGoalKeepers();

  }

  onAddPlayerClick(): void {
    this.router.navigate(['/assign-player-to-team', { id: this.chosenTeam!.id, name: this.chosenTeam!.name }]);
  }

  getTeamAttackers() {
    this.attackers = this.allPlayers!.filter(player => {
      return player.position === "ST" || player.position === "RW" || player.position === "LW" ||
        player.position === "RF" || player.position === "CF" || player.position === "LF"
    });
  }

  getTeamGoalKeepers() {
    this.goalkeepers = this.allPlayers!.filter(player => { return player.position === "GK" });
  }

  getTeamDefenders() {
    this.defenders = this.allPlayers!.filter(player => {
      return player.position === "RB" || player.position === "RWB" || player.position === "LWB" ||
        player.position === "LB" || player.position === "CB"
    });
  }

  getTeamMidfielders() {
    this.midfielders = this.allPlayers!.filter(player => {
      return player.position === "CDM" || player.position === "CM" || player.position === "RM" ||
        player.position === "LM" || player.position === "CAM"
    });
  }

  onPlayerClick(playerId: string): void {
    if (!this.isJiggleMode) {
      this.router.navigate(['/player-details', { id: playerId }]);
    }
  }

  // iOS-style jiggle mode methods
  toggleJiggleMode(): void {
    this.isJiggleMode = !this.isJiggleMode;
    if (!this.isJiggleMode) {
      this.removingPlayerIds.clear();
    }
  }

  async removePlayer(playerId: string, playerName: string): Promise<void> {
    if (!this.chosenTeam) {
      return;
    }

    const confirmRemove = confirm(
      `Are you sure you want to remove "${playerName}" from ${this.chosenTeam.name}?\n\n` +
      'This action cannot be undone.'
    );

    if (!confirmRemove) {
      return;
    }

    try {
      this.removingPlayerIds.add(playerId);

      await this.teamService.removePlayerFromTeam(this.chosenTeam.id, playerId);

      this.notificationService.success(`${playerName} removed from team successfully`);

      // Emit event to refresh team data
      this.onTeamUpdateEvent.emit();

    } catch (error) {
      console.error('Error removing player:', error);
      this.notificationService.error('Failed to remove player. Please try again.');
    } finally {
      this.removingPlayerIds.delete(playerId);
    }
  }

  isPlayerRemoving(playerId: string): boolean {
    return this.removingPlayerIds.has(playerId);
  }

  isAdmin() {
    return this.authService.isAdmin();
  }

  async onRemoveAllPlayersClick(): Promise<void> {
    if (!this.chosenTeam || this.chosenTeam.players.length === 0) {
      return;
    }

    const confirmRemove = confirm(
      `⚠️ Are you sure you want to remove ALL players from ${this.chosenTeam.name}?\n\n` +
      `This will remove ${this.chosenTeam.players.length} player(s) from the team.\n\n` +
      'This action cannot be undone.'
    );

    if (!confirmRemove) {
      return;
    }

    try {
      this.isRemovingAllPlayers = true;

      await this.teamService.removeAllPlayersFromTeam(this.chosenTeam.id);

      this.notificationService.success(`All players removed from ${this.chosenTeam.name} successfully`);

      // Emit event to refresh team data
      this.onTeamUpdateEvent.emit();

    } catch (error) {
      console.error('Error removing all players:', error);
      this.notificationService.error('Failed to remove all players. Please try again.');
    } finally {
      this.isRemovingAllPlayers = false;
    }
  }
}
