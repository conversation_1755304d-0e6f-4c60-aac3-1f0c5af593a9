.admin-requests-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--background-color);
  background-image: 
    radial-gradient(at 40% 20%, hsla(228, 100%, 74%, 0.05) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 0.05) 0px, transparent 50%);
  min-height: 100vh;
  color: var(--text-color);
}

.admin-header {
  text-align: center;
  margin-bottom: 2rem;

  .admin-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-primary, #8b5cf6));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .admin-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0;
  }
}

.requests-stats {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;

  .stat-card {
    background: var(--card-background, rgba(30, 41, 59, 0.8));
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color, rgba(148, 163, 184, 0.1));
    border-radius: 16px;
    padding: 1.5rem 2rem;
    text-align: center;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    .stat-number {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    .stat-label {
      font-size: 0.9rem;
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
}

.requests-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.request-card {
  background: var(--card-background, rgba(30, 41, 59, 0.8));
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color, rgba(148, 163, 184, 0.1));
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
  }
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-primary);

  .user-info {
    display: flex;
    align-items: center;
    gap: 1rem;

    .user-avatar {
      width: 50px;
      height: 50px;
      background: var(--primary-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.2rem;
    }

    .user-details {
      .user-name {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 0.25rem 0;
      }

      .user-email {
        font-size: 0.9rem;
        color: var(--text-secondary);
        margin: 0;
      }
    }
  }

  .request-date {
    text-align: right;

    .date-label {
      display: block;
      font-size: 0.8rem;
      color: var(--text-tertiary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .date-value {
      display: block;
      font-size: 0.9rem;
      color: var(--text-secondary);
      font-weight: 500;
    }
  }
}

.request-body {
  margin-bottom: 1.5rem;

  .player-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;

    .player-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid var(--border-primary);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .player-details {
      .player-name {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 0.25rem 0;
      }

      .player-position {
        font-size: 0.9rem;
        color: var(--accent-primary);
        font-weight: 500;
        margin: 0 0 0.25rem 0;
      }

      .player-team {
        font-size: 0.85rem;
        color: var(--text-secondary);
        margin: 0;
      }
    }
  }

  .request-message {
    background: var(--surface-secondary);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;

    h5 {
      font-size: 0.9rem;
      color: var(--text-secondary);
      margin: 0 0 0.5rem 0;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    p {
      color: var(--text-primary);
      margin: 0;
      line-height: 1.5;
    }
  }

  .request-email {
    p {
      font-size: 0.9rem;
      color: var(--text-secondary);
      margin: 0;
    }
  }
}

.request-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.btn-success {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #059669, #047857);
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
      }
    }

    &.btn-danger {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      color: white;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
      }
    }
  }
}

.no-requests {
  text-align: center;
  padding: 4rem 2rem;

  .no-requests-icon {
    font-size: 4rem;
    color: var(--text-tertiary);
    margin-bottom: 1rem;
  }

  h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
  }

  p {
    color: var(--text-secondary);
    margin: 0;
  }
}

.loading-container {
  text-align: center;
  padding: 4rem 2rem;

  .loading-spinner {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
  }

  p {
    color: var(--text-secondary);
    margin: 0;
  }
}

@media (max-width: 768px) {
  .admin-requests-container {
    padding: 1rem;
  }

  .request-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .request-actions {
    flex-direction: column;

    .btn {
      width: 100%;
      justify-content: center;
    }
  }

  .player-info {
    flex-direction: column;
    align-items: flex-start;
    text-align: center;
  }
}
