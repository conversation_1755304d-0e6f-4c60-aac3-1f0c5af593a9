import { Component, Input, Output, EventEmitter } from '@angular/core';
import { ListOption } from '../../models/list-option.model';
import { FormControl } from '@angular/forms';
import { Observable, map, startWith } from 'rxjs';

@Component({
  selector: 'pro-clubs-multiple-select',
  templateUrl: './pro-clubs-multiple-select.component.html',
  styleUrl: './pro-clubs-multiple-select.component.scss'
})
export class ProClubsMultipleSelectComponent {

  filteredOptions?: Observable<string[]>;
  myControl = new FormControl('');
  selectOptionsTextOnly: string[] = [];
  selectedOptions: ListOption[] = [];
  filteredSelectOptions: ListOption[] = [];
  isDropdownOpen: boolean = false;
  isFocused: boolean = false;
  searchTerm: string = '';

  @Input() selectOptions: ListOption[] = [];
  @Input() placeholder: string = '';
  @Input() set defaultSelectedOptions(options: ListOption[]) {
    if (options && options.length > 0) {
      this.selectedOptions = [...options];
      this.onOptionChange();
    }
  }

  @Output() selectionChange = new EventEmitter<ListOption[]>();

  ngOnInit() {
    this.selectOptionsTextOnly = this.selectOptions.map(x => x.displayText);
    this.filteredSelectOptions = [...this.selectOptions];
    this.selectedOptions = [];
  }

  hasSelectedValues(): boolean {
    return this.selectedOptions && this.selectedOptions.length > 0;
  }

  getSelectedOptions(): ListOption[] {
    return this.selectedOptions || [];
  }

  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
    this.isFocused = this.isDropdownOpen;
    if (this.isDropdownOpen) {
      this.searchTerm = '';
      this.filterOptions();
    }
  }

  closeDropdown() {
    this.isDropdownOpen = false;
    this.isFocused = false;
  }

  toggleOption(option: ListOption) {
    const index = this.selectedOptions.findIndex(selected => selected.value === option.value);
    if (index > -1) {
      this.selectedOptions.splice(index, 1);
    } else {
      this.selectedOptions.push(option);
    }
    this.onOptionChange();
  }

  removeOption(option: ListOption, event: Event) {
    event.stopPropagation();
    const index = this.selectedOptions.findIndex(selected => selected.value === option.value);
    if (index > -1) {
      this.selectedOptions.splice(index, 1);
      this.onOptionChange();
    }
  }

  isSelected(option: ListOption): boolean {
    return this.selectedOptions.some(selected => selected.value === option.value);
  }

  filterOptions() {
    if (!this.searchTerm) {
      this.filteredSelectOptions = [...this.selectOptions];
    } else {
      this.filteredSelectOptions = this.selectOptions.filter(option =>
        option.displayText.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
  }

  isAllSelected(): boolean {
    return this.filteredSelectOptions.length > 0 &&
           this.filteredSelectOptions.every(option => this.isSelected(option));
  }

  isIndeterminate(): boolean {
    const selectedCount = this.filteredSelectOptions.filter(option => this.isSelected(option)).length;
    return selectedCount > 0 && selectedCount < this.filteredSelectOptions.length;
  }

  toggleSelectAll() {
    if (this.isAllSelected()) {
      // Deselect all filtered options
      this.filteredSelectOptions.forEach(option => {
        const index = this.selectedOptions.findIndex(selected => selected.value === option.value);
        if (index > -1) {
          this.selectedOptions.splice(index, 1);
        }
      });
    } else {
      // Select all filtered options
      this.filteredSelectOptions.forEach(option => {
        if (!this.isSelected(option)) {
          this.selectedOptions.push(option);
        }
      });
    }
    this.onOptionChange();
  }

  onOptionChange() {
    this.selectionChange.emit(this.selectedOptions);
  }
}
