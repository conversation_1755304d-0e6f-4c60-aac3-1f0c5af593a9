import { injectable, inject } from "tsyringe";
import { ClientSession, Types } from "mongoose";
import { ITransferRequestRepository } from "../repositories/transfer-request-repository";
import { ITransferRequest } from "../models/transfer-request";
import { IPlayerRepository } from "../interfaces/player";
import { ITeamRepository } from "../interfaces/team";
import { IUserRepository } from "../interfaces/user";
import { IPlayerTeamService } from "../interfaces/wrapper-services";
import { transactionService } from "./util-services/transaction-service";
import { BadRequestError, NotFoundError } from "../errors";
import logger from "../config/logger";

export interface CreateTransferRequestData {
  playerId: string;
  fromTeamId: string;
  toTeamId: string;
  message?: string;
}

export interface ProcessTransferRequestData {
  requestId: string;
  action: 'approve' | 'reject';
  reason?: string;
}

export interface ITransferRequestService {
  createTransferRequest(requestedBy: string, data: CreateTransferRequestData): Promise<ITransferRequest>;
  processTransferRequest(processedBy: string, data: ProcessTransferRequestData): Promise<ITransferRequest>;
  getTransferRequestsByTeam(teamId: string, status?: string): Promise<ITransferRequest[]>;
  getTransferRequestById(requestId: string): Promise<ITransferRequest>;
  cancelTransferRequest(requestId: string, userId: string): Promise<void>;
  cleanupExpiredRequests(): Promise<number>;
}

@injectable()
export class TransferRequestService implements ITransferRequestService {
  constructor(
    @inject("ITransferRequestRepository") private transferRequestRepository: ITransferRequestRepository,
    @inject("IPlayerRepository") private playerRepository: IPlayerRepository,
    @inject("ITeamRepository") private teamRepository: ITeamRepository,
    @inject("IUserRepository") private userRepository: IUserRepository,
    @inject("IPlayerTeamService") private playerTeamService: IPlayerTeamService
  ) {}

  async createTransferRequest(requestedBy: string, data: CreateTransferRequestData): Promise<ITransferRequest> {
    const { playerId, fromTeamId, toTeamId, message } = data;

    // Validate input
    if (fromTeamId === toTeamId) {
      throw new BadRequestError("Cannot transfer player to the same team");
    }

    // Get entities
    const [player, fromTeam, toTeam, requester] = await Promise.all([
      this.playerRepository.getPlayerById(playerId),
      this.teamRepository.getTeamById(fromTeamId),
      this.teamRepository.getTeamById(toTeamId),
      this.userRepository.findById(requestedBy)
    ]);

    if (!player) throw new NotFoundError("Player not found");
    if (!fromTeam) throw new NotFoundError("Source team not found");
    if (!toTeam) throw new NotFoundError("Destination team not found");
    if (!requester) throw new NotFoundError("Requesting user not found");

    // Validate player is in the source team
    if (!player.team || player.team.toString() !== fromTeamId) {
      throw new BadRequestError("Player is not in the specified source team");
    }

    // Validate requester is captain of destination team
    if (toTeam.captain.toString() !== requester.associatedPlayers?.[0]?.toString()) {
      throw new BadRequestError("Only the team captain can initiate transfer requests");
    }

    // Check if player already has a pending transfer request
    const existingRequest = await this.transferRequestRepository.findPendingByPlayer(playerId);
    if (existingRequest) {
      throw new BadRequestError("Player already has a pending transfer request");
    }

    // Create transfer request
    const transferRequest = await this.transferRequestRepository.create({
      playerId: new Types.ObjectId(playerId),
      fromTeamId: new Types.ObjectId(fromTeamId),
      toTeamId: new Types.ObjectId(toTeamId),
      requestedBy: new Types.ObjectId(requestedBy),
      message,
      status: 'pending'
    });

    logger.info(`Transfer request created: Player ${playerId} from ${fromTeamId} to ${toTeamId} by ${requestedBy}`);
    return transferRequest;
  }

  async processTransferRequest(processedBy: string, data: ProcessTransferRequestData): Promise<ITransferRequest> {
    const { requestId, action, reason } = data;

    // Get the transfer request
    const transferRequest = await this.transferRequestRepository.findById(requestId);
    if (!transferRequest) {
      throw new NotFoundError("Transfer request not found");
    }

    if (transferRequest.status !== 'pending') {
      throw new BadRequestError("Transfer request has already been processed");
    }

    // Check if request has expired
    if (new Date() > transferRequest.expiresAt) {
      throw new BadRequestError("Transfer request has expired");
    }

    // Get processor and validate they are captain of the source team
    const [processor, fromTeam] = await Promise.all([
      this.userRepository.findById(processedBy),
      this.teamRepository.getTeamById(transferRequest.fromTeamId.toString())
    ]);

    if (!processor) throw new NotFoundError("Processing user not found");
    if (!fromTeam) throw new NotFoundError("Source team not found");

    // Validate processor is captain of source team or admin
    const isSourceTeamCaptain = fromTeam.captain.toString() === processor.associatedPlayers?.[0]?.toString();
    const isAdmin = processor.role === 'admin';

    if (!isSourceTeamCaptain && !isAdmin) {
      throw new BadRequestError("Only the source team captain or admin can process this transfer request");
    }

    // Update request status
    const updatedRequest = await this.transferRequestRepository.updateStatus(
      requestId,
      action === 'approve' ? 'approved' : 'rejected',
      processedBy,
      reason
    );

    if (!updatedRequest) {
      throw new Error("Failed to update transfer request");
    }

    // If approved, execute the transfer
    if (action === 'approve') {
      try {
        await transactionService.withTransaction(async (session) => {
          await this.playerTeamService.addPlayerToTeam(
            transferRequest.playerId.toString(),
            transferRequest.toTeamId.toString()
          );
        });

        logger.info(`Transfer completed: Player ${transferRequest.playerId} moved from ${transferRequest.fromTeamId} to ${transferRequest.toTeamId}`);
      } catch (error) {
        logger.error("Error executing transfer:", error);
        // Revert the request status if transfer fails
        await this.transferRequestRepository.updateStatus(
          requestId,
          'pending',
          processedBy,
          'Transfer execution failed'
        );
        throw new Error("Failed to execute transfer");
      }
    }

    logger.info(`Transfer request ${requestId} ${action}ed by ${processedBy}`);
    return updatedRequest;
  }

  async getTransferRequestsByTeam(teamId: string, status?: string): Promise<ITransferRequest[]> {
    return await this.transferRequestRepository.findByTeam(teamId, status);
  }

  async getTransferRequestById(requestId: string): Promise<ITransferRequest> {
    const transferRequest = await this.transferRequestRepository.findById(requestId);
    if (!transferRequest) {
      throw new NotFoundError("Transfer request not found");
    }
    return transferRequest;
  }

  async cancelTransferRequest(requestId: string, userId: string): Promise<void> {
    const transferRequest = await this.transferRequestRepository.findById(requestId);
    if (!transferRequest) {
      throw new NotFoundError("Transfer request not found");
    }

    if (transferRequest.status !== 'pending') {
      throw new BadRequestError("Can only cancel pending transfer requests");
    }

    // Only the requester or admin can cancel
    const user = await this.userRepository.findById(userId);
    if (!user) throw new NotFoundError("User not found");

    const isRequester = transferRequest.requestedBy.toString() === userId;
    const isAdmin = user.role === 'admin';

    if (!isRequester && !isAdmin) {
      throw new BadRequestError("Only the requester or admin can cancel this transfer request");
    }

    await this.transferRequestRepository.updateStatus(
      requestId,
      'cancelled',
      userId,
      'Cancelled by user'
    );

    logger.info(`Transfer request ${requestId} cancelled by ${userId}`);
  }

  async cleanupExpiredRequests(): Promise<number> {
    return await this.transferRequestRepository.deleteExpiredRequests();
  }
}
