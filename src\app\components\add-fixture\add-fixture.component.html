<div class="add-fixture-container" *ngIf="teamsOptions && !isLoading">
    <div class="add-fixture-card">
        <div class="add-fixture-header">
            <h1 class="add-fixture-title">
                <i class="fas fa-calendar-plus"></i>
                Add Fixture
            </h1>
            <p class="add-fixture-subtitle">Create a new fixture with multiple games</p>
        </div>

        <form [formGroup]="addFixtureFormGroup!" (ngSubmit)="onSubmit()" class="add-fixture-form">
            <!-- Fixture Details Section -->
            <div class="form-section">
                <h2 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    Fixture Details
                </h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">
                            {{ isPlayoffSelected() ? 'Playoff Stage' : 'Fixture Number' }}
                            <span class="required-indicator">*</span>
                        </label>
                        <input
                            *ngIf="!isPlayoffSelected()"
                            formControlName="round"
                            maxlength="2"
                            placeholder="Enter fixture number"
                            class="form-input"
                            [class.error]="addFixtureFormGroup!.get('round')?.invalid && addFixtureFormGroup!.get('round')?.touched">
                        <div
                            *ngIf="isPlayoffSelected()"
                            class="form-input playoff-display">
                            {{ getDisplayTitle() }}
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            Start Date
                            <span class="required-indicator">*</span>
                        </label>
                        <input
                            formControlName="startDate"
                            type="date"
                            placeholder="Select start date"
                            class="form-input"
                            [class.error]="addFixtureFormGroup!.get('startDate')?.invalid && addFixtureFormGroup!.get('startDate')?.touched">
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            End Date
                            <span class="required-indicator">*</span>
                        </label>
                        <input
                            formControlName="endDate"
                            type="date"
                            placeholder="Select end date"
                            class="form-input"
                            [class.error]="addFixtureFormGroup!.get('endDate')?.invalid && addFixtureFormGroup!.get('endDate')?.touched">
                    </div>

                    <!-- Playoff Options -->
                    <div class="form-group playoff-section">
                        <div class="playoff-checkbox">
                            <input
                                type="checkbox"
                                id="isPlayoff"
                                formControlName="isPlayoff"
                                (change)="onPlayoffChange($any($event.target).checked)"
                                class="checkbox-input">
                            <label for="isPlayoff" class="checkbox-label">
                                <i class="fas fa-trophy"></i>
                                Playoff Match
                            </label>
                        </div>

                        <div class="form-group" *ngIf="isPlayoffSelected()">
                            <label class="form-label">
                                Playoff Stage
                                <span class="required-indicator">*</span>
                            </label>
                            <pro-clubs-auto-complete-select
                                formControlName="playoffStage"
                                [selectOptions]="playoffStageOptions"
                                (selectionChange)="onPlayoffStageChange($event)"
                                placeholder="Select playoff stage"
                                [class.error]="addFixtureFormGroup!.get('playoffStage')?.invalid && addFixtureFormGroup!.get('playoffStage')?.touched">
                            </pro-clubs-auto-complete-select>
                        </div>

                        <div class="form-group" *ngIf="isPlayoffSelected()">
                            <label class="form-label">
                                Series Format
                                <span class="required-indicator">*</span>
                            </label>
                            <pro-clubs-auto-complete-select
                                formControlName="seriesFormat"
                                [selectOptions]="seriesFormatOptions"
                                (selectionChange)="onSeriesFormatChange($any($event.value))"
                                placeholder="Select series format"
                                [class.error]="addFixtureFormGroup!.get('seriesFormat')?.invalid && addFixtureFormGroup!.get('seriesFormat')?.touched">
                            </pro-clubs-auto-complete-select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Games Section -->
            <div class="form-section teams-section">
                <h2 class="section-title">
                    <i class="fas fa-futbol"></i>
                    Games ({{ games.length }})
                </h2>

                <!-- Playoff Mode Indicator -->
                <div class="playoff-mode-indicator" *ngIf="isPlayoffSelected()">
                    <i class="fas fa-info-circle"></i>
                    <span>Playoff Mode: Same teams can be selected multiple times for best-of series</span>
                </div>

                <div formArrayName="games">
                    <div *ngFor="let item of games.controls; let i=index; trackBy: trackByIndex" [formGroupName]="i" class="game-item">
                        <div class="game-header">
                            <h3 class="game-title">Game {{ i + 1 }}</h3>
                            <button
                                type="button"
                                class="remove-game-btn"
                                (click)="removeGame(i)"
                                [disabled]="games.length <= 1"
                                title="Remove Game">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>

                        <div class="teams-row">
                            <div class="team-selection">
                                <label class="form-label">Home Team</label>
                                <pro-clubs-auto-complete-select
                                    [selectOptions]="sourceTeamsOptions!"
                                    (selectionChange)="onSelectionChange($event, i, true)">
                                </pro-clubs-auto-complete-select>
                            </div>

                            <div class="vs-indicator">VS</div>

                            <div class="team-selection">
                                <label class="form-label">Away Team</label>
                                <pro-clubs-auto-complete-select
                                    [selectOptions]="sourceTeamsOptions!"
                                    (selectionChange)="onSelectionChange($event, i, false)">
                                </pro-clubs-auto-complete-select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Add Game Button -->
                <div class="add-match-button" (click)="addGame()">
                    <i class="fas fa-plus"></i>
                    <div class="button-text">Add Another Game</div>
                </div>
            </div>

            <!-- Submit Button -->
            <button
                type="submit"
                class="submit-button"
                [disabled]="addFixtureFormGroup!.invalid">
                <i class="fas fa-save"></i>
                Create Fixture
            </button>
        </form>
    </div>
</div>

<pro-clubs-spinner *ngIf="isLoading"></pro-clubs-spinner>