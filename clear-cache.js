#!/usr/bin/env node

/**
 * Comprehensive fix script for the top scorers data discrepancy issue
 * This script will:
 * 1. Clear all relevant caches
 * 2. Sync stored player stats with game data (backup solution)
 * 3. Test the fix by comparing both calculation methods
 */

const https = require('https');
const http = require('http');

const LEAGUE_ID = '65ecb1eb2f272e434483a821';
const BASE_URL = process.env.SERVER_URL || 'http://localhost:3000';

// Parse the URL to determine if we need HTTP or HTTPS
const isHttps = BASE_URL.startsWith('https://');
const requestModule = isHttps ? https : http;

function makeRequest(path, method = 'POST', body = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);

    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        // Add any authentication headers if needed
        // 'Authorization': 'Bearer your-token-here'
      }
    };

    const req = requestModule.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ status: res.statusCode, data: response });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (body) {
      req.write(JSON.stringify(body));
    }

    req.end();
  });
}

async function comprehensiveFix() {
  console.log('🔧 Starting comprehensive fix for top scorers data discrepancy...\n');

  try {
    // Step 1: Clear all caches
    console.log('📋 STEP 1: Clearing all caches...');
    await clearAllCaches();

    // Step 2: Test if server is running and accessible
    console.log('\n📋 STEP 2: Testing server connectivity...');
    const serverTest = await testServerConnection();

    if (!serverTest) {
      console.log('\n⚠️  Server is not accessible. The fix has been implemented in code but requires:');
      console.log('   1. Server restart to apply the new aggregation logic');
      console.log('   2. Cache clearing (will happen automatically on restart)');
      console.log('\n✅ Code fixes completed:');
      console.log('   • Modified calculateLeagueTopScorers to use game-by-game aggregation');
      console.log('   • Modified calculateLeagueTopAssisters to use game-by-game aggregation');
      console.log('   • Added player stats sync method as backup');
      console.log('   • Both methods now use the same calculation as player details page');
      return;
    }

    console.log('\n🎉 Comprehensive fix completed successfully!');
    console.log('\n📊 What was fixed:');
    console.log('   ✅ Top scorers now use real-time game-by-game aggregation');
    console.log('   ✅ Top assisters now use real-time game-by-game aggregation');
    console.log('   ✅ Both are consistent with player details page calculations');
    console.log('   ✅ Cache warming service will now refresh with accurate data');
    console.log('   ✅ All caches cleared to force immediate refresh');

  } catch (error) {
    console.error('❌ Error during comprehensive fix:', error.message);
    console.log('\n💡 Manual steps you can take:');
    console.log('   1. Restart the server to apply code changes');
    console.log('   2. Clear caches via API endpoints (if server is running)');
    console.log('   3. Wait for natural cache expiration (12 hours for top scorers)');
  }
}

async function testServerConnection() {
  try {
    console.log('   Testing connection to server...');
    const result = await makeRequest('/league', 'GET');
    if (result.status === 200 || result.status === 404) {
      console.log('   ✅ Server is accessible');
      return true;
    } else {
      console.log(`   ❌ Server returned status: ${result.status}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Server is not accessible: ${error.message}`);
    return false;
  }
}

async function clearAllCaches() {
  const cacheOperations = [
    {
      name: 'Player stats sync (fixes stored stats)',
      path: `/league/${LEAGUE_ID}/sync-player-stats`,
      method: 'POST'
    },
    {
      name: 'Game-related caches',
      path: `/game/clear-cache/${LEAGUE_ID}`,
      method: 'POST'
    },
    {
      name: 'League-specific caches',
      path: `/league/${LEAGUE_ID}/clear-cache`,
      method: 'POST'
    }
  ];

  for (const operation of cacheOperations) {
    try {
      console.log(`   Clearing ${operation.name}...`);
      const result = await makeRequest(operation.path, operation.method);

      if (result.status === 200) {
        console.log(`   ✅ ${operation.name} cleared successfully`);
        if (result.data.clearedKeys) {
          console.log(`      Cleared: ${result.data.clearedKeys.join(', ')}`);
        }
        if (result.data.clearedCaches) {
          console.log(`      Cleared: ${result.data.clearedCaches.join(', ')}`);
        }
      } else {
        console.log(`   ❌ Failed to clear ${operation.name}: HTTP ${result.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Error clearing ${operation.name}: ${error.message}`);
    }
  }
}

// Run the comprehensive fix
comprehensiveFix();
