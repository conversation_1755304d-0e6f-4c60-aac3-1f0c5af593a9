import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition, MatSnackBarRef, TextOnlySnackBar } from '@angular/material/snack-bar';

export interface ToastOptions {
  duration?: number;
  action?: string;
  position?: {
    horizontal?: MatSnackBarHorizontalPosition;
    vertical?: MatSnackBarVerticalPosition;
  };
  persistent?: boolean;
}

@Injectable({
    providedIn: 'root'
})
export class NotificationService {

    private defaultHorizontalPosition: MatSnackBarHorizontalPosition = 'right';
    private defaultVerticalPosition: MatSnackBarVerticalPosition = 'top';

    constructor(private snackBar: MatSnackBar) {}

    /**
     * Show a success notification
     * @param message The message to display
     * @param options Optional configuration
     */
    success(message: string, options?: ToastOptions): MatSnackBarRef<TextOnlySnackBar> {
      return this.showToast(message, 'success', {
        duration: 3000,
        action: '✓ GOT IT',
        ...options
      });
    }

    /**
     * Show an error notification
     * @param message The message to display
     * @param options Optional configuration
     */
    error(message: string, options?: ToastOptions): MatSnackBarRef<TextOnlySnackBar> {
      return this.showToast(message, 'error', {
        duration: 5000,
        action: '✕ DISMISS',
        ...options
      });
    }

    /**
     * Show an info notification
     * @param message The message to display
     * @param options Optional configuration
     */
    info(message: string, options?: ToastOptions): MatSnackBarRef<TextOnlySnackBar> {
      return this.showToast(message, 'info', {
        duration: 4000,
        action: 'ℹ OK',
        ...options
      });
    }

    /**
     * Show a warning notification
     * @param message The message to display
     * @param options Optional configuration
     */
    warning(message: string, options?: ToastOptions): MatSnackBarRef<TextOnlySnackBar> {
      return this.showToast(message, 'warning', {
        duration: 4000,
        action: '⚠ NOTED',
        ...options
      });
    }

    /**
     * Show a loading notification (persistent until dismissed)
     * @param message The message to display
     */
    loading(message: string): MatSnackBarRef<TextOnlySnackBar> {
      return this.showToast(message, 'info', {
        persistent: true,
        action: 'CANCEL'
      });
    }

    /**
     * Show a custom notification with AI-specific styling
     * @param message The message to display
     * @param type The type of notification
     */
    aiSuccess(message: string): MatSnackBarRef<TextOnlySnackBar> {
      return this.success(`🤖 AI: ${message}`, {
        duration: 4000,
        action: '🎉 AWESOME'
      });
    }

    /**
     * Show an AI error notification
     * @param message The message to display
     */
    aiError(message: string): MatSnackBarRef<TextOnlySnackBar> {
      return this.error(`🤖 AI Error: ${message}`, {
        duration: 6000,
        action: '🔄 RETRY'
      });
    }

    /**
     * Dismiss all active notifications
     */
    dismissAll(): void {
      this.snackBar.dismiss();
    }

    /**
     * Private method to show toast with consistent configuration
     */
    private showToast(
      message: string,
      type: 'success' | 'error' | 'info' | 'warning',
      options: ToastOptions
    ): MatSnackBarRef<TextOnlySnackBar> {
      const config = {
        horizontalPosition: options.position?.horizontal || this.defaultHorizontalPosition,
        verticalPosition: options.position?.vertical || this.defaultVerticalPosition,
        duration: options.persistent ? 0 : (options.duration || 4000),
        panelClass: [`${type}-snackbar`],
        politeness: type === 'error' ? 'assertive' as const : 'polite' as const
      };

      return this.snackBar.open(message, options.action || 'CLOSE', config);
    }
}