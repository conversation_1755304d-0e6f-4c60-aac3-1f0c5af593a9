import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

import { DashboardSummary } from '../dashboard.service';

export interface DashboardData extends DashboardSummary {
  lastUpdated: Date | null;
}

export interface DashboardState {
  dashboardData: DashboardData | null;
  lastDashboardUpdate: Date | null;
  isDashboardLoading: boolean;
  cacheKey: string;
  lastForceRefresh: Date | null;
}

@Injectable({
  providedIn: 'root'
})
export class DashboardStateService {
  private readonly initialState: DashboardState = {
    dashboardData: null,
    lastDashboardUpdate: null,
    isDashboardLoading: false,
    cacheKey: this.generateDailyCacheKey(),
    lastForceRefresh: null
  };

  private stateSubject = new BehaviorSubject<DashboardState>(this.initialState);
  public state$ = this.stateSubject.asObservable();

  // Cache duration in milliseconds (4 hours for regular cache, 24 hours for daily refresh)
  private readonly DASHBOARD_CACHE_DURATION = 4 * 60 * 60 * 1000; // 4 hours (server cache is 5 days)
  private readonly DAILY_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private readonly FORCE_REFRESH_COOLDOWN = 30 * 1000; // 30 seconds

  constructor() {
    this.loadStateFromStorage();
    this.checkDailyCacheKey();
  }

  get currentState(): DashboardState {
    return this.stateSubject.value;
  }

  updateDashboardData(dashboardData: DashboardData): void {
    const newState = {
      ...this.currentState,
      dashboardData,
      lastDashboardUpdate: new Date(),
      isDashboardLoading: false
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  setDashboardLoading(isLoading: boolean): void {
    this.updateState({ isDashboardLoading: isLoading });
  }

  isDashboardStale(): boolean {
    const lastUpdate = this.currentState.lastDashboardUpdate;
    if (!lastUpdate) return true;

    // Check if daily cache key has changed (forces refresh once per day)
    if (this.currentState.cacheKey !== this.generateDailyCacheKey()) {
      return true;
    }

    return Date.now() - lastUpdate.getTime() > this.DASHBOARD_CACHE_DURATION;
  }

  forceRefresh(): boolean {
    // Check cooldown to prevent spam
    const lastForceRefresh = this.currentState.lastForceRefresh;
    if (lastForceRefresh && Date.now() - lastForceRefresh.getTime() < this.FORCE_REFRESH_COOLDOWN) {
      return false; // Cooldown active
    }

    const newState = {
      ...this.currentState,
      lastDashboardUpdate: null,
      lastForceRefresh: new Date(),
      cacheKey: this.generateDailyCacheKey()
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
    return true; // Refresh allowed
  }

  clearCache(): void {
    this.stateSubject.next(this.initialState);
    this.clearStateFromStorage();
  }

  private updateState(partialState: Partial<DashboardState>): void {
    const newState = { ...this.currentState, ...partialState };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  private generateDailyCacheKey(): string {
    const today = new Date();
    return `dashboard-${today.getFullYear()}-${today.getMonth()}-${today.getDate()}`;
  }

  private checkDailyCacheKey(): void {
    const currentCacheKey = this.generateDailyCacheKey();
    if (this.currentState.cacheKey !== currentCacheKey) {
      // Cache key has changed, update it and mark data as stale
      const newState = {
        ...this.currentState,
        cacheKey: currentCacheKey,
        lastDashboardUpdate: null, // Force refresh
        isDashboardLoading: false // Ensure loading state is reset
      };
      this.stateSubject.next(newState);
      this.saveStateToStorage(newState);
    }
  }

  canForceRefresh(): boolean {
    const lastForceRefresh = this.currentState.lastForceRefresh;
    if (!lastForceRefresh) return true;
    return Date.now() - lastForceRefresh.getTime() >= this.FORCE_REFRESH_COOLDOWN;
  }

  getTimeUntilNextRefresh(): number {
    const lastForceRefresh = this.currentState.lastForceRefresh;
    if (!lastForceRefresh) return 0;
    const timeElapsed = Date.now() - lastForceRefresh.getTime();
    return Math.max(0, this.FORCE_REFRESH_COOLDOWN - timeElapsed);
  }

  getCacheInfo(): {
    isStale: boolean;
    lastUpdate: Date | null;
    cacheKey: string;
    canRefresh: boolean;
    timeUntilRefresh: number;
  } {
    return {
      isStale: this.isDashboardStale(),
      lastUpdate: this.currentState.lastDashboardUpdate,
      cacheKey: this.currentState.cacheKey,
      canRefresh: this.canForceRefresh(),
      timeUntilRefresh: this.getTimeUntilNextRefresh()
    };
  }

  private saveStateToStorage(state: DashboardState): void {
    try {
      const stateToSave = {
        ...state,
        lastDashboardUpdate: state.lastDashboardUpdate?.toISOString(),
        lastForceRefresh: state.lastForceRefresh?.toISOString(),
        dashboardData: state.dashboardData ? {
          ...state.dashboardData,
          lastUpdated: state.dashboardData.lastUpdated?.toISOString()
        } : null
      };
      localStorage.setItem('dashboard-state', JSON.stringify(stateToSave));
    } catch (error) {
      console.warn('Failed to save dashboard state to localStorage:', error);
    }
  }

  private loadStateFromStorage(): void {
    try {
      const savedState = localStorage.getItem('dashboard-state');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        const restoredState: DashboardState = {
          ...this.initialState,
          ...parsedState,
          lastDashboardUpdate: parsedState.lastDashboardUpdate ? new Date(parsedState.lastDashboardUpdate) : null,
          lastForceRefresh: parsedState.lastForceRefresh ? new Date(parsedState.lastForceRefresh) : null,
          cacheKey: parsedState.cacheKey || this.generateDailyCacheKey(),
          dashboardData: parsedState.dashboardData ? {
            ...parsedState.dashboardData,
            lastUpdated: parsedState.dashboardData.lastUpdated ? new Date(parsedState.dashboardData.lastUpdated) : null
          } : null,
          // Reset loading state on app start
          isDashboardLoading: false
        };
        this.stateSubject.next(restoredState);
      }
    } catch (error) {
      console.warn('Failed to load dashboard state from localStorage:', error);
    }
  }

  private clearStateFromStorage(): void {
    try {
      localStorage.removeItem('dashboard-state');
    } catch (error) {
      console.warn('Failed to clear dashboard state from localStorage:', error);
    }
  }
}
