import { PlayerDTO } from "@pro-clubs-manager/shared-dtos";
import { IPlayer, IPlayerStats, ITransferHistoryEntry } from "../models/player/player";
import { ITeam } from "../models/team";

// Extended PlayerDTO with transfer history (until shared DTOs are updated)
interface PlayerDTOWithTransferHistory extends PlayerDTO {
  transferHistory: ITransferHistoryEntry[];
}

export class PlayerMapper {
  static async mapToDto(player: IPlayer): Promise<PlayerDTOWithTransferHistory> {
    if (!player) {
      throw new Error("Player object is null or undefined");
    }

    const { team } = await player.populate<{ team: ITeam }>({ path: "team", select: "id name imgUrl" });

    const playerStats = this.calculatePlayerStats(player);
    return {
      id: player.id,
      name: player.name,
      imgUrl: player.imgUrl,
      age: player.age,
      position: player.position,
      playablePositions: player.playablePositions,
      stats: playerStats,
      transferHistory: player.transferHistory || [],
      team: team
        ? {
            id: team.id,
            name: team.name,
            imgUrl: team.imgUrl,
          }
        : undefined,
    };
  }

  static async mapToDtos(players: IPlayer[]): Promise<PlayerDTOWithTransferHistory[]> {
    if (!players) {
      throw new Error("Players object is null or undefined");
    }
    return await Promise.all(players.map((player) => this.mapToDto(player)));
  }

  static async mapToDtoWithStats(
    player: IPlayer,
    currentSeasonStats: { goals: number; assists: number; games: number } | null
  ): Promise<PlayerDTOWithTransferHistory> {
    if (!player) {
      throw new Error("Player object is null or undefined");
    }

    const { team } = await player.populate<{ team: ITeam }>({ path: "team", select: "id name imgUrl" });

    // Use the provided current season stats if available, otherwise fall back to the old method
    const playerStats = currentSeasonStats
      ? this.calculatePlayerStatsWithCurrentSeason(player, currentSeasonStats)
      : this.calculatePlayerStats(player);

    return {
      id: player.id,
      name: player.name,
      imgUrl: player.imgUrl,
      age: player.age,
      position: player.position,
      playablePositions: player.playablePositions,
      stats: playerStats,
      transferHistory: player.transferHistory || [],
      team: team
        ? {
            id: team.id,
            name: team.name,
            imgUrl: team.imgUrl,
          }
        : undefined,
    };
  }

  public static calculatePlayerStats(player: IPlayer): IPlayerStats {
    const { seasonsHistory, currentSeason } = player;

    const stats: IPlayerStats = {
      games: 0,
      goals: 0,
      assists: 0,
      cleanSheets: 0,
      playerOfTheMatch: 0,
      avgRating: 0,
    };

    if (!currentSeason) return stats;

    let totalRating = 0;
    let totalGames = 0;

    // Add current season stats
    if (currentSeason.stats.games > 0) {
      stats.games += currentSeason.stats.games;
      stats.goals += currentSeason.stats.goals;
      stats.assists += currentSeason.stats.assists;
      stats.cleanSheets += currentSeason.stats.cleanSheets;
      stats.playerOfTheMatch += currentSeason.stats.playerOfTheMatch;

      totalRating += currentSeason.stats.avgRating * currentSeason.stats.games;
      totalGames += currentSeason.stats.games;
    }

    // Add historical season stats
    seasonsHistory
      .filter((season) => season.league.equals(currentSeason.league) && season.seasonNumber === currentSeason.seasonNumber)
      .forEach((season) => {
        if (season.stats.games > 0) {
          stats.games += season.stats.games;
          stats.goals += season.stats.goals;
          stats.assists += season.stats.assists;
          stats.cleanSheets += season.stats.cleanSheets;
          stats.playerOfTheMatch += season.stats.playerOfTheMatch;

          totalRating += season.stats.avgRating * season.stats.games;
          totalGames += season.stats.games;
        }
      });

    // Calculate average rating
    if (totalGames > 0) {
      stats.avgRating = totalRating / totalGames;
    }

    return stats;
  }

  /**
   * Calculate player stats using the provided current season stats from game-by-game aggregation
   * This method handles mid-season transfers correctly
   */
  public static calculatePlayerStatsWithCurrentSeason(
    player: IPlayer,
    currentSeasonStats: { goals: number; assists: number; games: number }
  ): IPlayerStats {
    const { seasonsHistory, currentSeason } = player;

    const stats: IPlayerStats = {
      games: currentSeasonStats.games,
      goals: currentSeasonStats.goals,
      assists: currentSeasonStats.assists,
      cleanSheets: 0,
      playerOfTheMatch: 0,
      avgRating: 0,
    };

    if (!currentSeason) return stats;

    let totalRating = 0;
    let totalGames = 0;

    // For other stats (cleanSheets, POTM, avgRating), we still need to combine currentSeason and seasonsHistory
    // since the game aggregation doesn't include these stats yet

    // Add current season stats for non-goal/assist stats
    if (currentSeason.stats.games > 0) {
      stats.cleanSheets += currentSeason.stats.cleanSheets;
      stats.playerOfTheMatch += currentSeason.stats.playerOfTheMatch;

      totalRating += currentSeason.stats.avgRating * currentSeason.stats.games;
      totalGames += currentSeason.stats.games;
    }

    // Add historical season stats for the same season (from previous teams)
    seasonsHistory
      .filter((season) => season.league.equals(currentSeason.league) && season.seasonNumber === currentSeason.seasonNumber)
      .forEach((season) => {
        if (season.stats.games > 0) {
          stats.cleanSheets += season.stats.cleanSheets;
          stats.playerOfTheMatch += season.stats.playerOfTheMatch;

          totalRating += season.stats.avgRating * season.stats.games;
          totalGames += season.stats.games;
        }
      });

    // Calculate average rating
    if (totalGames > 0) {
      stats.avgRating = totalRating / totalGames;
    }

    return stats;
  }
}
