<div class="brackets-container">
  <!-- Header -->
  <div class="header">
    <div class="header-content">
      <h1>
        <i class="fas fa-trophy"></i>
        Playoff Brackets
      </h1>
      <div class="header-actions">
        <div class="season-selector">
          <label for="seasonSelect">Season:</label>
          <select 
            id="seasonSelect"
            [value]="selectedSeason || ''"
            (change)="onSeasonChange(+$any($event.target).value)"
            class="form-select">
            <option *ngFor="let season of availableSeasons" [value]="season">
              Season {{ season }}
            </option>
          </select>
        </div>
        
        <div class="view-toggle">
          <button 
            class="toggle-btn"
            [class.active]="viewMode === 'bracket'"
            (click)="onViewModeChange('bracket')">
            <i class="fas fa-sitemap"></i>
            Bracket
          </button>
          <button 
            class="toggle-btn"
            [class.active]="viewMode === 'list'"
            (click)="onViewModeChange('list')">
            <i class="fas fa-list"></i>
            List
          </button>
        </div>

        <button 
          class="refresh-btn"
          (click)="refreshBracket()"
          [disabled]="loading">
          <i class="fas fa-sync-alt" [class.spinning]="loading"></i>
          Refresh
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="loading">
    <i class="fas fa-spinner fa-spin"></i>
    <p>Loading playoff bracket...</p>
  </div>

  <!-- Bracket Content -->
  <div class="bracket-content" *ngIf="!loading && bracket && hasPlayoffData()">
    <!-- Progress Overview -->
    <div class="progress-overview">
      <div class="progress-card">
        <h3>Tournament Progress</h3>
        <div class="progress-bar">
          <div class="progress-fill" [style.width.%]="getCompletionPercentage()"></div>
        </div>
        <p>{{ getCompletionPercentage() }}% Complete</p>
      </div>

      <div class="next-match-card" *ngIf="getNextMatch() as nextMatch">
        <h3>Next Match</h3>
        <div class="next-match-info">
          <div class="teams">
            <span class="team">{{ nextMatch.homeTeam.name }}</span>
            <span class="vs">vs</span>
            <span class="team">{{ nextMatch.awayTeam.name }}</span>
          </div>
          <div class="match-details">
            <span class="stage">{{ getStageDisplayName(nextMatch.playoffStage) }}</span>
            <span class="date" *ngIf="nextMatch.date">{{ formatMatchDate(nextMatch.date) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Championship Results -->
    <div class="championship-results" *ngIf="hasChampionshipResults()">
      <h2>{{ getChampionshipTitle() }}</h2>
      <div class="podium">
        <div class="podium-position champion" *ngIf="bracket.champion">
          <div class="position-icon">
            <i class="fas fa-crown"></i>
          </div>
          <img [src]="getTeamImage(bracket.champion)" [alt]="bracket.champion.name" class="team-logo">
          <h3>{{ bracket.champion.name }}</h3>
          <span class="position-label">Champion</span>
        </div>

        <div class="podium-position runner-up" *ngIf="bracket.runnerUp">
          <div class="position-icon">
            <i class="fas fa-medal"></i>
          </div>
          <img [src]="getTeamImage(bracket.runnerUp)" [alt]="bracket.runnerUp.name" class="team-logo">
          <h3>{{ bracket.runnerUp.name }}</h3>
          <span class="position-label">Runner-up</span>
        </div>

        <div class="podium-position third-place" *ngIf="bracket.thirdPlace">
          <div class="position-icon">
            <i class="fas fa-award"></i>
          </div>
          <img [src]="getTeamImage(bracket.thirdPlace)" [alt]="bracket.thirdPlace.name" class="team-logo">
          <h3>{{ bracket.thirdPlace.name }}</h3>
          <span class="position-label">3rd Place</span>
        </div>
      </div>
    </div>

    <!-- Bracket View -->
    <div class="bracket-view" *ngIf="viewMode === 'bracket'">
      <!-- Tournament Tree Structure -->
      <div class="tournament-tree">
        <!-- Quarter-Finals (Left Side) -->
        <div class="bracket-column quarter-finals-left">
          <div class="column-header">
            <h3>Quarter-Finals</h3>
            <span class="series-info" *ngIf="getSeriesFormat('QUARTER_FINAL')">({{ getSeriesFormat('QUARTER_FINAL') }})</span>
          </div>
          <div class="matches-column">
            <div class="bracket-match" *ngFor="let series of getLeftSideQuarterFinals(); trackBy: trackBySeries"
                 [class.completed]="isSeriesCompleted(series)">
              <div class="series-header">
                <span class="series-title">{{ series.title }}</span>
                <span class="series-score" *ngIf="getSeriesScore(series) as score">{{ score }}</span>
              </div>
              <div class="match-teams">
                <div class="team" [class.series-winner]="getSeriesWinner(series) === series.homeTeam.id">
                  <img [src]="series.homeTeam.imgUrl || '/assets/icons/default-team.png'"
                       [alt]="series.homeTeam.name" class="team-logo">
                  <span class="team-name">{{ series.homeTeam.name }}</span>
                </div>
                <div class="vs-separator">vs</div>
                <div class="team" [class.series-winner]="getSeriesWinner(series) === series.awayTeam.id">
                  <span class="team-name">{{ series.awayTeam.name }}</span>
                  <img [src]="series.awayTeam.imgUrl || '/assets/icons/default-team.png'"
                       [alt]="series.awayTeam.name" class="team-logo">
                </div>
              </div>
              <div class="series-matches">
                <div class="match-result"
                     *ngFor="let game of getIndividualGames(series); let i = index"
                     [class.completed]="game.status === 'Played' || game.status === 'Completed'"
                     (click)="goToGameDetails(game.id)"
                     [style.cursor]="'pointer'">
                  <span class="match-score" *ngIf="game.result">
                    {{ getGameResult(game, series.homeTeam.id) }}
                  </span>
                  <span class="match-status" *ngIf="!game.result">{{ getFormattedStatus(game.status) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Semi-Finals (Left Side) -->
        <div class="bracket-column semi-finals-left">
          <div class="column-header">
            <h3>Semi-Finals</h3>
          </div>
          <div class="matches-column">
            <div class="bracket-match placeholder">
              <div class="match-teams">
                <div class="team tbd">
                  <span class="team-name">TBD</span>
                </div>
                <div class="vs-separator">vs</div>
                <div class="team tbd">
                  <span class="team-name">TBD</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Final -->
        <div class="bracket-column final">
          <div class="column-header">
            <h3>Final</h3>
          </div>
          <div class="matches-column">
            <div class="bracket-match final-match placeholder">
              <div class="match-teams">
                <div class="team tbd">
                  <span class="team-name">TBD</span>
                </div>
                <div class="vs-separator">vs</div>
                <div class="team tbd">
                  <span class="team-name">TBD</span>
                </div>
              </div>
              <div class="trophy-icon">
                <i class="fas fa-trophy"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Semi-Finals (Right Side) -->
        <div class="bracket-column semi-finals-right">
          <div class="column-header">
            <h3>Semi-Finals</h3>
          </div>
          <div class="matches-column">
            <div class="bracket-match placeholder">
              <div class="match-teams">
                <div class="team tbd">
                  <span class="team-name">TBD</span>
                </div>
                <div class="vs-separator">vs</div>
                <div class="team tbd">
                  <span class="team-name">TBD</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quarter-Finals (Right Side) -->
        <div class="bracket-column quarter-finals-right">
          <div class="column-header">
            <h3>Quarter-Finals</h3>
            <span class="series-info" *ngIf="getSeriesFormat('QUARTER_FINAL')">({{ getSeriesFormat('QUARTER_FINAL') }})</span>
          </div>
          <div class="matches-column">
            <div class="bracket-match" *ngFor="let series of getRightSideQuarterFinals(); trackBy: trackBySeries"
                 [class.completed]="isSeriesCompleted(series)">
              <div class="series-header">
                <span class="series-title">{{ series.title }}</span>
                <span class="series-score" *ngIf="getSeriesScore(series) as score">{{ score }}</span>
              </div>
              <div class="match-teams">
                <div class="team" [class.series-winner]="getSeriesWinner(series) === series.homeTeam.id">
                  <img [src]="series.homeTeam.imgUrl || '/assets/icons/default-team.png'"
                       [alt]="series.homeTeam.name" class="team-logo">
                  <span class="team-name">{{ series.homeTeam.name }}</span>
                </div>
                <div class="vs-separator">vs</div>
                <div class="team" [class.series-winner]="getSeriesWinner(series) === series.awayTeam.id">
                  <span class="team-name">{{ series.awayTeam.name }}</span>
                  <img [src]="series.awayTeam.imgUrl || '/assets/icons/default-team.png'"
                       [alt]="series.awayTeam.name" class="team-logo">
                </div>
              </div>
              <div class="series-matches">
                <div class="match-result"
                     *ngFor="let game of getIndividualGames(series); let i = index"
                     [class.completed]="game.status === 'Played' || game.status === 'Completed'"
                     (click)="goToGameDetails(game.id)"
                     [style.cursor]="'pointer'">
                  <span class="match-score" *ngIf="game.result">
                    {{ getGameResult(game, series.homeTeam.id) }}
                  </span>
                  <span class="match-status" *ngIf="!game.result">{{ getFormattedStatus(game.status) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>

    <!-- List View -->
    <div class="list-view" *ngIf="viewMode === 'list'">
      <!-- Stage Filter -->
      <div class="stage-filter">
        <h3>Filter by Stage:</h3>
        <div class="filter-buttons">
          <button 
            class="filter-btn"
            [class.active]="selectedStage === null"
            (click)="onStageSelect(null)">
            All Stages
          </button>
          <button 
            *ngFor="let stage of bracket.stages"
            class="filter-btn"
            [class.active]="selectedStage === stage.stage"
            [class]="'btn-' + getStageColor(stage.stage)"
            (click)="onStageSelect(stage.stage)">
            <i [class]="getStageIcon(stage.stage)"></i>
            {{ stage.displayName }}
          </button>
        </div>
      </div>

      <!-- Matches List -->
      <div class="matches-list">
        <div class="stage-section" *ngFor="let stage of getFilteredStages()">
          <h3 class="stage-title">
            <i [class]="getStageIcon(stage.stage)"></i>
            {{ stage.displayName }}
          </h3>

          <div class="match-card" *ngFor="let match of stage.matches" (click)="goToGameDetails(match.id)">
            <div class="match-header">
              <div class="match-teams-horizontal">
                <div class="team-info">
                  <img [src]="getTeamImage(match.homeTeam)" [alt]="match.homeTeam.name" class="team-logo">
                  <span class="team-name">{{ match.homeTeam.name }}</span>
                </div>
                
                <div class="match-score">
                  {{ getMatchScore(match) }}
                </div>
                
                <div class="team-info">
                  <span class="team-name">{{ match.awayTeam.name }}</span>
                  <img [src]="getTeamImage(match.awayTeam)" [alt]="match.awayTeam.name" class="team-logo">
                </div>
              </div>

              <div class="match-status-info">
                <span class="status-badge" [class]="'status-' + getMatchStatusColor(match)">
                  <i [class]="getMatchStatusIcon(match)"></i>
                  {{ getFormattedStatus(match.status) }}
                </span>
              </div>
            </div>

            <div class="match-details" *ngIf="match.date">
              <span class="match-date">{{ formatMatchDate(match.date) }}</span>
              <div class="match-actions">
                <button 
                  class="action-btn"
                  (click)="$event.stopPropagation(); shareMatch(match)">
                  <i class="fas fa-share-alt"></i>
                  Share
                </button>
                <button 
                  class="action-btn"
                  *ngIf="match.broadcast?.streamUrl"
                  (click)="$event.stopPropagation()">
                  <i class="fas fa-video"></i>
                  Watch
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!loading && (!bracket || !hasPlayoffData())">
    <i class="fas fa-trophy"></i>
    <h3>🏆 Season {{ selectedSeason }} Playoffs Coming Soon!</h3>
    <p>The playoff bracket for Season {{ selectedSeason }} hasn't been created yet.</p>
    <p>Get ready for the most exciting matches of the season!</p>
    <p>Check back soon for the tournament bracket, match schedules, and live results.</p>
  </div>
</div>
