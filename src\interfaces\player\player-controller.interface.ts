import { NextFunction, Request, Response } from "express";

export interface IPlayerController {
  createPlayer(req: Request, res: Response, next: NextFunction): Promise<void>;

  renamePlayer(req: Request, res: Response, next: NextFunction): Promise<void>;
  editPlayerAge(req: Request, res: Response, next: NextFunction): Promise<void>;
  editPlayerPosition(req: Request, res: Response, next: NextFunction): Promise<void>;
  setPlayerImage(req: Request, res: Response, next: NextFunction): Promise<void>;

  getPlayerById(req: Request, res: Response, next: NextFunction): Promise<void>;
  getAllPlayers(req: Request, res: Response, next: NextFunction): Promise<void>;

  getPlayerStatsByPosition(req: Request, res: Response, next: NextFunction): Promise<void>;
  getPlayerForm(req: Request, res: Response, next: NextFunction): Promise<void>;

  getFreeAgents(req: Request, res: Response, next: NextFunction): Promise<void>;

  deletePlayer(req: Request, res: Response, next: NextFunction): Promise<void>;

  playerSearchByText(req: Request, res: Response, next: NextFunction): Promise<void>;
  getTransferHistoryByPlayerId(req: Request, res: Response, next: NextFunction): Promise<void>;
  comparePlayersById(req: Request, res: Response, next: NextFunction): Promise<void>;
  getPlayerSeasonHistory(req: Request, res: Response, next: NextFunction): Promise<void>;
  deletePlayerSeasonHistory(req: Request, res: Response, next: NextFunction): Promise<void>;

}