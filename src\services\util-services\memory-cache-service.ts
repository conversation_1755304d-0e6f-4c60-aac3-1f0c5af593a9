import { injectable } from "tsyringe";
import logger from "../../config/logger";
import { CacheService } from "../../interfaces/util-services/cache-service.interface";

interface CacheItem {
  value: any;
  expiresAt?: number;
}

@injectable()
export class MemoryCacheService implements CacheService {
  private cache: Map<string, CacheItem> = new Map();

  async set(key: string, value: any, expiresIn?: number): Promise<void> {
    try {
      const item: CacheItem = {
        value: JSON.stringify(value),
        expiresAt: expiresIn ? Date.now() + (expiresIn * 1000) : undefined
      };
      
      this.cache.set(key, item);
      logger.info(`MemoryCache: Set key ${key} with expiry ${expiresIn || 'none'}`);
    } catch (error) {
      logger.error(`MemoryCache: Error setting key ${key}:`, error);
    }
  }

  async get(key: string): Promise<any | null> {
    try {
      const item = this.cache.get(key);
      
      if (!item) {
        logger.info(`MemoryCache: Cache miss for key ${key}`);
        return null;
      }

      // Check if item has expired
      if (item.expiresAt && Date.now() > item.expiresAt) {
        this.cache.delete(key);
        logger.info(`MemoryCache: Cache expired for key ${key}`);
        return null;
      }

      logger.info(`MemoryCache: Cache hit for key ${key}`);
      return item.value;
    } catch (error) {
      logger.error(`MemoryCache: Error getting key ${key}:`, error);
      return null;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      this.cache.delete(key);
      logger.info(`MemoryCache: Deleted key ${key}`);
    } catch (error) {
      logger.error(`MemoryCache: Error deleting key ${key}:`, error);
    }
  }

  async quit(): Promise<void> {
    this.cache.clear();
    logger.info("MemoryCache: Cleared all cache entries");
  }

  // Utility method to clean up expired entries
  private cleanupExpired(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (item.expiresAt && now > item.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  // Start periodic cleanup (call this in constructor if needed)
  startPeriodicCleanup(intervalMs: number = 5 * 60 * 1000): void {
    setInterval(() => {
      this.cleanupExpired();
    }, intervalMs);
  }
}
