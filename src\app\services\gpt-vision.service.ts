import { Injectable } from '@angular/core';
import { ConfigurationService } from './configuration.service';
import { NotificationService } from './notification.service';

export interface PlayerData {
  position: string;
  name: string;
  rating: string;
  goals: string;
  assists: string;
}

export interface GameAnalysisResult {
  success: boolean;
  teamName?: string;
  teamRating?: string;
  players?: PlayerData[];
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class GptVisionService {

  constructor(
    private configService: ConfigurationService,
    private notificationService: NotificationService
  ) { }

  /**
   * Analyzes a FIFA Pro Clubs game screenshot using GPT-4 Vision
   * @param imageFile The image file to analyze
   * @returns Promise with extracted player data
   */
  async analyzeGameScreenshot(imageFile: File): Promise<GameAnalysisResult> {
    try {
      // Convert image to base64
      const base64Image = await this.fileToBase64(imageFile);
      
      // Prepare the request payload
      const payload = {
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `Analyze this FIFA Pro Clubs game screenshot and extract player data. 
                
                Return ONLY a valid JSON object with this exact structure:
                {
                  "success": true,
                  "teamName": "team name from image",
                  "teamRating": "overall team rating",
                  "players": [
                    {
                      "position": "position code (e.g., RCB, LS, RS, LM, etc.)",
                      "name": "player name",
                      "rating": "player rating (e.g., 7.6)",
                      "goals": "number of goals",
                      "assists": "number of assists"
                    }
                  ]
                }
                
                Important:
                - Extract ALL 11 players from the table
                - Use exact position codes from the image (RCB, LS, RS, LM, LCM, RCM, RM, CDM, LCB, CB, GK, etc.)
                - Include exact player names as shown
                - Convert all numeric values to strings
                - If you cannot read the image clearly, return: {"success": false, "error": "Could not analyze image"}
                
                Return ONLY the JSON, no other text.`
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/jpeg;base64,${base64Image}`
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      };

      // Make API call to OpenAI
      const config = this.configService.getOpenAIConfig();

      // Validate API key
      if (!config.apiKey || !config.apiKey.startsWith('sk-')) {
        throw new Error('Invalid or missing OpenAI API key. Please check your environment configuration.');
      }

      const response = await fetch(config.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('OpenAI API Error Details:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText,
          url: config.apiUrl,
          hasApiKey: !!config.apiKey
        });
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      
      // Extract the response content
      const content = data.choices?.[0]?.message?.content;
      if (!content) {
        throw new Error('No content received from OpenAI API');
      }

      // Parse the JSON response
      try {
        const result: GameAnalysisResult = JSON.parse(content);
        
        // Validate the response structure
        if (result.success && result.players && result.players.length > 0) {
          this.notificationService.aiSuccess(`Successfully extracted data for ${result.players.length} players!`);
          return result;
        } else {
          return {
            success: false,
            error: result.error || 'Failed to extract player data from image'
          };
        }
      } catch (parseError) {
        console.error('Failed to parse OpenAI response:', content);
        return {
          success: false,
          error: 'Failed to parse AI response. Please try again.'
        };
      }

    } catch (error) {
      console.error('Error analyzing image:', error);
      this.notificationService.aiError('Failed to analyze image. Please check your API key and try again.');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Converts a File to base64 string
   * @param file The file to convert
   * @returns Promise with base64 string (without data URL prefix)
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix (e.g., "data:image/jpeg;base64,")
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Validates if the extracted data looks reasonable
   * @param players Array of player data
   * @returns boolean indicating if data is valid
   */
  validatePlayerData(players: PlayerData[]): boolean {
    if (!players || players.length === 0) {
      return false;
    }

    // Check if we have reasonable number of players (should be 11 for full team)
    if (players.length > 11) {
      console.warn('More than 11 players detected, this might be incorrect');
    }

    // Validate each player has required fields
    return players.every(player => 
      player.position && 
      player.name && 
      player.rating &&
      player.goals !== undefined &&
      player.assists !== undefined
    );
  }
}
