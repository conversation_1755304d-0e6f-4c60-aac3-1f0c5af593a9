<div class="fixture-card"
     [class.status-scheduled]="game.status === GAME_STATUS.SCHEDULED"
     [class.status-played]="game.status === GAME_STATUS.PLAYED"
     [class.status-completed]="game.status === GAME_STATUS.COMPLETED"
     [class.playoff-match]="isPlayoffMatch()"
     [class.updating]="gameUpdateState === 'updating'"
     [class.update-success]="gameUpdateState === 'success'"
     [class.update-error]="gameUpdateState === 'error'">
    <!-- Match Header -->
    <div class="match-header">
        <div class="match-status">
            <span class="status-badge">{{ getGameStatusText() }}</span>
            <!-- <span class="playoff-badge" *ngIf="isPlayoffMatch()">PLAYOFF</span> -->
            <span class="match-number-badge" *ngIf="isPlayoffMatch() && game.matchNumber">{{ getMatchDisplayText() }}</span>
        </div>
        <div class="match-date">
            {{ game.date | date:'dd/MM HH:mm' }}
        </div>
    </div>

    <!-- Teams Section -->
    <div class="teams-section">
        <!-- Home Team -->
        <div class="team home-team">
            <div class="team-info">
                <img [src]="game.homeTeam.imgUrl || 'assets/Icons/Team.jpg'" 
                     [alt]="game.homeTeam.name" 
                     class="team-logo">
                <span class="team-name">{{ game.homeTeam.name }}</span>
            </div>
        </div>

        <!-- Score Section -->
        <div class="score-section">
            <!-- Update State Indicator -->
            <div class="update-state-indicator" *ngIf="gameUpdateState">
                <i [class]="getUpdateStateIcon()"></i>
                <span class="update-text" *ngIf="isUpdating()">Updating...</span>
                <span class="update-text" *ngIf="isUpdateSuccess()">Updated!</span>
                <span class="update-text" *ngIf="isUpdateError()">Failed</span>
            </div>

            <div class="score-display" *ngIf="!isCurrentlyEditing">
                <div class="score" *ngIf="hasResult()">
                    <span class="home-score">{{ game.result!.homeTeamGoals }}</span>
                    <span class="score-separator">-</span>
                    <span class="away-score">{{ game.result!.awayTeamGoals }}</span>
                </div>
                <div class="no-score" *ngIf="!hasResult()">
                    <span class="vs-text">VS</span>
                </div>
            </div>

            <!-- Edit Score Inputs -->
            <div class="score-edit" *ngIf="isCurrentlyEditing">
                <input type="number" 
                       class="score-input home-input"
                       [value]="homeTeamGoals"
                       (input)="onHomeGoalsInputChange($event)"
                       min="0" max="20">
                <span class="score-separator">-</span>
                <input type="number" 
                       class="score-input away-input"
                       [value]="awayTeamGoals"
                       (input)="onAwayGoalsInputChange($event)"
                       min="0" max="20">
            </div>
        </div>

        <!-- Away Team -->
        <div class="team away-team">
            <div class="team-info">
                <img [src]="game.awayTeam.imgUrl || 'assets/Icons/Team.jpg'" 
                [alt]="game.awayTeam.name" 
                class="team-logo">
                <span class="team-name">{{ game.awayTeam.name }}</span>
            </div>
        </div>
    </div>

    <!-- Time Edit Section -->
    <div class="time-edit-section" *ngIf="isCurrentlyEditingTime">
        <div class="time-edit-inputs">
            <div class="time-input-group">
                <label>Date</label>
                <input type="date"
                       [value]="editingGameDate"
                       (input)="onEditingGameDateChange($event)"
                       class="time-input">
            </div>
            <div class="time-input-group">
                <label>Time</label>
                <input type="time"
                       [value]="editingGameTime"
                       (input)="onEditingGameTimeChange($event)"
                       class="time-input">
            </div>
        </div>
        <div class="time-edit-actions">
            <button class="action-btn save-btn" (click)="onSaveTimeClick()">
                <i class="fas fa-save"></i>
                <span>Save</span>
            </button>
            <button class="action-btn cancel-btn" (click)="onCancelTimeEditClick()">
                <i class="fas fa-times"></i>
                <span>Cancel</span>
            </button>
        </div>
    </div>

    <!-- Actions Section -->
    <div class="actions-section">
        <!-- Game Details Button -->
        <button class="action-btn details-btn"
                (click)="onGameDetailsClick()"
                title="View Game Details">
            <i class="fas fa-eye"></i>
            <span>Details</span>
        </button>

        <!-- Edit/Save/Cancel Buttons -->
        <div class="edit-actions" *ngIf="canEdit$ | async">
            <!-- Edit Button -->
            <button class="action-btn edit-btn"
                    *ngIf="isAdmin && !isCurrentlyEditing"
                    (click)="onEditGameClick()"
                    title="Edit Result">
                <i class="fas fa-edit"></i>
                <span>Edit</span>
            </button>

            <!-- Clock Button for Date/Time Edit -->
            <button class="action-btn clock-btn"
                    *ngIf="isAdmin && !isCurrentlyEditing"
                    (click)="onEditTimeClick()"
                    title="Edit Date & Time">
                <i class="fas fa-clock"></i>
                <span>Time</span>
            </button>

            <!-- Save Button -->
            <button class="action-btn save-btn"
                    *ngIf="isCurrentlyEditing"
                    (click)="onSaveGameClick()"
                    title="Save Result">
                <i class="fas fa-save"></i>
                <span>Save</span>
            </button>

            <!-- Cancel Button -->
            <button class="action-btn cancel-btn"
                    *ngIf="isCurrentlyEditing"
                    (click)="onCancelGameClick()"
                    title="Cancel Edit">
                <i class="fas fa-times"></i>
                <span>Cancel</span>
            </button>
        </div>

        <!-- Live Stream Link -->
        <a class="action-btn stream-btn"
           *ngIf="game.broadcast?.streamUrl"
           [href]="game.broadcast!.streamUrl"
           target="_blank"
           title="Watch Live Stream">
            <i class="fas fa-play"></i>
            <span>Live</span>
        </a>
    </div>
</div>
