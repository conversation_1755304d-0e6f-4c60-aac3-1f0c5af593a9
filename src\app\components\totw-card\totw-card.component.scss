/* === STUNNING TOTW CARD DESIGN === */

.totw-card-container {
    position: relative;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));

    &:hover {
        transform: translateY(-12px) scale(1.08);
        filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.4));
    }
}

/* === FIFA ULTIMATE TEAM TOTW CARD SIZING === */
@media (min-width: 769px) {
    .totw-card-container {
        width: 180px;
        height: 300px;
    }

    .player-image {
        width: 100px;
        height: 100px;
    }

    .team-badge {
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 768px) and (min-width: 481px) {
    .totw-card-container {
        width: 160px;
        height: 260px;
    }

    .player-image {
        width: 90px;
        height: 90px;
    }

    .team-badge {
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 480px) {
    .totw-card-container {
        width: 120px;
        height: 180px;
    }

    .player-image {
        width: 60px;
        height: 60px;
    }

    .team-badge {
        width: 24px;
        height: 24px;
    }
}

/* === FIFA ULTIMATE TEAM TOTW CARD DESIGN === */
.card-main {
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg,
        #0a0a0a 0%,
        #1a1a1a 20%,
        #2d2d2d 40%,
        #1a1a1a 70%,
        #000000 100%);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 215, 0, 0.4);
    box-shadow:
        0 0 40px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 215, 0, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.5);

    @media (max-width: 480px) {
        padding: var(--spacing-sm);
        border-radius: var(--radius-lg);
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(145deg,
            rgba(255, 215, 0, 0.08) 0%,
            rgba(255, 215, 0, 0.04) 30%,
            transparent 50%,
            rgba(255, 215, 0, 0.02) 70%,
            rgba(255, 215, 0, 0.05) 100%);
        pointer-events: none;
        z-index: 1;
    }

    &::after {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(45deg,
            #FFD700,
            #FFA500,
            #FF8C00,
            #DAA520,
            #FFD700);
        background-size: 400% 400%;
        z-index: -1;
        border-radius: var(--radius-xl);
        animation: borderShimmer 4s ease-in-out infinite;
    }
}

@keyframes borderShimmer {
    0%, 100% {
        background-position: 0% 50%;
        opacity: 0.8;
    }
    50% {
        background-position: 100% 50%;
        opacity: 1;
    }
}

/* === PLAYER HEADER === */
.player-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    z-index: 2;
    position: relative;
}

.player-name {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    color: #FFD700;
    padding: 2px var(--spacing-xs);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-black);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8), 0 0 10px rgba(255, 215, 0, 0.3);
    max-width: 70%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border: 1px solid rgba(255, 215, 0, 0.4);

    @media (max-width: 480px) {
        font-size: 9px;
        padding: 1px 4px;
    }
}

.rating-badge {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    color: #000;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-black);
    text-shadow: none;
    box-shadow:
        0 2px 8px rgba(0, 255, 136, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.4);
    min-width: 24px;
    text-align: center;

    @media (max-width: 480px) {
        font-size: 9px;
        padding: 1px 4px;
        min-width: 20px;
    }
}

/* === PLAYER SECTION === */
.player-section {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: var(--spacing-xs) 0;
    z-index: 2;
    position: relative;
}

.player-image-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.player-image {
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.9);
    object-fit: cover;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(255, 215, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;

    &::before {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        background: linear-gradient(45deg, #FFD700, #FFA500, #FF8C00);
        border-radius: 50%;
        z-index: -1;
        animation: imageGlow 3s ease-in-out infinite;
    }
}

@keyframes imageGlow {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

.team-badge {
    position: absolute;
    bottom: -3px;
    right: -3px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.9);
    object-fit: cover;
    background: rgba(255, 255, 255, 0.95);
    box-shadow:
        0 2px 10px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(255, 215, 0, 0.4);
    z-index: 3;
    transition: all 0.3s ease;

    &:hover {
        transform: scale(1.1);
        box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.5),
            0 0 20px rgba(255, 215, 0, 0.6);
    }
}

/* === STATS SECTION === */
.stats-section {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 6px;
    margin-top: var(--spacing-lg);
    z-index: 2;
    position: relative;
    padding: 0 4px;

    @media (max-width: 768px) {
        gap: 4px;
        margin-top: var(--spacing-md);
        padding: 0 3px;
    }

    @media (max-width: 480px) {
        gap: 2px;
        padding: 0 2px;
    }
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-md);
    padding: 8px 6px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    flex: 1;
    min-height: 48px;
    justify-content: center;
    text-align: center;
    box-shadow:
        inset 0 1px 0 rgba(255, 215, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.3);

    @media (max-width: 768px) {
        padding: 6px 4px;
        min-height: 42px;
    }

    @media (max-width: 480px) {
        padding: 5px 3px;
        min-height: 38px;
    }
}

.stat-value {
    color: #FFD700;
    font-size: var(--text-sm);
    font-weight: var(--font-weight-black);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
    line-height: 1;
    margin-bottom: 1px;

    @media (max-width: 768px) {
        font-size: var(--text-xs);
    }

    @media (max-width: 480px) {
        font-size: 11px;
    }
}

.stat-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 9px;
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.3px;
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

    @media (max-width: 768px) {
        font-size: 8px;
    }

    @media (max-width: 480px) {
        font-size: 7px;
    }
}

/* === SPECIAL EFFECTS === */
.totw-card-container:hover {
    .card-main::after {
        animation-duration: 2s;
    }

    .player-image::before {
        animation-duration: 2s;
    }

    .rating-badge {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
        transform: scale(1.1);
    }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
    .totw-card-container {
        transition: none;
    }

    .totw-card-container:hover {
        transform: none;
        filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
    }

    .borderShimmer,
    .imageGlow {
        animation: none;
    }

    .card-main::after {
        animation: none;
    }

    .player-image::before {
        animation: none;
    }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
    .card-main {
        border: 4px solid var(--text-primary);
        background: linear-gradient(145deg, #FFD700, #FFA500);
    }

    .player-name,
    .stat-item {
        background: var(--surface-primary);
        border: 2px solid var(--text-primary);
        color: var(--text-primary);
    }

    .rating-badge {
        background: var(--surface-primary);
        color: var(--text-primary);
        border: 2px solid var(--text-primary);
    }

    .player-image,
    .team-badge {
        border-width: 4px;
        border-color: var(--text-primary);
    }

    .stat-value {
        color: #FFD700;
    }

    .stat-label {
        color: white;
    }
}