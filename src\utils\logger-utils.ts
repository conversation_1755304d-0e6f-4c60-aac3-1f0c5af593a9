import logger from "../config/logger";
import { IUser } from "../models/user";
import { Request } from "express";

export interface LogContext {
  user?: IUser;
  userId?: string;
  userName?: string;
}

/**
 * Enhanced logger utility that includes user information in logs
 */
export class LoggerUtils {
  
  /**
   * Extract user context from Express request
   */
  static getUserContext(req: Request): LogContext {
    const context: LogContext = {};
    
    if (req.user) {
      context.user = req.user;
    } else if (req.userId) {
      context.userId = req.userId;
    }
    
    return context;
  }

  /**
   * Create user context from user object
   */
  static createUserContext(user: IUser): LogContext {
    return { user };
  }

  /**
   * Create user context from user ID
   */
  static createUserContextFromId(userId: string): LogContext {
    return { userId };
  }

  /**
   * Create user context from user name
   */
  static createUserContextFromName(userName: string): LogContext {
    return { userName };
  }

  /**
   * Log info message with user context
   */
  static info(message: string, context?: LogContext, meta?: any): void {
    logger.info(message, { ...context, ...meta });
  }

  /**
   * Log error message with user context
   */
  static error(message: string, context?: LogContext, error?: any): void {
    const errorMeta = error ? { error: error.message || error, stack: error.stack } : {};
    logger.error(message, { ...context, ...errorMeta });
  }

  /**
   * Log warning message with user context
   */
  static warn(message: string, context?: LogContext, meta?: any): void {
    logger.warn(message, { ...context, ...meta });
  }

  /**
   * Log debug message with user context
   */
  static debug(message: string, context?: LogContext, meta?: any): void {
    logger.debug(message, { ...context, ...meta });
  }

  /**
   * Log info message with request context
   */
  static infoWithRequest(message: string, req: Request, meta?: any): void {
    const context = this.getUserContext(req);
    this.info(message, context, meta);
  }

  /**
   * Log error message with request context
   */
  static errorWithRequest(message: string, req: Request, error?: any): void {
    const context = this.getUserContext(req);
    this.error(message, context, error);
  }

  /**
   * Log warning message with request context
   */
  static warnWithRequest(message: string, req: Request, meta?: any): void {
    const context = this.getUserContext(req);
    this.warn(message, context, meta);
  }

  /**
   * Log user action (for audit trail)
   */
  static logUserAction(action: string, req: Request, details?: any): void {
    const context = this.getUserContext(req);
    const message = `User Action: ${action}`;
    this.info(message, context, { action, details, ip: req.ip, userAgent: req.get('User-Agent') });
  }

  /**
   * Log authentication events
   */
  static logAuth(event: string, user?: IUser, details?: any): void {
    const context = user ? this.createUserContext(user) : undefined;
    const message = `Auth Event: ${event}`;
    this.info(message, context, { event, details });
  }

  /**
   * Log database operations
   */
  static logDbOperation(operation: string, collection: string, context?: LogContext, details?: any): void {
    const message = `DB Operation: ${operation} on ${collection}`;
    this.info(message, context, { operation, collection, details });
  }

  /**
   * Log API requests
   */
  static logApiRequest(req: Request, responseTime?: number): void {
    const context = this.getUserContext(req);
    const message = `API Request: ${req.method} ${req.originalUrl}`;
    const meta = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      responseTime: responseTime ? `${responseTime}ms` : undefined
    };
    this.info(message, context, meta);
  }

  /**
   * Log service operations
   */
  static logServiceOperation(service: string, operation: string, context?: LogContext, details?: any): void {
    const message = `${service}: ${operation}`;
    this.info(message, context, { service, operation, details });
  }
}
