/* === COMBINED FIXTURE NAVIGATION === */
.fixture-navigation-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
    gap: var(--spacing-lg);

    .fixture-selector {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);

        label {
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            white-space: nowrap;
        }
    }

    .navigation-controls {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
    }

    .quick-nav {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);

        .nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--surface-secondary);
            color: var(--text-primary);
            font-size: var(--text-base);
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);

            &:hover:not(:disabled) {
                background: var(--primary);
                color: var(--text-on-primary);
                border-color: var(--primary);
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }

            &:active:not(:disabled) {
                transform: translateY(0);
                box-shadow: var(--shadow-sm);
            }

            &:disabled {
                opacity: 0.4;
                cursor: not-allowed;
                background: var(--surface-disabled);
                color: var(--text-disabled);
                border-color: var(--border-disabled);
            }

            i {
                font-size: var(--text-sm);
            }
        }

        .current-fixture {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--primary);
            color: var(--text-on-primary);
            border-radius: var(--radius-lg);
            font-size: var(--text-base);
            font-weight: var(--font-weight-semibold);
            min-width: 100px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--primary);
        }
    }

    .pagination-info {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-md);
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
        margin-left: var(--spacing-md);
    }

    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        align-items: stretch;

        .navigation-controls {
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .quick-nav {
            gap: var(--spacing-sm);
            justify-content: center;

            .nav-btn {
                width: 36px;
                height: 36px;
                font-size: var(--text-sm);
            }

            .current-fixture {
                padding: var(--spacing-sm) var(--spacing-md);
                font-size: var(--text-sm);
                min-width: 80px;
            }
        }

        .pagination-info {
            margin-left: 0;
            text-align: center;
        }
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .fixture-controls {
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
  }

  .fixture-navigation {
    padding: var(--spacing-sm);
    margin-top: var(--spacing-md);

    .quick-nav {
      gap: var(--spacing-xs);

      .nav-btn {
        width: 36px;
        height: 36px;
        font-size: var(--font-size-xs);
      }

      .current-fixture {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
        min-width: 60px;
      }
    }
  }
}

@media (max-width: 480px) {
  .fixture-navigation {
    .quick-nav {
      .nav-btn {
        width: 32px;
        height: 32px;
      }

      .current-fixture {
        font-size: var(--font-size-xs);
        min-width: 50px;
      }
    }
  }
}
