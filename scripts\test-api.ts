import axios from 'axios';

async function testTeamAchievementAPI() {
  try {
    console.log('Testing team achievement API...');
    
    // Test UTOPIA XI team achievements
    const utopiaResponse = await axios.get('http://localhost:3000/season-achievements/team/678907060ac8f44728a5e0dc/history');

    console.log('\n=== UTOPIA XI API Response ===');
    console.log('Status:', utopiaResponse.status);
    console.log('Data:', JSON.stringify(utopiaResponse.data, null, 2));

    // Test Guns N Roses team achievements
    const gunsResponse = await axios.get('http://localhost:3000/season-achievements/team/66058b5119a6c5698f4ba74b/history');
    
    console.log('\n=== Guns N Roses API Response ===');
    console.log('Status:', gunsResponse.status);
    console.log('Data:', JSON.stringify(gunsResponse.data, null, 2));
    
  } catch (error: any) {
    console.error('API Test Error:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
}

testTeamAchievementAPI();
