/* === ENHANCED NEWS COMPONENT === */

.news-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 5px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
    position: relative;

    &::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
        pointer-events: none;
        z-index: 0;
    }

    > * {
        position: relative;
        z-index: 1;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg) 5px;
        gap: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md) 5px;
        gap: var(--spacing-md);
    }
}

/* === NEWS HEADER === */
.news-header {
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-xl);
    text-align: center;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--info), var(--primary), var(--success));
        background-size: 200% 100%;
        animation: shimmer 3s ease-in-out infinite;
    }

    h1 {
        margin: 0;
        font-size: var(--text-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    p {
        margin: var(--spacing-md) 0 0 0;
        color: var(--text-secondary);
        font-size: var(--text-lg);
    }
}

/* === NEWS GRID === */
.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}

/* === ANIMATIONS === */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
