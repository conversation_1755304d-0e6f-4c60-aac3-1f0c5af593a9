.brackets-container {
  padding: 5px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
}

.header {
  margin-bottom: 2rem;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;

    h1 {
      margin: 0;
      color: var(--text-color);
      font-size: 2rem;
      font-weight: 700;

      i {
        margin-right: 0.75rem;
        color: var(--warning-color);
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex-wrap: wrap;

      .season-selector {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        label {
          font-weight: 600;
          color: var(--text-color);
        }

        .form-select {
          min-width: 120px;
          padding: 0.5rem;
          border: 1px solid var(--border-color);
          border-radius: 6px;
          background: var(--background-secondary);
          color: var(--text-color);
        }
      }

      .view-toggle {
        display: flex;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        overflow: hidden;

        .toggle-btn {
          padding: 0.5rem 1rem;
          border: none;
          background: var(--background-secondary);
          color: var(--text-color);
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 0.5rem;

          &:hover {
            background: var(--background-color);
          }

          &.active {
            background: var(--primary-color);
            color: white;
          }
        }
      }

      .refresh-btn {
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--background-secondary);
        color: var(--text-color);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          background: var(--primary-color);
          color: white;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;

  i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
  }

  p {
    font-size: 1.125rem;
    margin: 0;
    color: var(--text-color);
    font-weight: 500;
  }
}

.progress-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }

  .progress-card,
  .next-match-card {
    background: var(--background-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);

    h3 {
      margin: 0 0 1rem 0;
      color: var(--text-color);
      font-size: 1.125rem;
      font-weight: 600;
    }
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: var(--background-color);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--success-color), var(--primary-color));
      transition: width 0.3s ease;
    }
  }

  .next-match-info {
    .teams {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 0.5rem;

      .team {
        font-weight: 600;
        color: var(--text-color);
      }

      .vs {
        color: var(--primary-color);
        font-size: 0.875rem;
        font-weight: 600;
      }
    }

    .match-details {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;

      .stage {
        color: var(--primary-color);
        font-weight: 600;
        font-size: 0.875rem;
      }

      .date {
        color: var(--info-color);
        font-size: 0.8125rem;
        font-weight: 500;
      }
    }
  }
}

.championship-results {
  background: var(--background-secondary);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 3rem;
  border: 1px solid var(--border-color);

  h2 {
    text-align: center;
    margin: 0 0 2rem 0;
    color: var(--text-color);
    font-size: 1.5rem;
    font-weight: 700;
  }

  .podium {
    display: flex;
    justify-content: center;
    align-items: end;
    gap: 2rem;
    flex-wrap: wrap;

    .podium-position {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 1.5rem;
      border-radius: 12px;
      min-width: 150px;

      &.champion {
        background: linear-gradient(135deg, var(--warning-color), var(--warning-color-light));
        order: 2;
        transform: scale(1.1);
      }

      &.runner-up {
        background: linear-gradient(135deg, var(--text-secondary), var(--text-secondary-light));
        order: 1;
      }

      &.third-place {
        background: linear-gradient(135deg, var(--info-color), var(--info-color-light));
        order: 3;
      }

      .position-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: white;
      }

      .team-logo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        margin-bottom: 1rem;
        border: 3px solid white;
      }

      h3 {
        margin: 0 0 0.5rem 0;
        color: white;
        font-size: 1.125rem;
        font-weight: 600;
      }

      .position-label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.875rem;
        font-weight: 500;
      }
    }
  }
}

.bracket-view {
  .bracket-stages {
    display: flex;
    gap: 3rem;
    overflow-x: auto;
    padding: 2rem 0;
    min-height: 600px;

    .bracket-stage {
      flex-shrink: 0;
      position: relative;

      .stage-header {
        text-align: center;
        margin-bottom: 2rem;

        h3 {
          margin: 0 0 0.5rem 0;
          color: var(--text-color);
          font-size: 1.125rem;
          font-weight: 600;

          i {
            margin-right: 0.5rem;
            color: var(--primary-color);
          }
        }

        .series-format {
          color: var(--warning-color);
          font-size: 0.875rem;
          font-weight: 500;
          display: block;
          margin-bottom: 0.25rem;
        }

        .series-format {
          color: var(--warning-color);
          font-size: 0.875rem;
          font-weight: 500;
          display: block;
          margin-bottom: 0.25rem;
        }

        .match-count {
          color: var(--primary-color);
          font-size: 0.875rem;
          font-weight: 500;
        }
      }

      .stage-matches {
        position: relative;
        height: 500px;

        .bracket-match {
          position: absolute;
          width: 100%;
          background: var(--background-secondary);
          border: 1px solid var(--border-color);
          border-radius: 8px;
          padding: 1rem;
          cursor: pointer;
          transition: all 0.2s ease;
          transform: translateY(-50%);

          &:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          &.completed {
            border-color: var(--success-color);
          }

          .match-teams {
            margin-bottom: 0.75rem;

            .team {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              padding: 0.5rem;
              border-radius: 6px;
              transition: all 0.2s ease;

              &.winner {
                background: var(--success-color-light);
                color: var(--success-color);
                font-weight: 600;
              }

              .team-logo {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                object-fit: cover;
              }

              .team-name {
                flex: 1;
                font-size: 0.875rem;
              }

              .team-score {
                font-weight: 700;
                font-size: 1rem;
              }
            }

            .away-team {
              flex-direction: row-reverse;
            }

            .match-separator {
              text-align: center;
              padding: 0.25rem;
              color: var(--text-color);
              font-size: 0.875rem;
              font-weight: 600;
            }
          }

          .match-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8125rem;
            margin-bottom: 0.5rem;
            flex-wrap: wrap;
            gap: 0.5rem;

            .match-status {
              display: flex;
              align-items: center;
              gap: 0.25rem;
              padding: 0.25rem 0.5rem;
              border-radius: 6px;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              font-size: 0.7rem;
              font-weight: 500;

              &.status-success {
                background: var(--success-color-light);
                color: var(--success-color);
              }

              &.status-primary {
                background: var(--primary-color-light);
                color: var(--primary-color);
              }

              &.status-warning {
                background: var(--warning-color-light);
                color: var(--warning-color);
              }

              &.status-secondary {
                background: var(--info-100);
                color: var(--info-color);
              }
            }

            .match-number {
              background: var(--warning-color-light);
              color: var(--warning-color);
              padding: 0.25rem 0.5rem;
              border-radius: 12px;
              font-weight: 600;
              font-size: 0.75rem;
            }

            .match-date {
              color: var(--info-color);
              font-weight: 500;
            }
          }

          .match-actions {
            display: flex;
            gap: 0.5rem;
            opacity: 0;
            transition: opacity 0.2s ease;

            .action-btn {
              background: var(--background-secondary);
              border: 1px solid var(--primary-color);
              color: var(--primary-color);
              padding: 0.25rem 0.5rem;
              border-radius: 4px;
              cursor: pointer;
              font-size: 0.75rem;
              font-weight: 500;
              transition: all 0.2s ease;

              &:hover {
                background: var(--primary-color);
                color: white;
                border-color: var(--primary-color);
                transform: translateY(-1px);
              }
            }
          }

          &:hover .match-actions {
            opacity: 1;
          }
        }
      }
    }
  }
}

.list-view {
  .stage-filter {
    margin-bottom: 2rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--text-color);
      font-size: 1.125rem;
      font-weight: 600;
    }

    .filter-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;

      .filter-btn {
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 20px;
        background: var(--background-secondary);
        color: var(--text-color);
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;

        &:hover {
          border-color: var(--primary-color);
        }

        &.active {
          background: var(--primary-color);
          color: white;
          border-color: var(--primary-color);
        }

        // Stage-specific colors
        &.btn-success { border-color: var(--success-color); }
        &.btn-warning { border-color: var(--warning-color); }
        &.btn-danger { border-color: var(--danger-color); }
        &.btn-info { border-color: var(--info-color); }
      }
    }
  }

  .matches-list {
    .stage-section {
      margin-bottom: 2rem;

      .stage-title {
        margin: 0 0 1rem 0;
        color: var(--text-color);
        font-size: 1.25rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        i {
          color: var(--primary-color);
        }
      }

      .match-card {
        background: var(--background-secondary);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: var(--primary-color);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .match-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;

          .match-teams-horizontal {
            display: flex;
            align-items: center;
            gap: 1.5rem;

            .team-info {
              display: flex;
              align-items: center;
              gap: 0.75rem;

              .team-logo {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                object-fit: cover;
              }

              .team-name {
                font-weight: 600;
                color: var(--text-color);
              }
            }

            .match-score {
              font-size: 1.25rem;
              font-weight: 700;
              color: var(--primary-color);
              min-width: 60px;
              text-align: center;
            }
          }

          .match-status-info {
            .status-badge {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              padding: 0.5rem 1rem;
              border-radius: 20px;
              font-size: 0.875rem;
              font-weight: 600;

              &.status-success {
                background: var(--success-color-light);
                color: var(--success-color);
              }

              &.status-primary {
                background: var(--primary-color-light);
                color: var(--primary-color);
              }

              &.status-warning {
                background: var(--warning-color-light);
                color: var(--warning-color);
              }

              &.status-secondary {
                background: var(--info-100);
                color: var(--info-color);
              }
            }
          }
        }

        .match-details {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .match-date {
            color: var(--info-color);
            font-size: 0.875rem;
            font-weight: 500;
          }

          .match-actions {
            display: flex;
            gap: 0.5rem;

            .action-btn {
              padding: 0.5rem 1rem;
              border: 1px solid var(--border-color);
              border-radius: 6px;
              background: var(--background-color);
              color: var(--text-color);
              cursor: pointer;
              transition: all 0.2s ease;
              display: flex;
              align-items: center;
              gap: 0.5rem;
              font-size: 0.875rem;

              &:hover {
                background: var(--primary-color);
                color: white;
                border-color: var(--primary-color);
              }
            }
          }
        }
      }
    }
  }

  // Mobile styles for list view
  @media (max-width: 768px) {
    .stage-filter {
      margin-bottom: var(--spacing-lg);
      text-align: center;

      h3 {
        font-size: var(--font-size-lg);
        text-align: center;
        margin-bottom: var(--spacing-md);
      }

      .filter-buttons {
        justify-content: center;
        gap: var(--spacing-xs);

        .filter-btn {
          padding: var(--spacing-xs) var(--spacing-sm);
          font-size: var(--font-size-xs);
          border-radius: var(--radius-full);
          flex: 0 0 auto;
          min-width: 80px;
          text-align: center;
        }
      }
    }

    .matches-list {
      .stage-section {
        margin-bottom: var(--spacing-xl);

        .stage-title {
          font-size: var(--font-size-lg);
          text-align: center;
          margin-bottom: var(--spacing-md);
          padding: var(--spacing-sm);
          background: var(--surface-primary);
          border-radius: var(--radius-md);
          border: 1px solid var(--border-color);
        }

        .match-card {
          padding: var(--spacing-md);
          margin-bottom: var(--spacing-md);
          border-radius: var(--radius-md);

          .match-header {
            flex-direction: column;
            gap: var(--spacing-sm);
            text-align: center;

            .match-teams-horizontal {
              flex-direction: column;
              gap: var(--spacing-sm);
              width: 100%;

              .team-info {
                justify-content: center;
                padding: var(--spacing-sm);
                background: var(--surface-secondary);
                border-radius: var(--radius-sm);
                width: 100%;
                max-width: 200px;
                margin: 0 auto;

                .team-logo {
                  width: 25px;
                  height: 25px;
                }

                .team-name {
                  font-size: var(--font-size-sm);
                  text-align: center;
                  flex: 1;
                }
              }

              .vs-text {
                font-size: var(--font-size-xs);
                color: var(--text-secondary);
                font-weight: 600;
              }
            }

            .match-status {
              text-align: center;
              padding: var(--spacing-xs) var(--spacing-sm);
              border-radius: var(--radius-sm);
              font-size: var(--font-size-xs);
              font-weight: 600;
            }
          }

          .match-details {
            text-align: center;
            margin-top: var(--spacing-sm);

            .match-date {
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
            }

            .match-venue {
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
              margin-top: var(--spacing-xs);
            }
          }
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-primary) 100%);
  border-radius: 16px;
  border: 2px solid var(--primary-color);
  margin: 2rem 0;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, var(--primary-color) 50%, transparent 70%);
    opacity: 0.1;
    animation: shimmer 3s infinite;
  }

  i {
    font-size: 5rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
  }

  h3 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    font-size: 1.75rem;
    font-weight: 700;
    position: relative;
    z-index: 1;
  }

  p {
    margin: 0.5rem 0;
    font-size: 1.1rem;
    color: var(--text-color);
    position: relative;
    z-index: 1;
    font-weight: 500;

    &:last-child {
      margin-top: 1.5rem;
      font-size: 0.95rem;
      font-style: italic;
      color: var(--info-color);
      font-weight: 400;
    }
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Mobile responsive
@media (max-width: 768px) {
  .brackets-container {
    padding: var(--spacing-md);
    max-width: 100vw;
    overflow-x: hidden;
    box-sizing: border-box;
    margin: 0 auto;
    text-align: center;
  }

  .header {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);

    .header-content {
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: var(--spacing-md);

      h1 {
        font-size: var(--font-size-xl);
        margin: 0;
        text-align: center;
      }

      .header-actions {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
        width: 100%;

        .season-selector {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--spacing-xs);
          text-align: center;

          label {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--text-primary);
          }

          select {
            padding: var(--spacing-sm);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            background: var(--surface-primary);
            color: var(--text-primary);
            text-align: center;
          }
        }

        .view-toggle {
          display: flex;
          gap: var(--spacing-xs);
          width: 100%;
          max-width: 300px;

          .toggle-btn {
            flex: 1;
            padding: var(--spacing-sm);
            font-size: var(--font-size-sm);
            text-align: center;
            border-radius: var(--radius-md);
          }
        }
      }
    }
  }

  .bracket-view .bracket-stages {
    gap: 1.5rem;
    padding: var(--spacing-md) 0;

    .bracket-stage {
      min-width: 280px;
      max-width: 100%;
      margin: 0 auto;
    }
  }

  .championship-results {
    padding: var(--spacing-md);
    text-align: center;

    .podium {
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-md);

      .podium-position {
        order: unset !important;
        transform: none !important;
        width: 100%;
        max-width: 280px;
        text-align: center;
      }
    }
  }
}

// Tournament Tree Styles
.tournament-tree {
  display: flex;
  justify-content: center;
  align-items: stretch;
  gap: 2rem;
  padding: 2rem;
  position: relative;
  min-height: 600px;
  overflow: hidden;
  background: var(--background-secondary);
  border-radius: 16px;
  margin: 2rem 0;
  border: 2px solid var(--border-color);

  .bracket-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 220px;
    position: relative;
    flex: 1;

    &.quarter-finals-left,
    &.quarter-finals-right {
      flex: 1;

      .matches-column {
        gap: 2rem;
      }
    }

    &.semi-finals-left,
    &.semi-finals-right {
      flex: 0.9;
      justify-content: center;
      position: relative;

      .column-header {
        margin-bottom: 1rem;
      }

      .matches-column {
        gap: 4rem;
        justify-content: center;
        flex: 1;
        display: flex;
      }

      // Add subtle connecting lines
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        width: 30px;
        height: 2px;
        background: var(--border-color);
        z-index: 1;
      }

      &.semi-finals-left::before {
        right: -31px;
      }

      &.semi-finals-right::before {
        left: -31px;
      }
    }

    &.final {
      flex: 1.2;
      justify-content: center;
      position: relative;

      .column-header {
        margin-bottom: 1rem;
      }

      .matches-column {
        align-items: center;
        justify-content: center;
        flex: 1;
        display: flex;
      }

      // Add connecting lines from both sides
      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 30px;
        height: 2px;
        background: var(--border-color);
        z-index: 1;
      }

      &::before {
        left: -31px;
      }

      &::after {
        right: -31px;
      }
    }

    .column-header {
      text-align: center;
      margin-bottom: 2rem;
      flex-shrink: 0;

      h3 {
        color: var(--primary-color);
        font-size: 1.2rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
      }

      .series-info {
        color: var(--warning-color);
        font-size: 0.875rem;
        font-weight: 500;
      }
    }

    .matches-column {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      align-items: center;
      justify-content: center;
    }

    .bracket-match {
      background: var(--card-background);
      border: 2px solid var(--border-color);
      border-radius: 12px;
      padding: 1.25rem;
      min-width: 200px;
      max-width: 250px;
      width: 100%;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        border-color: var(--primary-color);
      }

      &.completed {
        border-color: var(--success-color);
        background: linear-gradient(135deg, var(--card-background) 0%, var(--success-color-light) 100%);
      }

      &.placeholder {
        border-style: dashed;
        border-color: var(--border-color);
        opacity: 0.6;

        .team.tbd {
          color: var(--text-muted);
          font-style: italic;
        }
      }

      &.final-match {
        border-color: var(--warning-color);
        background: linear-gradient(135deg, var(--card-background) 0%, var(--warning-color-light) 100%);

        .trophy-icon {
          text-align: center;
          margin-top: 1rem;

          i {
            font-size: 1.5rem;
            color: var(--warning-color);
          }
        }
      }

      .series-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--border-color);

        .series-title {
          font-weight: 700;
          color: var(--primary-color);
          font-size: 1rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .series-score {
          background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
          color: white;
          padding: 0.4rem 0.8rem;
          border-radius: 12px;
          font-weight: 800;
          font-size: 0.85rem;
          box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3);
          min-width: 40px;
          text-align: center;
        }
      }

      .match-teams {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;

        .team {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem;
          border-radius: 10px;
          transition: all 0.3s ease;
          background: var(--background-secondary);
          border: 1px solid var(--border-color);

          &.series-winner {
            background: linear-gradient(135deg, var(--success-color-light) 0%, var(--success-color-100) 100%);
            border: 2px solid var(--success-color);
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(var(--success-color-rgb), 0.3);
          }

          &.tbd {
            background: var(--background-tertiary);
            border-style: dashed;
            opacity: 0.7;
          }

          .team-logo {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--border-color);
          }

          .team-name {
            flex: 1;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-color);
          }
        }

        .vs-separator {
          text-align: center;
          color: var(--text-muted);
          font-weight: 700;
          font-size: 0.8rem;
          padding: 0.5rem 0;
          position: relative;

          &::before,
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 20px;
            height: 1px;
            background: var(--border-color);
          }

          &::before {
            left: 20%;
          }

          &::after {
            right: 20%;
          }
        }
      }

      .series-matches {
        margin-top: 1rem;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;

        .match-result {
          background: var(--background-tertiary);
          border: 1px solid var(--border-color);
          border-radius: 8px;
          padding: 0.4rem 0.7rem;
          font-size: 0.8rem;
          display: flex;
          align-items: center;
          gap: 0.4rem;
          transition: all 0.3s ease;
          min-width: 60px;
          justify-content: center;
          cursor: pointer;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
            background: linear-gradient(135deg, var(--background-tertiary) 0%, var(--primary-color-light) 100%);
          }

          &.completed {
            background: linear-gradient(135deg, var(--success-color-light) 0%, var(--success-color-100) 100%);
            border-color: var(--success-color);
            box-shadow: 0 2px 6px rgba(var(--success-color-rgb), 0.2);

            &:hover {
              background: linear-gradient(135deg, var(--success-color-100) 0%, var(--success-color-200) 100%);
              box-shadow: 0 4px 12px rgba(var(--success-color-rgb), 0.3);
              border-color: var(--success-color);
            }
          }

          .match-number {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 0.75rem;
          }

          .match-score {
            font-weight: 800;
            color: var(--text-color);
            font-size: 0.8rem;
          }

          .match-status {
            color: var(--warning-color);
            font-style: normal;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0.2rem 0.4rem;
            background: rgba(var(--warning-color), 0.1);
            border-radius: 4px;
            border: 1px solid rgba(var(--warning-color), 0.3);
          }
        }
      }
    }
  }
}



// Responsive Design for Tournament Tree
@media (max-width: 1200px) {
  .tournament-tree {
    gap: 1rem;
    padding: 1rem;

    .bracket-column {
      min-width: 160px;

      .bracket-match {
        min-width: 150px;
        padding: 0.75rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .tournament-tree {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xl);
    padding: var(--spacing-md);
    margin: var(--spacing-lg) 0;
    min-height: auto;
    overflow: visible;

    .bracket-column {
      width: 100%;
      max-width: 320px;
      margin: 0 auto;
      text-align: center;

      .column-header {
        margin-bottom: var(--spacing-md);
        padding: var(--spacing-sm);
        background: rgba(30, 41, 59, 0.8);
        border-radius: var(--radius-md);
        border: 1px solid rgba(71, 85, 105, 0.5);

        h3 {
          font-size: var(--font-size-lg);
          margin: 0;
          color: #3b82f6;
          text-align: center;
        }

        .series-info {
          display: block;
          margin-top: var(--spacing-xs);
          font-size: var(--font-size-xs);
          color: #94a3b8;
          text-align: center;
        }
      }

      .matches-column {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
        width: 100%;
      }

      &.quarter-finals-left,
      &.quarter-finals-right {
        .matches-column {
          display: grid;
          grid-template-columns: 1fr;
          gap: var(--spacing-md);
          justify-items: center;
        }
      }

      &.semi-finals-left,
      &.semi-finals-right,
      &.final {
        .matches-column {
          justify-content: center;
          align-items: center;
        }
      }

      .bracket-match {
        width: 100%;
        max-width: 280px;
        margin: 0 auto;
        text-align: center;
        box-sizing: border-box;
        background: rgba(15, 23, 42, 0.8);
        border: 1px solid rgba(71, 85, 105, 0.5);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);

        .series-header {
          text-align: center;
          margin-bottom: var(--spacing-sm);

          .series-title {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: #f1f5f9;
          }

          .series-score {
            font-size: var(--font-size-xs);
            color: #3b82f6;
            margin-left: var(--spacing-xs);
          }
        }

        .match-teams {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);
          align-items: center;
          text-align: center;

          .team {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            width: 100%;
            max-width: 250px;
            background: rgba(30, 41, 59, 0.6);
            border-radius: var(--radius-md);
            border: 1px solid rgba(71, 85, 105, 0.3);

            .team-logo {
              width: 30px;
              height: 30px;
              flex-shrink: 0;
            }

            .team-name {
              font-size: var(--font-size-sm);
              font-weight: 500;
              text-align: center;
              flex: 1;
              min-width: 0;
              word-break: break-word;
              color: #f1f5f9;
            }

            &.series-winner {
              background: rgba(34, 197, 94, 0.2);
              border-color: #22c55e;
              color: #22c55e;
              font-weight: 600;

              .team-name {
                color: #22c55e;
              }
            }

            &.tbd {
              color: #94a3b8;
              font-style: italic;
              background: rgba(30, 41, 59, 0.3);

              .team-name {
                color: #94a3b8;
              }
            }
          }

          .vs-separator {
            font-size: var(--font-size-xs);
            color: #94a3b8;
            font-weight: 600;
            padding: var(--spacing-xs) 0;
          }
        }

        .series-matches {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: var(--spacing-xs);
          margin-top: var(--spacing-sm);

          .match-result {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-xs);
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(71, 85, 105, 0.5);
            text-align: center;
            min-width: 40px;
            color: #f1f5f9;

            &.completed {
              background: rgba(34, 197, 94, 0.2);
              color: #22c55e;
              border-color: #22c55e;
            }
          }
        }

        &.placeholder {
          opacity: 0.6;
          border-style: dashed;
        }

        &.final-match {
          .trophy-icon {
            text-align: center;
            margin-top: var(--spacing-sm);

            i {
              font-size: var(--font-size-lg);
              color: var(--warning-500);
            }
          }
        }
      }
    }

    .bracket-lines {
      display: none; // Hide connecting lines on mobile
    }
  }
}
