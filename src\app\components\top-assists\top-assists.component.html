<div class="top-assists-container" *ngIf="topAssistsData && topAssistsData.length > 0 && !isLoading">
    <!-- Header Section -->
    <div class="header-wrapper" *ngIf="!hideTitle">
        <div class="assists-header">
            <div class="header-content">
                <h1 class="header-title">
                    <i class="fas fa-hands-helping"></i>
                    Top <span class="highlight">Assists</span>
                </h1>
            </div>
            <div class="header-actions">
                <button class="refresh-btn"
                        (click)="refreshData()"
                        [disabled]="isLoading"
                        title="Refresh top assists">
                    <i class="fas fa-sync-alt" [class.spinning]="isLoading"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>



    <!-- Leaderboard Table -->
    <div class="leaderboard-section">
        <div class="leaderboard-header" *ngIf="!hideTitle">
            <h2 class="leaderboard-title">
                <i class="fas fa-list-ol"></i>
                Complete Rankings
            </h2>
        </div>

        <div class="leaderboard-content">
            <!-- Left Side: Table -->
            <div class="leaderboard-table">
            <div class="table-header" *ngIf="hideTitle">
                <h3 class="table-title">Top Assists</h3>
            </div>

            <div class="table-content">
                <div class="table-row header-row" *ngIf="!hideTitle">
                    <div class="rank-cell">Rank</div>
                    <div class="player-cell">Player</div>
                    <div class="team-cell" [class.hide-mobile]="true">Team</div>
                    <div class="position-cell" [class.hide-mobile]="true">Position</div>
                    <div class="assists-cell">Assists</div>
                    <div class="games-cell" [class.hide-mobile]="true">Games</div>
                    <div class="avg-cell" [class.hide-mobile]="true">Avg</div>
                </div>

                <div class="table-row player-row"
                     *ngFor="let assister of topAssistsData; let i = index; trackBy: trackByPlayerId"
                     (click)="onPlayerClick(assister)"
                     [class.top-three]="i < 3 && !hideTitle">
                    <div class="rank-cell">
                        <span class="rank-number" [class.gold]="i === 0" [class.silver]="i === 1" [class.bronze]="i === 2">
                            {{i + 1}}
                        </span>
                    </div>
                    <div class="player-cell">
                        <img class="player-avatar"
                             [src]="assister.playerImgUrl || 'assets/Icons/User.jpg'"
                             [alt]="assister.playerName">
                        <div class="player-info">
                            <span class="player-name">{{assister.playerName}}</span>
                        </div>
                    </div>
                    <div class="team-cell" [class.hide-mobile]="true">
                        <span class="team-name">{{assister.teamName}}</span>
                    </div>
                    <div class="position-cell" [class.hide-mobile]="true">
                        <span class="position-badge">{{assister.position}}</span>
                    </div>
                    <div class="assists-cell">
                        <span class="assists-number">{{assister.assists}}</span>
                    </div>
                    <div class="games-cell" [class.hide-mobile]="true">
                        <span class="games-number">{{assister.games}}</span>
                    </div>
                    <div class="avg-cell" [class.hide-mobile]="true">
                        <span class="avg-number">{{assister.assistsPerGame || 0}}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side: Top 3 Compact Podium -->
        <div class="top-three-sidebar" *ngIf="!hideTitle && topAssistsData && topAssistsData.length >= 3">
            <div class="sidebar-header">
                <h3 class="sidebar-title">
                    <i class="fas fa-trophy"></i>
                    Top 3
                </h3>
            </div>

            <div class="compact-podium">
                <!-- 1st Place -->
                <div class="compact-player first" *ngIf="topAssistsData[0]" (click)="onPlayerClick(topAssistsData[0])">
                    <div class="player-rank">1</div>
                    <img class="player-image"
                         [src]="topAssistsData[0].playerImgUrl || 'assets/Icons/User.jpg'"
                         [alt]="topAssistsData[0].playerName">
                    <div class="player-details">
                        <div class="player-name">{{topAssistsData[0].playerName}}</div>
                        <div class="player-team">{{topAssistsData[0].teamName}}</div>
                        <div class="player-stat">
                            <span class="stat-value">{{topAssistsData[0].assists}}</span>
                            <span class="stat-label">assists</span>
                        </div>
                    </div>
                </div>

                <!-- 2nd Place -->
                <div class="compact-player second" *ngIf="topAssistsData[1]" (click)="onPlayerClick(topAssistsData[1])">
                    <div class="player-rank">2</div>
                    <img class="player-image"
                         [src]="topAssistsData[1].playerImgUrl || 'assets/Icons/User.jpg'"
                         [alt]="topAssistsData[1].playerName">
                    <div class="player-details">
                        <div class="player-name">{{topAssistsData[1].playerName}}</div>
                        <div class="player-team">{{topAssistsData[1].teamName}}</div>
                        <div class="player-stat">
                            <span class="stat-value">{{topAssistsData[1].assists}}</span>
                            <span class="stat-label">assists</span>
                        </div>
                    </div>
                </div>

                <!-- 3rd Place -->
                <div class="compact-player third" *ngIf="topAssistsData[2]" (click)="onPlayerClick(topAssistsData[2])">
                    <div class="player-rank">3</div>
                    <img class="player-image"
                         [src]="topAssistsData[2].playerImgUrl || 'assets/Icons/User.jpg'"
                         [alt]="topAssistsData[2].playerName">
                    <div class="player-details">
                        <div class="player-name">{{topAssistsData[2].playerName}}</div>
                        <div class="player-team">{{topAssistsData[2].teamName}}</div>
                        <div class="player-stat">
                            <span class="stat-value">{{topAssistsData[2].assists}}</span>
                            <span class="stat-label">assists</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</div>

<div class="loading-container" *ngIf="isLoading">
    <pro-clubs-spinner></pro-clubs-spinner>
</div>

<!-- No Data State -->
<div class="no-data-container" *ngIf="!isLoading && (!topAssistsData || topAssistsData.length === 0)">
    <div class="no-data-card">
        <i class="fas fa-hands-helping no-data-icon"></i>
        <h3 class="no-data-title">No Assists Data</h3>
        <p class="no-data-message">No assist statistics are available at the moment.</p>
    </div>
</div>