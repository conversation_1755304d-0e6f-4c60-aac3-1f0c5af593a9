import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { LeagueDTO } from '@pro-clubs-manager/shared-dtos';
import { NotificationService } from '../../../services/notification.service';

@Component({
  selector: 'app-cms-leagues-table',
  templateUrl: './cms-leagues-table.component.html',
  styleUrl: './cms-leagues-table.component.scss'
})
export class CmsLeaguesTableComponent {
  @Input() leagues: LeagueDTO[] = [];
  @Output() leagueDeleted = new EventEmitter<void>();
  @Output() leagueUpdated = new EventEmitter<void>();

  constructor(
    private notificationService: NotificationService,
    private router: Router
  ) {}

  viewLeagueDetails(leagueId: string): void {
    // Navigate to league table or league details
    this.router.navigate(['/league-table'], { queryParams: { leagueId } });
  }

  editLeague(leagueId: string): void {
    // For now, just show a message that this feature is coming
    this.notificationService.info('League editing feature coming soon');
  }

  deleteLeague(league: LeagueDTO): void {
    // For now, just show a message that this feature is coming
    this.notificationService.info('League deletion feature coming soon');
  }

  getLeagueTeamCount(league: LeagueDTO): number {
    // LeagueDTO doesn't include teams count, so we'll show N/A
    return 0;
  }

  getCurrentSeason(league: LeagueDTO): string {
    // LeagueDTO doesn't include season info, so we'll show N/A
    return 'N/A';
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/League.png';
    }
  }
}
