/* === MAIN CONTENT === */
.totw-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    flex: 1;
}

.pitch-section {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--success-400), var(--success-600));
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md);
    }
}

.pitch-formation-view {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* === NO TOTW MESSAGE === */
.no-totw-message {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);

    @media (max-width: 768px) {
        min-height: 300px;
        margin: 0 var(--spacing-sm);
    }
}

.no-totw-content {
    text-align: center;
    padding: var(--spacing-xl);
    max-width: 400px;

    i {
        font-size: 4rem;
        color: var(--text-tertiary);
        margin-bottom: var(--spacing-lg);
        opacity: 0.6;
    }

    h3 {
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-md) 0;
    }

    p {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
        line-height: 1.6;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg);

        i {
            font-size: 3rem;
        }

        h3 {
            font-size: var(--text-lg);
        }

        p {
            font-size: var(--text-sm);
        }
    }
}

.totw-pitch-size {
    @media (min-width: 768px) {
        width: 50%;
    }
}