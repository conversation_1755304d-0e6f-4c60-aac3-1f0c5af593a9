{"name": "proclubsstatsserver", "version": "1.1.0", "description": "Pro Clubs Stats Server", "type": "commonjs", "main": "dist/app.js", "scripts": {"start": "tsc && node dist/app.js", "build": "tsc", "dev": "nodemon --exec ts-node src/app.ts", "test": "jest --verbose", "add-achievements": "ts-node scripts/add-achievements.ts"}, "repository": {"type": "git", "url": "https://<EMAIL>/ProClubsStats/ProClubsStatsServer/_git/ProClubsStatsServer"}, "author": "", "license": "ISC", "dependencies": {"@pro-clubs-manager/shared-dtos": "^1.0.17", "@types/morgan": "^1.9.9", "@types/node": "^20.11.17", "@types/redis": "^4.0.11", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "cloudinary": "^2.1.0", "cloudinary-build-url": "^0.2.4", "cors": "^2.8.5", "dotenv": "^16.4.4", "express": "^4.18.2", "google-auth-library": "^10.1.0", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "openai": "^4.28.0", "redis": "^4.6.13", "reflect-metadata": "^0.2.2", "tsyringe": "^4.8.0", "winston": "^3.12.0", "winston-daily-rotate-file": "^5.0.0"}, "keywords": [], "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.10", "@types/mongodb": "^4.0.7", "@types/multer": "^1.4.11", "jest": "^29.7.0", "mongodb": "^6.7.0", "mongodb-memory-server": "^9.2.0", "nodemon": "^3.0.3", "ts-jest": "^29.1.4", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}