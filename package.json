{"name": "pro-clubs-stats-client", "version": "1.1.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"google-one-tap": "^1.0.6", "@angular/animations": "^17.3.2", "@angular/cdk": "^17.3.2", "@angular/common": "^17.1.0", "@angular/compiler": "^17.1.0", "@angular/core": "^17.1.0", "@angular/forms": "^17.1.0", "@angular/material": "^17.3.2", "@angular/platform-browser": "^17.1.0", "@angular/platform-browser-dynamic": "^17.1.0", "@angular/router": "^17.1.0", "@fortawesome/fontawesome-free": "^6.5.1", "@pro-clubs-manager/shared-dtos": "^1.0.16", "ag-charts-angular": "^10.1.0", "axios": "^1.6.7", "bootstrap": "^5.3.2", "cloudinary-core": "^2.13.1", "rxjs": "~7.8.0", "tesseract.js": "^6.0.1", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.1.2", "@angular/cli": "^17.1.2", "@angular/compiler-cli": "^17.1.0", "@playwright/test": "^1.53.2", "@types/bootstrap": "^5.2.10", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.3.2"}}