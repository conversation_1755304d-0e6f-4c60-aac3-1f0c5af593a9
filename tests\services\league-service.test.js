"use strict";
'import "reflect-metadata"';
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const tsyringe_1 = require("tsyringe");
const errors_1 = require("../../src/errors");
const mock_league_repository_1 = require("../../src/mocks/repositories/mock-league-repository");
const services_1 = require("../../src/mocks/services");
const services_2 = require("../../src/services");
describe("LeagueService", () => {
    let leagueService;
    let leagueRepository;
    let cacheService;
    let fixtureService;
    let teamService;
    beforeEach(() => {
        leagueRepository = new mock_league_repository_1.MockLeagueRepository();
        cacheService = new services_1.MockCacheService();
        fixtureService = new services_1.MockFixtureService();
        teamService = new services_1.MockTeamService();
        tsyringe_1.container.registerInstance("ILeagueRepository", leagueRepository);
        tsyringe_1.container.registerInstance("CacheService", cacheService);
        tsyringe_1.container.registerInstance("IFixtureService", fixtureService);
        tsyringe_1.container.registerInstance("ITeamService", teamService);
        leagueService = tsyringe_1.container.resolve(services_2.LeagueService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe("addLeague", () => {
        it("should add a new league", () => __awaiter(void 0, void 0, void 0, function* () {
            const name = "Test League";
            const imgUrl = "http://example.com/image.jpg";
            leagueRepository.isLeagueNameExists.mockResolvedValue(false);
            leagueRepository.createLeague.mockResolvedValue({ name, imgUrl });
            const league = yield leagueService.addLeague(name, imgUrl);
            expect(leagueRepository.isLeagueNameExists).toHaveBeenCalledWith(name);
            expect(leagueRepository.createLeague).toHaveBeenCalledWith(name, imgUrl);
            expect(league).toEqual({ name, imgUrl });
        }));
        it("should throw BadRequestError if league name already exists", () => __awaiter(void 0, void 0, void 0, function* () {
            const name = "Existing League";
            leagueRepository.isLeagueNameExists.mockResolvedValue(true);
            yield expect(leagueService.addLeague(name)).rejects.toThrow(errors_1.BadRequestError);
        }));
    });
    describe("startNewSeason", () => {
        it("should start a new season for the league", () => __awaiter(void 0, void 0, void 0, function* () {
            const leagueId = "60d5ec49b4dcd204d8e8bc17";
            const startDateString = "2024-01-01";
            const endDateString = "2024-12-31";
            leagueRepository.getLeagueById.mockResolvedValue({
                _id: new mongoose_1.Types.ObjectId(leagueId),
                currentSeason: { seasonNumber: 1 },
                seasonsHistory: [],
            });
            yield leagueService.startNewSeason(leagueId, startDateString, endDateString);
            expect(leagueRepository.getLeagueById).toHaveBeenCalledWith(leagueId);
            expect(teamService.startNewLeagueSeason).toHaveBeenCalled();
        }));
        it("should throw NotFoundError if league not found", () => __awaiter(void 0, void 0, void 0, function* () {
            const leagueId = "60d5ec49b4dcd204d8e8bc17";
            const startDateString = "2024-01-01";
            leagueRepository.getLeagueById.mockRejectedValue(new errors_1.NotFoundError(`League with id ${leagueId} not found`));
            yield expect(leagueService.startNewSeason(leagueId, startDateString)).rejects.toThrow(errors_1.NotFoundError);
        }));
    });
    describe("createFixture", () => {
        it("should create a fixture for the league", () => __awaiter(void 0, void 0, void 0, function* () {
            const leagueId = "60d5ec49b4dcd204d8e8bc17";
            const addFixtureData = {
                round: 1,
                games: [{ homeTeamId: "60d5ec49b4dcd204d8e8bc19", awayTeamId: "60d5ec49b4dcd204d8e8bc1a" }],
                startDate: "2024-01-01",
                endDate: "2024-01-02",
            };
            leagueRepository.getLeagueById.mockResolvedValue({
                _id: new mongoose_1.Types.ObjectId(leagueId),
                currentSeason: { seasonNumber: 1, fixtures: [] },
            });
            fixtureService.generateFixture.mockResolvedValue({
                _id: new mongoose_1.Types.ObjectId("60d5ec49b4dcd204d8e8bc1b"),
            });
            const result = yield leagueService.createFixture(leagueId, addFixtureData);
            expect(leagueRepository.getLeagueById).toHaveBeenCalledWith(leagueId);
            expect(fixtureService.generateFixture).toHaveBeenCalled();
            expect(result).toHaveProperty("_id");
        }));
        it("should throw NotFoundError if league not found", () => __awaiter(void 0, void 0, void 0, function* () {
            const leagueId = "60d5ec49b4dcd204d8e8bc17";
            const addFixtureData = {
                round: 1,
                games: [{ homeTeamId: "60d5ec49b4dcd204d8e8bc19", awayTeamId: "60d5ec49b4dcd204d8e8bc1a" }],
                startDate: "2024-01-01",
                endDate: "2024-01-02",
            };
            leagueRepository.getLeagueById.mockRejectedValue(new errors_1.NotFoundError(`League with id ${leagueId} not found`));
            yield expect(leagueService.createFixture(leagueId, addFixtureData)).rejects.toThrow(errors_1.NotFoundError);
        }));
    });
});
