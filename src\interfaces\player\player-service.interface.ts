import { ClientSession, Types } from "mongoose";
import { IPlayer, ITransferHistoryEntry } from "../../models/player/player";
import { PlayerGamePerformance } from "../../models/game/game";
import { PlayerDTO, CreatePlayerDataRequest } from "@pro-clubs-manager/shared-dtos";
import { PlayerComparisonData } from "./player-comparison.interface";
import { PlayerSeasonHistoryData } from "./player-season-history.interface";

export interface IPlayerService {
  getPlayerById(id: string | Types.ObjectId, session?: ClientSession): Promise<PlayerDTO>;
  getPlayerByEmail(email: string, session?: ClientSession): Promise<PlayerDTO | null>;
  getFreeAgents(session?: ClientSession): Promise<PlayerDTO[]>;
  getAllPlayers(): Promise<PlayerDTO[]>;

  createPlayer(playerData: CreatePlayerDataRequest): Promise<PlayerDTO>;
  deletePlayer(player: IPlayer, session: ClientSession): Promise<void>;

  renamePlayer(id: string, newName: string): Promise<void>;
  editPlayerAge(id: string, age: number): Promise<void>;
  editPlayerPosition(id: string, position: string): Promise<void>;
  editPlayerPlayablePositions(id: string, playablePositions: string[]): Promise<void>;
  setPlayerImage(playerId: string, file: Express.Multer.File): Promise<string>;

  removePlayersFromTeam(playersIds: Types.ObjectId[], session: ClientSession): Promise<void>;

  updatePlayersGamePerformance(playersStats: PlayerGamePerformance[], session: ClientSession): Promise<void>;
  revertPlayersGamePerformance(playersStats: PlayerGamePerformance[], session: ClientSession): Promise<void>;
  playerSearchByText(searchText: string): Promise<PlayerDTO[]>;
  getTransferHistoryByPlayerId(playerId: string): Promise<ITransferHistoryEntry[]>;
  comparePlayersById(player1Id: string, player2Id: string): Promise<PlayerComparisonData>;
  getPlayerSeasonHistory(playerId: string): Promise<PlayerSeasonHistoryData>;
  deletePlayerSeasonHistory(playerId: string, seasonNumber: number): Promise<void>;

  startNewSeason(teamId: Types.ObjectId, leagueId: Types.ObjectId, seasonNumber: number, session: ClientSession): Promise<void>;

  // Cache management
  clearFreeAgentsCache(): Promise<void>;
}
