import { injectable } from "tsyringe";
import { ClientSession, Types } from "mongoose";
import PlayerAssociationRequest, { IPlayerAssociationRequest } from "../models/player-association-request";
import logger from "../config/logger";
import { QueryFailedError, NotFoundError } from "../errors";

export interface IPlayerAssociationRequestRepository {
  create(data: {
    userId: Types.ObjectId;
    playerId: Types.ObjectId;
    playerEmail?: string;
    userMessage?: string;
  }, session?: ClientSession): Promise<IPlayerAssociationRequest>;
  
  findById(id: string, session?: ClientSession): Promise<IPlayerAssociationRequest | null>;
  findByIdRaw(id: string, session?: ClientSession): Promise<IPlayerAssociationRequest | null>;
  findByUserId(userId: string, session?: ClientSession): Promise<IPlayerAssociationRequest[]>;
  findPendingRequests(session?: ClientSession): Promise<IPlayerAssociationRequest[]>;
  findByUserAndPlayer(userId: string, playerId: string, session?: ClientSession): Promise<IPlayerAssociationRequest | null>;
  
  updateStatus(
    id: string, 
    status: 'approved' | 'rejected', 
    processedBy: Types.ObjectId, 
    reason?: string,
    session?: ClientSession
  ): Promise<IPlayerAssociationRequest>;
  
  deleteById(id: string, session?: ClientSession): Promise<void>;
}

@injectable()
export class PlayerAssociationRequestRepository implements IPlayerAssociationRequestRepository {
  
  async create(data: {
    userId: Types.ObjectId;
    playerId: Types.ObjectId;
    playerEmail?: string;
    userMessage?: string;
  }, session?: ClientSession): Promise<IPlayerAssociationRequest> {
    try {
      const request = new PlayerAssociationRequest(data);
      return await request.save({ session });
    } catch (error: any) {
      logger.error(`Failed to create player association request: ${error.message}`);
      if (error.code === 11000) {
        throw new QueryFailedError("A pending request for this player already exists");
      }
      throw new QueryFailedError(`Failed to create player association request: ${error.message}`);
    }
  }

  async findById(id: string, session?: ClientSession): Promise<IPlayerAssociationRequest | null> {
    try {
      return await PlayerAssociationRequest.findById(id)
        .populate('userId', 'firstName lastName email')
        .populate('playerId', 'name position team imgUrl')
        .populate('processedBy', 'firstName lastName')
        .session(session || null);
    } catch (error: any) {
      logger.error(`Failed to find player association request by id: ${error.message}`);
      throw new QueryFailedError(`Failed to find player association request: ${error.message}`);
    }
  }

  async findByIdRaw(id: string, session?: ClientSession): Promise<IPlayerAssociationRequest | null> {
    try {
      return await PlayerAssociationRequest.findById(id)
        .session(session || null);
    } catch (error: any) {
      logger.error(`Failed to find player association request by id (raw): ${error.message}`);
      throw new QueryFailedError(`Failed to find player association request: ${error.message}`);
    }
  }

  async findByUserId(userId: string, session?: ClientSession): Promise<IPlayerAssociationRequest[]> {
    try {
      return await PlayerAssociationRequest.find({ userId })
        .populate('playerId', 'name position team imgUrl')
        .populate('processedBy', 'firstName lastName')
        .sort({ requestedAt: -1 })
        .session(session || null);
    } catch (error: any) {
      logger.error(`Failed to find player association requests by user id: ${error.message}`);
      throw new QueryFailedError(`Failed to find player association requests: ${error.message}`);
    }
  }

  async findPendingRequests(session?: ClientSession): Promise<IPlayerAssociationRequest[]> {
    try {
      return await PlayerAssociationRequest.find({ status: 'pending' })
        .populate('userId', 'firstName lastName email')
        .populate('playerId', 'name position team imgUrl')
        .sort({ requestedAt: 1 })
        .session(session || null);
    } catch (error: any) {
      logger.error(`Failed to find pending player association requests: ${error.message}`);
      throw new QueryFailedError(`Failed to find pending requests: ${error.message}`);
    }
  }

  async findByUserAndPlayer(userId: string, playerId: string, session?: ClientSession): Promise<IPlayerAssociationRequest | null> {
    try {
      return await PlayerAssociationRequest.findOne({ 
        userId, 
        playerId, 
        status: 'pending' 
      }).session(session || null);
    } catch (error: any) {
      logger.error(`Failed to find player association request by user and player: ${error.message}`);
      throw new QueryFailedError(`Failed to find player association request: ${error.message}`);
    }
  }

  async updateStatus(
    id: string, 
    status: 'approved' | 'rejected', 
    processedBy: Types.ObjectId, 
    reason?: string,
    session?: ClientSession
  ): Promise<IPlayerAssociationRequest> {
    try {
      const request = await PlayerAssociationRequest.findByIdAndUpdate(
        id,
        {
          status,
          processedAt: new Date(),
          processedBy,
          reason
        },
        { new: true, session }
      ).populate('userId', 'firstName lastName email')
       .populate('playerId', 'name position team imgUrl')
       .populate('processedBy', 'firstName lastName');

      if (!request) {
        throw new NotFoundError(`Player association request with id ${id} not found`);
      }

      return request;
    } catch (error: any) {
      logger.error(`Failed to update player association request status: ${error.message}`);
      throw error;
    }
  }

  async deleteById(id: string, session?: ClientSession): Promise<void> {
    try {
      const result = await PlayerAssociationRequest.findByIdAndDelete(id, { session });
      if (!result) {
        throw new NotFoundError(`Player association request with id ${id} not found`);
      }
    } catch (error: any) {
      logger.error(`Failed to delete player association request: ${error.message}`);
      throw error;
    }
  }
}
