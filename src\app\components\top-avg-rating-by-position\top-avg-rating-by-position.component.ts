import { Component, Input } from '@angular/core';
import { Column } from '../../shared/models/column.model';
import { Router } from '@angular/router';
import { TopAssister } from '../../shared/models/topassister.model';
import { GameService } from '../../services/game.service';
import { TOP_AVG_RATING_BY_POSITION_COLUMNS } from './top-avg-rating-by-position.definitions';
import { ListOption } from '../../shared/models/list-option.model';
import { PLAYABLE_POSITIONS_OPTIONS } from '../top-scorers/top-scorers.definitions';

const LIMIT = 10;

// export it to the library
export type TopAvgRatingByPosition = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl: string;
  position: string;
  games: number;
  goals: number;
  assists: number;
  cleanSheets: number;
  avgRating: number;
  tableIcon?: {
    name: string;
    imgUrl: string;
    isTeam: boolean;
  }
};

@Component({
  selector: 'top-avg-rating-by-position',
  templateUrl: './top-avg-rating-by-position.component.html',
  styleUrl: './top-avg-rating-by-position.component.scss'
})
export class TopAvgRatingByPositionComponent {
  displayedColumns: Column[] = [];
  topAvgRatingData: TopAvgRatingByPosition[] = [];
  topPlayersByPosition: { [position: string]: TopAvgRatingByPosition } = {};
  playablePositionOptions: ListOption[] = [...PLAYABLE_POSITIONS_OPTIONS];
  isLoading: boolean = false;
  chosenPosition: string = 'ST';
  minimumGames: number = 15; // Half of 17 fixtures (rounded down)
  keyPositions: string[] = ['ST', 'LM', 'RM', 'CAM', 'CDM', 'CB', 'GK'];
  totalFixtures: number = 29;

  @Input() hideTitle: boolean = false;

  constructor(private router: Router, private gameService: GameService) { }

  ngOnInit() {
    this.displayedColumns = TOP_AVG_RATING_BY_POSITION_COLUMNS;
    this.minimumGames = Math.floor(this.totalFixtures / 2); // Set to half of total fixtures
    this.loadTopAvgData();
    this.loadTopPlayersByPosition();
  }

  private async loadTopAvgData() {
    this.isLoading = true;

    const topAvgRatingResponse = await this.gameService.getTopAvgRatingByPosition(this.chosenPosition, this.minimumGames);

    topAvgRatingResponse.map(topAvg => {
      topAvg.tableIcon = { name: topAvg.playerName, imgUrl: topAvg.playerImgUrl!, isTeam: false };
      topAvg.avgRating = parseFloat(topAvg.avgRating.toFixed(2));
    });

    this.topAvgRatingData = topAvgRatingResponse;
    this.isLoading = false;
  }

  private async loadTopPlayersByPosition() {
    this.isLoading = true;
    this.topPlayersByPosition = {};

    try {
      // Load top player for each key position
      for (const position of this.keyPositions) {
        const playersForPosition = await this.gameService.getTopAvgRatingByPosition(position, this.minimumGames);
        if (playersForPosition && playersForPosition.length > 0) {
          const topPlayer = playersForPosition[0];
          topPlayer.tableIcon = { name: topPlayer.playerName, imgUrl: topPlayer.playerImgUrl!, isTeam: false };
          topPlayer.avgRating = parseFloat(topPlayer.avgRating.toFixed(2));
          this.topPlayersByPosition[position] = topPlayer;
        }
      }
    } catch (error) {
      console.error('Error loading top players by position:', error);
    }

    this.isLoading = false;
  }

  getTopPlayersByPositionArray(): { position: string, player: TopAvgRatingByPosition }[] {
    return Object.keys(this.topPlayersByPosition).map(position => ({
      position,
      player: this.topPlayersByPosition[position]
    }));
  }

  getPositionDisplayName(position: string): string {
    const positionNames: { [key: string]: string } = {
      'ST': 'Striker',
      'LM': 'Left Mid',
      'RM': 'Right Mid',
      'CAM': 'Attacking Mid',
      'CDM': 'Defensive Mid',
      'CB': 'Centre Back',
      'GK': 'Goalkeeper'
    };
    return positionNames[position] || position;
  }

  getPositionIcon(position: string): string {
    const positionIcons: { [key: string]: string } = {
      'ST': 'fas fa-futbol',
      'LM': 'fas fa-arrow-left',
      'RM': 'fas fa-arrow-right',
      'CAM': 'fas fa-magic',
      'CDM': 'fas fa-shield-alt',
      'CB': 'fas fa-user-shield',
      'GK': 'fas fa-hand-paper'
    };
    return positionIcons[position] || 'fas fa-user';
  }

  isFilteringByPosition(): boolean {
    // Consider it filtering if a specific position is selected
    // You could also add logic here to check if it's different from a default "all positions" state
    return this.chosenPosition !== null && this.chosenPosition !== undefined && this.chosenPosition !== '';
  }


  onPlayerClick($playerDetails: TopAvgRatingByPosition): void {
    this.router.navigate(['/player-details', { id: $playerDetails.playerId }])
  }

  onSelectionChange($chosenOption: ListOption) {
    if (!$chosenOption) return;

    this.chosenPosition = $chosenOption.value;
  }

  onLoadClick(): void {
    this.loadTopAvgData();
    this.loadTopPlayersByPosition();
  }
}