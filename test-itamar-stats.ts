import { container } from './src/container';
import { IGameRepository } from './src/interfaces/game/game-repository.interface';
import { IPlayerService } from './src/interfaces/player/player-service.interface';
import { connectToDatabase } from './src/config/database';

async function testItamarStats() {
  try {
    console.log('Connecting to database...');
    await connectToDatabase();
    
    console.log('Getting repositories...');
    const gameRepository = container.resolve<IGameRepository>("IGameRepository");
    const playerService = container.resolve<IPlayerService>("IPlayerService");
    
    const itamarId = '67f2bd3317ec7db8bcd4f087';
    const leagueId = '65ecb1eb2f272e434483a821';
    const currentSeason = 6;
    
    console.log('\n=== Testing Game Repository Aggregation ===');
    const aggregationResult = await gameRepository.aggregatePlayerStatsForSeason(leagueId, currentSeason);
    const itamarAggregation = aggregationResult.find(stats => stats._id.toString() === itamarId);
    
    console.log('Itamar aggregation result:', itamarAggregation);
    
    console.log('\n=== Testing Player Service ===');
    const playerData = await playerService.getPlayerById(itamarId);
    
    console.log('Player service result - current season stats:', {
      games: playerData.currentSeason.games,
      goals: playerData.currentSeason.goals,
      assists: playerData.currentSeason.assists
    });
    
    console.log('\n=== Full Player Data ===');
    console.log(JSON.stringify(playerData, null, 2));
    
  } catch (error) {
    console.error('Error testing Itamar stats:', error);
  } finally {
    process.exit(0);
  }
}

testItamarStats();
