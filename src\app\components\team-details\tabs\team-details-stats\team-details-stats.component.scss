/* === MODERN TEAM STATS CONTAINER === */
.team-stats-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    min-height: 100vh;
    font-family: var(--font-sans);

    @media (max-width: 768px) {
        padding: var(--spacing-md);
        gap: var(--spacing-lg);
    }
}

.stats-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);

    .stats-title {
        font-size: var(--text-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);

        i {
            color: var(--primary);
        }

        @media (max-width: 768px) {
            font-size: var(--text-2xl);
        }
    }

    .stats-subtitle {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
        font-weight: var(--font-weight-medium);
    }
}

/* === REVOLUTIONARY STATS GRID === */
.quick-stats-section {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;

    .section-title {
        font-size: var(--text-xl);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-lg) 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        i {
            color: var(--primary);
            font-size: var(--text-lg);
        }
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: var(--spacing-md);

        @media (max-width: 1200px) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }

        @media (max-width: 480px) {
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
        }

        .stat-card {
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-sm);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            gap: 2px;
            transition: all 0.3s ease-in-out;
            position: relative;
            overflow: hidden;
            min-height: 90px;

            &:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-xl);
                border-color: var(--border-accent);

                .stat-icon {
                    transform: scale(1.1);
                }
            }

            .stat-icon {
                width: 40px;
                height: 40px;
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: var(--text-lg);
                flex-shrink: 0;
                margin-bottom: var(--spacing-xs);
                transition: all 0.3s ease;

                &.win-streak {
                    background: linear-gradient(135deg, #10b981, #059669);
                    color: white;
                }

                &.unbeaten {
                    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                    color: white;
                }

                &.no-goals {
                    background: linear-gradient(135deg, #f59e0b, #d97706);
                    color: white;
                }

                &.lose-streak {
                    background: linear-gradient(135deg, #ef4444, #dc2626);
                    color: white;
                }
            }

            .stat-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 2px;

                .stat-value {
                    font-size: var(--text-2xl);
                    font-weight: var(--font-weight-bold);
                    color: var(--text-primary);
                    line-height: 1;
                }

                .stat-label {
                    font-size: var(--text-xs);
                    font-weight: var(--font-weight-medium);
                    color: var(--text-secondary);
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
            }
        }
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .stats-grid {
            .stat-card {
                padding: var(--spacing-xs);
                min-height: 80px;
                gap: 2px;

                .stat-icon {
                    width: 32px;
                    height: 32px;
                    font-size: var(--text-base);
                }

                .stat-content {
                    .stat-value {
                        font-size: var(--text-xl);
                    }

                    .stat-label {
                        font-size: 10px;
                    }
                }
            }
        }
    }
}

/* === PLAYER STATISTICS SECTION === */
.player-stats-section {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    overflow: hidden;

    .stats-grid-horizontal {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-lg);

        @media (max-width: 1200px) {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
    }

    .player-stat-card {
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-xl);
        overflow: hidden;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--border-secondary);
        }

        .card-header {
            background: var(--surface-tertiary);
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--border-primary);

            .card-title {
                font-size: var(--text-base);
                font-weight: var(--font-weight-semibold);
                color: var(--text-primary);
                margin: 0;
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);

                i {
                    color: var(--primary);
                    font-size: var(--text-sm);
                }
            }
        }

        .card-content {
            padding: var(--spacing-md);

            .table-header {
                display: grid;
                grid-template-columns: 2fr 1fr 1fr;
                gap: var(--spacing-sm);
                padding: var(--spacing-sm) 0;
                border-bottom: 1px solid var(--border-primary);
                margin-bottom: var(--spacing-sm);

                @media (max-width: 768px) {
                    grid-template-columns: 3fr 1fr 1fr;
                    gap: var(--spacing-xs);
                }

                @media (max-width: 480px) {
                    grid-template-columns: 2fr 1fr 1fr;
                    gap: 4px;
                    padding: var(--spacing-xs) 0;
                }

                .header-name,
                .header-games,
                .header-stat {
                    font-size: var(--text-xs);
                    font-weight: var(--font-weight-semibold);
                    color: var(--text-secondary);
                    text-transform: uppercase;
                    letter-spacing: 0.5px;

                    @media (max-width: 480px) {
                        font-size: 10px;
                        letter-spacing: 0.2px;
                    }
                }

                .header-name {
                    text-align: left;
                }

                .header-games,
                .header-stat {
                    text-align: center;
                }
            }

            .player-list {
                display: flex;
                flex-direction: column;
                gap: var(--spacing-xs);
                max-height: 300px;
                overflow-y: auto;

                .player-row {
                    display: grid;
                    grid-template-columns: 2fr 1fr 1fr;
                    gap: var(--spacing-sm);
                    padding: var(--spacing-sm);
                    border-radius: var(--radius-md);
                    transition: all 0.3s ease;
                    align-items: center;

                    @media (max-width: 768px) {
                        grid-template-columns: 3fr 1fr 1fr;
                        gap: var(--spacing-xs);
                        padding: var(--spacing-xs);
                    }

                    @media (max-width: 480px) {
                        grid-template-columns: 2fr 1fr 1fr;
                        gap: 4px;
                        padding: 6px;
                    }

                    &:hover {
                        background: var(--surface-tertiary);
                    }

                    .player-name {
                        font-size: var(--text-sm);
                        font-weight: var(--font-weight-medium);
                        color: var(--text-primary);
                        cursor: pointer;
                        transition: all 0.3s ease;
                        text-align: left;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;

                        @media (max-width: 480px) {
                            font-size: var(--text-xs);
                        }

                        &:hover {
                            color: var(--primary);
                        }
                    }

                    .player-games {
                        font-size: var(--text-sm);
                        color: var(--text-secondary);
                        text-align: center;

                        @media (max-width: 480px) {
                            font-size: var(--text-xs);
                        }
                    }

                    .player-stat {
                        font-size: var(--text-sm);
                        font-weight: var(--font-weight-semibold);
                        text-align: center;

                        @media (max-width: 480px) {
                            font-size: var(--text-xs);
                        }

                        &.rating {
                            color: var(--warning-500);
                        }

                        &.goals {
                            color: var(--success-500);
                        }

                        &.assists {
                            color: var(--info-500);
                        }
                    }
                }
            }
        }
    }
}

/* === LOADING STATE === */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
    min-height: 300px;

    .loading-title {
        font-family: var(--font-sans);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: var(--spacing-lg) 0 var(--spacing-sm) 0;
    }

    .loading-description {
        font-family: var(--font-sans);
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
    }
}

