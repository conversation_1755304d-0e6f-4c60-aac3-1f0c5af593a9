import { ChangeDetectorRef, Component, Input, OnInit, Optional } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { GameService } from '../../services/game.service';
import { MatDialogRef } from '@angular/material/dialog';
import { GAME_STATUS, GameDTO, PlayerPerformanceDTO } from '@pro-clubs-manager/shared-dtos';
import { TeamFormation } from '../mini-pitch-formation/mini-pitch-formation.component';
import { ExtendedGameDTO, getGameBroadcast, hasLiveBroadcast } from '../../shared/types/extended-game-dto';
import { AuthService } from '../../services/auth.service';
import { PermissionsService } from '../../services/permissions.service';
import { NotificationService } from '../../services/notification.service';
import { PromotionalBannerComponent } from '../promotional-banner/promotional-banner.component';
import { Observable, map } from 'rxjs';

@Component({
  selector: 'app-game-details',
  templateUrl: './game-details.component.html',
  styleUrl: './game-details.component.scss'
})
export class GameDetailsComponent implements OnInit {
  GameStatus = GAME_STATUS;
  selectedGame: ExtendedGameDTO | undefined = undefined;
  homeTeamScorersAndAssists: PlayerPerformanceDTO[] = [];
  awayTeamScorersAndAssists: PlayerPerformanceDTO[] = [];
  homeTeamAllPlayers: PlayerPerformanceDTO[] = [];
  awayTeamAllPlayers: PlayerPerformanceDTO[] = [];
  playerOfTheMatch: PlayerPerformanceDTO | undefined = undefined;
  playerOfTheMatchTeamName: string = '';
  homeTeamFormation: string = '4-3-3';
  awayTeamFormation: string = '4-3-3';
  isAuthenticated: boolean = false;
  promotionalMessage = PromotionalBannerComponent.MESSAGES.GAME_DETAILS;

  // Permission observables
  isAdmin$: Observable<boolean>;
  canEditGame$: Observable<boolean>;
  canEditHomeTeam$: Observable<boolean>;
  canEditAwayTeam$: Observable<boolean>;

  @Input() set selectedGameId(gameId: string) {
    this._selectedGameId = gameId;
    this.loadGameDetails()
  }

  ngOnDestroy() {
    this.homeTeamScorersAndAssists = [];
    this.awayTeamScorersAndAssists = [];
    this.homeTeamAllPlayers = [];
    this.awayTeamAllPlayers = [];
  }

  _selectedGameId: string | undefined = undefined;

  // Properties to determine if we're in modal or standalone mode
  isStandalonePage: boolean = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private gameService: GameService,
    private cdRef: ChangeDetectorRef,
    @Optional() private dialogRef: MatDialogRef<GameDetailsComponent>,
    private authService: AuthService,
    private permissionsService: PermissionsService,
    private notificationService: NotificationService
  ) {
    // Initialize permission observables
    this.isAdmin$ = this.authService.currentUser$.pipe(
      map(user => user?.role === 'admin' || false)
    );
    this.canEditGame$ = this.permissionsService.isAdmin();
    this.canEditHomeTeam$ = this.permissionsService.isAdmin();
    this.canEditAwayTeam$ = this.permissionsService.isAdmin();

    // Determine if we're in standalone mode (no dialog reference)
    this.isStandalonePage = !this.dialogRef;
  }

  ngOnInit() {
    // Subscribe to authentication state
    this.authService.isAuthenticated$.subscribe(
      isAuth => this.isAuthenticated = isAuth
    );

    // If we're in standalone mode, get the game ID from route params
    if (this.isStandalonePage) {
      this.route.params.subscribe(params => {
        if (params['id']) {
          this.selectedGameId = params['id'];
        }
      });
    }
  }

  async loadGameDetails() {
    this.selectedGame = await this.gameService.getGameById(this._selectedGameId!);
    this.initializePermissions();
    this.cdRef.detectChanges();
    this.loadScorersAndAssists();
    this.loadAllPlayers();
    this.loadPlayerOfTheMatch();
  }

  initializePermissions() {
    if (this.selectedGame) {
      this.canEditGame$ = this.permissionsService.canEditGame(this.selectedGame.id);
      this.canEditHomeTeam$ = this.permissionsService.canEditTeam(this.selectedGame.homeTeam.id);
      this.canEditAwayTeam$ = this.permissionsService.canEditTeam(this.selectedGame.awayTeam.id);
    }
  }

  loadScorersAndAssists() {
    if (!this.selectedGame)
      return;

    const homeTeamPerformance = this.selectedGame.homeTeam.playersPerformance;
    const awayTeamPerformance = this.selectedGame.awayTeam.playersPerformance;

    if (homeTeamPerformance) {
      this.homeTeamScorersAndAssists = homeTeamPerformance.filter(player => player.goals! > 0 || player.assists! > 0);
    }

    if (awayTeamPerformance) {
      this.awayTeamScorersAndAssists = awayTeamPerformance.filter(player => player.goals! > 0 || player.assists! > 0);
    }
  };

  loadAllPlayers() {
    if (!this.selectedGame)
      return;

    const homeTeamPerformance = this.selectedGame.homeTeam.playersPerformance;
    const awayTeamPerformance = this.selectedGame.awayTeam.playersPerformance;

    if (homeTeamPerformance) {
      this.homeTeamAllPlayers = homeTeamPerformance;
      this.homeTeamFormation = this.detectFormation(homeTeamPerformance);
    }

    if (awayTeamPerformance) {
      this.awayTeamAllPlayers = awayTeamPerformance;
      this.awayTeamFormation = this.detectFormation(awayTeamPerformance);
    }
  };

  loadPlayerOfTheMatch() {
    this.playerOfTheMatch = this.selectedGame!.homeTeam.playersPerformance?.find(performance => performance.playerOfTheMatch == true);
    this.playerOfTheMatchTeamName = this.selectedGame!.homeTeam.name;

    if (!this.playerOfTheMatch) {
      this.playerOfTheMatch = this.selectedGame!.awayTeam.playersPerformance?.find(performance => performance.playerOfTheMatch == true);
      this.playerOfTheMatchTeamName = this.selectedGame!.awayTeam.name;
    }
  }

  navigateToTeamDetails(teamId: string): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
    this.router.navigate(['/team-details', { id: teamId }])
  }

  navigateToPlayerDetails(playerId: string): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
    this.router.navigate(['/player-details', { id: playerId }]);
  }

  getBroadcastInfo(): { streamUrl: string; broadcastingTeam: string } | undefined {
    const extendedGame = this.selectedGame as any;
    return extendedGame?.broadcast;
  }

  hasLiveBroadcast(): boolean {
    return hasLiveBroadcast(this.selectedGame as ExtendedGameDTO);
  }

  getHomeTeamFormation(): TeamFormation {
    if (!this.selectedGame) {
      return this.getDefaultFormation('home');
    }

    const homeTeamPlayers = this.homeTeamAllPlayers.map((player, index) => ({
      id: player.playerId,
      name: player.name,
      position: player.positionPlayed || this.getPlayerPosition(index, 'home'),
      jerseyNumber: index + 1,
      goals: player.goals || 0,
      assists: player.assists || 0,
      rating: player.rating || 0,
      isPlayerOfMatch: this.playerOfTheMatch?.playerId === player.playerId,
      profileImage: player.imgUrl,
      ...this.getPlayerCoordinatesByPosition(player.positionPlayed || this.getPlayerPosition(index, 'home'), 'home', this.homeTeamFormation)
    }));

    const adjustedPlayers = homeTeamPlayers.length > 0 ? this.adjustPlayerPositions(homeTeamPlayers) : this.getDefaultFormation('home').players;

    return {
      teamId: this.selectedGame.homeTeam.id,
      teamName: this.selectedGame.homeTeam.name,
      formation: this.homeTeamFormation,
      players: adjustedPlayers
    };
  }

  getAwayTeamFormation(): TeamFormation {
    if (!this.selectedGame) {
      return this.getDefaultFormation('away');
    }

    const awayTeamPlayers = this.awayTeamAllPlayers.map((player, index) => ({
      id: player.playerId,
      name: player.name,
      position: player.positionPlayed || this.getPlayerPosition(index, 'away'),
      jerseyNumber: index + 1,
      goals: player.goals || 0,
      assists: player.assists || 0,
      rating: player.rating || 0,
      isPlayerOfMatch: this.playerOfTheMatch?.playerId === player.playerId,
      profileImage: player.imgUrl,
      ...this.getPlayerCoordinatesByPosition(player.positionPlayed || this.getPlayerPosition(index, 'away'), 'away', this.awayTeamFormation)
    }));

    const adjustedPlayers = awayTeamPlayers.length > 0 ? this.adjustPlayerPositions(awayTeamPlayers) : this.getDefaultFormation('away').players;

    return {
      teamId: this.selectedGame.awayTeam.id,
      teamName: this.selectedGame.awayTeam.name,
      formation: this.awayTeamFormation,
      players: adjustedPlayers
    };
  }

  detectFormation(players: PlayerPerformanceDTO[]): string {
    if (!players || players.length === 0) return '4-3-3';

    // Count players by position category
    const positionCounts = {
      defenders: 0,
      midfielders: 0,
      forwards: 0,
      goalkeeper: 0
    };
    let positionCAMExists: boolean = false;

    players.forEach(player => {
      const position = this.normalizePosition(player.positionPlayed || '');
      const category = this.getPositionCategory(position);
      positionCounts[category]++;
      if (position === 'CAM') {
        positionCAMExists = true;
      }
    });

    // Determine formation based on outfield player distribution
    const { defenders, midfielders, forwards } = positionCounts;

    // Common formations
    if (defenders === 4 && midfielders === 3 && forwards === 3) return '4-3-3';
    if (defenders === 4 && midfielders === 4 && forwards === 2 && positionCAMExists) return '4-1-2-1-2';
    if (defenders === 4 && midfielders === 4 && forwards === 2 && !positionCAMExists) return '4-4-2';
    if (defenders === 3 && midfielders === 5 && forwards === 2 && !positionCAMExists) return '3-4-1-2';
    if (defenders === 3 && midfielders === 5 && forwards === 2 && positionCAMExists) return '3-5-2';
    if (defenders === 4 && midfielders === 3 && forwards === 3) return '4-2-3-1';
    if (defenders === 3 && midfielders === 4 && forwards === 3) return '3-4-3';
    if (defenders === 5 && midfielders === 3 && forwards === 2) return '5-3-2';
    if (defenders === 5 && midfielders === 4 && forwards === 1) return '5-4-1';
    if (defenders === 3 && midfielders === 3 && forwards === 4) return '3-3-4';

    // Fallback based on total players
    const totalOutfield = defenders + midfielders + forwards;
    if (totalOutfield <= 8) return '3-4-1';
    if (totalOutfield === 9) return '3-3-3';
    if (totalOutfield === 10) return '4-3-3';

    return '4-3-3'; // Default fallback
  }

  private getPositionCategory(position: string): 'defenders' | 'midfielders' | 'forwards' | 'goalkeeper' {
    const defenderPositions = ['CB', 'LB', 'RB', 'LCB', 'RCB', 'LWB', 'RWB'];
    const midfielderPositions = ['CM', 'CDM', 'CAM', 'LM', 'RM', 'LCM', 'RCM', 'LDM', 'RDM', 'LAM', 'RAM'];
    const forwardPositions = ['ST', 'LW', 'RW', 'CF', 'LF', 'RF'];

    if (position === 'GK') return 'goalkeeper';
    if (defenderPositions.includes(position)) return 'defenders';
    if (midfielderPositions.includes(position)) return 'midfielders';
    if (forwardPositions.includes(position)) return 'forwards';

    return 'midfielders'; // Default for unknown positions
  }

  private getDefaultFormation(side: 'home' | 'away'): TeamFormation {
    const isHome = side === 'home';
    const formation = isHome ? this.homeTeamFormation : this.awayTeamFormation;

    // Use the same formation templates as the mini-pitch component
    const formationTemplate = this.getFormationTemplate(formation, isHome);

    return {
      teamId: `${side}-team`,
      teamName: `${side.charAt(0).toUpperCase() + side.slice(1)} Team`,
      formation: formation,
      players: formationTemplate
    };
  }

  private getFormationTemplate(formation: string, isHome: boolean): any[] {
    const templates = {
      '4-3-3': isHome ? this.getHome433Formation() : this.getAway433Formation(),
      '4-4-2': isHome ? this.getHome442Formation() : this.getAway442Formation(),
      '3-5-2': isHome ? this.getHome352Formation() : this.getAway352Formation()
    };

    return templates[formation as keyof typeof templates] || templates['4-3-3'];
  }

  private getHome433Formation(): any[] {
    return [
      // Goalkeeper - far left
      { id: '1', name: '', position: 'GK', x: 5, y: 50, jerseyNumber: 1 },

      // Defense line - well spaced vertically
      { id: '2', name: '', position: 'LB', x: 20, y: 12, jerseyNumber: 2 },
      { id: '3', name: '', position: 'CB', x: 18, y: 32, jerseyNumber: 3 },
      { id: '4', name: '', position: 'CB', x: 18, y: 68, jerseyNumber: 4 },
      { id: '5', name: '', position: 'RB', x: 20, y: 88, jerseyNumber: 5 },

      // Midfield line - triangle formation
      { id: '6', name: '', position: 'CDM', x: 35, y: 50, jerseyNumber: 6 },
      { id: '7', name: '', position: 'CM', x: 42, y: 25, jerseyNumber: 7 },
      { id: '8', name: '', position: 'CM', x: 42, y: 75, jerseyNumber: 8 },

      // Attack line - wide spread
      { id: '9', name: '', position: 'LW', x: 65, y: 15, jerseyNumber: 9 },
      { id: '10', name: '', position: 'ST', x: 68, y: 50, jerseyNumber: 10 },
      { id: '11', name: '', position: 'RW', x: 65, y: 85, jerseyNumber: 11 }
    ];
  }

  private getAway433Formation(): any[] {
    return [
      // Goalkeeper - far right
      { id: '1', name: '', position: 'GK', x: 95, y: 50, jerseyNumber: 1 },

      // Defense line - well spaced vertically
      { id: '2', name: '', position: 'RB', x: 80, y: 12, jerseyNumber: 2 },
      { id: '3', name: '', position: 'CB', x: 82, y: 32, jerseyNumber: 3 },
      { id: '4', name: '', position: 'CB', x: 82, y: 68, jerseyNumber: 4 },
      { id: '5', name: '', position: 'LB', x: 80, y: 88, jerseyNumber: 5 },

      // Midfield line - triangle formation
      { id: '6', name: '', position: 'CDM', x: 65, y: 50, jerseyNumber: 6 },
      { id: '7', name: '', position: 'CM', x: 58, y: 25, jerseyNumber: 7 },
      { id: '8', name: '', position: 'CM', x: 58, y: 75, jerseyNumber: 8 },

      // Attack line - wide spread
      { id: '9', name: '', position: 'RW', x: 35, y: 15, jerseyNumber: 9 },
      { id: '10', name: '', position: 'ST', x: 32, y: 50, jerseyNumber: 10 },
      { id: '11', name: '', position: 'LW', x: 35, y: 85, jerseyNumber: 11 }
    ];
  }

  private getHome442Formation(): any[] {
    return [
      // Goalkeeper
      { id: '1', name: '', position: 'GK', x: 5, y: 50, jerseyNumber: 1 },

      // Defense line
      { id: '2', name: '', position: 'LB', x: 20, y: 15, jerseyNumber: 2 },
      { id: '3', name: '', position: 'CB', x: 18, y: 35, jerseyNumber: 3 },
      { id: '4', name: '', position: 'CB', x: 18, y: 65, jerseyNumber: 4 },
      { id: '5', name: '', position: 'RB', x: 20, y: 85, jerseyNumber: 5 },

      // Midfield line
      { id: '6', name: '', position: 'LM', x: 40, y: 20, jerseyNumber: 6 },
      { id: '7', name: '', position: 'CM', x: 38, y: 40, jerseyNumber: 7 },
      { id: '8', name: '', position: 'CM', x: 38, y: 60, jerseyNumber: 8 },
      { id: '9', name: '', position: 'RM', x: 40, y: 80, jerseyNumber: 9 },

      // Attack line
      { id: '10', name: '', position: 'ST', x: 65, y: 35, jerseyNumber: 10 },
      { id: '11', name: '', position: 'ST', x: 65, y: 65, jerseyNumber: 11 }
    ];
  }

  private getAway442Formation(): any[] {
    return [
      // Goalkeeper
      { id: '1', name: '', position: 'GK', x: 95, y: 50, jerseyNumber: 1 },

      // Defense line
      { id: '2', name: '', position: 'RB', x: 80, y: 15, jerseyNumber: 2 },
      { id: '3', name: '', position: 'CB', x: 82, y: 35, jerseyNumber: 3 },
      { id: '4', name: '', position: 'CB', x: 82, y: 65, jerseyNumber: 4 },
      { id: '5', name: '', position: 'LB', x: 80, y: 85, jerseyNumber: 5 },

      // Midfield line
      { id: '6', name: '', position: 'RM', x: 60, y: 20, jerseyNumber: 6 },
      { id: '7', name: '', position: 'CM', x: 62, y: 40, jerseyNumber: 7 },
      { id: '8', name: '', position: 'CM', x: 62, y: 60, jerseyNumber: 8 },
      { id: '9', name: '', position: 'LM', x: 60, y: 80, jerseyNumber: 9 },

      // Attack line
      { id: '10', name: '', position: 'ST', x: 35, y: 35, jerseyNumber: 10 },
      { id: '11', name: '', position: 'ST', x: 35, y: 65, jerseyNumber: 11 }
    ];
  }

  private getHome352Formation(): any[] {
    return [
      // Goalkeeper
      { id: '1', name: '', position: 'GK', x: 5, y: 50, jerseyNumber: 1 },

      // Defense line (3 CBs)
      { id: '2', name: '', position: 'CB', x: 18, y: 25, jerseyNumber: 2 },
      { id: '3', name: '', position: 'CB', x: 16, y: 50, jerseyNumber: 3 },
      { id: '4', name: '', position: 'CB', x: 18, y: 75, jerseyNumber: 4 },

      // Midfield line (5 players)
      { id: '5', name: '', position: 'LWB', x: 35, y: 10, jerseyNumber: 5 },
      { id: '6', name: '', position: 'CM', x: 40, y: 30, jerseyNumber: 6 },
      { id: '7', name: '', position: 'CM', x: 42, y: 50, jerseyNumber: 7 },
      { id: '8', name: '', position: 'CM', x: 40, y: 70, jerseyNumber: 8 },
      { id: '9', name: '', position: 'RWB', x: 35, y: 90, jerseyNumber: 9 },

      // Attack line
      { id: '10', name: '', position: 'ST', x: 65, y: 35, jerseyNumber: 10 },
      { id: '11', name: '', position: 'ST', x: 65, y: 65, jerseyNumber: 11 }
    ];
  }

  private getAway352Formation(): any[] {
    return [
      // Goalkeeper
      { id: '1', name: '', position: 'GK', x: 95, y: 50, jerseyNumber: 1 },

      // Defense line (3 CBs)
      { id: '2', name: '', position: 'CB', x: 82, y: 25, jerseyNumber: 2 },
      { id: '3', name: '', position: 'CB', x: 84, y: 50, jerseyNumber: 3 },
      { id: '4', name: '', position: 'CB', x: 82, y: 75, jerseyNumber: 4 },

      // Midfield line (5 players)
      { id: '5', name: '', position: 'RWB', x: 65, y: 10, jerseyNumber: 5 },
      { id: '6', name: '', position: 'CM', x: 60, y: 30, jerseyNumber: 6 },
      { id: '7', name: '', position: 'CM', x: 58, y: 50, jerseyNumber: 7 },
      { id: '8', name: '', position: 'CM', x: 60, y: 70, jerseyNumber: 8 },
      { id: '9', name: '', position: 'LWB', x: 65, y: 90, jerseyNumber: 9 },

      // Attack line
      { id: '10', name: '', position: 'ST', x: 35, y: 35, jerseyNumber: 10 },
      { id: '11', name: '', position: 'ST', x: 35, y: 65, jerseyNumber: 11 }
    ];
  }

  private getPlayerPosition(index: number, _side: 'home' | 'away'): string {
    const positions = ['GK', 'LB', 'CB', 'CB', 'RB', 'CM', 'CM', 'CM', 'LW', 'ST', 'RW'];
    return positions[index % positions.length] || 'SUB';
  }

  private getPlayerCoordinatesByPosition(position: string, side: 'home' | 'away', formation: string): { x: number; y: number } {
    const isHome = side === 'home';

    // Get the formation template
    const formationTemplate = this.getFormationTemplate(formation, isHome);

    // Normalize position names
    const normalizedPosition = this.normalizePosition(position);

    // Find a player with matching position in the template
    const templatePlayer = formationTemplate.find(player =>
      this.normalizePosition(player.position) === normalizedPosition
    );

    if (templatePlayer) {
      return { x: templatePlayer.x, y: templatePlayer.y };
    }

    // Fallback: try to find similar position
    const similarPositions = this.getSimilarPositions(normalizedPosition);
    for (const similarPos of similarPositions) {
      const similarPlayer = formationTemplate.find(player =>
        this.normalizePosition(player.position) === similarPos
      );
      if (similarPlayer) {
        return { x: similarPlayer.x, y: similarPlayer.y };
      }
    }

    // Final fallback to default position based on side
    const baseX = isHome ? 25 : 75;
    return { x: baseX, y: 50 };
  }

  private getSimilarPositions(position: string): string[] {
    const similarityMap: { [key: string]: string[] } = {
      'CB': ['LCB', 'RCB'],
      'LCB': ['CB', 'RCB'],
      'RCB': ['CB', 'LCB'],
      'CM': ['LCM', 'RCM', 'CAM', 'CDM'],
      'LCM': ['CM', 'RCM', 'LM'],
      'RCM': ['CM', 'LCM', 'RM'],
      'CAM': ['CM', 'LAM', 'RAM'],
      'CDM': ['CM', 'LDM', 'RDM'],
      'LM': ['LCM', 'LW'],
      'RM': ['RCM', 'RW'],
      'LW': ['LM', 'LF'],
      'RW': ['RM', 'RF'],
      'ST': ['CF', 'LF', 'RF']
    };

    return similarityMap[position] || [];
  }

  private normalizePosition(position: string): string {
    const positionMap: { [key: string]: string } = {
      // Goalkeeper
      'GK': 'GK', 'GOALKEEPER': 'GK',

      // Defenders
      'LB': 'LB', 'LEFT_BACK': 'LB', 'LWB': 'LB',
      'CB': 'CB', 'CENTER_BACK': 'CB', 'CENTRE_BACK': 'CB',
      'RB': 'RB', 'RIGHT_BACK': 'RB', 'RWB': 'RB',
      'LCB': 'CB', 'RCB': 'CB',

      // Midfielders
      'CDM': 'CDM', 'DEFENSIVE_MIDFIELDER': 'CDM',
      'CM': 'CM', 'CENTRAL_MIDFIELDER': 'CM', 'CENTRE_MIDFIELDER': 'CM',
      'CAM': 'CAM', 'ATTACKING_MIDFIELDER': 'CAM',
      'LM': 'LM', 'LEFT_MIDFIELDER': 'LM',
      'RM': 'RM', 'RIGHT_MIDFIELDER': 'RM',
      'LCM': 'CM', 'RCM': 'CM',
      'LDM': 'CDM', 'RDM': 'CDM',
      'LAM': 'CAM', 'RAM': 'CAM',

      // Forwards
      'LW': 'LW', 'LEFT_WINGER': 'LW',
      'RW': 'RW', 'RIGHT_WINGER': 'RW',
      'ST': 'ST', 'STRIKER': 'ST', 'CENTRE_FORWARD': 'ST',
      'CF': 'ST', 'LF': 'LW', 'RF': 'RW'
    };

    return positionMap[position.toUpperCase()] || position;
  }





  private adjustPlayerPositions(players: any[]): any[] {
    if (!players || players.length === 0) return [];

    // Group players by position to handle multiple players in same position
    const positionGroups: { [position: string]: any[] } = {};

    players.forEach(player => {
      const normalizedPosition = this.normalizePosition(player.position);
      if (!positionGroups[normalizedPosition]) {
        positionGroups[normalizedPosition] = [];
      }
      positionGroups[normalizedPosition].push(player);
    });

    const adjustedPlayers: any[] = [];
    const minDistance = 12; // Increased minimum distance for better spacing

    // Process each position group
    Object.keys(positionGroups).forEach(position => {
      const playersInPosition = positionGroups[position];

      if (playersInPosition.length === 1) {
        // Single player in position - use original coordinates
        adjustedPlayers.push(playersInPosition[0]);
      } else {
        // Multiple players in same position - spread them out intelligently
        const basePlayer = playersInPosition[0];
        const spreadDistance = 15; // Distance to spread players apart

        playersInPosition.forEach((player, index) => {
          let adjustedPlayer = { ...player };

          if (index === 0) {
            // Keep first player in original position
            adjustedPlayers.push(adjustedPlayer);
          } else {
            // Spread other players around the base position
            const angle = (index - 1) * (Math.PI / (playersInPosition.length - 1));
            const offsetX = Math.cos(angle) * spreadDistance;
            const offsetY = Math.sin(angle) * spreadDistance;

            adjustedPlayer.x = Math.max(5, Math.min(95, basePlayer.x + offsetX));
            adjustedPlayer.y = Math.max(10, Math.min(90, basePlayer.y + offsetY));

            // Final overlap check and adjustment
            adjustedPlayer = this.resolveOverlap(adjustedPlayer, adjustedPlayers, minDistance);
            adjustedPlayers.push(adjustedPlayer);
          }
        });
      }
    });

    return adjustedPlayers;
  }

  private resolveOverlap(player: any, existingPlayers: any[], minDistance: number): any {
    let adjustedPlayer = { ...player };
    let attempts = 0;
    const maxAttempts = 8;

    while (attempts < maxAttempts) {
      let hasOverlap = false;

      for (const existingPlayer of existingPlayers) {
        const distance = Math.sqrt(
          Math.pow(adjustedPlayer.x - existingPlayer.x, 2) +
          Math.pow(adjustedPlayer.y - existingPlayer.y, 2)
        );

        if (distance < minDistance) {
          hasOverlap = true;

          // Calculate direction to move away from overlapping player
          const deltaX = adjustedPlayer.x - existingPlayer.x;
          const deltaY = adjustedPlayer.y - existingPlayer.y;
          const magnitude = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

          if (magnitude > 0) {
            const moveDistance = minDistance + 3;
            const normalizedX = deltaX / magnitude;
            const normalizedY = deltaY / magnitude;

            adjustedPlayer.x = Math.max(5, Math.min(95,
              existingPlayer.x + normalizedX * moveDistance
            ));
            adjustedPlayer.y = Math.max(10, Math.min(90,
              existingPlayer.y + normalizedY * moveDistance
            ));
          } else {
            // If players are at exact same position, move randomly
            const angle = Math.random() * 2 * Math.PI;
            const moveDistance = minDistance + 3;
            adjustedPlayer.x = Math.max(5, Math.min(95,
              adjustedPlayer.x + Math.cos(angle) * moveDistance
            ));
            adjustedPlayer.y = Math.max(10, Math.min(90,
              adjustedPlayer.y + Math.sin(angle) * moveDistance
            ));
          }
          break;
        }
      }

      if (!hasOverlap) {
        break;
      }
      attempts++;
    }

    return adjustedPlayer;
  }

  // === NEW METHODS FOR ENHANCED FUNCTIONALITY ===

  // Technical Result Methods
  hasTechnicalResult(): boolean {
    return !!(this.selectedGame as ExtendedGameDTO)?.technicalLoss;
  }

  getTechnicalLosingTeam() {
    const technicalLoss = (this.selectedGame as ExtendedGameDTO)?.technicalLoss;
    if (!technicalLoss || !this.selectedGame) return null;

    if (technicalLoss.teamId === this.selectedGame.homeTeam.id) {
      return this.selectedGame.homeTeam;
    } else if (technicalLoss.teamId === this.selectedGame.awayTeam.id) {
      return this.selectedGame.awayTeam;
    }
    return null;
  }

  getTechnicalReason(): string {
    return (this.selectedGame as ExtendedGameDTO)?.technicalLoss?.reason || '';
  }

  // Broadcast Methods
  getBroadcastTeam(): string {
    const broadcast = getGameBroadcast(this.selectedGame as ExtendedGameDTO);
    return broadcast?.broadcastingTeam || '';
  }

  isGameTime(): boolean {
    if (!this.selectedGame?.date) return false;
    const gameTime = new Date(this.selectedGame.date);
    const now = new Date();
    const timeDiff = Math.abs(now.getTime() - gameTime.getTime());
    // Consider it "game time" if within 2 hours of scheduled time
    return timeDiff <= 2 * 60 * 60 * 1000;
  }

  openLiveStream(): void {
    const broadcast = getGameBroadcast(this.selectedGame as ExtendedGameDTO);
    if (broadcast?.streamUrl) {
      window.open(broadcast.streamUrl, '_blank', 'noopener,noreferrer');
    }
  }

  // Iframe broadcast methods
  shouldShowIframe(): boolean {
    return this.hasLiveBroadcast() && this.isGameTime() && !!this.getEmbedUrl();
  }

  getEmbedUrl(): string | null {
    const broadcast = getGameBroadcast(this.selectedGame as ExtendedGameDTO);
    if (!broadcast?.streamUrl) return null;

    return this.convertToEmbedUrl(broadcast.streamUrl);
  }

  private convertToEmbedUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();

      // Handle Twitch URLs
      if (hostname.includes('twitch.tv')) {
        const pathParts = urlObj.pathname.split('/').filter(part => part);
        if (pathParts.length > 0) {
          const channelName = pathParts[0];
          const currentDomain = window.location.hostname;
          return `https://player.twitch.tv/?channel=${channelName}&parent=${currentDomain}&autoplay=false`;
        }
      }

      // Handle YouTube URLs
      if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
        // Handle youtube.com/watch?v=VIDEO_ID
        if (urlObj.pathname === '/watch' && urlObj.searchParams.has('v')) {
          const videoId = urlObj.searchParams.get('v');
          return `https://www.youtube.com/embed/${videoId}?autoplay=0`;
        }

        // Handle youtube.com/channel/CHANNEL_ID/live
        if (urlObj.pathname.includes('/live')) {
          const pathParts = urlObj.pathname.split('/').filter(part => part);
          const channelIndex = pathParts.indexOf('channel');
          if (channelIndex !== -1 && channelIndex + 1 < pathParts.length) {
            const channelId = pathParts[channelIndex + 1];
            return `https://www.youtube.com/embed/live_stream?channel=${channelId}&autoplay=0`;
          }
        }

        // Handle youtu.be/VIDEO_ID
        if (hostname.includes('youtu.be')) {
          const videoId = urlObj.pathname.substring(1); // Remove leading slash
          return `https://www.youtube.com/embed/${videoId}?autoplay=0`;
        }
      }

      // Add more platforms as needed
      return null;
    } catch (error) {
      console.warn('Failed to convert URL to embed format:', url, error);
      return null;
    }
  }

  // Permission Methods
  isAdmin(): Observable<boolean> {
    return this.isAdmin$;
  }

  canEditGame(): Observable<boolean> {
    return this.canEditGame$;
  }

  canEditStats(): Observable<boolean> {
    if (!this.selectedGame) return this.permissionsService.isAdmin();
    return this.permissionsService.canEditGame(this.selectedGame.id);
  }

  canEditHomeTeam(): Observable<boolean> {
    if (!this.selectedGame) return this.permissionsService.isAdmin();
    return this.permissionsService.canEditTeam(this.selectedGame.homeTeam.id);
  }

  canEditAwayTeam(): Observable<boolean> {
    if (!this.selectedGame) return this.permissionsService.isAdmin();
    return this.permissionsService.canEditTeam(this.selectedGame.awayTeam.id);
  }

  // Action Methods
  onEditGameClick(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
    this.router.navigate(['/modify-game', this.selectedGame?.id]);
  }

  async onDeleteGameClick(): Promise<void> {
    if (!this.selectedGame) {
      return;
    }

    const confirmDelete = confirm(
      `⚠️ Are you sure you want to delete this game?\n\n` +
      `${this.selectedGame.homeTeam.name} vs ${this.selectedGame.awayTeam.name}\n` +
      `${this.selectedGame.date ? new Date(this.selectedGame.date).toLocaleDateString() : 'No date set'}\n\n` +
      'This action cannot be undone and will permanently remove:\n' +
      '• Game result and statistics\n' +
      '• Player performances\n' +
      '• All comments and predictions\n' +
      '• Match broadcast information'
    );

    if (!confirmDelete) {
      return;
    }

    try {
      await this.gameService.deleteGame(this.selectedGame.id);
      this.notificationService.success('Game deleted successfully');

      // Navigate back to dashboard or fixtures
      if (this.dialogRef) {
        this.dialogRef.close();
      } else {
        this.router.navigate(['/fixtures']);
      }
    } catch (error: any) {
      console.error('Error deleting game:', error);
      this.notificationService.error(error.message || 'Failed to delete game. Please try again.');
    }
  }

  onEditTeamStatsClick(team: 'home' | 'away'): void {
    if (!this.selectedGame) return;

    if (this.dialogRef) {
      this.dialogRef.close();
    }
    this.router.navigate(['/modify-game', this.selectedGame.id], {
      queryParams: {
        team: team,
        mode: 'stats'
      }
    });
  }

  goBack(): void {
    // Navigate back to previous page or to fixtures if no history
    if (window.history.length > 1) {
      window.history.back();
    } else {
      this.router.navigate(['/fixtures']);
    }
  }

  shouldShowFormationPitch(): boolean {
    return this.selectedGame?.status === GAME_STATUS.PLAYED || this.selectedGame?.status === GAME_STATUS.COMPLETED;
  }

  hasPlayerPerformances(): boolean {
    if (!this.selectedGame) return false;

    const homeTeamPerformance = this.selectedGame.homeTeam.playersPerformance;
    const awayTeamPerformance = this.selectedGame.awayTeam.playersPerformance;

    return !!(homeTeamPerformance && homeTeamPerformance.length > 0) ||
           !!(awayTeamPerformance && awayTeamPerformance.length > 0);
  }
}