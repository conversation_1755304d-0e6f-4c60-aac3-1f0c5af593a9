import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { UpdatePlayerPerformanceDataRequest } from '../shared/models/game.model';
import { GameDTO } from '@pro-clubs-manager/shared-dtos';
import { TopAvgRatingByPosition } from '../components/top-avg-rating-by-position/top-avg-rating-by-position.component';

@Injectable({
  providedIn: 'root'
})
export class GameService {
  GAME_CONTROLLER_URL = "game";

  constructor(private apiService: ApiService) { }

  async getGameById(id: string): Promise<GameDTO> {
    const response = await this.apiService.get<GameDTO>(`${this.GAME_CONTROLLER_URL}/${id}/`);

    return response.data;
  }

  async getAllGames(): Promise<GameDTO[]> {
    const response = await this.apiService.get<GameDTO[]>(`${this.GAME_CONTROLLER_URL}/all`);

    return response.data;
  }

  async getTopAvgRatingByPosition(position: string, minimumGames: number): Promise<TopAvgRatingByPosition[]> {
    const response = await this.apiService.get<TopAvgRatingByPosition[]>(`${this.GAME_CONTROLLER_URL}/topAvgRatingByPosition/${position}`,
       { params: {minimumGames: minimumGames} });

    return response.data;
  }

  async deleteGame(id: string): Promise<GameDTO> {
    const response = await this.apiService.delete<GameDTO>(`${this.GAME_CONTROLLER_URL}/${id}/`);

    return response.data;
  }

  async updateGameResult(id: string, homeTeamGoals: number, awayTeamGoals: number, date: Date, isPlayoffGame: boolean = false, penalties?: { homeTeamPenalties: number; awayTeamPenalties: number }): Promise<GameDTO> {
    const response = await this.apiService.put<GameDTO>(`${this.GAME_CONTROLLER_URL}/${id}/updateResult`,
      { homeTeamGoals: homeTeamGoals, awayTeamGoals: awayTeamGoals, date: date, isPlayoffGame: isPlayoffGame, penalties: penalties });

    return response.data;
  }

  async updateGameDate(id: string, date: Date): Promise<void> {
    await this.apiService.put<void>(`${this.GAME_CONTROLLER_URL}/${id}/updateDate`, { date: date });
  }

  async updateGameBroadcast(id: string, streamUrl: string, broadcastingTeam: string): Promise<void> {
    await this.apiService.put<void>(`${this.GAME_CONTROLLER_URL}/${id}/updateBroadcast`, {
      streamUrl: streamUrl,
      broadcastingTeam: broadcastingTeam
    });
  }

  async setTechnicalResult(id: string, losingTeamId: string, reason: string): Promise<void> {
    await this.apiService.put<void>(`${this.GAME_CONTROLLER_URL}/${id}/technical-loss`, {
      losingTeamId: losingTeamId,
      reason: reason,
      date: new Date()
    });
  }

  async updateTeamPlayersPerformance(gameId: string, playersPerformace: UpdatePlayerPerformanceDataRequest[], isHomeTeam: boolean): Promise<any> {
    const response = await this.apiService.put<any>(`${this.GAME_CONTROLLER_URL}/${gameId}/teamPlayersPerformance`,
      { playersPerformace: playersPerformace, isHomeTeam: isHomeTeam });

    return response.data;
  }

  async getTeamGames(teamId: string): Promise<GameDTO[]> {
    const response = await this.apiService.get<GameDTO[]>(`${this.GAME_CONTROLLER_URL}/team/${teamId}`);

    return response.data;
  }

  async getTeamVsTeamHistory(team1Id: string, team2Id: string, limit?: number): Promise<GameDTO[]> {
    const params = limit ? `?limit=${limit}` : '';
    const response = await this.apiService.get<GameDTO[]>(`${this.GAME_CONTROLLER_URL}/history/${team1Id}/${team2Id}${params}`);
    return response.data;
  }
}