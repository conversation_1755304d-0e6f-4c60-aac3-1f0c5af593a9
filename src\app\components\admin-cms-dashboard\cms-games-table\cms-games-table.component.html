<div class="cms-table-container">
    <div class="table-header">
        <h3>
            <i class="fas fa-futbol"></i>
            Games Management
        </h3>
        <div class="table-actions">
            <button class="action-btn create-btn" routerLink="/add-fixture">
                <i class="fas fa-calendar-plus"></i>
                Add Fixture
            </button>
        </div>
    </div>

    <div class="table-wrapper">
        <table class="cms-table">
            <thead>
                <tr>
                    <th>Match</th>
                    <th>Result</th>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let game of games" class="table-row">
                    <td class="match-cell">
                        <div class="match-info">
                            <div class="team home-team">
                                <img [src]="game.homeTeam.imgUrl || 'assets/Icons/Team.png'" 
                                     [alt]="game.homeTeam.name"
                                     (error)="onImageError($event)"
                                     class="team-logo">
                                <span class="team-name">{{ game.homeTeam.name }}</span>
                            </div>
                            <div class="vs-separator">
                                <span>vs</span>
                            </div>
                            <div class="team away-team">
                                <span class="team-name">{{ game.awayTeam.name }}</span>
                                <img [src]="game.awayTeam.imgUrl || 'assets/Icons/Team.png'" 
                                     [alt]="game.awayTeam.name"
                                     (error)="onImageError($event)"
                                     class="team-logo">
                            </div>
                        </div>
                        <div class="game-id">ID: {{ game.id }}</div>
                    </td>
                    <td>
                        <span class="result-text" [class.no-result]="game.status === GameStatus.SCHEDULED">
                            {{ getGameResult(game) }}
                        </span>
                    </td>
                    <td>
                        <span class="date-text">{{ getGameDate(game) }}</span>
                    </td>
                    <td>
                        <span class="time-text">{{ getGameTime(game) }}</span>
                    </td>
                    <td>
                        <span class="status-badge" [class]="getStatusBadgeClass(game.status)">
                            {{ getStatusText(game.status) }}
                        </span>
                    </td>
                    <td class="actions-cell">
                        <div class="action-buttons">
                            <button class="action-btn view-btn" 
                                    (click)="viewGameDetails(game.id)"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn edit-btn" 
                                    (click)="editGame(game.id)"
                                    title="Edit Game">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-btn" 
                                    (click)="deleteGame(game)"
                                    [disabled]="isGameDeleting(game.id)"
                                    title="Delete Game">
                                <i class="fas fa-trash" *ngIf="!isGameDeleting(game.id)"></i>
                                <i class="fas fa-spinner fa-spin" *ngIf="isGameDeleting(game.id)"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="games.length === 0">
            <div class="empty-icon">
                <i class="fas fa-futbol"></i>
            </div>
            <h4>No Games Found</h4>
            <p>No games match your current search criteria.</p>
            <button class="action-btn create-btn" routerLink="/add-fixture">
                <i class="fas fa-calendar-plus"></i>
                Create First Fixture
            </button>
        </div>
    </div>
</div>
