const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
async function connectToDatabase() {
  try {
    const runMode = process.env.RUN_MODE || 'prod';
    const dbName = runMode === 'prod' ? process.env.MONGODB_DB_PROD : process.env.MONGODB_DB_DEV;
    const mongoUri = `mongodb+srv://${process.env.MONGO_DB_USERNAME}:${process.env.MONGO_DB_PASSWORD}@${process.env.MONGODB_CLUSTER}/${dbName}?retryWrites=true&w=majority`;
    
    console.log(`Connecting to MongoDB (${runMode} mode)...`);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

// Test the aggregation directly
async function testItamarAggregation() {
  try {
    await connectToDatabase();
    
    const itamarId = '67f2bd3317ec7db8bcd4f087';
    const leagueId = '65ecb1eb2f272e434483a821';
    const seasonNumber = 6;
    
    console.log('\n=== Testing Direct MongoDB Aggregation ===');
    console.log(`Player ID: ${itamarId}`);
    console.log(`League ID: ${leagueId}`);
    console.log(`Season: ${seasonNumber}`);
    
    // Get the Game collection
    const Game = mongoose.connection.collection('games');
    
    // Run the same aggregation as in the repository
    const result = await Game.aggregate([
      {
        $match: {
          league: new mongoose.Types.ObjectId(leagueId),
          seasonNumber: seasonNumber,
          result: { $exists: true } // Only completed games
        }
      },
      {
        $project: {
          players: {
            $concatArrays: [
              { $ifNull: ["$homeTeamPlayersPerformance", []] },
              { $ifNull: ["$awayTeamPlayersPerformance", []] }
            ]
          }
        }
      },
      { $unwind: "$players" },
      {
        $group: {
          _id: "$players.playerId",
          totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
          totalAssists: { $sum: { $ifNull: ["$players.assists", 0] } },
          totalGames: { $sum: 1 }
        }
      },
      { $sort: { totalGoals: -1, totalGames: 1 } }
    ]).toArray();
    
    console.log(`\nFound stats for ${result.length} players`);
    
    // Find Itamar's stats
    const itamarStats = result.find(stats => stats._id.toString() === itamarId);
    
    if (itamarStats) {
      console.log('\n=== Itamar\'s Aggregated Stats ===');
      console.log('Games:', itamarStats.totalGames);
      console.log('Goals:', itamarStats.totalGoals);
      console.log('Assists:', itamarStats.totalAssists);
      
      console.log('\n=== Expected vs Actual ===');
      console.log('Expected: 25 games, 23 goals');
      console.log(`Actual: ${itamarStats.totalGames} games, ${itamarStats.totalGoals} goals`);
      
      if (itamarStats.totalGoals === 23 && itamarStats.totalGames === 25) {
        console.log('\n✅ AGGREGATION IS WORKING CORRECTLY!');
        console.log('The issue is that the deployed server is not using the updated code.');
      } else {
        console.log('\n❌ AGGREGATION MISMATCH!');
        console.log('The aggregation method itself has an issue.');
      }
    } else {
      console.log('\n❌ Itamar not found in aggregation results');
    }
    
    // Show top 5 players for reference
    console.log('\n=== Top 5 Players (for reference) ===');
    result.slice(0, 5).forEach((player, index) => {
      console.log(`${index + 1}. Player ${player._id}: ${player.totalGoals} goals, ${player.totalGames} games`);
    });
    
  } catch (error) {
    console.error('Error testing aggregation:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the test
testItamarAggregation();
