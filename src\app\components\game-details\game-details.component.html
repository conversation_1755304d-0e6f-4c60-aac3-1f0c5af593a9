<div class="game-details-container" *ngIf="selectedGame">
    <!-- Promotional Banner for Non-Authenticated Users -->
    <app-promotional-banner
        *ngIf="!isAuthenticated"
        [message]="promotionalMessage"
        size="medium"
        [dismissible]="true"
        [showSecondaryAction]="true">
    </app-promotional-banner>

    <!-- Game Header Component -->
    <app-game-header [game]="selectedGame" [showBackButton]="isStandalonePage" (backClick)="goBack()"
        (homeTeamClick)="navigateToTeamDetails($event)" (awayTeamClick)="navigateToTeamDetails($event)">
    </app-game-header>

    <!-- Technical Result -->
    <div class="technical-result" *ngIf="hasTechnicalResult()">
        <div class="alert alert-warning">
            <i class="fas fa-gavel"></i>
            <div>
                <h4>Technical Result</h4>
                <p><strong>{{ getTechnicalLosingTeam()?.name }}</strong> - {{ getTechnicalReason() }}</p>
            </div>
        </div>
    </div>

    <!-- Live Broadcast -->
    <div class="broadcast-info" *ngIf="hasLiveBroadcast()">
        <div class="alert alert-info">
            <i class="fas fa-video"></i>
            <div>
                <h4>Live Broadcast</h4>
                <p *ngIf="isGameTime()">Stream is now live by <strong>{{ getBroadcastTeam() }}</strong></p>
                <p *ngIf="!isGameTime()">
                    <i class="fas fa-clock"></i>
                    Will be streamed by <strong>{{ getBroadcastTeam() }}</strong> at game time
                </p>
            </div>
            <div class="broadcast-actions">
                <button class="broadcast-btn" (click)="openLiveStream()" [disabled]="!isGameTime()"
                    [class.live]="isGameTime()">
                    <i class="fas fa-external-link-alt"></i>
                    <span *ngIf="isGameTime()">Watch Live</span>
                    <span *ngIf="!isGameTime()">Available at Game Time</span>
                </button>
            </div>
        </div>

        <!-- Embedded Stream -->
        <div class="broadcast-iframe-container" *ngIf="shouldShowIframe()">
            <div class="iframe-header">
                <h5>
                    <i class="fas fa-play-circle"></i>
                    Live Stream - {{ getBroadcastTeam() }}
                </h5>
                <button class="iframe-fullscreen-btn" (click)="openLiveStream()" title="Open in new tab">
                    <i class="fas fa-external-link-alt"></i>
                </button>
            </div>
            <div class="iframe-wrapper">
                <iframe
                    [src]="getEmbedUrl()"
                    frameborder="0"
                    allowfullscreen
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    sandbox="allow-scripts allow-same-origin allow-presentation"
                    loading="lazy">
                </iframe>
            </div>
        </div>
    </div>

    <!-- Match Details -->
    <div class="match-details"
        *ngIf="selectedGame?.status === GameStatus.PLAYED || selectedGame?.status === GameStatus.COMPLETED">


        <!-- Formation Pitch -->
        <div class="formation-pitch" *ngIf="shouldShowFormationPitch()">
            <!-- Mini Pitch with Players -->
            <app-mini-pitch-formation
                *ngIf="hasPlayerPerformances()"
                [homeTeam]="getHomeTeamFormation()"
                [awayTeam]="getAwayTeamFormation()"
                [showPlayerStats]="true"
                [showPlayerNames]="true"
                [compactMode]="false">
            </app-mini-pitch-formation>

            <!-- Empty State for No Player Performances -->
            <div class="empty-formation-state" *ngIf="!hasPlayerPerformances()">
                <div class="empty-state-content">
                    <div class="empty-state-icon">
                        <i class="fas fa-users-slash"></i>
                    </div>
                    <h4>No Player Performances</h4>
                    <p>Player formations and stats will appear here once match data is available.</p>
                </div>
            </div>
        </div>

        <!-- Player of the Match -->
        <div class="potm" *ngIf="playerOfTheMatch">
            <div class="potm-card" (click)="navigateToPlayerDetails(playerOfTheMatch.playerId)">
                <div class="potm-header">
                    <i class="fas fa-crown"></i>
                    <h4>Player of the Match</h4>
                </div>
                <div class="potm-content">
                    <img [src]="playerOfTheMatch.imgUrl || 'assets/Icons/User.jpg'" [alt]="playerOfTheMatch.name">
                    <div class="potm-info">
                        <h5>{{playerOfTheMatch.name}}</h5>
                        <div class="potm-details">
                            <span class="potm-team">{{playerOfTheMatchTeamName}}</span>
                            <div class="potm-rating">
                                <i class="fas fa-star"></i>
                                <span>{{playerOfTheMatch.rating}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scheduled Game -->
    <div class="scheduled-message" *ngIf="selectedGame.status === GameStatus.SCHEDULED &&
        selectedGame.date">
        <div class="alert alert-info">
            <i class="fas fa-calendar-alt"></i>
            <div>
                <h4>Upcoming Match</h4>
                <p>Scheduled for {{selectedGame.date | date:'fullDate'}} at {{selectedGame.date | date:'shortTime'}}
                </p>
            </div>
        </div>
    </div>

    <!-- Match Predictions -->
    <div class="predictions-section" *ngIf="selectedGame">
        <app-prediction-voting
            [game]="selectedGame"
            [showResults]="true"
            [compact]="false">
        </app-prediction-voting>
    </div>

    <!-- Game Actions -->
    <app-game-actions [canEdit$]="isAdmin()" [hasLiveStream]="hasLiveBroadcast()"
        [liveStreamUrl]="getBroadcastInfo()?.streamUrl" [isGameTime]="isGameTime()" (editGameClick)="onEditGameClick()">
    </app-game-actions>

    <!-- Team Stats Actions -->
    <div class="stats-actions" *ngIf="selectedGame && ((isAdmin() | async) || (canEditHomeTeam() | async) || (canEditAwayTeam() | async))">
        <div class="stats-buttons">
            <!-- Admin can edit both teams -->
            <ng-container *ngIf="isAdmin() | async">
                <button class="btn-secondary" (click)="onEditTeamStatsClick('home')" *ngIf="selectedGame.homeTeam">
                    <i class="fas fa-chart-line"></i>
                    Edit {{selectedGame.homeTeam.name}} Stats
                </button>
                <button class="btn-secondary" (click)="onEditTeamStatsClick('away')" *ngIf="selectedGame.awayTeam">
                    <i class="fas fa-chart-line"></i>
                    Edit {{selectedGame.awayTeam.name}} Stats
                </button>

                <!-- Admin-only Delete Game Button -->
                <button class="btn-danger" (click)="onDeleteGameClick()" title="Delete this game permanently">
                    <i class="fas fa-trash"></i>
                    Delete Game
                </button>
            </ng-container>

            <!-- Captain can edit only their team -->
            <ng-container *ngIf="!(isAdmin() | async)">
                <button class="btn-secondary" *ngIf="canEditHomeTeam() | async" (click)="onEditTeamStatsClick('home')">
                    <i class="fas fa-chart-line"></i>
                    Edit {{selectedGame.homeTeam.name}} Stats
                </button>
                <button class="btn-secondary" *ngIf="canEditAwayTeam() | async" (click)="onEditTeamStatsClick('away')">
                    <i class="fas fa-chart-line"></i>
                    Edit {{selectedGame.awayTeam.name}} Stats
                </button>
            </ng-container>
        </div>
    </div>

    <!-- Game Comments -->
    <div class="comments-section" *ngIf="selectedGame">
        <app-game-comments [gameId]="selectedGame.id"></app-game-comments>
    </div>
</div>