<!-- <PERSON>er (only for standalone mode) -->
<div class="page-header" *ngIf="showBackButton">
    <button class="back-btn" (click)="onBackClick()">
        <i class="fas fa-arrow-left"></i>
        <span>Back</span>
    </button>
    <h1 class="page-title">Game Details</h1>
</div>

<!-- Match Header -->
<div class="match-header">
    <div class="match-status-badge" [ngClass]="getStatusClass()">
        <i class="fas fa-circle status-indicator"></i>
        <span>{{ game.status }}</span>
    </div>

    <!-- Teams and Score -->
    <div class="teams-container">
        <!-- Home Team -->
        <div class="team-section home-team" (click)="onHomeTeamClick()">
            <div class="team-info">
                <img class="team-logo" 
                     [src]="game.homeTeam.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                     [alt]="game.homeTeam.name + ' logo'">
                <div class="team-details">
                    <h3 class="team-name">{{ game.homeTeam.name }}</h3>
                    <span class="team-label">Home</span>
                </div>
            </div>
        </div>

        <!-- Score Section -->
        <div class="score-section">
            <div class="score-container" *ngIf="hasResult()">
                <div class="score-display">
                    <span class="score-number home-score">{{ game.result!.homeTeamGoals }}</span>
                    <span class="score-separator">-</span>
                    <span class="score-number away-score">{{ game.result!.awayTeamGoals }}</span>
                </div>

                <!-- Penalty Shootout Result -->
                <div class="penalty-result" *ngIf="hasPenalties()">
                    <div class="penalty-display">
                        <span class="penalty-label">PENS</span>
                        <span class="penalty-score">
                            <span class="penalty-number home-penalty">{{ game.result!.penalties!.homeTeamPenalties }}</span>
                            <span class="penalty-separator">-</span>
                            <span class="penalty-number away-penalty">{{ game.result!.penalties!.awayTeamPenalties }}</span>
                        </span>
                    </div>
                </div>

                <div class="match-time">
                    <i class="fas fa-clock"></i>
                    <span>{{ game.date | date:'dd/MM/yyyy HH:mm' }}</span>
                </div>
            </div>

            <div class="vs-container" *ngIf="isScheduled()">
                <div class="vs-text">VS</div>
                <div class="match-time">
                    <i class="fas fa-calendar-alt"></i>
                    <span>{{ game.date | date:'dd/MM/yyyy HH:mm' }}</span>
                </div>
            </div>
        </div>

        <!-- Away Team -->
        <div class="team-section away-team" (click)="onAwayTeamClick()">
            <div class="team-info">
                <div class="team-details">
                    <h3 class="team-name">{{ game.awayTeam.name }}</h3>
                    <span class="team-label">Away</span>
                </div>
                <img class="team-logo" 
                     [src]="game.awayTeam.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                     [alt]="game.awayTeam.name + ' logo'">
            </div>
        </div>
    </div>
</div>
