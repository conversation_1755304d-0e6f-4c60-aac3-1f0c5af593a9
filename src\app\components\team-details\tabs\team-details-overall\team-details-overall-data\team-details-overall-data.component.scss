/* Modern Team Management Panel Styles */

.team-management-panel {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

.management-section {
    background: var(--surface-primary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-secondary);
    overflow: hidden;
    transition: all 0.2s ease;

    &:hover {
        border-color: var(--border-accent);
    }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface-tertiary);
    border-bottom: 1px solid var(--border-secondary);

    .section-title {
        font-family: var(--font-sans);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        i {
            color: var(--accent-primary);
            font-size: var(--text-base);
        }
    }

    .section-actions {
        display: flex;
        gap: var(--spacing-sm);
    }
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--radius-sm);
    font-family: var(--font-sans);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;

    &.edit-btn {
        background: var(--accent-primary);
        color: var(--text-on-accent);

        &:hover {
            background: var(--accent-primary-hover);
            transform: translateY(-1px);
        }
    }

    &.cancel-btn {
        background: var(--surface-secondary);
        color: var(--text-secondary);
        border: 1px solid var(--border-secondary);

        &:hover {
            background: var(--surface-tertiary);
            color: var(--text-primary);
        }
    }

    &.save-btn {
        background: var(--success);
        color: var(--text-on-accent);

        &:hover {
            background: var(--success-hover);
            transform: translateY(-1px);
        }
    }
}

.edit-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.section-content {
    padding: var(--spacing-lg);
}

/* Team Name Management */
.team-name-display {
    .current-name {
        font-family: var(--font-sans);
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        display: block;
        padding: var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-sm);
        border: 1px solid var(--border-secondary);
    }
}

.team-name-edit {
    .name-input {
        width: 100%;
        padding: var(--spacing-md);
        border: 2px solid var(--border-secondary);
        border-radius: var(--radius-sm);
        background: var(--surface-primary);
        color: var(--text-primary);
        font-family: var(--font-sans);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-medium);
        transition: border-color 0.2s ease;

        &:focus {
            outline: none;
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 3px var(--accent-primary-alpha);
        }

        &::placeholder {
            color: var(--text-tertiary);
        }
    }
}

/* Logo Management */
.logo-management {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);

    .current-logo {
        .team-logo-preview {
            width: 80px;
            height: 80px;
            border-radius: var(--radius-md);
            border: 2px solid var(--border-secondary);
            object-fit: cover;
            transition: all 0.2s ease;

            &:hover {
                border-color: var(--accent-primary);
                transform: scale(1.05);
            }
        }
    }

    .logo-actions {
        .upload-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--accent-secondary);
            color: var(--text-on-accent);
            border: none;
            border-radius: var(--radius-sm);
            font-family: var(--font-sans);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background: var(--accent-secondary-hover);
                transform: translateY(-1px);
            }

            i {
                font-size: var(--text-xs);
            }
        }

        .file-input {
            display: none;
        }
    }
}

/* Captain Management */
.captain-display {
    .captain-card {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-secondary);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            border-color: var(--accent-primary);
            background: var(--surface-tertiary);
            transform: translateY(-1px);
        }

        .captain-photo {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-md);
            border: 2px solid var(--border-secondary);
            object-fit: cover;
            transition: border-color 0.2s ease;
        }

        .captain-info {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);

            .captain-name {
                font-family: var(--font-sans);
                font-size: var(--text-lg);
                font-weight: var(--font-weight-semibold);
                color: var(--text-primary);
            }

            .captain-role {
                font-family: var(--font-sans);
                font-size: var(--text-sm);
                color: var(--text-secondary);
                font-weight: var(--font-weight-medium);
            }
        }

        &:hover .captain-photo {
            border-color: var(--accent-primary);
        }
    }
}

.no-captain {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-tertiary);
    text-align: center;

    i {
        font-size: var(--text-2xl);
        opacity: 0.5;
    }

    span {
        font-family: var(--font-sans);
        font-size: var(--text-base);
        font-weight: var(--font-weight-medium);
    }
}

.captain-selection {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);

    .save-captain-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
        background: var(--success);
        color: var(--text-on-accent);
        border: none;
        border-radius: var(--radius-sm);
        font-family: var(--font-sans);
        font-size: var(--text-base);
        font-weight: var(--font-weight-semibold);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background: var(--success-hover);
            transform: translateY(-1px);
        }

        &:active {
            transform: translateY(0);
        }

        i {
            font-size: var(--text-sm);
        }
    }
}

/* Team Information Display */
.team-info-display {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-md);

    .team-logo-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
        background: linear-gradient(135deg, var(--surface-secondary), var(--surface-tertiary));
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-primary);
        margin-bottom: var(--spacing-md);
        min-width: 0; /* Allow flex items to shrink */

        @media (max-width: 768px) {
            gap: var(--spacing-md);
            padding: var(--spacing-md);
        }

        @media (max-width: 480px) {
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            flex-direction: column;
            text-align: center;
        }

        .team-logo-container {
            flex-shrink: 0;

            .team-logo-display {
                width: 80px;
                height: 80px;
                border-radius: var(--radius-lg);
                border: 3px solid var(--border-primary);
                object-fit: cover;
                transition: all 0.3s ease;
                box-shadow: var(--shadow-md);

                @media (max-width: 768px) {
                    width: 60px;
                    height: 60px;
                }

                @media (max-width: 480px) {
                    width: 50px;
                    height: 50px;
                }

                &:hover {
                    transform: scale(1.05);
                    border-color: var(--primary);
                    box-shadow: var(--shadow-lg);
                }
            }
        }

        .team-name-display {
            flex: 1;
            min-width: 0; /* Allow text to shrink */

            .team-name {
                font-size: var(--text-lg);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
                margin: 0 0 var(--spacing-xs) 0;
                background: linear-gradient(135deg, var(--primary), var(--accent-primary));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                word-wrap: break-word;
                overflow-wrap: break-word;
                line-height: 1.2;

                @media (max-width: 768px) {
                    font-size: var(--text-base);
                }

                @media (max-width: 480px) {
                    font-size: var(--text-sm);
                    line-height: 1.3;
                }
            }

            .team-subtitle {
                font-size: var(--text-sm);
                color: var(--text-secondary);
                margin: 0;
                font-weight: var(--font-weight-medium);
            }
        }
    }

    .info-item {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-md);
        background: var(--bg-secondary);
        border-radius: var(--border-radius-md);
        border: 1px solid var(--border-color);
        transition: all 0.2s ease;

        &:hover {
            border-color: var(--primary-color);
            background: var(--bg-primary);
        }

        .info-label {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--text-sm);
            color: var(--text-secondary);
            font-weight: 500;

            i {
                color: var(--primary-color);
                width: 16px;
                text-align: center;
            }
        }

        .info-value {
            font-size: var(--text-base);
            color: var(--text-primary);
            font-weight: 600;
            margin-left: 24px; // Align with label text after icon
        }
    }
}

/* Quick Actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);

    .action-btn {
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: var(--text-base);
        min-width: 200px;
        justify-content: center;
    }

    @media (min-width: 768px) {
        flex-direction: row;
        justify-content: center;

        .action-btn {
            min-width: 180px;
        }
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .team-management-panel {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }

    .section-header {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);

        .section-actions {
            width: 100%;
            justify-content: flex-end;
        }
    }

    .section-content {
        padding: var(--spacing-md);
    }

    .captain-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm) !important;

        .captain-info {
            align-items: center;
        }
    }

    .action-btn {
        font-size: var(--text-xs);
        padding: var(--spacing-xs);
    }
}