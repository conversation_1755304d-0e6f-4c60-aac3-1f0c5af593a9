/* === STUNNING MODERN TEAM GAMES DESIGN === */

.team-games-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
    background: var(--surface-primary);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    font-family: var(--font-sans);
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--primary-300), transparent);
        opacity: 0.6;
    }
}

/* === STUNNING HEADER DESIGN === */
.games-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2xl);
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border-bottom: 1px solid var(--border-primary);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(99, 102, 241, 0.05) 0%,
            rgba(255, 215, 0, 0.03) 100%);
        pointer-events: none;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-xl);
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    @media (max-width: 480px) {
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
        z-index: 1;

        @media (max-width: 768px) {
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .header-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
                animation: shimmer 3s infinite;
            }

            i {
                font-size: var(--text-2xl);
                color: white;
                z-index: 1;
            }

            @media (max-width: 768px) {
                width: 56px;
                height: 56px;

                i {
                    font-size: var(--text-xl);
                }
            }
        }

        .header-text {
            .games-title {
                font-size: var(--text-3xl);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
                margin: 0 0 var(--spacing-xs) 0;
                background: linear-gradient(135deg, var(--text-primary), var(--primary));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;

                @media (max-width: 768px) {
                    font-size: var(--text-2xl);
                }

                @media (max-width: 480px) {
                    font-size: var(--text-xl);
                }
            }

            .games-subtitle {
                font-size: var(--text-base);
                color: var(--text-secondary);
                margin: 0;
                font-weight: var(--font-weight-medium);

                @media (max-width: 480px) {
                    font-size: var(--text-sm);
                }
            }
        }
    }

    .games-stats {
        display: flex;
        gap: var(--spacing-xl);
        z-index: 1;

        @media (max-width: 768px) {
            gap: var(--spacing-lg);
        }

        @media (max-width: 480px) {
            gap: var(--spacing-md);
            flex-wrap: wrap;
            justify-content: center;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-md) var(--spacing-lg);
            background: rgba(15, 23, 42, 0.8);
            border-radius: var(--radius-xl);
            border: 1px solid var(--border-primary);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                border-color: var(--primary-300);
            }

            @media (max-width: 480px) {
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .stat-number {
                font-size: var(--text-2xl);
                font-weight: var(--font-weight-bold);
                color: var(--primary);
                font-family: var(--font-mono);

                @media (max-width: 480px) {
                    font-size: var(--text-xl);
                }
            }

            .stat-label {
                font-size: var(--text-xs);
                color: var(--text-secondary);
                font-weight: var(--font-weight-medium);
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }
        }
    }
}

/* === MODERN GAMES GRID === */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--spacing-xl);
    padding: var(--spacing-2xl);

    @media (max-width: 1200px) {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: var(--spacing-lg);
        padding: var(--spacing-xl);
    }

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md);
        gap: var(--spacing-sm);
    }
}

/* === STUNNING GAME CARDS === */
.game-card {
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    position: relative;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    backdrop-filter: blur(10px);
    padding: var(--spacing-xl);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(99, 102, 241, 0.02) 0%,
            rgba(255, 215, 0, 0.01) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 8px 16px rgba(99, 102, 241, 0.1);
        border-color: var(--primary-300);

        &::before {
            opacity: 1;
        }

        .card-hover-effect {
            opacity: 1;
            transform: scale(1);
        }

        .team-glow {
            opacity: 0.6;
        }
    }

    &.playoff-game {
        border: 2px solid var(--warning-400);
        background: linear-gradient(135deg,
            var(--surface-secondary) 0%,
            rgba(245, 158, 11, 0.05) 100%);

        .playoff-badge {
            display: flex;
        }
    }

    &.completed {
        .game-status-badge.status-completed {
            background: linear-gradient(135deg, var(--success-500), var(--success-600));
        }
    }

    &.scheduled {
        .game-status-badge.status-scheduled {
            background: linear-gradient(135deg, var(--info-500), var(--info-600));
        }
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md);
    }

    .card-hover-effect {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(99, 102, 241, 0.03) 0%,
            rgba(255, 215, 0, 0.02) 100%);
        opacity: 0;
        transform: scale(0.95);
        transition: all 0.3s ease;
        pointer-events: none;
    }
}

/* === GAME STATUS BADGE === */
.game-status-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-semibold);
    color: white;
    z-index: 2;
    backdrop-filter: blur(10px);

    &.status-completed {
        background: linear-gradient(135deg, var(--success-500), var(--success-600));
        box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
    }

    &.status-scheduled {
        background: linear-gradient(135deg, var(--info-500), var(--info-600));
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .status-indicator {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: currentColor;
        animation: pulse 2s infinite;
    }
}

/* === PLAYOFF BADGE === */
.playoff-badge {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    display: none;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
    color: white;
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    z-index: 2;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

    i {
        font-size: 10px;
    }
}

/* === GAME DATE === */
.game-date {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
    z-index: 1;

    .date-primary {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        font-family: var(--font-mono);
    }

    .date-secondary {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
    }

    @media (max-width: 480px) {
        margin-bottom: var(--spacing-md);

        .date-primary {
            font-size: var(--text-base);
        }

        .date-secondary {
            font-size: var(--text-xs);
        }
    }
}

/* === TEAMS CONTAINER === */
.teams-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
    z-index: 1;

    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

/* === TEAM STYLES === */
.team {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
    min-width: 0;

    &.home-team {
        justify-content: flex-end;

        @media (max-width: 768px) {
            justify-content: center;
        }

        .team-info {
            text-align: right;
            order: -1;

            @media (max-width: 768px) {
                text-align: center;
                order: 0;
            }
        }
    }

    &.away-team {
        justify-content: flex-start;

        @media (max-width: 768px) {
            justify-content: center;
        }

        .team-info {
            text-align: left;

            @media (max-width: 768px) {
                text-align: center;
            }
        }
    }

    .team-logo-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .team-logo {
            width: 64px;
            height: 64px;
            border-radius: var(--radius-2xl);
            object-fit: cover;
            border: 3px solid var(--border-primary);
            transition: all 0.3s ease;
            z-index: 1;

            @media (max-width: 768px) {
                width: 56px;
                height: 56px;
            }

            @media (max-width: 480px) {
                width: 48px;
                height: 48px;
            }
        }

        .team-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, var(--primary-400), transparent 70%);
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;

            @media (max-width: 768px) {
                width: 70px;
                height: 70px;
            }

            @media (max-width: 480px) {
                width: 60px;
                height: 60px;
            }
        }
    }

    .team-info {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
        min-width: 0;

        .team-name {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            @media (max-width: 768px) {
                font-size: var(--text-base);
                white-space: normal;
                overflow: visible;
                text-overflow: unset;
            }

            @media (max-width: 480px) {
                font-size: var(--text-sm);
            }
        }

        .team-label {
            font-size: var(--text-xs);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
    }
}

/* === SCORE CONTAINER === */
.score-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--surface-tertiary), var(--surface-secondary));
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border-primary);
    min-width: 120px;
    z-index: 1;

    @media (max-width: 768px) {
        min-width: 100px;
        padding: var(--spacing-md);
    }

    @media (max-width: 480px) {
        min-width: 80px;
        padding: var(--spacing-sm);
    }

    .score-display {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);

        .score-main {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            .score-number {
                font-size: var(--text-3xl);
                font-weight: var(--font-weight-black);
                font-family: var(--font-mono);
                color: var(--text-primary);
                min-width: 40px;
                text-align: center;

                &.home {
                    color: var(--primary);
                }

                &.away {
                    color: var(--accent-primary);
                }

                @media (max-width: 768px) {
                    font-size: var(--text-2xl);
                    min-width: 32px;
                }

                @media (max-width: 480px) {
                    font-size: var(--text-xl);
                    min-width: 28px;
                }
            }

            .score-separator {
                display: flex;
                align-items: center;
                justify-content: center;

                .separator-dot {
                    width: 8px;
                    height: 8px;
                    background: var(--text-secondary);
                    border-radius: 50%;
                    animation: pulse 2s infinite;

                    @media (max-width: 480px) {
                        width: 6px;
                        height: 6px;
                    }
                }
            }
        }

        .score-vs {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);

            .vs-text {
                font-size: var(--text-lg);
                font-weight: var(--font-weight-bold);
                color: var(--text-secondary);
                font-family: var(--font-mono);

                @media (max-width: 480px) {
                    font-size: var(--text-base);
                }
            }

            .vs-line {
                width: 40px;
                height: 2px;
                background: linear-gradient(90deg, var(--primary-400), var(--accent-primary));
                border-radius: 1px;

                @media (max-width: 480px) {
                    width: 30px;
                }
            }
        }
    }

    .score-edit {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);

        .edit-container {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            .score-input {
                width: 60px;
                height: 60px;
                border: 2px solid var(--border-primary);
                border-radius: var(--radius-xl);
                background: var(--surface-primary);
                color: var(--text-primary);
                font-size: var(--text-xl);
                font-weight: var(--font-weight-bold);
                font-family: var(--font-mono);
                text-align: center;
                transition: all 0.3s ease;

                &:focus {
                    outline: none;
                    border-color: var(--primary-500);
                    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
                }

                &.home-input:focus {
                    border-color: var(--primary-500);
                }

                &.away-input:focus {
                    border-color: var(--accent-primary);
                }

                @media (max-width: 768px) {
                    width: 50px;
                    height: 50px;
                    font-size: var(--text-lg);
                }

                @media (max-width: 480px) {
                    width: 40px;
                    height: 40px;
                    font-size: var(--text-base);
                }
            }

            .edit-separator {
                font-size: var(--text-xl);
                font-weight: var(--font-weight-bold);
                color: var(--text-secondary);
                margin: 0 var(--spacing-xs);
            }
        }

        .edit-actions {
            display: flex;
            gap: var(--spacing-sm);

            .save-btn, .cancel-btn {
                width: 36px;
                height: 36px;
                border: none;
                border-radius: var(--radius-full);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: var(--text-sm);

                &:hover {
                    transform: scale(1.1);
                }
            }

            .save-btn {
                background: linear-gradient(135deg, var(--success-500), var(--success-600));
                color: white;

                &:hover {
                    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
                }
            }

            .cancel-btn {
                background: linear-gradient(135deg, var(--error-500), var(--error-600));
                color: white;

                &:hover {
                    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
                }
            }
        }
    }
}

/* === GAME ACTIONS === */
.game-actions {
    position: absolute;
    bottom: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    gap: var(--spacing-sm);
    z-index: 2;

    .action-button {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        color: white;
        border: none;
        border-radius: var(--radius-full);
        font-size: var(--text-xs);
        font-weight: var(--font-weight-semibold);
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
        }

        &.edit-button {
            background: linear-gradient(135deg, var(--info-500), var(--info-600));

            &:hover {
                box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
            }
        }

        i {
            font-size: 10px;
        }

        @media (max-width: 480px) {
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: 10px;

            span {
                display: none;
            }
        }
    }
}

/* === LOADING STATES === */
.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: var(--surface-primary);
    border-radius: var(--radius-2xl);

    .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xl);
        text-align: center;

        .loading-spinner {
            position: relative;
            width: 80px;
            height: 80px;

            .spinner-ring {
                position: absolute;
                width: 100%;
                height: 100%;
                border: 3px solid transparent;
                border-radius: 50%;
                animation: spin 2s linear infinite;

                &:nth-child(1) {
                    border-top-color: var(--primary-500);
                    animation-delay: 0s;
                }

                &:nth-child(2) {
                    border-right-color: var(--accent-500);
                    animation-delay: 0.3s;
                    width: 70%;
                    height: 70%;
                    top: 15%;
                    left: 15%;
                }

                &:nth-child(3) {
                    border-bottom-color: var(--success-500);
                    animation-delay: 0.6s;
                    width: 40%;
                    height: 40%;
                    top: 30%;
                    left: 30%;
                }
            }
        }

        .loading-title {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0;
        }

        .loading-subtitle {
            font-size: var(--text-base);
            color: var(--text-secondary);
            margin: 0;
        }
    }
}

/* === EMPTY STATE === */
.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: var(--surface-primary);
    border-radius: var(--radius-2xl);

    .empty-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);
        text-align: center;
        max-width: 400px;
        padding: var(--spacing-2xl);

        .empty-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--surface-tertiary), var(--surface-secondary));
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-primary);

            i {
                font-size: var(--text-3xl);
                color: var(--text-secondary);
            }
        }

        .empty-title {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0;
        }

        .empty-subtitle {
            font-size: var(--text-base);
            color: var(--text-secondary);
            margin: 0;
            line-height: var(--leading-relaxed);
        }
    }
}

/* === ANIMATIONS === */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}



