/* === AMAZING IPL SPINNER DESIGN === */

.pro-clubs-spinner-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  overflow: hidden;
}

.spinner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(0, 0, 0, 0.98) 70%);
  backdrop-filter: blur(10px);
  animation: overlayPulse 3s ease-in-out infinite;
}

@keyframes overlayPulse {
  0%, 100% {
    background: radial-gradient(circle at center,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(0, 0, 0, 0.98) 70%);
  }
  50% {
    background: radial-gradient(circle at center,
      rgba(30, 41, 59, 0.95) 0%,
      rgba(15, 23, 42, 0.98) 70%);
  }
}

.spinner-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300px;
  height: 300px;
}

/* === SPINNING RINGS === */
.spinner-ring {
  position: absolute;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.outer-ring {
  width: 280px;
  height: 280px;
  animation: spinClockwise 4s linear infinite;

  .ring-segment {
    position: absolute;
    width: 60px;
    height: 8px;
    background: linear-gradient(90deg,
      transparent 0%,
      var(--primary) 50%,
      transparent 100%);
    border-radius: 4px;

    &:nth-child(1) {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    &:nth-child(2) {
      right: 0;
      top: 50%;
      transform: translateY(-50%) rotate(90deg);
    }

    &:nth-child(3) {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) rotate(180deg);
    }

    &:nth-child(4) {
      left: 0;
      top: 50%;
      transform: translateY(-50%) rotate(270deg);
    }
  }
}

.middle-ring {
  width: 200px;
  height: 200px;
  animation: spinCounterClockwise 3s linear infinite;

  .ring-segment {
    position: absolute;
    width: 40px;
    height: 6px;
    background: linear-gradient(90deg,
      transparent 0%,
      var(--secondary) 50%,
      transparent 100%);
    border-radius: 3px;

    &:nth-child(1) {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    &:nth-child(2) {
      right: 0;
      top: 50%;
      transform: translateY(-50%) rotate(120deg);
    }

    &:nth-child(3) {
      left: 0;
      top: 50%;
      transform: translateY(-50%) rotate(240deg);
    }
  }
}

.inner-ring {
  width: 120px;
  height: 120px;
  animation: spinClockwise 2s linear infinite;

  .ring-segment {
    position: absolute;
    width: 30px;
    height: 4px;
    background: linear-gradient(90deg,
      transparent 0%,
      var(--warning) 50%,
      transparent 100%);
    border-radius: 2px;

    &:nth-child(1) {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    &:nth-child(2) {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) rotate(180deg);
    }
  }
}

@keyframes spinClockwise {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spinCounterClockwise {
  from { transform: rotate(360deg); }
  to { transform: rotate(0deg); }
}

/* === LOGO CONTAINER === */
.logo-container {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.logo-glow {
  position: absolute;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle,
    rgba(99, 102, 241, 0.3) 0%,
    rgba(139, 92, 246, 0.2) 50%,
    transparent 70%);
  border-radius: 50%;
  animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.ipl-logo {
  width: 60px;
  height: auto;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 0 20px rgba(99, 102, 241, 0.5));
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
    filter: drop-shadow(0 0 20px rgba(99, 102, 241, 0.5));
  }
  25% {
    transform: translateY(-5px) scale(1.05);
    filter: drop-shadow(0 0 30px rgba(139, 92, 246, 0.7));
  }
  50% {
    transform: translateY(0px) scale(1.1);
    filter: drop-shadow(0 0 25px rgba(168, 85, 247, 0.6));
  }
  75% {
    transform: translateY(5px) scale(1.05);
    filter: drop-shadow(0 0 30px rgba(139, 92, 246, 0.7));
  }
}

.logo-pulse {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 2px solid var(--primary);
  border-radius: 50%;
  animation: logoPulse 2s ease-in-out infinite;
  opacity: 0.6;
}

@keyframes logoPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
    border-color: var(--primary);
  }
  50% {
    transform: scale(1.3);
    opacity: 0.3;
    border-color: var(--secondary);
  }
  100% {
    transform: scale(1.6);
    opacity: 0;
    border-color: var(--warning);
  }
}

/* === FLOATING PARTICLES === */
.particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary);
  border-radius: 50%;
  animation: particleFloat 4s ease-in-out infinite;
  opacity: 0.7;
  box-shadow: 0 0 10px var(--primary);
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) scale(0.5);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) scale(1.2);
    opacity: 1;
  }
  75% {
    transform: translateY(-30px) scale(0.8);
    opacity: 0.6;
  }
}

/* === LOADING TEXT === */
.loading-text {
  position: absolute;
  bottom: -80px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: var(--text-primary);
}

.loading-word {
  display: flex;
  gap: 2px;
  justify-content: center;
  margin-bottom: var(--spacing-sm);
}

.letter {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-bold);
  font-family: var(--font-sans);
  text-transform: uppercase;
  letter-spacing: 2px;
  animation: letterBounce 1.4s ease-in-out infinite;
  background: linear-gradient(45deg, var(--primary), var(--secondary), var(--warning));
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: letterBounce 1.4s ease-in-out infinite, gradientShift 3s ease-in-out infinite;
}

@keyframes letterBounce {
  0%, 80%, 100% {
    transform: translateY(0px) scale(1);
  }
  40% {
    transform: translateY(-10px) scale(1.1);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xs);
}

.dot {
  width: 8px;
  height: 8px;
  background: var(--primary);
  border-radius: 50%;
  animation: dotPulse 1.5s ease-in-out infinite;
  box-shadow: 0 0 10px var(--primary);

  &:nth-child(1) {
    animation-delay: 0s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* === PROGRESS BAR === */
.progress-container {
  position: absolute;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg,
    var(--primary) 0%,
    var(--secondary) 50%,
    var(--warning) 100%);
  background-size: 200% 100%;
  border-radius: 2px;
  animation: progressFill 3s ease-in-out infinite, progressShimmer 2s linear infinite;
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
}

@keyframes progressFill {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

@keyframes progressShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.progress-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 1px;
  animation: textFade 2s ease-in-out infinite alternate;
}

@keyframes textFade {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .spinner-content {
    width: 250px;
    height: 250px;
  }

  .outer-ring {
    width: 230px;
    height: 230px;

    .ring-segment {
      width: 50px;
      height: 6px;
    }
  }

  .middle-ring {
    width: 170px;
    height: 170px;

    .ring-segment {
      width: 35px;
      height: 5px;
    }
  }

  .inner-ring {
    width: 100px;
    height: 100px;

    .ring-segment {
      width: 25px;
      height: 3px;
    }
  }

  .logo-container {
    width: 60px;
    height: 60px;
  }

  .ipl-logo {
    width: 45px;
  }

  .logo-glow {
    width: 80px;
    height: 80px;
  }

  .logo-pulse {
    width: 60px;
    height: 60px;
  }

  .loading-text {
    bottom: -60px;
  }

  .letter {
    font-size: var(--text-base);
  }

  .progress-container {
    width: 250px;
    bottom: 40px;
  }
}

@media (max-width: 480px) {
  .spinner-content {
    width: 200px;
    height: 200px;
  }

  .outer-ring {
    width: 180px;
    height: 180px;

    .ring-segment {
      width: 40px;
      height: 5px;
    }
  }

  .middle-ring {
    width: 130px;
    height: 130px;

    .ring-segment {
      width: 30px;
      height: 4px;
    }
  }

  .inner-ring {
    width: 80px;
    height: 80px;

    .ring-segment {
      width: 20px;
      height: 2px;
    }
  }

  .logo-container {
    width: 50px;
    height: 50px;
  }

  .ipl-logo {
    width: 35px;
  }

  .logo-glow {
    width: 60px;
    height: 60px;
  }

  .logo-pulse {
    width: 50px;
    height: 50px;
  }

  .loading-text {
    bottom: -50px;
  }

  .letter {
    font-size: var(--text-sm);
  }

  .progress-container {
    width: 200px;
    bottom: 30px;
  }

  .progress-text {
    font-size: var(--text-xs);
  }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
  .spinner-ring,
  .logo-glow,
  .ipl-logo,
  .logo-pulse,
  .particle,
  .letter,
  .dot,
  .progress-fill {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }

  .spinner-overlay {
    animation: none;
    background: rgba(15, 23, 42, 0.95);
  }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
  .spinner-overlay {
    background: rgba(0, 0, 0, 0.98);
  }

  .ring-segment {
    background: white !important;
  }

  .ipl-logo {
    filter: contrast(2) brightness(1.2);
  }

  .letter {
    -webkit-text-fill-color: white;
    color: white;
  }

  .progress-fill {
    background: white;
  }
}