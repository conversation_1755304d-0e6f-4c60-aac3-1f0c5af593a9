import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ApiService } from './api.service';

export interface ChatMessage {
  id: string;
  content: string;
  author: {
    id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    role: 'user' | 'admin';
  };
  timestamp: Date;
  edited: boolean;
  editedAt?: Date;
  replyTo?: {
    id: string;
    content: string;
    author: {
      firstName: string;
      lastName: string;
    };
  };
  reactions: {
    emoji: string;
    count: number;
    userReacted: boolean;
  }[];
  canEdit: boolean;
  canDelete: boolean;
}

export interface ChatMessagesResponse {
  messages: ChatMessage[];
  totalPages: number;
  currentPage: number;
  hasMore: boolean;
}

export interface CreateMessageRequest {
  content: string;
  replyTo?: string;
}

export interface EditMessageRequest {
  content: string;
}

export interface AddReactionRequest {
  emoji: string;
}

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private messagesSubject = new BehaviorSubject<ChatMessage[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private hasMoreSubject = new BehaviorSubject<boolean>(true);
  private currentPageSubject = new BehaviorSubject<number>(1);

  public messages$ = this.messagesSubject.asObservable();
  public loading$ = this.loadingSubject.asObservable();
  public hasMore$ = this.hasMoreSubject.asObservable();

  constructor(private apiService: ApiService) { }

  async loadMessages(page: number = 1, limit: number = 50): Promise<void> {
    try {
      this.loadingSubject.next(true);

      const response = await this.apiService.get<ChatMessagesResponse>('chat/messages', {
        params: { page, limit }
      });

      const { messages, hasMore, currentPage } = response.data;

      if (page === 1) {
        // First load or refresh
        this.messagesSubject.next(messages);
      } else {
        // Load more (pagination)
        const currentMessages = this.messagesSubject.value;
        this.messagesSubject.next([...messages, ...currentMessages]);
      }

      this.hasMoreSubject.next(hasMore);
      this.currentPageSubject.next(currentPage);
    } catch (error) {
      console.error('Error loading chat messages:', error);
      throw error;
    } finally {
      this.loadingSubject.next(false);
    }
  }

  async loadMoreMessages(): Promise<void> {
    if (!this.hasMoreSubject.value || this.loadingSubject.value) {
      return;
    }

    const nextPage = this.currentPageSubject.value + 1;
    await this.loadMessages(nextPage);
  }

  async sendMessage(request: CreateMessageRequest): Promise<ChatMessage> {
    try {
      const response = await this.apiService.post<ChatMessage>('chat/messages', request);
      const newMessage = response.data;

      // Add new message to the end of the list
      const currentMessages = this.messagesSubject.value;
      this.messagesSubject.next([...currentMessages, newMessage]);

      return newMessage;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  async editMessage(messageId: string, request: EditMessageRequest): Promise<ChatMessage> {
    try {
      const response = await this.apiService.put<ChatMessage>(`chat/messages/${messageId}`, request);
      const updatedMessage = response.data;

      // Update message in the list
      const currentMessages = this.messagesSubject.value;
      const updatedMessages = currentMessages.map(msg =>
        msg.id === messageId ? updatedMessage : msg
      );
      this.messagesSubject.next(updatedMessages);

      return updatedMessage;
    } catch (error) {
      console.error('Error editing message:', error);
      throw error;
    }
  }

  async deleteMessage(messageId: string): Promise<void> {
    try {
      await this.apiService.delete(`chat/messages/${messageId}`);

      // Remove message from the list
      const currentMessages = this.messagesSubject.value;
      const filteredMessages = currentMessages.filter(msg => msg.id !== messageId);
      this.messagesSubject.next(filteredMessages);
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }

  async addReaction(messageId: string, request: AddReactionRequest): Promise<ChatMessage> {
    try {
      const response = await this.apiService.post<ChatMessage>(`chat/messages/${messageId}/reactions`, request);
      const updatedMessage = response.data;

      // Update message in the list
      const currentMessages = this.messagesSubject.value;
      const updatedMessages = currentMessages.map(msg =>
        msg.id === messageId ? updatedMessage : msg
      );
      this.messagesSubject.next(updatedMessages);

      return updatedMessage;
    } catch (error) {
      console.error('Error adding reaction:', error);
      throw error;
    }
  }

  refreshMessages(): Promise<void> {
    return this.loadMessages(1);
  }

  clearMessages(): void {
    this.messagesSubject.next([]);
    this.hasMoreSubject.next(true);
    this.currentPageSubject.next(1);
  }

  // Helper method to get current messages synchronously
  getCurrentMessages(): ChatMessage[] {
    return this.messagesSubject.value;
  }

  // Helper method to check if user can edit a specific message
  canEditMessage(message: ChatMessage): boolean {
    return message.canEdit;
  }

  // Helper method to check if user can delete a specific message
  canDeleteMessage(message: ChatMessage): boolean {
    return message.canDelete;
  }
}
