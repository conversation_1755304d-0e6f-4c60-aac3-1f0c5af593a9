<div class="team-details-container" *ngIf="chosenTeam">
    <!-- Header -->
    <header class="team-header">
        <button class="back-btn" (click)="onArrowBackClick()">
            <i class="fas fa-arrow-left"></i>
            Back
        </button>
        <div class="breadcrumb">
            <button class="breadcrumb-link" (click)="navigateToLeagueTable()">Teams</button>
            <i class="fas fa-chevron-right"></i>
            <span>{{chosenTeam.name}}</span>
        </div>
    </header>

    <!-- Tabs -->
    <div class="team-tabs">
        <mat-tab-group class="modern-tabs">

            <!-- Overview Tab -->
            <mat-tab>
                <ng-template mat-tab-label>
                    <i class="fas fa-home"></i>
                    Overview
                </ng-template>
                <team-details-overall (onTeamUpdateEvent)="loadTeamDetails()" [chosenTeam]="chosenTeam"></team-details-overall>
            </mat-tab>

            <!-- Matches Tab -->
            <mat-tab>
                <ng-template mat-tab-label>
                    <i class="fas fa-futbol"></i>
                    Matches
                </ng-template>
                <team-details-matches [teamId]="teamID"></team-details-matches>
            </mat-tab>

            <!-- Statistics Tab -->
            <mat-tab>
                <ng-template mat-tab-label>
                    <i class="fas fa-chart-bar"></i>
                    Statistics
                </ng-template>
                <team-details-stats [teamId]="teamID"></team-details-stats>
            </mat-tab>

            <!-- Achievements Tab -->
            <mat-tab>
                <ng-template mat-tab-label>
                    <i class="fas fa-trophy"></i>
                    Achievements
                </ng-template>
                <div class="achievements-tab-content">
                    <app-achievement-history
                        [entityId]="teamID"
                        [entityType]="'team'"
                        [entityName]="chosenTeam.name">
                    </app-achievement-history>
                </div>
            </mat-tab>
        </mat-tab-group>
    </div>
</div>

<!-- Loading State -->
<div class="loading-state" *ngIf="isLoading">
    <div class="loading-container">
        <pro-clubs-spinner></pro-clubs-spinner>
        <h3 class="loading-title">Loading Team Details</h3>
        <p class="loading-description">Fetching team information...</p>
    </div>
</div>

<!-- Empty State -->
<div class="empty-state" *ngIf="!chosenTeam && !isLoading">
    <div class="empty-state-content">
        <i class="fas fa-shield-alt empty-icon"></i>
        <h2>No Team Selected</h2>
        <p>Please select a team to view its details.</p>
        <button class="back-button" (click)="onArrowBackClick()">
            <i class="fas fa-arrow-left"></i>
            <span>Go Back</span>
        </button>
    </div>
</div>