<div class="assign-player-container" *ngIf="!isLoading">
    <div class="assign-header">
        <button class="back-button" (click)="onArrowBackClick()">
            <i class="fas fa-arrow-left"></i>
            <span>Back</span>
        </button>
        <h1 class="assign-title">
            <i class="fas fa-user-plus"></i>
            Assign Player to Team
        </h1>
    </div>

    <div class="assign-content">
        <div class="selection-section">
            <div class="selection-group" *ngIf="teamsOptions">
                <label class="selection-label">
                    <i class="fas fa-shield-alt"></i>
                    Select Team
                </label>
                <pro-clubs-auto-complete-select
                    [selectOptions]="teamsOptions"
                    [placeholder]="'Choose a team'"
                    (selectionChange)="onTeamSelect($event)">
                </pro-clubs-auto-complete-select>
            </div>

            <div class="selection-group" *ngIf="playersOptions">
                <label class="selection-label">
                    <i class="fas fa-user"></i>
                    Select Player
                </label>
                <pro-clubs-auto-complete-select
                    [selectOptions]="playersOptions"
                    [placeholder]="'Choose a player'"
                    (selectionChange)="onPlayerSelect($event)">
                </pro-clubs-auto-complete-select>
            </div>
        </div>

        <div class="player-preview" *ngIf="selectedPlayer">
            <div class="preview-header">
                <h3 class="preview-title">
                    <i class="fas fa-eye"></i>
                    Player Preview
                </h3>
            </div>

            <div class="player-card">
                <div class="player-avatar">
                    <img class="player-image"
                         [src]="selectedPlayer.imgUrl || 'assets/Icons/User.jpg'"
                         [alt]="selectedPlayer.name">
                </div>

                <div class="player-info">
                    <h4 class="player-name">{{selectedPlayer.name}}</h4>

                    <div class="player-stats">
                        <div class="stat-item">
                            <span class="stat-label">Age</span>
                            <span class="stat-value">{{selectedPlayer.age}}</span>
                        </div>

                        <div class="stat-item">
                            <span class="stat-label">Position</span>
                            <span class="stat-value">{{selectedPlayer.position}}</span>
                        </div>

                        <div class="stat-item full-width">
                            <span class="stat-label">Playable Positions</span>
                            <span class="stat-value">{{selectedPlayer.playablePositions}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="assign-actions" *ngIf="selectedPlayer">
            <button class="assign-button" (click)="onAssignClick()">
                <i class="fas fa-check"></i>
                Assign Player to Team
            </button>
        </div>
    </div>
</div>

<div class="loading-container" *ngIf="isLoading">
    <pro-clubs-spinner></pro-clubs-spinner>
</div>