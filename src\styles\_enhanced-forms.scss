/* === ENHANCED FORM STYLES === */

/* Form Container */
.form-container {
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary));
        background-size: 200% 100%;
        animation: shimmer 3s ease-in-out infinite;
    }
}

/* Form Group */
.form-group {
    margin-bottom: var(--spacing-lg);
    position: relative;
}

/* Labels */
.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Input Fields */
.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface-secondary);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    color: var(--text-primary);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;

    &::placeholder {
        color: var(--text-tertiary);
        opacity: 0.7;
    }

    &:focus {
        outline: none;
        border-color: var(--primary);
        background: var(--surface-primary);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
        transform: translateY(-1px);
    }

    &:hover:not(:focus) {
        border-color: var(--border-secondary);
        background: var(--surface-tertiary);
    }

    &.error {
        border-color: var(--danger);
        background: var(--danger-50);

        &:focus {
            box-shadow: 0 0 0 3px rgba(var(--danger-rgb), 0.1);
        }
    }

    &.success {
        border-color: var(--success);
        background: var(--success-50);

        &:focus {
            box-shadow: 0 0 0 3px rgba(var(--success-rgb), 0.1);
        }
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: var(--surface-disabled);
    }
}

/* Textarea */
.form-textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

/* Select */
.form-select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--spacing-md) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: var(--spacing-2xl);
}

/* Input Groups */
.input-group {
    display: flex;
    position: relative;

    .form-input {
        border-radius: 0;

        &:first-child {
            border-top-left-radius: var(--radius-lg);
            border-bottom-left-radius: var(--radius-lg);
        }

        &:last-child {
            border-top-right-radius: var(--radius-lg);
            border-bottom-right-radius: var(--radius-lg);
        }

        &:not(:last-child) {
            border-right: none;
        }
    }

    .input-addon {
        display: flex;
        align-items: center;
        padding: var(--spacing-md) var(--spacing-lg);
        background: var(--surface-tertiary);
        border: 2px solid var(--border-primary);
        color: var(--text-secondary);
        font-size: var(--text-sm);
        font-weight: var(--font-weight-medium);

        &:first-child {
            border-top-left-radius: var(--radius-lg);
            border-bottom-left-radius: var(--radius-lg);
            border-right: none;
        }

        &:last-child {
            border-top-right-radius: var(--radius-lg);
            border-bottom-right-radius: var(--radius-lg);
            border-left: none;
        }
    }
}

/* Floating Labels */
.form-floating {
    position: relative;

    .form-input {
        padding-top: var(--spacing-xl);
        padding-bottom: var(--spacing-sm);

        &::placeholder {
            opacity: 0;
        }

        &:focus ~ .form-label,
        &:not(:placeholder-shown) ~ .form-label {
            transform: translateY(-50%) scale(0.85);
            color: var(--primary);
        }
    }

    .form-label {
        position: absolute;
        top: 50%;
        left: var(--spacing-lg);
        transform: translateY(-50%);
        background: var(--surface-secondary);
        padding: 0 var(--spacing-xs);
        transition: all 0.3s ease;
        pointer-events: none;
        margin: 0;
        text-transform: none;
        letter-spacing: normal;
    }
}

/* Checkboxes and Radios */
.form-check {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);

    .form-check-input {
        width: 20px;
        height: 20px;
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-sm);
        background: var(--surface-secondary);
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &:checked {
            background: var(--primary);
            border-color: var(--primary);

            &::after {
                content: '✓';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-size: 12px;
                font-weight: bold;
            }
        }

        &[type="radio"] {
            border-radius: var(--radius-full);

            &:checked::after {
                content: '';
                width: 8px;
                height: 8px;
                background: white;
                border-radius: var(--radius-full);
            }
        }

        &:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.2);
        }
    }

    .form-check-label {
        cursor: pointer;
        font-size: var(--text-sm);
        color: var(--text-primary);
        user-select: none;
    }
}

/* Error Messages */
.form-error {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-xs);
    font-size: var(--text-xs);
    color: var(--danger);

    i {
        font-size: var(--text-xs);
    }
}

/* Success Messages */
.form-success {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-xs);
    font-size: var(--text-xs);
    color: var(--success);

    i {
        font-size: var(--text-xs);
    }
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Animations */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .form-container {
        padding: var(--spacing-lg);
    }

    .form-input,
    .form-select,
    .form-textarea {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--text-sm);
    }

    .input-group {
        flex-direction: column;

        .form-input,
        .input-addon {
            border-radius: var(--radius-lg) !important;
            border: 2px solid var(--border-primary) !important;
        }

        .form-input:not(:last-child),
        .input-addon:not(:last-child) {
            margin-bottom: var(--spacing-xs);
        }
    }
}
