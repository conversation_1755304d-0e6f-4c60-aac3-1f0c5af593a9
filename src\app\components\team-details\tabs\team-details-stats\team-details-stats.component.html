<div class="team-stats-container" *ngIf="teamStats && playersStats">
    <!-- Stats Header -->
    <div class="stats-header">
        <h2 class="stats-title">
            <i class="fas fa-chart-line"></i>
            Team Statistics
        </h2>
        <p class="stats-subtitle">Performance metrics and player rankings</p>
    </div>

    <!-- Performance Overview Section -->
    <section class="quick-stats-section">
        <h2 class="section-title">
            <i class="fas fa-chart-bar"></i>
            <span>Performance Overview</span>
        </h2>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon win-streak">
                    <i class="fas fa-fire"></i>
                </div>
                <div class="stat-content">
                    <span class="stat-value">{{teamStats.longestWinStreak}}</span>
                    <span class="stat-label">Win Streak</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon unbeaten">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="stat-content">
                    <span class="stat-value">{{teamStats.longestUnbeatenStreak}}</span>
                    <span class="stat-label">Unbeaten</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon no-goals">
                    <i class="fas fa-ban"></i>
                </div>
                <div class="stat-content">
                    <span class="stat-value">{{teamStats.longestWithoutScoringStreak}}</span>
                    <span class="stat-label">No Goals</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon lose-streak">
                    <i class="fas fa-skull"></i>
                </div>
                <div class="stat-content">
                    <span class="stat-value">{{teamStats.longestLoseStreak}}</span>
                    <span class="stat-label">Lose Streak</span>
                </div>
            </div>
        </div>
    </section>
    <!-- Player Statistics Section -->
    <section class="player-stats-section">
        <h2 class="section-title">
            <i class="fas fa-users"></i>
            <span>Player Statistics</span>
        </h2>

        <!-- Responsive Grid Layout -->
        <div class="stats-grid-horizontal">
            <div class="player-stat-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-star"></i>
                        Top Ratings
                    </h3>
                </div>
                <div class="card-content">
                    <div class="table-header">
                        <div class="header-name">Name</div>
                        <div class="header-games">G</div>
                        <div class="header-stat">RTG</div>
                    </div>
                    <div class="player-list">
                        <div class="player-row" *ngFor="let topRating of playersStats.topAvgRating">
                            <div class="player-name" (click)="onPlayerClick(topRating.playerId)">
                                {{topRating.playerName}}
                            </div>
                            <div class="player-games">{{topRating.games}}</div>
                            <div class="player-stat rating">{{topRating.avgRating.toFixed(2)}}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="player-stat-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-futbol"></i>
                        Top Scorers
                    </h3>
                </div>
                <div class="card-content">
                    <div class="table-header">
                        <div class="header-name">Name</div>
                        <div class="header-games">G</div>
                        <div class="header-stat">Goals</div>
                    </div>
                    <div class="player-list">
                        <div class="player-row" *ngFor="let topScorer of playersStats.topScorers">
                            <div class="player-name" (click)="onPlayerClick(topScorer.playerId)">
                                {{topScorer.playerName}}
                            </div>
                            <div class="player-games">{{topScorer.games}}</div>
                            <div class="player-stat goals">{{topScorer.goals}}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="player-stat-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-hands-helping"></i>
                        Top Assists
                    </h3>
                </div>
                <div class="card-content">
                    <div class="table-header">
                        <div class="header-name">Name</div>
                        <div class="header-games">G</div>
                        <div class="header-stat">Assists</div>
                    </div>
                    <div class="player-list">
                        <div class="player-row" *ngFor="let topAssister of playersStats.topAssisters">
                            <div class="player-name" (click)="onPlayerClick(topAssister.playerId)">
                                {{topAssister.playerName}}
                            </div>
                            <div class="player-games">{{topAssister.games}}</div>
                            <div class="player-stat assists">{{topAssister.assists}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Loading State -->
<div class="loading-container" *ngIf="isLoading">
    <pro-clubs-spinner></pro-clubs-spinner>
    <h3 class="loading-title">Loading Team Statistics</h3>
    <p class="loading-description">Fetching performance data...</p>
</div>