import { Router } from "express";
import UserController from "../controllers/user-controller";
import { container } from "../config/container.config";
import { authenticateToken, requireAdmin } from "../middlewares/auth-middleware";

const router = Router();
const userController = container.resolve(UserController);

// Public routes (no authentication required)
router.post("/register", (req, res, next) => userController.register(req, res, next));
router.post("/login", (req, res, next) => userController.login(req, res, next));
router.post("/google-auth", (req, res, next) => userController.googleAuth(req, res, next));
router.get("/google-callback", (req, res, next) => userController.googleCallback(req, res, next));
router.get("/public/search-players", (req, res, next) => userController.searchAvailablePlayersPublic(req, res, next));

// Protected routes (authentication required)
router.get("/profile", authenticateToken, (req, res, next) => userController.getProfile(req, res, next));
router.put("/profile", authenticateToken, (req, res, next) => userController.updateProfile(req, res, next));
router.delete("/account", authenticateToken, (req, res, next) => userController.deleteAccount(req, res, next));

// Player association routes
router.post("/associate-player", authenticateToken, (req, res, next) => userController.associatePlayer(req, res, next));
router.post("/remove-player", authenticateToken, (req, res, next) => userController.removePlayer(req, res, next));
router.get("/debug", authenticateToken, (req, res, next) => userController.debugUser(req, res, next));
router.get("/search-players", authenticateToken, (req, res, next) => userController.searchAvailablePlayers(req, res, next));
router.get("/associated-players", authenticateToken, (req, res, next) => userController.getAssociatedPlayers(req, res, next));
router.get("/check-ownership/:playerId", authenticateToken, (req, res, next) => userController.checkPlayerOwnership(req, res, next));

// Public route to check if a player is associated (no auth required)
router.get("/player-association/check/:playerId", (req, res, next) => userController.checkPlayerAssociation(req, res, next));

// Email verification
router.post("/verify-email", authenticateToken, (req, res, next) => userController.verifyEmail(req, res, next));

export default router;
