/* === MODERN TOP SCORERS DESIGN === */

.top-scorers-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    padding: 0 5px;
    background: var(--bg-primary);
    min-height: 100vh;
    font-family: var(--font-sans);
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;

    @media (max-width: 768px) {
        gap: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        gap: var(--spacing-md);
    }
}

/* === HEADER SECTION === */
.header-wrapper {
    padding: var(--spacing-xl) 5px 0;

    @media (max-width: 768px) {
        padding: var(--spacing-lg) 5px 0;
    }
}

.scorers-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    border: 1px solid var(--border-primary);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, var(--warning-400), var(--primary), var(--success));
        background-size: 200% 100%;
        animation: shimmer 4s ease-in-out infinite;
    }

    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 50% 0%, rgba(var(--primary-rgb), 0.05) 0%, transparent 50%);
        pointer-events: none;
    }

    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
    }
}

.header-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
        background: var(--primary-hover);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    i.spinning {
        animation: spin 1s linear infinite;
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.header-content {
    flex: 1;

    .header-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-xl);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0;

        i {
            color: var(--success-500);
            font-size: var(--text-lg);
        }

        .highlight {
            color: var(--success-500);
        }

        @media (max-width: 768px) {
            font-size: var(--text-lg);
        }
    }
}




/* === LEADERBOARD SECTION === */
.leaderboard-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    flex: 1;
    min-height: 0;
    padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.leaderboard-content {
    display: flex;
    gap: var(--spacing-xl);
    flex: 1;
    min-height: 0;

    @media (max-width: 1024px) {
        flex-direction: column;
        gap: var(--spacing-lg);
    }
}

.leaderboard-header {
    .leaderboard-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;

        i {
            color: var(--primary);
        }
    }
}

.leaderboard-table {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    flex: 2;
    display: flex;
    flex-direction: column;
    min-height: 0;

    @media (max-width: 1024px) {
        flex: 1;
    }
}

.table-header {
    background: var(--surface-secondary);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);

    .table-title {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;
    }
}

.table-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.table-row {
    display: grid;
    grid-template-columns: 60px 2fr 1.5fr 1fr 1fr 1fr 1fr;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-secondary);
    transition: all 0.2s ease;

    &.header-row {
        background: var(--surface-tertiary);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        font-size: var(--text-sm);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border-bottom: 2px solid var(--border-primary);

        .rank-cell,
        .player-cell,
        .team-cell,
        .position-cell,
        .goals-cell,
        .games-cell,
        .avg-cell {
            text-align: center;
            justify-content: center;
        }
    }

    &.player-row {
        cursor: pointer;

        &:hover {
            background: var(--surface-hover);
            transform: translateX(4px);
        }

        &:last-child {
            border-bottom: none;
        }

        &.top-three {
            background: var(--surface-secondary);
            border-left: 4px solid var(--success-400);
        }
    }

    @media (max-width: 768px) {
        grid-template-columns: 50px 3fr 1fr 1fr;
        padding: var(--spacing-sm) var(--spacing-md);

        .hide-mobile {
            display: none;
        }
    }

    @media (max-width: 480px) {
        grid-template-columns: 40px 4fr 1fr;
    }
}

.rank-cell {
    display: flex;
    align-items: center;
    justify-content: center;

    .rank-number {
        font-weight: var(--font-weight-black);
        color: var(--text-primary);
        font-size: var(--text-base);
        font-family: var(--font-mono);
        line-height: 1;
        min-width: 24px;
        text-align: center;

        &.gold {
            color: var(--warning-500);
            font-size: var(--text-lg);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        &.silver {
            color: var(--info-500);
            font-size: var(--text-lg);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        &.bronze {
            color: var(--warning-600);
            font-size: var(--text-lg);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
    }
}

.player-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    min-width: 0;

    .player-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 2px solid var(--border-secondary);
        flex-shrink: 0;
    }

    .player-info {
        min-width: 0;
        flex: 1;

        .player-name {
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            font-size: var(--text-base);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

.team-cell {
    display: flex;
    align-items: center;
    justify-content: center;

    .team-name {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.position-cell {
    display: flex;
    justify-content: center;

    .position-badge {
        background: var(--surface-tertiary);
        color: var(--text-secondary);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-md);
        font-size: var(--text-xs);
        font-weight: var(--font-weight-medium);
        text-transform: uppercase;
        border: 1px solid var(--border-secondary);
    }
}

.goals-cell {
    display: flex;
    justify-content: center;

    .goals-number {
        font-weight: var(--font-weight-bold);
        color: var(--success-500);
        font-size: var(--text-lg);
    }
}

.games-cell {
    display: flex;
    justify-content: center;

    .games-number {
        font-weight: var(--font-weight-medium);
        color: var(--text-secondary);
    }
}

.avg-cell {
    display: flex;
    justify-content: center;

    .avg-number {
        font-weight: var(--font-weight-medium);
        color: var(--text-secondary);
        font-family: var(--font-mono);
    }
}
/* === TOP 3 SIDEBAR === */
.top-three-sidebar {
    flex: 1;
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    min-height: 0;

    @media (max-width: 1024px) {
        flex: none;
        min-height: auto;
    }
}

.sidebar-header {
    .sidebar-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;

        i {
            color: var(--success-500);
        }
    }
}

.compact-podium {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    flex: 1;
}

.compact-player {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-secondary);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        transform: translateX(4px);
        box-shadow: var(--shadow-md);
        border-color: var(--border-primary);
    }

    &.first {
        border-color: #fbbf24;
        background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), var(--surface-secondary));
        border: 2px solid #fbbf24;

        .player-rank {
            background: #fbbf24;
            color: white;
            box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
        }

        .stat-value {
            color: #fbbf24;
            font-weight: var(--font-weight-bold);
        }

        .player-name {
            color: var(--text-primary) !important;
            font-weight: var(--font-weight-bold);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .team-name {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: var(--font-weight-semibold);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }
    }

    &.second {
        border-color: #60a5fa;
        background: linear-gradient(135deg, rgba(96, 165, 250, 0.15), var(--surface-secondary));
        border: 2px solid #60a5fa;

        .player-rank {
            background: #60a5fa;
            color: white;
            box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
        }

        .stat-value {
            color: #60a5fa;
            font-weight: var(--font-weight-bold);
        }

        .player-name {
            color: var(--text-primary) !important;
            font-weight: var(--font-weight-bold);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .team-name {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: var(--font-weight-semibold);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }
    }

    &.third {
        border-color: #ea580c;
        background: linear-gradient(135deg, rgba(234, 88, 12, 0.15), var(--surface-secondary));
        border: 2px solid #ea580c;

        .player-rank {
            background: #ea580c;
            color: white;
            box-shadow: 0 2px 8px rgba(234, 88, 12, 0.3);
        }

        .stat-value {
            color: #ea580c;
            font-weight: var(--font-weight-bold);
        }

        .player-name {
            color: var(--text-primary) !important;
            font-weight: var(--font-weight-bold);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .team-name {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: var(--font-weight-semibold);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }
    }
}

.player-rank {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    background: var(--surface-tertiary);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    font-size: var(--text-sm);
    flex-shrink: 0;
}

.player-image {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-full);
    object-fit: cover;
    border: 2px solid var(--border-secondary);
    flex-shrink: 0;
}

.player-details {
    flex: 1;
    min-width: 0;

    .player-name {
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        font-size: var(--text-sm);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: var(--spacing-xs);
    }

    .player-team {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: var(--spacing-xs);
    }

    .player-stat {
        display: flex;
        align-items: baseline;
        gap: var(--spacing-xs);

        .stat-value {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            color: var(--success-500);
            line-height: 1;
        }

        .stat-label {
            font-size: var(--text-xs);
            color: var(--text-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
    }
}

/* === LOADING STATE === */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
}

/* === NO DATA STATE === */
.no-data-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: var(--spacing-xl);
}

.no-data-card {
    text-align: center;
    padding: var(--spacing-2xl);
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 2px dashed var(--border-primary);
    max-width: 400px;
    width: 100%;

    .no-data-icon {
        font-size: 4rem;
        color: var(--text-tertiary);
        margin-bottom: var(--spacing-lg);
        opacity: 0.5;
    }

    .no-data-title {
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-md) 0;
    }

    .no-data-message {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
        line-height: 1.6;
    }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 480px) {
    .top-scorers-container {
        padding: var(--spacing-sm);
    }

    .scorers-header {
        padding: var(--spacing-md);
    }

    .podium-container {
        gap: var(--spacing-sm);
    }

    .podium-player {
        padding: var(--spacing-md);
    }
}

