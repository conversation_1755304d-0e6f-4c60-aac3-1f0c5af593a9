import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnDestroy } from '@angular/core';
import { GAME_STATUS } from '@pro-clubs-manager/shared-dtos';
import { GameFixtureData } from '../../../shared/models/game.model';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-fixture-card',
  templateUrl: './fixture-card.component.html',
  styleUrl: './fixture-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FixtureCardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  @Input() game!: GameFixtureData;
  @Input() canEdit$!: Observable<boolean>;
  @Input() isCurrentlyEditing: boolean = false;
  @Input() homeTeamGoals: number = 0;
  @Input() awayTeamGoals: number = 0;
  @Input() isAdmin: boolean = false;
  @Input() isCurrentlyEditingTime: boolean = false;
  @Input() editingGameDate: string = '';
  @Input() editingGameTime: string = '';
  @Input() gameUpdateState: 'updating' | 'success' | 'error' | null = null;

  @Output() editClick = new EventEmitter<GameFixtureData>();
  @Output() saveClick = new EventEmitter<GameFixtureData>();
  @Output() cancelClick = new EventEmitter<void>();
  @Output() gameDetailsClick = new EventEmitter<GameFixtureData>();
  @Output() homeGoalsChange = new EventEmitter<number>();
  @Output() awayGoalsChange = new EventEmitter<number>();
  @Output() editTimeClick = new EventEmitter<GameFixtureData>();
  @Output() saveTimeClick = new EventEmitter<GameFixtureData>();
  @Output() cancelTimeEdit = new EventEmitter<void>();
  @Output() editingGameDateChange = new EventEmitter<string>();
  @Output() editingGameTimeChange = new EventEmitter<string>();

  readonly GAME_STATUS = GAME_STATUS;



  getGameStatusClass(): string {
    switch (this.game.status) {
      case GAME_STATUS.SCHEDULED:
        return 'status-scheduled';
      case GAME_STATUS.PLAYED:
        return 'status-played';
      case GAME_STATUS.COMPLETED:
        return 'status-completed';
      default:
        return 'status-unknown';
    }
  }

  getGameStatusText(): string {
    switch (this.game.status) {
      case GAME_STATUS.SCHEDULED:
        return 'Scheduled';
      case GAME_STATUS.PLAYED:
        return 'Played';
      case GAME_STATUS.COMPLETED:
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  hasResult(): boolean {
    return this.game.result !== null && this.game.result !== undefined;
  }

  isPlayoffMatch(): boolean {
    return this.game.isPlayoff || false;
  }

  getMatchDisplayText(): string {
    if (this.isPlayoffMatch() && this.game.matchNumber) {
      // For playoff games, show "Match X of Y" if we have the match number
      // We'll need to determine the total matches from the series format
      return `Match ${this.game.matchNumber}`;
    }
    return '';
  }

  isUpdating(): boolean {
    return this.gameUpdateState === 'updating';
  }

  isUpdateSuccess(): boolean {
    return this.gameUpdateState === 'success';
  }

  isUpdateError(): boolean {
    return this.gameUpdateState === 'error';
  }

  getUpdateStateClass(): string {
    switch (this.gameUpdateState) {
      case 'updating':
        return 'updating';
      case 'success':
        return 'update-success';
      case 'error':
        return 'update-error';
      default:
        return '';
    }
  }

  getUpdateStateIcon(): string {
    switch (this.gameUpdateState) {
      case 'updating':
        return 'fas fa-spinner fa-spin';
      case 'success':
        return 'fas fa-check-circle';
      case 'error':
        return 'fas fa-exclamation-triangle';
      default:
        return '';
    }
  }

  ngOnInit(): void {
    // Initialize any subscriptions if needed
    // Currently no subscriptions needed, but structure is ready
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Optimized event handlers with proper typing
  onEditGameClick(): void {
    this.editClick.emit(this.game);
  }

  onSaveGameClick(): void {
    this.saveClick.emit(this.game);
  }

  onCancelGameClick(): void {
    this.cancelClick.emit();
  }

  onGameDetailsClick(): void {
    this.gameDetailsClick.emit(this.game);
  }

  onEditTimeClick(): void {
    this.editTimeClick.emit(this.game);
  }

  onSaveTimeClick(): void {
    this.saveTimeClick.emit(this.game);
  }

  onCancelTimeEditClick(): void {
    this.cancelTimeEdit.emit();
  }

  // Optimized input change handlers
  onHomeGoalsInputChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = parseInt(target.value, 10);
    if (!isNaN(value) && value >= 0) {
      this.homeGoalsChange.emit(value);
    }
  }

  onAwayGoalsInputChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = parseInt(target.value, 10);
    if (!isNaN(value) && value >= 0) {
      this.awayGoalsChange.emit(value);
    }
  }

  onEditingGameDateChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.editingGameDateChange.emit(target.value);
  }

  onEditingGameTimeChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.editingGameTimeChange.emit(target.value);
  }
}
