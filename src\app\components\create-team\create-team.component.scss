/* === MODERN CREATE TEAM DESIGN === */

.create-team-container {
    min-height: 100vh;
    background: linear-gradient(135deg,
        var(--bg-primary) 0%,
        var(--surface-primary) 50%,
        var(--bg-primary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    font-family: var(--font-sans);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 30% 30%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 70% 70%, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md);
    }
}

.create-team-card {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    padding: var(--spacing-2xl);
    width: 100%;
    max-width: 500px;
    position: relative;
    backdrop-filter: blur(20px);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary));
        border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-xl);
        max-width: 450px;
    }

    @media (max-width: 480px) {
        padding: var(--spacing-lg);
        max-width: 100%;
    }
}

.create-team-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);

    .create-team-title {
        font-size: var(--text-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;
        background: linear-gradient(135deg, var(--primary), var(--accent-primary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);

        i {
            color: var(--primary);
            -webkit-text-fill-color: var(--primary);
        }

        @media (max-width: 768px) {
            font-size: var(--text-2xl);
        }
    }

    .create-team-subtitle {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
        font-weight: var(--font-weight-medium);
    }
}

.create-team-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    .form-label {
        font-size: var(--text-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);

        .required-indicator {
            color: var(--error);
            font-size: var(--text-xs);
        }
    }

    .form-input {
        background: var(--surface-secondary);
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-base);
        color: var(--text-primary);
        transition: all 0.3s ease;
        font-family: var(--font-sans);

        &::placeholder {
            color: var(--text-tertiary);
        }

        &:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
            background: var(--surface-primary);
        }

        &:hover:not(:focus) {
            border-color: var(--border-secondary);
        }

        &.error {
            border-color: var(--error);
            box-shadow: 0 0 0 3px rgba(var(--error-rgb), 0.1);
        }
    }
}

.file-upload-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    .file-upload-label {
        font-size: var(--text-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
    }

    .file-upload-button {
        background: var(--surface-secondary);
        border: 2px dashed var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: var(--text-secondary);
        position: relative;
        overflow: hidden;

        &:hover {
            border-color: var(--primary);
            background: var(--surface-tertiary);
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .upload-icon {
            font-size: var(--text-2xl);
            margin-bottom: var(--spacing-sm);
            display: block;
            color: var(--primary);
        }

        .upload-text {
            font-size: var(--text-base);
            font-weight: var(--font-weight-medium);
            margin-bottom: var(--spacing-xs);
        }

        .upload-hint {
            font-size: var(--text-sm);
            color: var(--text-tertiary);
        }
    }

    .file-input {
        display: none;
    }
}

.submit-button {
    background: linear-gradient(135deg, var(--primary), var(--accent-primary));
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--text-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-inverse);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: var(--spacing-md);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

        &::before {
            left: 100%;
        }
    }

    &:active:not(:disabled) {
        transform: translateY(0);
    }

    &:disabled {
        background: var(--surface-tertiary);
        color: var(--text-tertiary);
        cursor: not-allowed;
        opacity: 0.6;
    }
}

/* === ANIMATIONS === */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.create-team-card {
    animation: fadeInUp 0.6s ease-out;
}

.form-group {
    animation: fadeInUp 0.6s ease-out;

    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
    .create-team-card,
    .form-group {
        animation: none !important;
    }

    .submit-button,
    .file-upload-button {
        &:hover:not(:disabled) {
            transform: none;
        }
    }
}