<!-- Modern Team Overview Layout -->
<div class="team-overview-container" *ngIf="chosenTeam">

    <!-- Content Grid -->
    <div class="overview-grid">

        <!-- Team Management Section -->
        <section class="team-management-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-cog"></i>
                    <span>Team Management</span>
                </h3>
            </div>
            <div class="section-content">
                <team-details-overall-data (onTeamUpdateEvent)="onTeamUpdate()" [chosenTeam]="chosenTeam!"></team-details-overall-data>
            </div>
        </section>

        <!-- Main Content Area -->
        <section class="main-content-area">

            <!-- Team Statistics -->
            <div class="stats-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-chart-line"></i>
                        <span>Performance Stats</span>
                    </h3>
                </div>
                <div class="section-content stats-content">
                    <team-details-overall-stats [chosenTeam]="chosenTeam!"></team-details-overall-stats>
                </div>
            </div>

            <!-- Squad Section -->
            <div class="squad-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-users"></i>
                        <span>Squad</span>
                    </h3>
                </div>
                <div class="section-content squad-content">
                    <team-details-squad [chosenTeam]="chosenTeam!" (onTeamUpdateEvent)="onTeamUpdateEvent.emit()"></team-details-squad>
                </div>
            </div>

        </section>
    </div>
</div>