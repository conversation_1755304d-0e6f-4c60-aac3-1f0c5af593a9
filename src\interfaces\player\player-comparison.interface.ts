export interface PlayerComparisonData {
  player1: {
    id: string;
    name: string;
    imgUrl?: string;
    age: number;
    position: string;
    team?: {
      id: string;
      name: string;
      imgUrl?: string;
    };
    stats: {
      games: number;
      goals: number;
      assists: number;
      cleanSheets: number;
      playerOfTheMatch: number;
      avgRating: number;
    };
  };
  player2: {
    id: string;
    name: string;
    imgUrl?: string;
    age: number;
    position: string;
    team?: {
      id: string;
      name: string;
      imgUrl?: string;
    };
    stats: {
      games: number;
      goals: number;
      assists: number;
      cleanSheets: number;
      playerOfTheMatch: number;
      avgRating: number;
    };
  };
  comparison: {
    gamesDifference: number;
    goalsDifference: number;
    assistsDifference: number;
    cleanSheetsDifference: number;
    potmDifference: number;
    avgRatingDifference: number;
    goalsPerGame: {
      player1: number;
      player2: number;
      difference: number;
    };
    assistsPerGame: {
      player1: number;
      player2: number;
      difference: number;
    };
    cleanSheetsPerGame: {
      player1: number;
      player2: number;
      difference: number;
    };
    potmPerGame: {
      player1: number;
      player2: number;
      difference: number;
    };
  };
}
