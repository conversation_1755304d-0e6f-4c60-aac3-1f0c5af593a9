<!-- Modern Squad Layout -->
<div class="modern-squad-container" *ngIf="chosenTeam">

    <!-- Squad Header -->
    <div class="squad-header">
        <div class="squad-title-section">
            <h3 class="squad-title">
                <i class="fas fa-users"></i>
                <span>Squad</span>
                <span class="player-count">({{chosenTeam.players.length}})</span>
            </h3>
        </div>
        <div class="squad-actions" *ngIf="canManagePlayers$ | async">
            <button class="add-player-btn" (click)="onAddPlayerClick()">
                <i class="fas fa-plus"></i>
                <span>Add Player</span>
            </button>
            <button class="jiggle-mode-btn"
                    (click)="toggleJiggleMode()"
                    *ngIf="chosenTeam.players.length > 0"
                    [class.active]="isJiggleMode">
                <i class="fas" [class.fa-minus-circle]="!isJiggleMode" [class.fa-check]="isJiggleMode"></i>
                <span>{{isJiggleMode ? 'Done' : 'Remove Players'}}</span>
            </button>
            <button class="remove-all-players-btn"
                    (click)="onRemoveAllPlayersClick()"
                    *ngIf="chosenTeam.players.length > 0 && !isJiggleMode"
                    [disabled]="isRemovingAllPlayers">
                <i class="fas fa-trash-alt"></i>
                <span>{{isRemovingAllPlayers ? 'Removing...' : 'Remove All'}}</span>
            </button>
        </div>
    </div>

    <!-- Squad Content -->
    <div class="squad-content">

        <!-- Goalkeepers Section -->
        <div class="position-section" *ngIf="goalkeepers.length > 0">
            <div class="position-header">
                <div class="position-badge goalkeeper">
                    <i class="fas fa-hand-paper"></i>
                    <span>Goalkeepers</span>
                </div>
                <div class="position-count">{{goalkeepers.length}}</div>
            </div>
            <div class="players-grid">
                <div class="player-card goalkeeper-card"
                     *ngFor="let goalkeeper of goalkeepers"
                     [class.jiggle]="isJiggleMode"
                     [class.removing]="isPlayerRemoving(goalkeeper.id)"
                     (click)="onPlayerClick(goalkeeper.id)">

                    <!-- Remove Button (iOS style) -->
                    <button class="remove-player-btn"
                            *ngIf="isJiggleMode && (canManagePlayers$ | async)"
                            (click)="removePlayer(goalkeeper.id, goalkeeper.name); $event.stopPropagation()"
                            [disabled]="isPlayerRemoving(goalkeeper.id)">
                        <i class="fas fa-minus"></i>
                    </button>

                    <div class="player-avatar">
                        <img [src]="goalkeeper.imgUrl || 'assets/Icons/User.jpg'"
                             [alt]="goalkeeper.name"
                             class="player-photo">
                        <div class="position-indicator goalkeeper">{{goalkeeper.position}}</div>
                    </div>
                    <div class="player-info">
                        <div class="player-name">{{goalkeeper.name}}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Defenders Section -->
        <div class="position-section" *ngIf="defenders.length > 0">
            <div class="position-header">
                <div class="position-badge defender">
                    <i class="fas fa-shield-alt"></i>
                    <span>Defenders</span>
                </div>
                <div class="position-count">{{defenders.length}}</div>
            </div>
            <div class="players-grid">
                <div class="player-card defender-card"
                     *ngFor="let defender of defenders"
                     [class.jiggle]="isJiggleMode"
                     [class.removing]="isPlayerRemoving(defender.id)"
                     (click)="onPlayerClick(defender.id)">

                    <!-- Remove Button (iOS style) -->
                    <button class="remove-player-btn"
                            *ngIf="isJiggleMode && (canManagePlayers$ | async)"
                            (click)="removePlayer(defender.id, defender.name); $event.stopPropagation()"
                            [disabled]="isPlayerRemoving(defender.id)">
                        <i class="fas fa-minus"></i>
                    </button>

                    <div class="player-avatar">
                        <img [src]="defender.imgUrl || 'assets/Icons/User.jpg'"
                             [alt]="defender.name"
                             class="player-photo">
                        <div class="position-indicator defender">{{defender.position}}</div>
                    </div>
                    <div class="player-info">
                        <div class="player-name">{{defender.name}}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Midfielders Section -->
        <div class="position-section" *ngIf="midfielders.length > 0">
            <div class="position-header">
                <div class="position-badge midfielder">
                    <i class="fas fa-running"></i>
                    <span>Midfielders</span>
                </div>
                <div class="position-count">{{midfielders.length}}</div>
            </div>
            <div class="players-grid">
                <div class="player-card midfielder-card"
                     *ngFor="let midfielder of midfielders"
                     [class.jiggle]="isJiggleMode"
                     [class.removing]="isPlayerRemoving(midfielder.id)"
                     (click)="onPlayerClick(midfielder.id)">

                    <!-- Remove Button (iOS style) -->
                    <button class="remove-player-btn"
                            *ngIf="isJiggleMode && (canManagePlayers$ | async)"
                            (click)="removePlayer(midfielder.id, midfielder.name); $event.stopPropagation()"
                            [disabled]="isPlayerRemoving(midfielder.id)">
                        <i class="fas fa-minus"></i>
                    </button>

                    <div class="player-avatar">
                        <img [src]="midfielder.imgUrl || 'assets/Icons/User.jpg'"
                             [alt]="midfielder.name"
                             class="player-photo">
                        <div class="position-indicator midfielder">{{midfielder.position}}</div>
                    </div>
                    <div class="player-info">
                        <div class="player-name">{{midfielder.name}}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attackers Section -->
        <div class="position-section" *ngIf="attackers.length > 0">
            <div class="position-header">
                <div class="position-badge attacker">
                    <i class="fas fa-futbol"></i>
                    <span>Attackers</span>
                </div>
                <div class="position-count">{{attackers.length}}</div>
            </div>
            <div class="players-grid">
                <div class="player-card attacker-card"
                     *ngFor="let attacker of attackers"
                     [class.jiggle]="isJiggleMode"
                     [class.removing]="isPlayerRemoving(attacker.id)"
                     (click)="onPlayerClick(attacker.id)">

                    <!-- Remove Button (iOS style) -->
                    <button class="remove-player-btn"
                            *ngIf="isJiggleMode && (canManagePlayers$ | async)"
                            (click)="removePlayer(attacker.id, attacker.name); $event.stopPropagation()"
                            [disabled]="isPlayerRemoving(attacker.id)">
                        <i class="fas fa-minus"></i>
                    </button>

                    <div class="player-avatar">
                        <img [src]="attacker.imgUrl || 'assets/Icons/User.jpg'"
                             [alt]="attacker.name"
                             class="player-photo">
                        <div class="position-indicator attacker">{{attacker.position}}</div>
                    </div>
                    <div class="player-info">
                        <div class="player-name">{{attacker.name}}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div class="empty-squad" *ngIf="chosenTeam.players.length === 0">
            <div class="empty-icon">
                <i class="fas fa-users-slash"></i>
            </div>
            <h4 class="empty-title">No Players Yet</h4>
            <p class="empty-description">Start building your squad by adding players to your team.</p>
            <button class="add-first-player-btn" (click)="onAddPlayerClick()" *ngIf="isAdmin()">
                <i class="fas fa-plus"></i>
                <span>Add First Player</span>
            </button>
        </div>

    </div>
</div>