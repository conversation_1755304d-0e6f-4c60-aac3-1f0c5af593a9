import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

export interface DashboardSummary {
  // Basic counts
  totalTeams: number;
  totalPlayers: number;
  totalFixtures: number;
  totalGames: number;
  
  // Season progress
  currentSeason: {
    seasonNumber: number;
    gamesPlayed: number;
    gamesRemaining: number;
    completionPercentage: number;
  };
  
  // Goals and scoring
  totalGoals: number;
  averageGoalsPerGame: number;
  topScorer: {
    playerId: string;
    playerName: string;
    playerImgUrl?: string;
    goals: number;
    teamName: string;
  } | null;
  
  // Team performance
  leagueLeader: {
    teamId: string;
    teamName: string;
    teamImgUrl?: string;
    points: number;
    gamesPlayed: number;
  } | null;
  
  // Recent activity
  recentGames: {
    id: string;
    homeTeam: string;
    awayTeam: string;
    result: string;
    date: Date;
  }[];
  
  upcomingGames: {
    id: string;
    homeTeam: string;
    awayTeam: string;
    date: Date;
  }[];
  
  // Player statistics
  totalAssists: number;
  averageRating: number;
  cleanSheets: number;
  
  // Transfer activity
  recentTransfers: {
    playerId: string;
    playerName: string;
    fromTeam: string | null;
    toTeam: string;
    date: Date;
  }[];
}

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  private readonly DASHBOARD_CONTROLLER_URL = 'dashboard';

  constructor(private apiService: ApiService) { }

  async getDashboardSummary(): Promise<DashboardSummary> {
    const response = await this.apiService.get<DashboardSummary>(`${this.DASHBOARD_CONTROLLER_URL}/summary`);
    return response.data;
  }
}
