<div class="formation-display-container">
  <!-- Formation Headers -->
  <div class="formation-headers">
    <div class="formation-header home-header">
      <div class="team-info">
        <img [src]="homeTeam?.imgUrl || 'assets/Icons/TeamLogo.jpg'" 
             [alt]="homeTeam?.name" 
             class="team-logo">
        <div class="team-details">
          <span class="team-name">{{ homeTeam?.name }}</span>
          <span class="formation-label">{{ homeFormation }}</span>
        </div>
      </div>
    </div>

    <div class="vs-divider">
      <span>VS</span>
    </div>

    <div class="formation-header away-header">
      <div class="team-info">
        <div class="team-details">
          <span class="team-name">{{ awayTeam?.name }}</span>
          <span class="formation-label">{{ awayFormation }}</span>
        </div>
        <img [src]="awayTeam?.imgUrl || 'assets/Icons/TeamLogo.jpg'" 
             [alt]="awayTeam?.name" 
             class="team-logo">
      </div>
    </div>
  </div>

  <!-- Football Pitch -->
  <div class="pitch-container">
    <div class="pitch">
      <!-- Pitch markings -->
      <div class="pitch-markings">
        <!-- Center circle -->
        <div class="center-circle"></div>
        <!-- Center line -->
        <div class="center-line"></div>
        <!-- Penalty areas -->
        <div class="penalty-area left-penalty"></div>
        <div class="penalty-area right-penalty"></div>
        <!-- Goal areas -->
        <div class="goal-area left-goal"></div>
        <div class="goal-area right-goal"></div>
      </div>

      <!-- Home Team Players -->
      <div class="players-container home-players">
        <div class="player-marker"
             *ngFor="let player of homePlayersWithPositions"
             [style.left.%]="player.x"
             [style.top.%]="player.y"
             [class.potm]="isPlayerOfTheMatch(player)"
             [title]="player.name + ' (' + player.position + ')'">
          
          <!-- Player Image -->
          <div class="player-image-container">
            <img [src]="player.imgUrl || 'assets/Icons/User.jpg'" 
                 [alt]="player.name"
                 class="player-image">
            
            <!-- Rating Circle -->
            <div class="rating-circle" 
                 *ngIf="player.rating > 0"
                 [class]="getPlayerRatingClass(player.rating)"
                 [style.border-color]="getPlayerRatingColor(player.rating)">
              <span class="rating-text">{{ player.rating.toFixed(1) }}</span>
            </div>

            <!-- POTM Crown -->
            <div class="potm-crown" *ngIf="isPlayerOfTheMatch(player)">
              <i class="fas fa-crown"></i>
            </div>
          </div>

          <!-- Player Info -->
          <div class="player-info">
            <span class="player-name">{{ player.name }}</span>
            <span class="player-position">{{ player.position }}</span>
            
            <!-- Player Stats -->
            <div class="player-stats" *ngIf="hasPlayerStats(player)">
              <span class="stats-text">{{ getPlayerStatsText(player) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Away Team Players -->
      <div class="players-container away-players">
        <div class="player-marker away-player"
             *ngFor="let player of awayPlayersWithPositions"
             [style.left.%]="player.x"
             [style.top.%]="player.y"
             [class.potm]="isPlayerOfTheMatch(player)"
             [title]="player.name + ' (' + player.position + ')'">
          
          <!-- Player Image -->
          <div class="player-image-container">
            <img [src]="player.imgUrl || 'assets/Icons/User.jpg'" 
                 [alt]="player.name"
                 class="player-image">
            
            <!-- Rating Circle -->
            <div class="rating-circle" 
                 *ngIf="player.rating > 0"
                 [class]="getPlayerRatingClass(player.rating)"
                 [style.border-color]="getPlayerRatingColor(player.rating)">
              <span class="rating-text">{{ player.rating.toFixed(1) }}</span>
            </div>

            <!-- POTM Crown -->
            <div class="potm-crown" *ngIf="isPlayerOfTheMatch(player)">
              <i class="fas fa-crown"></i>
            </div>
          </div>

          <!-- Player Info -->
          <div class="player-info">
            <span class="player-name">{{ player.name }}</span>
            <span class="player-position">{{ player.position }}</span>
            
            <!-- Player Stats -->
            <div class="player-stats" *ngIf="hasPlayerStats(player)">
              <span class="stats-text">{{ getPlayerStatsText(player) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Formation Legend -->
  <div class="formation-legend">
    <div class="legend-section">
      <h4 class="legend-title">
        <i class="fas fa-info-circle"></i>
        Formation Guide
      </h4>
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-icon rating-excellent"></div>
          <span>Excellent (8.5+)</span>
        </div>
        <div class="legend-item">
          <div class="legend-icon rating-good"></div>
          <span>Good (7.5+)</span>
        </div>
        <div class="legend-item">
          <div class="legend-icon rating-average"></div>
          <span>Average (6.5+)</span>
        </div>
        <div class="legend-item">
          <div class="legend-icon rating-poor"></div>
          <span>Poor (5.5+)</span>
        </div>
        <div class="legend-item">
          <div class="potm-icon">
            <i class="fas fa-crown"></i>
          </div>
          <span>Player of the Match</span>
        </div>
      </div>
    </div>
  </div>
</div>
