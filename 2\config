[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://github.com/proclubstats/server.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
[branch "develop"]
	remote = origin
	merge = refs/heads/develop
	vscode-merge-base = origin/develop
[branch "add-top-by-position"]
	vscode-merge-base = origin/develop
