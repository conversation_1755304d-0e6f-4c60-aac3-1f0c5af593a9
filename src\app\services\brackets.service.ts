import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { PLAYOFF_STAGE } from '../shared/models/game.model';

export interface BracketMatch {
  id: string;
  homeTeam: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  awayTeam: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  result?: {
    homeTeamGoals: number;
    awayTeamGoals: number;
    penalties?: {
      homeTeamPenalties: number;
      awayTeamPenalties: number;
    };
  };
  status: string;
  date?: Date;
  playoffStage: PLAYOFF_STAGE;
  round?: number;
  matchNumber?: number;
  seriesFormat?: string;
  broadcast?: {
    streamUrl: string;
    broadcastingTeam: string;
  };
  // Add individual games for series matches
  matches?: BracketMatch[];
}

export interface BracketStage {
  stage: PLAYOFF_STAGE;
  displayName: string;
  matches: BracketMatch[];
  order: number;
  seriesFormat?: string;
}

export interface PlayoffBracket {
  seasonNumber: number;
  leagueId: string;
  stages: BracketStage[];
  isComplete: boolean;
  champion?: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  runnerUp?: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  thirdPlace?: {
    id: string;
    name: string;
    imgUrl?: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class BracketsService {
  private readonly BRACKETS_CONTROLLER_URL = 'brackets';

  constructor(private apiService: ApiService) {}

  async getPlayoffBracket(leagueId: string, seasonNumber?: number, forceRefresh: boolean = false): Promise<PlayoffBracket> {
    const params: any = seasonNumber ? { seasonNumber } : {};

    // Add cache-busting parameter when forcing refresh
    if (forceRefresh) {
      params.t = Date.now();
      params.refresh = 'true';
    }

    const response = await this.apiService.get<PlayoffBracket>(`${this.BRACKETS_CONTROLLER_URL}/${leagueId}`, { params });
    return response.data;
  }

  async getPlayoffMatches(leagueId: string, seasonNumber?: number): Promise<BracketMatch[]> {
    const params = seasonNumber ? { seasonNumber } : {};
    const response = await this.apiService.get<BracketMatch[]>(`${this.BRACKETS_CONTROLLER_URL}/${leagueId}/matches`, { params });
    return response.data;
  }

  // Helper methods for bracket visualization
  getStageOrder(stage: PLAYOFF_STAGE): number {
    const stageOrder = {
      [PLAYOFF_STAGE.PLAY_IN_ROUND]: 1,
      [PLAYOFF_STAGE.QUARTER_FINAL]: 2,
      [PLAYOFF_STAGE.SEMI_FINAL]: 3,
      [PLAYOFF_STAGE.THIRD_PLACE]: 4,
      [PLAYOFF_STAGE.FINAL]: 5,
      [PLAYOFF_STAGE.PROMOTION_PLAYOFF]: 6,
      [PLAYOFF_STAGE.RELEGATION_PLAYOFF]: 7
    };
    return stageOrder[stage] || 0;
  }

  getStageDisplayName(stage: PLAYOFF_STAGE): string {
    const stageNames = {
      [PLAYOFF_STAGE.PLAY_IN_ROUND]: 'Play-in Round',
      [PLAYOFF_STAGE.QUARTER_FINAL]: 'Quarter-Finals',
      [PLAYOFF_STAGE.SEMI_FINAL]: 'Semi-Finals',
      [PLAYOFF_STAGE.THIRD_PLACE]: '3rd Place Match',
      [PLAYOFF_STAGE.FINAL]: 'Final',
      [PLAYOFF_STAGE.PROMOTION_PLAYOFF]: 'Promotion Playoff',
      [PLAYOFF_STAGE.RELEGATION_PLAYOFF]: 'Relegation Playoff'
    };
    return stageNames[stage] || stage;
  }

  getMatchWinner(match: BracketMatch): { id: string; name: string; imgUrl?: string } | null {
    if (!match.result || match.status !== 'COMPLETED') {
      return null;
    }

    const { homeTeamGoals, awayTeamGoals } = match.result;
    if (homeTeamGoals > awayTeamGoals) {
      return match.homeTeam;
    } else if (awayTeamGoals > homeTeamGoals) {
      return match.awayTeam;
    }
    return null; // Draw - would need additional logic for penalty shootouts
  }

  getMatchLoser(match: BracketMatch): { id: string; name: string; imgUrl?: string } | null {
    if (!match.result || match.status !== 'COMPLETED') {
      return null;
    }

    const { homeTeamGoals, awayTeamGoals } = match.result;
    if (homeTeamGoals < awayTeamGoals) {
      return match.homeTeam;
    } else if (awayTeamGoals < homeTeamGoals) {
      return match.awayTeam;
    }
    return null; // Draw
  }

  isMatchCompleted(match: BracketMatch): boolean {
    return match.status === 'COMPLETED' && !!match.result;
  }

  getMatchScore(match: BracketMatch): string {
    if (!match.result) {
      return 'vs';
    }
    return `${match.result.homeTeamGoals} - ${match.result.awayTeamGoals}`;
  }

  getMatchStatusIcon(match: BracketMatch): string {
    switch (match.status) {
      case 'COMPLETED':
        return 'fas fa-check-circle';
      case 'IN_PROGRESS':
        return 'fas fa-play-circle';
      case 'SCHEDULED':
        return 'fas fa-clock';
      default:
        return 'fas fa-question-circle';
    }
  }

  getMatchStatusColor(match: BracketMatch): string {
    switch (match.status) {
      case 'COMPLETED':
        return 'success';
      case 'IN_PROGRESS':
        return 'primary';
      case 'SCHEDULED':
        return 'warning';
      default:
        return 'secondary';
    }
  }

  // Organize matches into bracket structure
  organizeBracket(matches: BracketMatch[]): BracketStage[] {
    const stageMap = new Map<PLAYOFF_STAGE, BracketMatch[]>();

    // Group matches by stage
    matches.forEach(match => {
      if (!stageMap.has(match.playoffStage)) {
        stageMap.set(match.playoffStage, []);
      }
      stageMap.get(match.playoffStage)!.push(match);
    });

    // Convert to bracket stages and sort
    const stages: BracketStage[] = Array.from(stageMap.entries()).map(([stage, stageMatches]) => ({
      stage,
      displayName: this.getStageDisplayName(stage),
      matches: stageMatches.sort((a, b) => (a.round || 0) - (b.round || 0)),
      order: this.getStageOrder(stage)
    }));

    return stages.sort((a, b) => a.order - b.order);
  }

  // Calculate bracket completion percentage
  getBracketCompletionPercentage(bracket: PlayoffBracket): number {
    if (!bracket || !bracket.stages || bracket.stages.length === 0) {
      return 0;
    }

    const totalMatches = bracket.stages.reduce((total, stage) => total + (stage.matches?.length || 0), 0);
    const completedMatches = bracket.stages.reduce((total, stage) =>
      total + (stage.matches?.filter(match => this.isMatchCompleted(match)).length || 0), 0
    );

    return totalMatches > 0 ? Math.round((completedMatches / totalMatches) * 100) : 0;
  }

  // Get next upcoming match
  getNextMatch(bracket: PlayoffBracket): BracketMatch | null {
    if (!bracket || !bracket.stages || bracket.stages.length === 0) {
      return null;
    }

    for (const stage of bracket.stages) {
      if (!stage.matches || stage.matches.length === 0) continue;

      const upcomingMatch = stage.matches.find(match =>
        match.status === 'SCHEDULED' && match.date && new Date(match.date) > new Date()
      );
      if (upcomingMatch) {
        return upcomingMatch;
      }
    }
    return null;
  }

  // Format date for display
  formatMatchDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Get team image with fallback
  getTeamImage(team: { imgUrl?: string }): string {
    return team.imgUrl || 'assets/icons/default-team.png';
  }
}
