import { Router } from "express";
import TransferRequestController from "../controllers/transfer-request-controller";
import { container } from "../config/container.config";
import { authenticateToken, requireAdmin } from "../middlewares/auth-middleware";

const router = Router();
const transferRequestController = container.resolve(TransferRequestController);

// All transfer request routes require authentication
router.use(authenticateToken);

// Create a new transfer request
router.post("/", (req, res, next) => transferRequestController.createTransferRequest(req, res, next));

// Process a transfer request (approve/reject)
router.put("/:requestId/process", (req, res, next) => transferRequestController.processTransferRequest(req, res, next));

// Get transfer requests for a team
router.get("/team/:teamId", (req, res, next) => transferRequestController.getTransferRequestsByTeam(req, res, next));

// Get a specific transfer request
router.get("/:requestId", (req, res, next) => transferRequestController.getTransferRequestById(req, res, next));

// Cancel a transfer request
router.delete("/:requestId", (req, res, next) => transferRequestController.cancelTransferRequest(req, res, next));

// Admin: Cleanup expired requests
router.post("/cleanup/expired", requireAdmin, (req, res, next) => transferRequestController.cleanupExpiredRequests(req, res, next));

export { router as transferRequestRoutes };
