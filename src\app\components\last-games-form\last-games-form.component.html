<div class="last-games-form-container" *ngIf="lastGamesForm && lastGamesForm.lastGames.length > 0">
    <!-- Header Section -->
    <div class="form-header animate-fade-in-down">
        <div class="header-content">
            <h3 class="form-title">
                <i class="fas fa-chart-line"></i>
                Recent Performance
            </h3>
            <p class="form-subtitle">Last 5 games rating trend</p>
        </div>
        <div class="games-count animate-scale-in">
            {{lastGamesForm.lastGames.length}} Games
        </div>
    </div>

    <!-- Chart Section -->
    <div class="chart-section" *ngIf="lastGamesForm && lastGamesForm.lastGames">
        <div class="chart-container" *ngIf="ratingChartOptions && ratingChartOptions.data">
            <ag-charts-angular [options]="ratingChartOptions"></ag-charts-angular>
        </div>
        <div class="no-chart-data" *ngIf="!ratingChartOptions || !ratingChartOptions.data">
            <i class="fas fa-chart-line"></i>
            <p>No chart data available</p>
            <div class="debug-info">
                <details>
                    <summary>Debug Info</summary>
                    <pre>{{getDebugInfo()}}</pre>
                </details>
            </div>
        </div>
    </div>

    <!-- Games Summary -->
    <div class="games-summary">
        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-content">
                    <span class="stat-value">{{getAverageRating()}}</span>
                    <span class="stat-label">Avg Rating</span>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">
                    <i class="fas fa-futbol"></i>
                </div>
                <div class="stat-content">
                    <span class="stat-value">{{getTotalGoals()}}</span>
                    <span class="stat-label">Goals</span>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">
                    <i class="fas fa-hands-helping"></i>
                </div>
                <div class="stat-content">
                    <span class="stat-value">{{getTotalAssists()}}</span>
                    <span class="stat-label">Assists</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading State -->
<div class="loading-container" *ngIf="isLoading">
    <pro-clubs-spinner></pro-clubs-spinner>
    <p>Loading player performance data...</p>
</div>

<!-- No Data State -->
<div class="no-data-container" *ngIf="!isLoading && (!lastGamesForm || !lastGamesForm.lastGames || lastGamesForm.lastGames.length === 0)">
    <div class="no-data-content">
        <i class="fas fa-chart-line"></i>
        <h3>No Recent Performance Data</h3>
        <p>This player hasn't played any games recently or performance data is not available.</p>
        <div class="debug-info" *ngIf="lastGamesForm">
            <small>Debug: Form exists but no games data ({{lastGamesForm.lastGames.length || 0}} games)</small>
        </div>
    </div>
</div>