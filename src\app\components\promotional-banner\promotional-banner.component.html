<div class="promotional-banner" 
     [class.dismissed]="isDismissed"
     [class.small]="size === 'small'"
     [class.medium]="size === 'medium'"
     [class.large]="size === 'large'"
     [class.primary]="message.variant === 'primary'"
     [class.secondary]="message.variant === 'secondary'"
     [class.accent]="message.variant === 'accent'"
     *ngIf="!isDismissed">
  
  <div class="banner-content">
    <!-- Icon -->
    <div class="banner-icon">
      <i [class]="message.icon"></i>
    </div>

    <!-- Text Content -->
    <div class="banner-text">
      <h3 class="banner-title">{{ message.title }}</h3>
      <p class="banner-subtitle">{{ message.subtitle }}</p>
    </div>

    <!-- Actions -->
    <div class="banner-actions">
      <button class="btn btn-primary" (click)="onSignUp()">
        {{ message.ctaText }}
      </button>
      <button class="btn btn-secondary"
              *ngIf="showSecondaryAction"
              (click)="onLogin()">
        Already have an account?
      </button>
    </div>
  </div>

  <!-- Dismiss Button (moved outside banner-content) -->
  <button class="dismiss-btn"
          *ngIf="dismissible"
          (click)="dismiss()"
          aria-label="Dismiss">
    <i class="fas fa-times"></i>
  </button>
</div>
