import { Component, Input, Output, EventEmitter } from '@angular/core';
import { PlayerDTO } from '@pro-clubs-manager/shared-dtos';

@Component({
  selector: 'app-team-squad-list',
  templateUrl: './team-squad-list.component.html',
  styleUrl: './team-squad-list.component.scss'
})
export class TeamSquadListComponent {
  @Input() players: PlayerDTO[] = [];
  @Input() canEdit: boolean = false;
  @Input() isLoading: boolean = false;
  @Input() title: string = 'Squad';

  @Output() playerClick = new EventEmitter<PlayerDTO>();
  @Output() removePlayerClick = new EventEmitter<PlayerDTO>();

  onPlayerClick(player: PlayerDTO): void {
    this.playerClick.emit(player);
  }

  onRemovePlayerClick(event: Event, player: PlayerDTO): void {
    event.stopPropagation();
    this.removePlayerClick.emit(player);
  }

  getPositionColor(position: string): string {
    switch (position?.toUpperCase()) {
      case 'GK':
        return 'position-gk';
      case 'CB':
      case 'LB':
      case 'RB':
      case 'LWB':
      case 'RWB':
        return 'position-def';
      case 'CDM':
      case 'CM':
      case 'CAM':
      case 'LM':
      case 'RM':
        return 'position-mid';
      case 'LW':
      case 'RW':
      case 'ST':
      case 'CF':
      case 'LF':
      case 'RF':
        return 'position-att';
      default:
        return 'position-default';
    }
  }

  trackByPlayerId(index: number, player: PlayerDTO): string {
    return player.id;
  }
}
