<!-- Edit News Modal -->
<div class="modal-overlay" *ngIf="show" (click)="onClose()">
    <div class="modal-container" (click)="$event.stopPropagation()">
        <!-- Modal Header -->
        <div class="modal-header">
            <h2 class="modal-title">
                <i class="fas fa-edit"></i>
                Edit News
            </h2>
            <button class="close-btn" (click)="onClose()" type="button">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="modal-body">
            <form [formGroup]="editForm" (ngSubmit)="onSave()">
                <!-- Title Field -->
                <div class="form-group">
                    <label for="title" class="form-label">
                        <i class="fas fa-heading"></i>
                        Title
                    </label>
                    <input
                        type="text"
                        id="title"
                        formControlName="title"
                        class="form-input"
                        [class.error]="title?.invalid && title?.touched"
                        placeholder="Enter news title">
                    <div *ngIf="title?.invalid && title?.touched" class="error-message">
                        <span *ngIf="title?.errors?.['required']">Title is required</span>
                        <span *ngIf="title?.errors?.['minlength']">Title must be at least 3 characters</span>
                    </div>
                </div>

                <!-- Type Field -->
                <div class="form-group">
                    <label for="type" class="form-label">
                        <i class="fas fa-tag"></i>
                        Type
                    </label>
                    <select
                        id="type"
                        formControlName="type"
                        class="form-select"
                        [class.error]="type?.invalid && type?.touched">
                        <option value="">Select news type</option>
                        <option value="General">General</option>
                        <option value="Transfer">Transfer</option>
                        <option value="FreeAgent">Free Agent</option>
                    </select>
                    <div *ngIf="type?.invalid && type?.touched" class="error-message">
                        <span *ngIf="type?.errors?.['required']">Type is required</span>
                    </div>
                </div>

                <!-- Content Field -->
                <div class="form-group">
                    <label for="content" class="form-label">
                        <i class="fas fa-align-left"></i>
                        Content
                    </label>
                    <textarea
                        id="content"
                        formControlName="content"
                        class="form-textarea"
                        [class.error]="content?.invalid && content?.touched"
                        placeholder="Enter news content"
                        rows="6">
                    </textarea>
                    <div *ngIf="content?.invalid && content?.touched" class="error-message">
                        <span *ngIf="content?.errors?.['required']">Content is required</span>
                        <span *ngIf="content?.errors?.['minlength']">Content must be at least 10 characters</span>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" (click)="onClose()">
                <i class="fas fa-times"></i>
                Cancel
            </button>
            <button 
                type="button" 
                class="btn btn-primary" 
                (click)="onSave()"
                [disabled]="editForm.invalid || isLoading">
                <i *ngIf="!isLoading" class="fas fa-save"></i>
                <i *ngIf="isLoading" class="fas fa-spinner fa-spin"></i>
                {{ isLoading ? 'Saving...' : 'Save Changes' }}
            </button>
        </div>
    </div>
</div>
