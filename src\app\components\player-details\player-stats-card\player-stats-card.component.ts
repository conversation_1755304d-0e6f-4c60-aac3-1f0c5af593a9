import { Component, Input } from '@angular/core';

export interface PlayerStat {
  label: string;
  value: string | number;
  icon?: string;
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
}

@Component({
  selector: 'app-player-stats-card',
  templateUrl: './player-stats-card.component.html',
  styleUrl: './player-stats-card.component.scss'
})
export class PlayerStatsCardComponent {
  @Input() title!: string;
  @Input() stats!: PlayerStat[];
  @Input() columns: number = 2;
  @Input() compact: boolean = false;

  getStatColorClass(color?: string): string {
    if (!color) return 'stat-item--primary';
    return `stat-item--${color}`;
  }

  getTrendIcon(trend?: string): string {
    switch (trend) {
      case 'up':
        return 'fas fa-arrow-up';
      case 'down':
        return 'fas fa-arrow-down';
      case 'neutral':
        return 'fas fa-minus';
      default:
        return '';
    }
  }

  getGridColumns(): string {
    return `repeat(${this.columns}, 1fr)`;
  }

  trackByStat(index: number, stat: PlayerStat): string {
    return stat.label;
  }
}
