import { Component } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { CreateTeamModel } from '../../shared/models/team.model';
import { TeamService } from '../../services/team.service';
import { NotificationService } from '../../services/notification.service';
import { createWorker } from 'tesseract.js';

@Component({
  selector: 'app-create-team',
  templateUrl: './create-team.component.html',
  styleUrl: './create-team.component.scss'
})
export class CreateTeamComponent {
  addTeamFormGroup: FormGroup;
  addTeamFile: FormData = new FormData();
  LEAGUE_ID = "65ecb1eb2f272e434483a821";

  constructor(private formBuilder: FormBuilder, private teamService: TeamService, private notificationService: NotificationService) {
    this.addTeamFormGroup = this.formBuilder.group({
      name: ['', Validators.required]
    });
  }


  ngOnInit() {

  }

  clearForm() {
    this.addTeamFormGroup.reset();
  }

  async onSubmit() {
    if (this.addTeamFormGroup.valid) {
      var createTeamModel = this.convertFormToModel();
      const createTeamResponse = await this.teamService.createTeam(createTeamModel);

      this.notificationService.success(`${createTeamResponse.name} Added Successfuly`);
    }
  }

  convertFormToModel(): CreateTeamModel {
    const formValues = this.addTeamFormGroup.value;
    return {
      name: formValues.name,
      leagueId: this.LEAGUE_ID,
      logoUrl: formValues['photo-url']
    };
  }

  selectFile() {
    const fileInput = document.getElementById('customFile');
    fileInput?.click(); // Trigger click event on the hidden file input
  }

  async onFileSelected($event: any) {
    if (!$event.target.files)
      return;


    const file: File = $event.target.files[0];
    this.addTeamFile.append('file', file);
    const imageUrl = URL.createObjectURL(file);
    const worker = await createWorker('eng');
    const {
      data: { text },
    } = await worker.recognize(imageUrl);

    const ocrResult = text;

    const cleanedPlayers: any[] = [];

const lines = ocrResult
  .split('\n')
  .map(line => line.trim())
  .filter(line => line.length > 0);

// Example regex to extract relevant parts
const regex = /^([A-Z]{2,3})\s+([A-Za-z. '\-]+)\s+([6-9]\.\d)(?:\s+(\d))?(?:\s+(\d))?/;

for (const line of lines) {
  const match = line.match(regex);
  if (match) {
    const [, positionPlayed, playerName, ratingStr, goalsStr, assistsStr] = match;
    cleanedPlayers.push({
      positionPlayed,
      playerName: playerName.trim(),
      rating: parseFloat(ratingStr),
      goals: parseInt(goalsStr || '0'),
      assists: parseInt(assistsStr || '0'),
    });
  }
}

console.log(cleanedPlayers);
    console.log('OCR Result:', text);


  }
}
