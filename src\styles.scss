@import "./styles/color-variables.scss";
@import "./styles/typography.scss";
@import "./styles/animations.scss";
@import "./styles/common-classes.scss";
@import "./styles/mobile-adjust.scss";
@import "./styles/utilities.scss";
@import "./styles/component-mixins.scss";
@import "./styles/theme-utilities.scss";

/* === ENHANCED COMPONENT STYLES === */
@import "./styles/enhanced-buttons.scss";
@import "./styles/enhanced-cards.scss";
@import "./styles/enhanced-forms.scss";

/* === BEAUTIFUL NOTIFICATION TOAST STYLES === */

/* Base toast styling */
.mat-mdc-snack-bar-container {
    --mdc-snackbar-container-shape: 12px !important;
    --mdc-snackbar-supporting-text-color: white !important;
    --mdc-snackbar-supporting-text-font: var(--font-sans) !important;
    --mdc-snackbar-supporting-text-size: 14px !important;
    --mdc-snackbar-supporting-text-weight: 500 !important;
    --mdc-snackbar-supporting-text-line-height: 1.4 !important;

    border-radius: 12px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    min-width: 320px !important;
    max-width: 500px !important;

    /* Smooth entrance animation */
    animation: toastSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;

    /* Enhanced typography */
    font-family: var(--font-sans) !important;
    font-weight: 500 !important;
    letter-spacing: 0.01em !important;
}

/* Success Toast */
.success-snackbar {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white !important;
    border-left: 4px solid #34d399 !important;

    &::before {
        content: '✓';
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 18px;
        font-weight: bold;
        color: #34d399;
        background: rgba(255, 255, 255, 0.2);
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mat-mdc-snack-bar-label {
        padding-left: 48px !important;
        padding-right: 16px !important;
        padding-top: 16px !important;
        padding-bottom: 16px !important;
    }
}

/* Error Toast */
.error-snackbar {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    color: white !important;
    border-left: 4px solid #f87171 !important;

    &::before {
        content: '✕';
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        font-weight: bold;
        color: #f87171;
        background: rgba(255, 255, 255, 0.2);
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mat-mdc-snack-bar-label {
        padding-left: 48px !important;
        padding-right: 16px !important;
        padding-top: 16px !important;
        padding-bottom: 16px !important;
    }
}

/* Info Toast */
.info-snackbar {
    background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
    color: white !important;
    border-left: 4px solid #60a5fa !important;

    &::before {
        content: 'ℹ';
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 18px;
        font-weight: bold;
        color: #60a5fa;
        background: rgba(255, 255, 255, 0.2);
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mat-mdc-snack-bar-label {
        padding-left: 48px !important;
        padding-right: 16px !important;
        padding-top: 16px !important;
        padding-bottom: 16px !important;
    }
}

/* Warning Toast */
.warning-snackbar {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    color: white !important;
    border-left: 4px solid #fbbf24 !important;

    &::before {
        content: '⚠';
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        font-weight: bold;
        color: #fbbf24;
        background: rgba(255, 255, 255, 0.2);
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mat-mdc-snack-bar-label {
        padding-left: 48px !important;
        padding-right: 16px !important;
        padding-top: 16px !important;
        padding-bottom: 16px !important;
    }
}

/* Close button styling */
.mat-mdc-snack-bar-action {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    transition: all 0.2s ease !important;
    margin-left: 12px !important;

    &:hover {
        background: rgba(255, 255, 255, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.3) !important;
        transform: translateY(-1px) !important;
    }
}

/* Toast entrance animation */
@keyframes toastSlideIn {
    0% {
        transform: translateX(100%) scale(0.9);
        opacity: 0;
    }

    100% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

/* Toast exit animation */
.mat-mdc-snack-bar-container.ng-animating {
    animation: toastSlideOut 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) !important;
}

@keyframes toastSlideOut {
    0% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }

    100% {
        transform: translateX(100%) scale(0.9);
        opacity: 0;
    }
}

/* Progress bar for auto-dismiss */
.success-snackbar::after,
.error-snackbar::after,
.info-snackbar::after,
.warning-snackbar::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0 0 12px 12px;
    animation: progressBar linear;
}

.success-snackbar::after {
    animation-duration: 3s;
}

.error-snackbar::after {
    animation-duration: 5s;
}

.info-snackbar::after,
.warning-snackbar::after {
    animation-duration: 4s;
}

@keyframes progressBar {
    0% {
        width: 100%;
    }

    100% {
        width: 0%;
    }
}

/* Dark mode adjustments */
html[data-theme="dark"] {
    .mat-mdc-snack-bar-container {
        border-color: rgba(255, 255, 255, 0.15) !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 4px 10px rgba(0, 0, 0, 0.2) !important;
    }
}

/* Light mode adjustments */
html[data-theme="light"] {
    .mat-mdc-snack-bar-container {
        border-color: rgba(0, 0, 0, 0.1) !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05) !important;
    }
}

@font-face {
    font-family: Montserrat;
    src: url('assets/Fonts/Montserrat.ttf');
}

@font-face {
    font-family: Noto;
    src: url('assets/Fonts/Noto.ttf');
}

/* === THEME INITIALIZATION === */
html {
    /* Default to dark theme */
    color-scheme: dark;
}

html[data-theme="dark"] {
    color-scheme: dark;
}

html[data-theme="light"] {
    color-scheme: light;
}

/* Theme variables are now controlled by data-theme attribute in color-variables.scss */

body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-sans);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Enhanced body styling for light theme with colorful gradients */
[data-theme="light"] body {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 20%, #ecfdf5 40%, #fffbeb 60%, #fef2f2 80%, #f8fafc 100%);
    position: relative;
    min-height: 100vh;
}

/* Add beautiful colorful pattern for light theme */
[data-theme="light"] body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 20% 80%, rgba(245, 158, 11, 0.06) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(239, 68, 68, 0.06) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(168, 85, 247, 0.04) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Enhanced body styling for dark theme */
[data-theme="dark"] body {
    background: linear-gradient(135deg, #020617 0%, #0f172a 100%);
    position: relative;
}

/* Add subtle pattern for dark theme */
[data-theme="dark"] body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

* {
    font-family: var(--font-sans);
    box-sizing: border-box;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface-tertiary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: var(--radius-sm);

    &:hover {
        background: var(--text-muted);
    }
}

.place-content-center {
    place-content: center;
}

input:focus {
    outline: none;
}

.cursor-pointer {
    cursor: pointer;
}

// Override modal content
.modal-content {
    background-color: var(--bg-primary) !important;
}

html,
body {
    height: 100%;
    overflow-x: hidden;
}

/* Ensure app root has proper background */
app-root {
    display: block;
    min-height: 100vh;
    background-color: var(--bg-primary);
}

/* Router outlet container */
router-outlet+* {
    background-color: var(--bg-primary);
    min-height: 100vh;
}

/* === GLOBAL IMPROVEMENTS === */

/* Perfect box-sizing for all elements */
*,
*::before,
*::after {
    box-sizing: border-box;
}

/* Theme transition for all elements */
* {
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Perfect alignment for flex containers */
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.flex-end {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

/* Perfect grid alignment */
.grid-center {
    display: grid;
    place-items: center;
}

/* Focus styles */
*:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

/* Selection styles */
::selection {
    background: var(--primary);
    color: var(--text-inverse);
}

/* Image optimization */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Button reset */
button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
}

/* Link styles */
a {
    color: var(--primary);
    text-decoration: none;
    transition: color 0.2s ease-in-out;

    &:hover {
        color: var(--primary-hover);
    }
}

/* Form elements */
input,
textarea,
select {
    font-family: inherit;
    font-size: inherit;
}

/* Remove default margins */
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol {
    margin: 0;
    padding: 0;
}

/* List styles */
ul,
ol {
    list-style: none;
}

/* Table styles */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Utility classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* width */
::-webkit-scrollbar {
    width: 8px;
}

/* Track */
::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 20px;
}

.h-5 {
    height: 5%;
}

.h-7 {
    height: 7%;
}

.h-8 {
    height: 7%;
}

.h-10 {
    height: 10%;
}

.h-15 {
    height: 15%;
}

.h-60 {
    height: 60%;
}

.h-65 {
    height: 65%;
}

.h-70 {
    height: 70%;
}

.h-80 {
    height: 80%;
}

.h-81 {
    height: 81%;
}

.h-83 {
    height: 83%;
}

.h-85 {
    height: 85%;
}

.h-87 {
    height: 87%;
}

.h-90 {
    height: 90%;
}

.h-93 {
    height: 93%;
}

.h-95 {
    height: 95%;
}

.h-96 {
    height: 96%;
}

.w-7 {
    width: 7%;
}

.w-10 {
    width: 10%;
}

.w-15 {
    width: 15%;
}

.w-20 {
    width: 20%;
}

.w-25 {
    width: 25%;
}

.w-30 {
    width: 30%;
}

.w-40 {
    width: 40%;
}

.w-45 {
    width: 45%;
}

.w-45 {
    width: 45%;
}

.w-60 {
    width: 60%;
}

.w-70 {
    width: 70%;
}

.width-max-content {
    width: max-content;
}

.overflow-y-auto {
    overflow-y: auto;
}

.justify-items-center {
    justify-items: center;
}

input[type="file"] {
    display: none;
}

.gap-2 {
    gap: 2px;
}

.gap-10 {
    gap: 10px;
}

.gap-15 {
    gap: 15px;
}

// sets active tab text color
.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
    color: var(--primary) !important;
}

// sets note active tab text color
.mat-mdc-tab .mdc-tab__text-label {
    color: var(--text-primary) !important;
}

/* === MATERIAL DESIGN TAB THEMING === */

/* Tab Group Container */
.mat-mdc-tab-group {
    --mdc-tab-indicator-active-indicator-color: var(--primary) !important;
    --mat-tab-header-active-focus-label-text-color: var(--primary) !important;
    --mat-tab-header-active-hover-label-text-color: var(--primary) !important;
    --mat-tab-header-active-ripple-color: var(--primary) !important;
    --mat-tab-header-inactive-ripple-color: var(--primary) !important;
    --mat-tab-header-active-focus-indicator-color: var(--primary) !important;
    --mat-tab-header-active-hover-indicator-color: var(--primary) !important;

    /* Tab header background */
    .mat-mdc-tab-header {
        background-color: var(--bg-primary) !important;
        border-bottom: 1px solid var(--border-primary) !important;
    }

    /* Individual tab styling */
    .mdc-tab {
        background-color: var(--bg-primary) !important;
        color: var(--text-secondary) !important;
        border: none !important;
        transition: all 0.2s ease-in-out !important;

        /* Tab text label */
        .mdc-tab__text-label {
            color: var(--text-secondary) !important;
            font-weight: var(--font-weight-medium) !important;
            font-size: 0.875rem !important;
        }

        /* Hover state */
        &:hover {
            background-color: var(--surface-hover) !important;

            .mdc-tab__text-label {
                color: var(--text-primary) !important;
            }
        }

        /* Active state */
        &.mdc-tab--active {
            background-color: var(--surface-active) !important;

            .mdc-tab__text-label {
                color: var(--primary) !important;
                font-weight: var(--font-weight-semibold) !important;
            }
        }

        /* Focus state */
        &:focus {
            outline: 2px solid var(--primary) !important;
            outline-offset: -2px !important;
        }
    }

    /* Tab content area */
    .mat-mdc-tab-body-wrapper {
        background-color: var(--bg-primary) !important;
    }
}

/* Tab Navigation Bar (if used separately) */
.mat-mdc-tab-nav-bar {
    --mdc-tab-indicator-active-indicator-color: var(--primary) !important;
    --mat-tab-header-active-focus-label-text-color: var(--primary) !important;
    --mat-tab-header-active-hover-label-text-color: var(--primary) !important;
    --mat-tab-header-active-ripple-color: var(--primary) !important;
    --mat-tab-header-inactive-ripple-color: var(--primary) !important;
    --mat-tab-header-active-focus-indicator-color: var(--primary) !important;
    --mat-tab-header-active-hover-indicator-color: var(--primary) !important;

    background-color: var(--bg-primary) !important;
    border-bottom: 1px solid var(--border-primary) !important;

    .mdc-tab {
        background-color: var(--bg-primary) !important;
        color: var(--text-secondary) !important;

        .mdc-tab__text-label {
            color: var(--text-secondary) !important;
        }

        &:hover {
            background-color: var(--surface-hover) !important;

            .mdc-tab__text-label {
                color: var(--text-primary) !important;
            }
        }

        &.mdc-tab--active {
            background-color: var(--surface-active) !important;

            .mdc-tab__text-label {
                color: var(--primary) !important;
            }
        }
    }
}

.arrow-back {
    font-size: 24px;
}

.mat-mdc-text-field-wrapper {
    height: 2.2rem !important;
    align-items: center !important;
}

.height-max-content {
    height: max-content;
}

.mat-mdc-form-field-infix {
    width: 100% !important;
}

.border-white {
    border: 1px solid white;
}

.overflow-x-disabled {
    overflow-x: hidden;
}

.border-radius-8 {
    border-radius: 8px;
}

.cdk-overlay-pane.mat-mdc-dialog-panel {
    max-width: 95vw !important;
}

/* Game Details Dialog Specific Styling */
.cdk-overlay-pane.game-details-dialog {
    width: 95vw !important;
    max-width: 95vw !important;
    height: 90vh !important;
    max-height: 90vh !important;
}

.cdk-overlay-pane.game-details-dialog .mat-mdc-dialog-container {
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    max-height: none !important;
    padding: 0 !important;
}


// overwrite pagination arrows color

.mat-mdc-paginator-icon {
    fill: var(--text-primary) !important;
}

.mdc-data-table__cell,
.mdc-data-table__header-cell {
    padding: 0px 8px !important;
}

.breadcrumb {
    --bs-breadcrumb-margin-bottom: 0 !important;
}