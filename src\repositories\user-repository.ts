import { injectable } from "tsyringe";
import User, { IUser } from "../models/user";
import { IUserRepository } from "../interfaces/user";
import logger from "../config/logger";

@injectable()
export default class UserRepository implements IUserRepository {
  
  async create(userData: Partial<IUser>): Promise<IUser> {
    try {
      const user = new User(userData);
      const savedUser = await user.save();
      logger.info(`User created with ID: ${savedUser.id}`);
      return savedUser;
    } catch (error: any) {
      logger.error(`Error creating user: ${error.message}`);
      throw error;
    }
  }

  async findById(userId: string): Promise<IUser | null> {
    try {
      const user = await User.findById(userId);
      if (user) {
        logger.info(`Found user ${userId} with ${user.associatedPlayers.length} associated players (ObjectIds)`);
        logger.info(`Associated player IDs: ${user.associatedPlayers.map(id => id.toString()).join(', ')}`);
      } else {
        logger.warn(`User ${userId} not found`);
      }
      return user;
    } catch (error: any) {
      logger.error(`Error finding user by ID ${userId}: ${error.message}`);
      throw error;
    }
  }

  async findByEmail(email: string): Promise<IUser | null> {
    try {
      const user = await User.findOne({ email: email.toLowerCase() });
      return user;
    } catch (error: any) {
      logger.error(`Error finding user by email ${email}: ${error.message}`);
      throw error;
    }
  }

  async findByGoogleId(googleId: string): Promise<IUser | null> {
    try {
      const user = await User.findOne({ googleId });
      return user;
    } catch (error: any) {
      logger.error(`Error finding user by Google ID ${googleId}: ${error.message}`);
      throw error;
    }
  }

  async update(userId: string, updateData: Partial<IUser>): Promise<IUser | null> {
    try {
      const user = await User.findByIdAndUpdate(
        userId,
        updateData,
        { new: true, runValidators: true }
      );
      
      if (user) {
        logger.info(`User updated with ID: ${userId}`);
      }
      
      return user;
    } catch (error: any) {
      logger.error(`Error updating user ${userId}: ${error.message}`);
      throw error;
    }
  }

  async delete(userId: string): Promise<boolean> {
    try {
      const result = await User.findByIdAndDelete(userId);
      if (result) {
        logger.info(`User deleted with ID: ${userId}`);
        return true;
      }
      return false;
    } catch (error: any) {
      logger.error(`Error deleting user ${userId}: ${error.message}`);
      throw error;
    }
  }

  async findAll(): Promise<IUser[]> {
    try {
      const users = await User.find().populate('associatedPlayers');
      return users;
    } catch (error: any) {
      logger.error(`Error finding all users: ${error.message}`);
      throw error;
    }
  }
}
