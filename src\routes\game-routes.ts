import { Router } from "express";
import GameController from "../controllers/game-controller";
import { container } from "../config/container.config";
import { authenticateToken, requireAdmin, requireGameEditPermission } from "../middlewares/auth-middleware";

const router = Router();
const gameController = container.resolve(GameController);

router.get("/all", (req, res, next) => gameController.getAllGames(req, res, next));
router.get("/:id", (req, res, next) => gameController.getGameById(req, res, next));
router.get("/team/:teamId", (req, res, next) => gameController.getCurrentSeasonTeamGames(req, res, next));
router.get("/history/:team1Id/:team2Id", (req, res, next) => gameController.getTeamVsTeamHistory(req, res, next));
router.get("/topAvgRatingByPosition/:position", (req, res, next) => gameController.getTopAvgRatingByPosition(req, res, next));

// Admin only routes
router.put("/:id/updateDate", authenticateToken, requireAdmin, (req, res, next) => gameController.updateGameDate(req, res, next));
router.put("/:id/updateBroadcast", authenticateToken, requireAdmin, (req, res, next) => gameController.updateGameBroadcast(req, res, next));
router.put("/:id/technical-loss", authenticateToken, requireAdmin, (req, res, next) => gameController.setTechnicalResult(req, res, next));
router.delete("/:id", authenticateToken, requireAdmin, (req, res, next) => gameController.deleteGame(req, res, next));
router.post("/clear-cache/:leagueId", authenticateToken, requireAdmin, (req, res, next) => gameController.clearAllCaches(req, res, next));

// Team captain only routes (for match stats editing)
router.put("/:id/updateResult", authenticateToken, requireGameEditPermission, (req, res, next) => gameController.updateGameResult(req, res, next));
router.put("/:id/teamPlayersPerformance", authenticateToken, requireGameEditPermission, (req, res, next) => gameController.updateTeamPlayersPerformance(req, res, next));

export default router;
