import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { GameService } from '../../services/game.service';
import { LeagueService } from '../../services/league.service';
import { NotificationService } from '../../services/notification.service';
import { MatDialog } from '@angular/material/dialog';
import { GAME_STATUS, GameDTO } from '@pro-clubs-manager/shared-dtos';
import { AuthService } from '../../services/auth.service';
import { PermissionsService } from '../../services/permissions.service';
import { LEAGUE_ID } from '../../constants/constants';
import { Observable } from 'rxjs';

@Component({
  selector: 'team-games',
  templateUrl: './team-games.component.html',
  styleUrl: './team-games.component.scss'
})
export class TeamGamesComponent {
  GameStatus = GAME_STATUS;
  isLoading: boolean = false;
  dateFormat = 'dd.MM.YYYY';
  teamGamesData: GameDTO[] | undefined = undefined;
  selectedGame: GameDTO | null = null;
  currentEditedGameId: string | null = null;
  homeTeamGoals: number = 0;
  awayTeamGoals: number = 0;
  editGame: boolean = false;

  @Input() teamId: string | undefined = undefined;

  constructor(
    private router: Router,
    private gameService: GameService,
    private leagueService: LeagueService,
    private notificationService: NotificationService,
    private matDialog: MatDialog,
    private authService: AuthService,
    private permissionsService: PermissionsService
  ) { }

  ngOnInit() {
    this.isLoading = true;
    this.loadTeamGames();
  }

  async loadTeamGames() {
    if (this.teamId) {
      try {
        const response = await this.gameService.getTeamGames(this.teamId);
        this.teamGamesData = response;
      } catch (error) {
        console.error('Error loading team games:', error);
        this.notificationService.error('Failed to load team games. Please try again.');
      }

      this.isLoading = false;
    }
  }

  onGameClick(selectedGame: GameDTO): void {
    if (this.currentEditedGameId === selectedGame.id) {
      return;
    }

    // Navigate to standalone game details page
    this.router.navigate(['/game-details', selectedGame.id]);
  }



  onEditClick() {
    this.editGame = true;
  }

  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  canEditGame(gameId: string): Observable<boolean> {
    return this.permissionsService.canEditGame(gameId);
  }

  onCancelClick() {
    this.editGame = false;
  }

  onEditGameResultClick(game: GameDTO) {
    if (game!.status !== GAME_STATUS.SCHEDULED) {
      this.homeTeamGoals = game.result!.homeTeamGoals;
      this.awayTeamGoals = game.result!.awayTeamGoals;
    }
    else {
      this.homeTeamGoals = 0;
      this.awayTeamGoals = 0;
    }

    this.currentEditedGameId = game.id;
  }

  async onSaveClick(game: GameDTO) {
    try {
      const extendedGame = game as any;
      const isPlayoff = extendedGame.isPlayoff || false;
      await this.gameService.updateGameResult(game.id, this.homeTeamGoals, this.awayTeamGoals, new Date(), isPlayoff);
      this.notificationService.success(`Result: ${game.homeTeam.name} ${this.homeTeamGoals} : ${this.awayTeamGoals} ${game.awayTeam.name} updated successfully`);

      // Update game status and result
      game.status = GAME_STATUS.PLAYED;
      game.result = { homeTeamGoals: this.homeTeamGoals, awayTeamGoals: this.awayTeamGoals };

      // Refresh league table to reflect the updated result
      await this.refreshLeagueTable();
    } catch (error) {
      console.error('Error updating game result:', error);
      this.notificationService.error('Failed to update game result. Please try again.');
    }

    this.currentEditedGameId = null;
  }

  private async refreshLeagueTable() {
    try {
      // Force refresh the league table to get updated standings
      await this.leagueService.getLeagueTable(LEAGUE_ID, true);
    } catch (error) {
      console.error('Error refreshing league table:', error);
      // Don't show error to user as this is a background operation
    }
  }

  // New methods for the modern UI
  getWins(): number {
    if (!this.teamGamesData) return 0;
    return this.teamGamesData.filter(game => {
      if (game.status !== GAME_STATUS.PLAYED && game.status !== GAME_STATUS.COMPLETED) return false;
      if (!game.result) return false;

      // Check if this team won (either as home or away)
      const isHomeTeam = game.homeTeam.id === this.teamId;
      if (isHomeTeam) {
        return game.result.homeTeamGoals > game.result.awayTeamGoals;
      } else {
        return game.result.awayTeamGoals > game.result.homeTeamGoals;
      }
    }).length;
  }

  getDraws(): number {
    if (!this.teamGamesData) return 0;
    return this.teamGamesData.filter(game => {
      if (game.status !== GAME_STATUS.PLAYED && game.status !== GAME_STATUS.COMPLETED) return false;
      if (!game.result) return false;
      return game.result.homeTeamGoals === game.result.awayTeamGoals;
    }).length;
  }

  trackByGameId(index: number, game: GameDTO): string {
    return game.id;
  }

  isPlayoffGame(game: GameDTO): boolean {
    const extendedGame = game as any;
    return extendedGame.isPlayoff || false;
  }

  getPlayoffStage(game: GameDTO): string {
    const extendedGame = game as any;
    return extendedGame.playoffStage || 'Playoff';
  }

  getStatusClass(status: string): string {
    switch (status) {
      case GAME_STATUS.PLAYED:
      case GAME_STATUS.COMPLETED:
        return 'status-completed';
      case GAME_STATUS.SCHEDULED:
        return 'status-scheduled';
      default:
        return 'status-unknown';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case GAME_STATUS.PLAYED:
      case GAME_STATUS.COMPLETED:
        return 'Completed';
      case GAME_STATUS.SCHEDULED:
        return 'Scheduled';
      default:
        return 'Unknown';
    }
  }

  cancelEdit(): void {
    this.currentEditedGameId = null;
    this.homeTeamGoals = 0;
    this.awayTeamGoals = 0;
  }

}