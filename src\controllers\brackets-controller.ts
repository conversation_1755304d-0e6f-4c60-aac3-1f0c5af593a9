import { Request, Response, NextFunction } from "express";
import { inject, injectable } from "tsyringe";
import { IBracketsService } from "../services/brackets-service";
import logger from "../config/logger";

@injectable()
export default class BracketsController {
  constructor(
    @inject("IBracketsService") private bracketsService: IBracketsService
  ) {}

  async getPlayoffBracket(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { leagueId } = req.params;
      const { seasonNumber } = req.query;

      if (!leagueId) {
        res.status(400).json({ message: "League ID is required" });
        return;
      }

      const season = seasonNumber ? parseInt(seasonNumber as string) : undefined;
      const bracket = await this.bracketsService.getPlayoffBracket(leagueId, season);

      res.json({
        success: true,
        data: bracket
      });
    } catch (error: any) {
      logger.error("Error in getPlayoffBracket:", error);
      next(error);
    }
  }

  async getPlayoffMatches(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { leagueId } = req.params;
      const { seasonNumber } = req.query;

      if (!leagueId) {
        res.status(400).json({ message: "League ID is required" });
        return;
      }

      const season = seasonNumber ? parseInt(seasonNumber as string) : undefined;
      const matches = await this.bracketsService.getPlayoffMatches(leagueId, season);

      res.json({
        success: true,
        data: matches
      });
    } catch (error: any) {
      logger.error("Error in getPlayoffMatches:", error);
      next(error);
    }
  }
}
