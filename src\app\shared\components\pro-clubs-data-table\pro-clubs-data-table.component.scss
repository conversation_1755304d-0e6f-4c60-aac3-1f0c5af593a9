// override background color of all the table
.mat-mdc-table {
    background-color: var(--bg-primary) !important;
}

// override color of td
.mat-mdc-cell {
    color: var(--text-primary) !important;
}

// override color of headers
.mat-mdc-header-cell {
    color: var(--text-primary) !important;
    border-bottom: 2px solid var(--primary);
}

.photo-in-table-row {
    width: 4.5vh;
    height: 4.5vh;
    clip-path: circle(50% at 50% 50%);
}

.mat-mdc-row:hover {
    opacity: 0.6;
}