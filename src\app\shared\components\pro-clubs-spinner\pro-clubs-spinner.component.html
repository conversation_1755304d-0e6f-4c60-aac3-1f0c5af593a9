<div class="pro-clubs-spinner-container">
  <!-- Background Overlay -->
  <div class="spinner-overlay"></div>

  <!-- Main Spinner Content -->
  <div class="spinner-content">
    <!-- Outer Ring Animation -->
    <div class="spinner-ring outer-ring">
      <div class="ring-segment"></div>
      <div class="ring-segment"></div>
      <div class="ring-segment"></div>
      <div class="ring-segment"></div>
    </div>

    <!-- Middle Ring Animation -->
    <div class="spinner-ring middle-ring">
      <div class="ring-segment"></div>
      <div class="ring-segment"></div>
      <div class="ring-segment"></div>
    </div>

    <!-- Inner Ring Animation -->
    <div class="spinner-ring inner-ring">
      <div class="ring-segment"></div>
      <div class="ring-segment"></div>
    </div>

    <!-- Central Logo Container -->
    <div class="logo-container">
      <div class="logo-glow"></div>
      <img src="assets/Icons/IPL.png" alt="IPL Logo" class="ipl-logo" />
      <div class="logo-pulse"></div>
    </div>

    <!-- Floating Particles -->
    <div class="particles">
      <div class="particle" *ngFor="let particle of particles; let i = index"
           [style.animation-delay]="particle.delay + 's'"
           [style.left]="particle.x + '%'"
           [style.top]="particle.y + '%'">
      </div>
    </div>

    <!-- Loading Text -->
    <div class="loading-text">
      <span class="loading-word">
        <span class="letter" *ngFor="let letter of loadingLetters; let i = index"
              [style.animation-delay]="i * 0.1 + 's'">
          {{letter}}
        </span>
      </span>
      <div class="loading-dots">
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </div>
    </div>
  </div>

  <!-- Progress Bar -->
  <div class="progress-container">
    <div class="progress-bar">
      <div class="progress-fill"></div>
    </div>
    <div class="progress-text">Loading IPL Season 6</div>
  </div>
</div>