import { Router } from "express";
import { PlayerAssociationRequestController } from "../controllers/player-association-request-controller";
import { container } from "../config/container.config";
import { authenticateToken, requireAdmin } from "../middlewares/auth-middleware";

const router = Router();
const requestController = container.resolve(PlayerAssociationRequestController);

// User routes (authenticated users only)
router.post("/", authenticateToken, (req, res, next) => requestController.createRequest(req, res, next));
router.get("/my-requests", authenticateToken, (req, res, next) => requestController.getUserRequests(req, res, next));
router.delete("/:requestId", authenticateToken, (req, res, next) => requestController.cancelRequest(req, res, next));

// Admin routes (admin only)
router.get("/pending", authenticateToken, requireAdmin, (req, res, next) => requestController.getPendingRequests(req, res, next));
router.put("/:requestId/approve", authenticateToken, requireAdmin, (req, res, next) => requestController.approveRequest(req, res, next));
router.put("/:requestId/reject", authenticateToken, requireAdmin, (req, res, next) => requestController.rejectRequest(req, res, next));

export default router;
