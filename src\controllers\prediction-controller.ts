import { Request, Response, NextFunction } from "express";
import { inject, injectable } from "tsyringe";
import { IPredictionService } from "../services/prediction-service";
import { PREDICTION_OUTCOME } from "../models/prediction/prediction";

@injectable()
export class PredictionController {
  
  constructor(
    @inject("IPredictionService") private predictionService: IPredictionService
  ) {}

  async createOrUpdatePrediction(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { gameId } = req.params;
    const { predictedOutcome, confidence } = req.body;
    const userId = req.user!.id;

    try {
      // Validate prediction outcome
      if (!Object.values(PREDICTION_OUTCOME).includes(predictedOutcome)) {
        res.status(400).json({ 
          success: false, 
          message: "Invalid prediction outcome. Must be HOME_WIN, AWAY_WIN, or DRAW" 
        });
        return;
      }

      // Validate confidence if provided
      if (confidence !== undefined && (confidence < 1 || confidence > 5)) {
        res.status(400).json({ 
          success: false, 
          message: "Confidence must be between 1 and 5" 
        });
        return;
      }

      const prediction = await this.predictionService.createOrUpdatePrediction(
        gameId, 
        userId, 
        predictedOutcome, 
        confidence
      );

      res.status(200).json({ 
        success: true, 
        data: prediction 
      });
    } catch (error: any) {
      next(error);
    }
  }

  async deletePrediction(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { predictionId } = req.params;
    const userId = req.user!.id;

    try {
      await this.predictionService.deletePrediction(predictionId, userId);
      
      res.status(200).json({ 
        success: true, 
        message: "Prediction deleted successfully" 
      });
    } catch (error: any) {
      next(error);
    }
  }

  async getPredictionDistribution(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { gameId } = req.params;
    const userId = req.user?.id; // Optional for non-authenticated users

    try {
      const distribution = await this.predictionService.getPredictionDistribution(gameId, userId);
      
      res.status(200).json({ 
        success: true, 
        data: distribution 
      });
    } catch (error: any) {
      next(error);
    }
  }

  async getUserPredictions(req: Request, res: Response, next: NextFunction): Promise<void> {
    const userId = req.user!.id;
    const { limit } = req.query;

    try {
      const predictions = await this.predictionService.getUserPredictions(
        userId, 
        limit ? parseInt(limit as string) : undefined
      );
      
      res.status(200).json({ 
        success: true, 
        data: predictions 
      });
    } catch (error: any) {
      next(error);
    }
  }

  async getPredictionById(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { predictionId } = req.params;

    try {
      const prediction = await this.predictionService.getPredictionById(predictionId);
      
      res.status(200).json({ 
        success: true, 
        data: prediction 
      });
    } catch (error: any) {
      next(error);
    }
  }
}
