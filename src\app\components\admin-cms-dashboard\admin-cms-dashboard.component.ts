import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { PlayerService } from '../../services/player.service';
import { TeamService } from '../../services/team.service';
import { GameService } from '../../services/game.service';
import { LeagueService } from '../../services/league.service';
import { NewsService } from '../../services/news.service';
import { NotificationService } from '../../services/notification.service';
import { AuthService } from '../../services/auth.service';
import { PlayerDTO, TeamDTO, GameDTO, LeagueDTO } from '@pro-clubs-manager/shared-dtos';
import { News } from '../news/news.model';

interface CMSEntity {
  id: string;
  name: string;
  type: 'player' | 'team' | 'game' | 'league' | 'news';
  details: string;
  actions: string[];
}

@Component({
  selector: 'app-admin-cms-dashboard',
  templateUrl: './admin-cms-dashboard.component.html',
  styleUrl: './admin-cms-dashboard.component.scss'
})
export class AdminCmsDashboardComponent implements OnInit {
  // Data arrays
  players: PlayerDTO[] = [];
  teams: TeamDTO[] = [];
  games: GameDTO[] = [];
  leagues: LeagueDTO[] = [];
  news: News[] = [];

  // UI state
  activeTab: 'players' | 'teams' | 'games' | 'leagues' | 'news' | 'overview' = 'overview';
  isLoading = false;
  searchTerm = '';
  
  // Pagination
  currentPage = 1;
  itemsPerPage = 20;
  
  // Statistics
  stats = {
    totalPlayers: 0,
    totalTeams: 0,
    totalGames: 0,
    totalLeagues: 0,
    totalNews: 0
  };

  constructor(
    private playerService: PlayerService,
    private teamService: TeamService,
    private gameService: GameService,
    private leagueService: LeagueService,
    private newsService: NewsService,
    private notificationService: NotificationService,
    private authService: AuthService,
    private router: Router
  ) {}

  async ngOnInit(): Promise<void> {
    // Check if user is admin
    if (!this.authService.isAdmin()) {
      this.router.navigate(['/dashboard']);
      return;
    }

    await this.loadOverviewData();
  }

  async loadOverviewData(): Promise<void> {
    this.isLoading = true;
    try {
      // Load basic counts for overview
      const [players, teams, games, leagues, news] = await Promise.all([
        this.playerService.getAllPlayers(),
        this.teamService.getAllTeams(),
        this.gameService.getAllGames(),
        this.leagueService.getAllLeagues(),
        this.newsService.getAllNews()
      ]);

      this.stats = {
        totalPlayers: players.length,
        totalTeams: teams.length,
        totalGames: games.length,
        totalLeagues: leagues.length,
        totalNews: news.length
      };
    } catch (error) {
      console.error('Error loading overview data:', error);
      this.notificationService.error('Failed to load overview data');
    } finally {
      this.isLoading = false;
    }
  }

  async switchTab(tab: 'players' | 'teams' | 'games' | 'leagues' | 'news' | 'overview'): Promise<void> {
    this.activeTab = tab;
    this.currentPage = 1;
    this.searchTerm = '';

    switch (tab) {
      case 'players':
        await this.loadPlayers();
        break;
      case 'teams':
        await this.loadTeams();
        break;
      case 'games':
        await this.loadGames();
        break;
      case 'leagues':
        await this.loadLeagues();
        break;
      case 'news':
        await this.loadNews();
        break;
      case 'overview':
        await this.loadOverviewData();
        break;
    }
  }

  async loadPlayers(): Promise<void> {
    this.isLoading = true;
    try {
      this.players = await this.playerService.getAllPlayers();
    } catch (error) {
      console.error('Error loading players:', error);
      this.notificationService.error('Failed to load players');
    } finally {
      this.isLoading = false;
    }
  }

  async loadTeams(): Promise<void> {
    this.isLoading = true;
    try {
      this.teams = await this.teamService.getAllTeams();
    } catch (error) {
      console.error('Error loading teams:', error);
      this.notificationService.error('Failed to load teams');
    } finally {
      this.isLoading = false;
    }
  }

  async loadGames(): Promise<void> {
    this.isLoading = true;
    try {
      this.games = await this.gameService.getAllGames();
    } catch (error) {
      console.error('Error loading games:', error);
      this.notificationService.error('Failed to load games');
    } finally {
      this.isLoading = false;
    }
  }

  async loadLeagues(): Promise<void> {
    this.isLoading = true;
    try {
      this.leagues = await this.leagueService.getAllLeagues();
    } catch (error) {
      console.error('Error loading leagues:', error);
      this.notificationService.error('Failed to load leagues');
    } finally {
      this.isLoading = false;
    }
  }

  async loadNews(): Promise<void> {
    this.isLoading = true;
    try {
      this.news = await this.newsService.getAllNews();
    } catch (error) {
      console.error('Error loading news:', error);
      this.notificationService.error('Failed to load news');
    } finally {
      this.isLoading = false;
    }
  }

  // Search and filter methods
  get filteredPlayers(): PlayerDTO[] {
    if (!this.searchTerm) return this.players;
    return this.players.filter(player => 
      player.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      player.email?.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  get filteredTeams(): TeamDTO[] {
    if (!this.searchTerm) return this.teams;
    return this.teams.filter(team => 
      team.name.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  get filteredGames(): GameDTO[] {
    if (!this.searchTerm) return this.games;
    return this.games.filter(game => 
      game.homeTeam.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      game.awayTeam.name.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  get filteredLeagues(): LeagueDTO[] {
    if (!this.searchTerm) return this.leagues;
    return this.leagues.filter(league => 
      league.name.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  get filteredNews(): News[] {
    if (!this.searchTerm) return this.news;
    return this.news.filter(newsItem => 
      newsItem.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      newsItem.content.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  // Pagination methods
  get paginatedItems(): any[] {
    let items: any[] = [];
    
    switch (this.activeTab) {
      case 'players':
        items = this.filteredPlayers;
        break;
      case 'teams':
        items = this.filteredTeams;
        break;
      case 'games':
        items = this.filteredGames;
        break;
      case 'leagues':
        items = this.filteredLeagues;
        break;
      case 'news':
        items = this.filteredNews;
        break;
    }

    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return items.slice(startIndex, endIndex);
  }

  get totalPages(): number {
    let totalItems = 0;
    
    switch (this.activeTab) {
      case 'players':
        totalItems = this.filteredPlayers.length;
        break;
      case 'teams':
        totalItems = this.filteredTeams.length;
        break;
      case 'games':
        totalItems = this.filteredGames.length;
        break;
      case 'leagues':
        totalItems = this.filteredLeagues.length;
        break;
      case 'news':
        totalItems = this.filteredNews.length;
        break;
    }

    return Math.ceil(totalItems / this.itemsPerPage);
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }
}
