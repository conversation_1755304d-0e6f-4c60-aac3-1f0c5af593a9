import { GameDTO } from '@pro-clubs-manager/shared-dtos';

// Extended GameDTO with playoff properties and penalty support (until shared DTOs are updated)
export interface ExtendedGameDTO extends GameDTO {
  isPlayoff?: boolean;
  playoffStage?: string;
  broadcast?: {
    streamUrl: string;
    broadcastingTeam: string;
  };
  result?: {
    homeTeamGoals: number;
    awayTeamGoals: number;
    penalties?: {
      homeTeamPenalties: number;
      awayTeamPenalties: number;
    };
  };
}

// Helper functions for safely accessing extended properties
export function isPlayoffGame(game: GameDTO): boolean {
  return (game as ExtendedGameDTO).isPlayoff || false;
}

export function getGamePlayoffStage(game: GameDTO): string | undefined {
  return (game as ExtendedGameDTO).playoffStage;
}

export function getGameBroadcast(game: GameDTO): { streamUrl: string; broadcastingTeam: string } | undefined {
  return (game as ExtendedGameDTO).broadcast;
}

export function hasLiveBroadcast(game: GameDTO): boolean {
  const broadcast = getGameBroadcast(game);
  return !!broadcast && !!broadcast.streamUrl;
}

export function getGamePenalties(game: GameDTO): { homeTeamPenalties: number; awayTeamPenalties: number } | undefined {
  const extendedGame = game as ExtendedGameDTO;
  return extendedGame.result?.penalties;
}
