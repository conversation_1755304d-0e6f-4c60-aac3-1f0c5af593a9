import { Component, Input, OnInit } from '@angular/core';
import { FormationTemplateService } from '../../../services/formation-template.service';

@Component({
  selector: 'app-game-formation-display',
  templateUrl: './game-formation-display.component.html',
  styleUrl: './game-formation-display.component.scss'
})
export class GameFormationDisplayComponent implements OnInit {
  @Input() homeTeam: any;
  @Input() awayTeam: any;
  @Input() gameData: any;

  homeFormation: string = '4-3-3';
  awayFormation: string = '4-3-3';
  homePlayersWithPositions: any[] = [];
  awayPlayersWithPositions: any[] = [];

  constructor(private formationTemplateService: FormationTemplateService) { }

  ngOnInit(): void {
    this.setupFormations();
  }

  private setupFormations(): void {
    if (!this.gameData) return;

    // Detect formations
    this.homeFormation = this.formationTemplateService.detectFormation(this.homeTeam);
    this.awayFormation = this.formationTemplateService.detectFormation(this.awayTeam);

    // Setup player positions
    this.setupPlayerPositions();
  }

  private setupPlayerPositions(): void {
    // Home team players
    if (this.homeTeam && this.homeTeam.players) {
      this.homePlayersWithPositions = this.homeTeam.players.map((player: any) => {
        const position = this.formationTemplateService.getPlayerPosition(
          player.position, 
          this.homeFormation, 
          true
        );
        
        return {
          ...player,
          x: position.x,
          y: position.y,
          isHome: true
        };
      });

      // Adjust positions to avoid overlapping
      this.homePlayersWithPositions = this.formationTemplateService.adjustPlayerPositions(
        this.homePlayersWithPositions
      );
    }

    // Away team players
    if (this.awayTeam && this.awayTeam.players) {
      this.awayPlayersWithPositions = this.awayTeam.players.map((player: any) => {
        const position = this.formationTemplateService.getPlayerPosition(
          player.position, 
          this.awayFormation, 
          false
        );
        
        return {
          ...player,
          x: 100 - position.x, // Mirror for away team
          y: position.y,
          isHome: false
        };
      });

      // Adjust positions to avoid overlapping
      this.awayPlayersWithPositions = this.formationTemplateService.adjustPlayerPositions(
        this.awayPlayersWithPositions
      );
    }
  }

  getPlayerRatingColor(rating: number): string {
    if (rating >= 8.5) return '#10b981'; // Green
    if (rating >= 7.5) return '#3b82f6'; // Blue
    if (rating >= 6.5) return '#f59e0b'; // Orange
    if (rating >= 5.5) return '#ef4444'; // Red
    return '#6b7280'; // Gray
  }

  getPlayerRatingClass(rating: number): string {
    if (rating >= 8.5) return 'rating-excellent';
    if (rating >= 7.5) return 'rating-good';
    if (rating >= 6.5) return 'rating-average';
    if (rating >= 5.5) return 'rating-poor';
    return 'rating-very-poor';
  }

  hasPlayerStats(player: any): boolean {
    return player.goals > 0 || player.assists > 0 || player.rating > 0;
  }

  getPlayerStatsText(player: any): string {
    const stats = [];
    if (player.goals > 0) stats.push(`${player.goals}G`);
    if (player.assists > 0) stats.push(`${player.assists}A`);
    if (player.rating > 0) stats.push(`${player.rating.toFixed(1)}`);
    return stats.join(' • ');
  }

  isPlayerOfTheMatch(player: any): boolean {
    return player.potm === true;
  }
}
