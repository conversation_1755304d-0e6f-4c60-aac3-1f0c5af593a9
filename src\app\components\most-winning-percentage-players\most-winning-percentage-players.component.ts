import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { LeagueService } from '../../services/league.service';
import { NotificationService } from '../../services/notification.service';
import { MostWinningPercentagePlayer } from '../../shared/models/all-time-statistics.model';

@Component({
  selector: 'app-most-winning-percentage-players',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './most-winning-percentage-players.component.html',
  styleUrls: ['./most-winning-percentage-players.component.scss']
})
export class MostWinningPercentagePlayersComponent implements OnInit {
  mostWinningPercentagePlayersData: MostWinningPercentagePlayer[] = [];
  isLoading: boolean = false;
  selectedLeagueId: string = '';
  leagues: any[] = [];
  minimumGames: number = 10;

  @Input() hideTitle: boolean = false;
  @Input() leagueId?: string;

  constructor(
    private router: Router,
    private leagueService: LeagueService,
    private notificationService: NotificationService
  ) { }

  async ngOnInit(): Promise<void> {
    await this.loadLeagues();
    if (this.leagueId) {
      this.selectedLeagueId = this.leagueId;
      await this.loadData();
    }
  }

  private async loadLeagues(): Promise<void> {
    try {
      this.leagues = await this.leagueService.getAllLeagues();
      if (this.leagues.length > 0 && !this.selectedLeagueId) {
        this.selectedLeagueId = this.leagues[0].id;
        await this.loadData();
      }
    } catch (error) {
      console.error('Error loading leagues:', error);
      this.notificationService.error('Failed to load leagues');
    }
  }

  async onLeagueChange(): Promise<void> {
    if (this.selectedLeagueId) {
      await this.loadData();
    }
  }

  async onMinimumGamesChange(): Promise<void> {
    await this.loadData();
  }

  private async loadData(): Promise<void> {
    if (!this.selectedLeagueId) return;

    this.isLoading = true;
    try {
      const response = await this.leagueService.getMostWinningPercentagePlayers(
        this.selectedLeagueId, 
        this.minimumGames
      );

      this.mostWinningPercentagePlayersData = response.map(player => ({
        ...player,
        tableIcon: { 
          name: player.playerName, 
          imgUrl: player.playerImgUrl || '', 
          isTeam: false 
        },
        winningPercentage: parseFloat(player.winningPercentage.toFixed(1)),
        avgRating: parseFloat(player.avgRating.toFixed(2))
      }));
    } catch (error) {
      console.error('Error loading most winning percentage players data:', error);
      this.notificationService.error('Failed to load player winning percentage statistics');
      this.mostWinningPercentagePlayersData = [];
    } finally {
      this.isLoading = false;
    }
  }

  onPlayerClick(player: MostWinningPercentagePlayer): void {
    this.router.navigate(['/player', player.playerId]);
  }

  onTeamClick(teamId: string): void {
    this.router.navigate(['/team-details', teamId]);
  }

  getPlayerRank(index: number): string {
    return `#${index + 1}`;
  }

  getWinPercentageDisplay(player: MostWinningPercentagePlayer): string {
    return `${player.winningPercentage}%`;
  }

  getWinLossRecord(player: MostWinningPercentagePlayer): string {
    return `${player.wins}-${player.draws}-${player.losses}`;
  }

  getStarIconsArray(wins: number): number[] {
    return Array(Math.min(wins, 5)).fill(0);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/User.jpg';
    }
  }
}
