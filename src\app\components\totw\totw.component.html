<div class="totw-container">
    <!-- Header Section -->
    <div class="totw-header">
        <div class="header-content">
            <div class="title-section">
                <div class="main-title">
                    <i class="fas fa-trophy"></i>
                    <h1>Team of the Week</h1>
                    <span class="week-badge">{{this.currentTOTW}}</span>
                </div>
                <div class="date-range" *ngIf="this.totwStartDate && this.totwEndDate">
                    <i class="fas fa-calendar-alt"></i>
                    <span>{{ this.totwStartDate | date: dateFormat}} - {{this.totwEndDate | date: dateFormat}}</span>
                </div>
            </div>

            <div class="controls-section" *ngIf="totwOptions.length > 0">
                <div class="week-selector">
                    <label class="selector-label">
                        <i class="fas fa-list"></i>
                        Select Week
                    </label>
                    <pro-clubs-auto-complete-select [defaultOption]="defaultOption?.value"
                        (selectionChange)="onSelectionChange($event)" [width]="'180px'" [selectOptions]="totwOptions">
                    </pro-clubs-auto-complete-select>
                </div>

                <div class="navigation-buttons">
                    <button class="nav-button prev-button" (click)="onPrevClick()" [disabled]="currentTOTW == 1"
                        [class.disabled]="currentTOTW == 1">
                        <i class="fas fa-chevron-left"></i>
                        <span>Previous</span>
                    </button>

                    <button class="nav-button next-button" (click)="onNextClick()"
                        [disabled]="currentTOTW == maxTotwAmount" [class.disabled]="currentTOTW == maxTotwAmount">
                        <span>Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- Main Content -->
    <div class="totw-content" *ngIf="!isLoading && fixtures && totw; else spinner">
        <div class="pitch-section" *ngIf="totw && totw.length > 0; else noTotw">
            <!-- TOTW Mini Pitch Formation View -->
            <div class="pitch-formation-view">
                <app-mini-pitch-formation class="totw-pitch-size" [homeTeam]="getTotwTeamFormation()"
                    [awayTeam]="getTotwTeamFormation()" [showPlayerStats]="true" [showPlayerNames]="true"
                    [totwMode]="true" [compactMode]="false">
                </app-mini-pitch-formation>
            </div>
        </div>
    </div>
</div>

<ng-template #noTotw>
    <div class="no-totw-message">
        <div class="no-totw-content">
            <i class="fas fa-calendar-times"></i>
            <h3>No Team of the Week</h3>
            <p>No TOTW data available for this week. Please select a different week.</p>
        </div>
    </div>
</ng-template>

<ng-template #spinner>
    <pro-clubs-spinner></pro-clubs-spinner>
</ng-template>