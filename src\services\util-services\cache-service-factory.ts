import { injectable } from "tsyringe";
import logger from "../../config/logger";
import { CacheService } from "../../interfaces/util-services/cache-service.interface";
import { RedisCacheService } from "./redis-cache-service";
import { MemoryCacheService } from "./memory-cache-service";

@injectable()
export class CacheServiceFactory implements CacheService {
  private cacheService: CacheService | null = null;
  private initializationPromise: Promise<void>;

  constructor() {
    this.initializationPromise = this.initializeCacheService();
  }

  private async initializeCacheService() {
    try {
      // Try to initialize Redis cache service
      const redisService = new RedisCacheService();

      // Test Redis connection by trying to set a test key
      await redisService.set("test:connection", "test", 1);
      await redisService.delete("test:connection");

      this.cacheService = redisService;
      logger.info("CacheServiceFactory: Using Redis cache service");
    } catch (error) {
      logger.warn("CacheServiceFactory: Redis cache failed, falling back to memory cache:", error);
      this.cacheService = new MemoryCacheService();
      logger.info("CacheServiceFactory: Using memory cache service");
    }
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.cacheService) {
      await this.initializationPromise;
    }
    if (!this.cacheService) {
      // Final fallback
      this.cacheService = new MemoryCacheService();
    }
  }

  async set(key: string, value: any, expiresIn?: number): Promise<void> {
    await this.ensureInitialized();
    if (this.cacheService) {
      return this.cacheService.set(key, value, expiresIn);
    }
  }

  async get(key: string): Promise<any | null> {
    await this.ensureInitialized();
    if (this.cacheService) {
      return this.cacheService.get(key);
    }
    return null;
  }

  async delete(key: string): Promise<void> {
    await this.ensureInitialized();
    if (this.cacheService) {
      return this.cacheService.delete(key);
    }
  }

  async quit(): Promise<void> {
    await this.ensureInitialized();
    if (this.cacheService && 'quit' in this.cacheService) {
      return (this.cacheService as any).quit();
    }
  }
}
