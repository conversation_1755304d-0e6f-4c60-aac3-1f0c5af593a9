<!-- Combined Fixture Navigation -->
<div class="fixture-navigation-container">
    <!-- Left side: Fixture Selection -->
    <div class="fixture-selector">
        <label>Round</label>
        <pro-clubs-auto-complete-select
            [selectOptions]="fixturesOptions"
            [width]="'140px'"
            [defaultOption]="currentFixtureDisplayText"
            (selectionChange)="onSelectionChange($event)">
        </pro-clubs-auto-complete-select>
    </div>

    <!-- Right side: Navigation Controls -->
    <div class="navigation-controls" *ngIf="showNavigation">
        <!-- Quick Navigation -->
        <div class="quick-nav">
            <button class="nav-btn"
                    (click)="goToFirstFixture()"
                    [disabled]="!canGoToFirst()"
                    title="First Fixture">
                <i class="fas fa-angle-double-left"></i>
            </button>

            <button class="nav-btn"
                    (click)="goToPreviousFixture()"
                    [disabled]="!canGoToPrevious()"
                    title="Previous Fixture">
                <i class="fas fa-angle-left"></i>
            </button>

            <span class="current-fixture">
                {{ currentFixtureNumber }} / {{ totalFixtures }}
            </span>

            <button class="nav-btn"
                    (click)="goToNextFixture()"
                    [disabled]="!canGoToNext()"
                    title="Next Fixture">
                <i class="fas fa-angle-right"></i>
            </button>

            <button class="nav-btn"
                    (click)="goToLastFixture()"
                    [disabled]="!canGoToLast()"
                    title="Last Fixture">
                <i class="fas fa-angle-double-right"></i>
            </button>
        </div>

        <!-- Pagination Info -->
        <div class="pagination-info" *ngIf="showPagination">
            <span>Fixture {{ currentFixtureNumber }} of {{ totalFixtures }}</span>
        </div>
    </div>
</div>
