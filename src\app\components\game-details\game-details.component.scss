/* === MODERN GAME DETAILS COMPONENT === */
.game-details-container {
    padding: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    min-height: 100vh;
    background: linear-gradient(135deg,
        var(--bg-primary) 0%,
        var(--surface-secondary) 50%,
        var(--bg-primary) 100%);
    position: relative;

    /* Animated background pattern */
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(var(--primary-rgb), 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(var(--accent-primary-rgb), 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(var(--info-rgb), 0.05) 0%, transparent 50%);
        pointer-events: none;
        z-index: 0;
        animation: backgroundShift 20s ease-in-out infinite;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-md);
        gap: var(--spacing-lg);
    }

    /* Ensure proper spacing and z-index for all major sections */
    > * {
        margin-bottom: 0;
        position: relative;
        z-index: 1;
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
        transform: translateY(20px);
    }

    /* Staggered animation delays */
    > *:nth-child(1) { animation-delay: 0.1s; }
    > *:nth-child(2) { animation-delay: 0.2s; }
    > *:nth-child(3) { animation-delay: 0.3s; }
    > *:nth-child(4) { animation-delay: 0.4s; }
    > *:nth-child(5) { animation-delay: 0.5s; }
    > *:nth-child(6) { animation-delay: 0.6s; }
}

/* === TECHNICAL RESULT === */
.technical-result {
    .alert {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-md);
        padding: var(--spacing-xl);
        background: linear-gradient(135deg,
            rgba(var(--warning-rgb), 0.15) 0%,
            rgba(var(--warning-rgb), 0.05) 100%);
        border: 2px solid transparent;
        border-radius: var(--radius-xl);
        color: var(--text-primary);
        box-shadow:
            0 8px 32px rgba(var(--warning-rgb), 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(var(--warning-rgb), 0.1),
                transparent);
            transition: left 0.5s ease;
        }

        &:hover {
            transform: translateY(-2px);
            box-shadow:
                0 12px 40px rgba(var(--warning-rgb), 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);

            &::before {
                left: 100%;
            }
        }

        &.alert-warning {
            border-image: linear-gradient(135deg, var(--warning), rgba(var(--warning-rgb), 0.3)) 1;
        }

        i {
            color: var(--warning);
            font-size: 1.5rem;
            margin-top: 2px;
            filter: drop-shadow(0 2px 4px rgba(var(--warning-rgb), 0.3));
            animation: pulse 2s ease-in-out infinite;
        }

        h4 {
            margin: 0 0 var(--spacing-xs) 0;
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            background: linear-gradient(135deg, var(--text-primary), var(--warning));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        p {
            margin: 0;
            color: var(--text-secondary);
            font-size: var(--text-base);
            line-height: 1.6;
        }
    }
}

/* === SCHEDULED MESSAGE === */
.scheduled-message {
    .alert {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-md);
        padding: var(--spacing-xl);
        background: linear-gradient(135deg,
            rgba(var(--info-rgb), 0.15) 0%,
            rgba(var(--info-rgb), 0.05) 100%);
        border: 2px solid transparent;
        border-radius: var(--radius-xl);
        color: var(--text-primary);
        box-shadow:
            0 8px 32px rgba(var(--info-rgb), 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(var(--info-rgb), 0.1),
                transparent);
            transition: left 0.5s ease;
        }

        &:hover {
            transform: translateY(-2px);
            box-shadow:
                0 12px 40px rgba(var(--info-rgb), 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);

            &::before {
                left: 100%;
            }
        }

        &.alert-info {
            border-image: linear-gradient(135deg, var(--info), rgba(var(--info-rgb), 0.3)) 1;
        }

        i {
            color: var(--info);
            font-size: 1.5rem;
            margin-top: 2px;
            filter: drop-shadow(0 2px 4px rgba(var(--info-rgb), 0.3));
            animation: clockTick 1s ease-in-out infinite;
        }

        h4 {
            margin: 0 0 var(--spacing-xs) 0;
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            background: linear-gradient(135deg, var(--text-primary), var(--info));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        p {
            margin: 0;
            color: var(--text-secondary);
            font-size: var(--text-base);
            line-height: 1.6;
        }
    }
}

/* === BROADCAST INFO === */
.broadcast-info {
    .alert {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-lg);
        padding: var(--spacing-xl);
        background: linear-gradient(135deg,
            rgba(var(--primary-rgb), 0.15) 0%,
            rgba(var(--accent-primary-rgb), 0.1) 50%,
            rgba(var(--primary-rgb), 0.05) 100%);
        border: 2px solid transparent;
        border-radius: var(--radius-xl);
        color: var(--text-primary);
        box-shadow:
            0 8px 32px rgba(var(--primary-rgb), 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(var(--primary-rgb), 0.1),
                transparent);
            transition: left 0.5s ease;
        }

        &:hover {
            transform: translateY(-2px);
            box-shadow:
                0 12px 40px rgba(var(--primary-rgb), 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);

            &::before {
                left: 100%;
            }
        }

        &.alert-info {
            border-image: linear-gradient(135deg, var(--primary), var(--accent-primary)) 1;
        }

        i {
            color: var(--primary);
            font-size: 1.5rem;
            margin-top: 2px;
            filter: drop-shadow(0 2px 4px rgba(var(--primary-rgb), 0.3));
            animation: videoGlow 2s ease-in-out infinite;
        }

        h4 {
            margin: 0 0 var(--spacing-xs) 0;
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            background: linear-gradient(135deg, var(--primary), var(--accent-primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        p {
            margin: 0;
            color: var(--text-secondary);
            font-size: var(--text-base);
            line-height: 1.6;
        }
    }

    .broadcast-actions {
        margin-top: var(--spacing-lg);
        display: flex;
        justify-content: flex-end;

        .broadcast-btn {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md) var(--spacing-xl);
            background: linear-gradient(135deg, var(--primary), var(--accent-primary));
            color: var(--text-on-primary);
            border: none;
            border-radius: var(--radius-xl);
            font-weight: var(--font-weight-bold);
            font-size: var(--text-base);
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow:
                0 4px 16px rgba(var(--primary-rgb), 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg,
                    transparent,
                    rgba(255, 255, 255, 0.2),
                    transparent);
                transition: left 0.5s ease;
            }

            &:hover:not(:disabled) {
                transform: translateY(-3px);
                box-shadow:
                    0 8px 24px rgba(var(--primary-rgb), 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);

                &::before {
                    left: 100%;
                }
            }

            &:disabled {
                background: linear-gradient(135deg, var(--surface-disabled), var(--surface-tertiary));
                color: var(--text-disabled);
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }

            &.live {
                background: linear-gradient(135deg, var(--success), var(--success-600));
                animation: livePulse 2s ease-in-out infinite;
                box-shadow:
                    0 4px 16px rgba(var(--success-rgb), 0.4),
                    0 0 20px rgba(var(--success-rgb), 0.3);
            }

            i {
                font-size: 1rem;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
            }
        }
    }
}

/* === MATCH DETAILS === */
.match-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);



    /* === FORMATION PITCH === */
    .formation-pitch {
        background: var(--surface-primary);
        border-radius: var(--radius-xl);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-primary);
        margin-bottom: var(--spacing-xl);

        @media (max-width: 768px) {
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .empty-formation-state {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 300px;
            padding: var(--spacing-xl);

            .empty-state-content {
                text-align: center;
                color: var(--text-secondary);

                .empty-state-icon {
                    font-size: 3rem;
                    color: var(--text-muted);
                    margin-bottom: var(--spacing-lg);
                    opacity: 0.6;
                }

                h4 {
                    color: var(--text-primary);
                    margin-bottom: var(--spacing-md);
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                p {
                    color: var(--text-secondary);
                    font-size: 0.95rem;
                    line-height: 1.5;
                    max-width: 300px;
                    margin: 0 auto;
                }
            }

            @media (max-width: 768px) {
                min-height: 250px;
                padding: var(--spacing-lg);

                .empty-state-content {
                    .empty-state-icon {
                        font-size: 2.5rem;
                    }

                    h4 {
                        font-size: 1.1rem;
                    }

                    p {
                        font-size: 0.9rem;
                    }
                }
            }
        }
    }

    /* === PLAYER OF THE MATCH === */
    .potm {
        .potm-card {
            background: var(--surface-primary);
            border: 2px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);

            &:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-md);
                border-color: var(--primary);
            }

            .potm-header {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                margin-bottom: var(--spacing-md);

                i {
                    color: var(--warning);
                    font-size: var(--text-lg);
                }

                h4 {
                    margin: 0;
                    font-size: var(--text-lg);
                    font-weight: var(--font-weight-semibold);
                    color: var(--text-primary);
                }
            }

            .potm-content {
                display: flex;
                align-items: center;
                gap: var(--spacing-md);

                img {
                    width: 60px;
                    height: 60px;
                    border-radius: var(--radius-full);
                    object-fit: cover;
                    border: 3px solid var(--border-primary);
                }

                .potm-info {
                    flex: 1;

                    h5 {
                        margin: 0 0 var(--spacing-xs) 0;
                        font-size: var(--text-base);
                        font-weight: var(--font-weight-semibold);
                        color: var(--text-primary);
                    }

                    .potm-details {
                        display: flex;
                        align-items: center;
                        gap: var(--spacing-md);

                        .potm-team {
                            color: var(--text-secondary);
                            font-size: var(--text-sm);
                        }

                        .potm-rating {
                            display: flex;
                            align-items: center;
                            gap: var(--spacing-xs);
                            background: var(--warning);
                            color: var(--text-on-primary);
                            padding: var(--spacing-xs) var(--spacing-sm);
                            border-radius: var(--radius-md);
                            font-size: var(--text-sm);
                            font-weight: var(--font-weight-semibold);

                            i {
                                font-size: 0.75rem;
                            }
                        }
                    }
                }

                @media (max-width: 768px) {
                    flex-direction: column;
                    text-align: center;

                    .potm-info .potm-details {
                        justify-content: center;
                    }
                }
            }
        }
    }
}


/* === STATS ACTIONS === */
.stats-actions {
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);

    .stats-buttons {
        display: flex;
        gap: var(--spacing-md);
        justify-content: center;
        flex-wrap: wrap;

        @media (max-width: 768px) {
            flex-direction: column;
            align-items: stretch;
        }

        .btn-secondary {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--surface-secondary);
            color: var(--text-primary);
            border: 2px solid var(--border-primary);
            border-radius: var(--radius-md);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            white-space: nowrap;
            min-height: 44px;

            &:hover {
                border-color: var(--primary-500);
                background: var(--primary-50);
                color: var(--primary-700);
                transform: translateY(-2px);
                box-shadow: var(--shadow-md);
            }

            &:active {
                transform: translateY(0);
                box-shadow: var(--shadow-sm);
            }

            i {
                font-size: var(--text-sm);
                color: var(--primary-500);
                transition: color 0.2s ease;
            }

            &:hover i {
                color: var(--primary-600);
            }

            /* Enhanced styling for different team buttons */
            &:nth-child(1) {
                border-color: var(--info-300);

                &:hover {
                    border-color: var(--info-500);
                    background: var(--info-50);
                    color: var(--info-700);
                }

                i {
                    color: var(--info-500);
                }

                &:hover i {
                    color: var(--info-600);
                }
            }

            &:nth-child(2) {
                border-color: var(--success-300);

                &:hover {
                    border-color: var(--success-500);
                    background: var(--success-50);
                    color: var(--success-700);
                }

                i {
                    color: var(--success-500);
                }

                &:hover i {
                    color: var(--success-600);
                }
            }
        }
    }
}

/* === ADMIN ACTIONS === */
.admin-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    padding-top: var(--spacing-lg);
    border-top: 2px solid var(--border-primary);

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-secondary {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        background: var(--surface-secondary);
        color: var(--text-primary);
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-md);
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            border-color: var(--primary);
            background: var(--surface-tertiary);
            transform: translateY(-1px);
        }

        i {
            font-size: 0.875rem;
            color: var(--primary);
        }
    }
}

/* === ANIMATIONS === */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes backgroundShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-3px);
    }
}

@keyframes vsGlow {
    0%, 100% {
        filter: drop-shadow(0 0 5px rgba(var(--primary-rgb), 0.3));
    }
    50% {
        filter: drop-shadow(0 0 10px rgba(var(--primary-rgb), 0.6));
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes livePulse {
    0%, 100% {
        box-shadow:
            0 4px 16px rgba(var(--success-rgb), 0.4),
            0 0 20px rgba(var(--success-rgb), 0.3);
    }
    50% {
        box-shadow:
            0 4px 16px rgba(var(--success-rgb), 0.6),
            0 0 30px rgba(var(--success-rgb), 0.5);
    }
}

@keyframes videoGlow {
    0%, 100% {
        filter: drop-shadow(0 2px 4px rgba(var(--primary-rgb), 0.3));
    }
    50% {
        filter: drop-shadow(0 2px 8px rgba(var(--primary-rgb), 0.6));
    }
}

@keyframes clockTick {
    0%, 100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(5deg);
    }
    75% {
        transform: rotate(-5deg);
    }
}

/* === BROADCAST IFRAME === */
.broadcast-iframe-container {
    margin-top: var(--spacing-xl);
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-primary);
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: slideInFromBottom 0.6s ease-out;

    .iframe-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-lg) var(--spacing-xl);
        background: linear-gradient(135deg,
            rgba(var(--primary-rgb), 0.1),
            rgba(var(--accent-primary-rgb), 0.05));
        border-bottom: 1px solid var(--border-secondary);

        h5 {
            margin: 0;
            color: var(--text-primary);
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            i {
                color: var(--primary);
                animation: livePulse 2s ease-in-out infinite;
            }
        }

        .iframe-fullscreen-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                background: var(--surface-tertiary);
                color: var(--primary);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.2);
            }

            i {
                font-size: var(--text-sm);
            }
        }
    }

    .iframe-wrapper {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        background: var(--surface-secondary);

        iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            background: var(--surface-secondary);
        }
    }

    @media (max-width: 768px) {
        margin-top: var(--spacing-lg);

        .iframe-header {
            padding: var(--spacing-md) var(--spacing-lg);

            h5 {
                font-size: var(--text-base);
            }

            .iframe-fullscreen-btn {
                width: 36px;
                height: 36px;
            }
        }

        .iframe-wrapper {
            padding-bottom: 56.25%; /* Maintain 16:9 on mobile */
        }
    }

    @media (max-width: 480px) {
        .iframe-header {
            padding: var(--spacing-sm) var(--spacing-md);

            h5 {
                font-size: var(--text-sm);
                gap: var(--spacing-xs);
            }

            .iframe-fullscreen-btn {
                width: 32px;
                height: 32px;

                i {
                    font-size: 0.75rem;
                }
            }
        }
    }
}

/* === PREDICTIONS SECTION === */
.predictions-section {
    margin: var(--spacing-xl) 0;
    animation: fadeInUp 0.6s ease-out forwards;
    animation-delay: 0.7s;
    opacity: 0;
    transform: translateY(20px);
}

/* === COMMENTS SECTION === */
.comments-section {
    margin: var(--spacing-xl) 0;
    animation: fadeInUp 0.6s ease-out forwards;
    animation-delay: 0.8s;
    opacity: 0;
    transform: translateY(20px);
}