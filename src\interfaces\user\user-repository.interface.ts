import { IUser } from "../../models/user";

export interface IUserRepository {
  create(userData: Partial<IUser>): Promise<IUser>;
  findById(userId: string): Promise<IUser | null>;
  findByEmail(email: string): Promise<IUser | null>;
  findByGoogleId(googleId: string): Promise<IUser | null>;
  update(userId: string, updateData: Partial<IUser>): Promise<IUser | null>;
  delete(userId: string): Promise<boolean>;
  findAll(): Promise<IUser[]>;
}
