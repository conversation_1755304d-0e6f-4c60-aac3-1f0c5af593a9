import mongoose, { Schema, Document } from "mongoose";
import { AddGameData, PlayoffDetails } from "./game/game";

export interface AddFixtureData {
  leagueId: mongoose.Types.ObjectId;
  seasonNumber: number;
  gamesData: AddGameData[];
  round: number;
  startDate: Date;
  endDate: Date;
  isPlayoff?: boolean;
  playoffDetails?: PlayoffDetails;
}

export interface IFixture extends Document {
  id: string;
  league: mongoose.Types.ObjectId;
  seasonNumber: number;
  round: number;
  startDate: Date;
  endDate: Date;
  games: mongoose.Types.ObjectId[];
  isPlayoff?: boolean;
  playoffDetails?: PlayoffDetails;
}

const fixtureSchema = new Schema<IFixture>(
  {
    league: { type: mongoose.Schema.Types.ObjectId, ref: "League", required: true },
    round: { type: Number, required: true },
    seasonNumber: { type: Number, required: true },
    startDate: { type: mongoose.Schema.Types.Date, required: true },
    endDate: { type: mongoose.Schema.Types.Date, required: true },
    games: [{ type: mongoose.Schema.Types.ObjectId, ref: "Game", required: true }],
    isPlayoff: { type: Boolean, required: false, default: false },
    playoffDetails: {
      type: {
        stage: { type: String, required: true },
        format: { type: String, required: true }
      },
      required: false
    },
  },
  {
    toJSON: { virtuals: true },
    id: true, // Use 'id' instead of '_id'
  }
);

const Fixture = mongoose.model<IFixture>("Fixture", fixtureSchema);

export default Fixture;
