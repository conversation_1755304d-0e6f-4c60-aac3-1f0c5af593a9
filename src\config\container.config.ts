import { container } from "tsyringe";
import {
  CloudinaryImageService,
  FixtureService,
  GameService,
  LeagueService,
  PlayerService,
  PlayerTeamService,
  TeamLeagueService,
  TeamService,
  RedisCacheService,
} from "../services";
import { MemoryCacheService } from "../services/util-services/memory-cache-service";
import { CacheServiceFactory } from "../services/util-services/cache-service-factory";
import { IPlayerService, IPlayerRepository, IPlayerController } from "../interfaces/player";
import { IFixtureService, IFixtureController, IFixtureRepository } from "../interfaces/fixture";
import { FixtureController, GameController, LeagueController, PlayerController, TeamController } from "../controllers";
import { FixtureRepository, GameRepository, LeagueRepository, PlayerRepository, TeamRepository } from "../repositories";
import { IGameController, IGameService, IGameRepository } from "../interfaces/game";
import { ILeagueController, ILeagueService, ILeagueRepository } from "../interfaces/league";
import { ITeamController, ITeamRepository, ITeamService } from "../interfaces/team";
import { ImageService } from "../interfaces/util-services/image-service.interface";
import { CacheService } from "../interfaces/util-services/cache-service.interface";
import { ILeagueStatsService, IPlayerStatsService, IPlayerTeamService, ITeamLeagueService, ITeamStatsService, ISeasonCompletionService, ITeamOfTheSeasonService } from "../interfaces/wrapper-services";
import { ISeasonAchievementService } from "../interfaces/wrapper-services/season-achievement-service.interface";
import { TeamStatsService, LeagueStatsService, PlayerStatsService } from "../services/wrapper-services";
import { SeasonAchievementService } from "../services/wrapper-services/season-achievement-service";
import { ITeamOfTheWeekService } from "../interfaces/wrapper-services/team-of-the-week-service.interface";
import { TeamOfTheWeekService } from "../services/wrapper-services/team-of-the-week-service";
import { INewsRepository } from "../interfaces/news/news-repository.interface";
import { NewsRepository } from "../repositories/news-repository";
import { INewsService } from "../interfaces/news/news-service.interface";
import { NewsService } from "../services/news-service";
import { INewsController } from "../interfaces/news/news-controller.interface";
import NewsController from "../controllers/news-controller";
import { IDashboardService, DashboardService } from "../services/dashboard-service";
import { IDashboardController } from "../controllers/dashboard-controller";
import DashboardController from "../controllers/dashboard-controller";
import { SeasonCompletionService } from "../services/wrapper-services/season-completion-service";
import { TeamOfTheSeasonService } from "../services/wrapper-services/team-of-the-season-service";
// User system imports
import { IUserService, IUserRepository, IUserController } from "../interfaces/user";
import UserService from "../services/user-service";
import UserRepository from "../repositories/user-repository";
import UserController from "../controllers/user-controller";

// Player association request imports
import { PlayerAssociationRequestRepository, IPlayerAssociationRequestRepository } from "../repositories/player-association-request-repository";
import { PlayerAssociationRequestService, IPlayerAssociationRequestService } from "../services/player-association-request-service";
import { PlayerAssociationRequestController, IPlayerAssociationRequestController } from "../controllers/player-association-request-controller";

// AI service imports
import { AIService } from "../services/ai-service";
import { AIController } from "../controllers/ai-controller";

// Chat service imports
import { ChatService } from "../services/chat-service";
import ChatController from "../controllers/chat-controller";

// Transfer request imports
import TransferRequestRepository, { ITransferRequestRepository } from "../repositories/transfer-request-repository";
import { TransferRequestService, ITransferRequestService } from "../services/transfer-request-service";
import TransferRequestController from "../controllers/transfer-request-controller";

// Brackets imports
import { BracketsService, IBracketsService } from "../services/brackets-service";
import BracketsController from "../controllers/brackets-controller";

// Prediction imports
import { IPredictionRepository, PredictionRepository } from "../repositories/prediction-repository";
import { IPredictionService, PredictionService } from "../services/prediction-service";
import { PredictionController } from "../controllers/prediction-controller";

// Comment imports
import { ICommentRepository, CommentRepository } from "../repositories/comment-repository";
import { ICommentService, CommentService } from "../services/comment-service";
import { CommentController } from "../controllers/comment-controller";

// Cache warming imports
import { CacheWarmingService, ICacheWarmingService } from "../services/cache-warming-service";

// Register repositories
container.registerSingleton<IPlayerRepository>("IPlayerRepository", PlayerRepository);
container.registerSingleton<IGameRepository>("IGameRepository", GameRepository);
container.registerSingleton<ITeamRepository>("ITeamRepository", TeamRepository);
container.registerSingleton<IFixtureRepository>("IFixtureRepository", FixtureRepository);
container.registerSingleton<ILeagueRepository>("ILeagueRepository", LeagueRepository);
container.registerSingleton<INewsRepository>("INewsRepository", NewsRepository);
container.registerSingleton<IUserRepository>("IUserRepository", UserRepository);
container.registerSingleton<IPlayerAssociationRequestRepository>("IPlayerAssociationRequestRepository", PlayerAssociationRequestRepository);
container.registerSingleton<ITransferRequestRepository>("ITransferRequestRepository", TransferRequestRepository);
container.registerSingleton<IPredictionRepository>("IPredictionRepository", PredictionRepository);
container.registerSingleton<ICommentRepository>("ICommentRepository", CommentRepository);

// Register utility services first
container.registerSingleton<ImageService>("ImageService", CloudinaryImageService);
// Use simple Redis cache service (revert problematic factory)
container.registerSingleton<CacheService>("CacheService", RedisCacheService);

// Register wrapper services (dependencies for main services)
container.registerSingleton<IPlayerTeamService>("IPlayerTeamService", PlayerTeamService);
container.registerSingleton<ITeamLeagueService>("ITeamLeagueService", TeamLeagueService);
container.registerSingleton<ITeamStatsService>("ITeamStatsService", TeamStatsService);
container.registerSingleton<IPlayerStatsService>("IPlayerStatsService", PlayerStatsService);
container.registerSingleton<ILeagueStatsService>("ILeagueStatsService", LeagueStatsService);
container.registerSingleton<ITeamOfTheWeekService>("ITeamOfTheWeekService", TeamOfTheWeekService);
container.registerSingleton<ITeamOfTheSeasonService>("ITeamOfTheSeasonService", TeamOfTheSeasonService);
container.registerSingleton<ISeasonCompletionService>("ISeasonCompletionService", SeasonCompletionService);
container.registerSingleton<ISeasonAchievementService>("ISeasonAchievementService", SeasonAchievementService);

// Register main services
container.registerSingleton<IPlayerService>("IPlayerService", PlayerService);
container.registerSingleton<ITeamService>("ITeamService", TeamService);
container.registerSingleton<ILeagueService>("ILeagueService", LeagueService);
container.registerSingleton<IFixtureService>("IFixtureService", FixtureService);
container.registerSingleton<INewsService>("INewsService", NewsService);
container.registerSingleton<IDashboardService>("IDashboardService", DashboardService);
container.registerSingleton<IUserService>("IUserService", UserService);
container.registerSingleton<IPlayerAssociationRequestService>("IPlayerAssociationRequestService", PlayerAssociationRequestService);
container.registerSingleton<AIService>("AIService", AIService);
container.registerSingleton<ChatService>("ChatService", ChatService);
container.registerSingleton<ITransferRequestService>("ITransferRequestService", TransferRequestService);
container.registerSingleton<IBracketsService>("IBracketsService", BracketsService);
container.registerSingleton<IPredictionService>("IPredictionService", PredictionService);
container.registerSingleton<ICommentService>("ICommentService", CommentService);
container.registerSingleton<ICacheWarmingService>("ICacheWarmingService", CacheWarmingService);

// Register GameService last since it depends on SeasonCompletionService
container.registerSingleton<IGameService>("IGameService", GameService);

// Register controllers
container.registerSingleton<IPlayerController>("IPlayerController", PlayerController);
container.registerSingleton<IGameController>("IGameController", GameController);
container.registerSingleton<ITeamController>("ITeamController", TeamController);
container.registerSingleton<IFixtureController>("IFixtureController", FixtureController);
container.registerSingleton<ILeagueController>("ILeagueController", LeagueController);
container.registerSingleton<INewsController>("INewsController", NewsController);
container.registerSingleton<IDashboardController>("IDashboardController", DashboardController);
container.registerSingleton<IUserController>("IUserController", UserController);
container.registerSingleton<IPlayerAssociationRequestController>("IPlayerAssociationRequestController", PlayerAssociationRequestController);
container.registerSingleton<AIController>("AIController", AIController);
container.registerSingleton<ChatController>("ChatController", ChatController);
container.registerSingleton<TransferRequestController>("TransferRequestController", TransferRequestController);
container.registerSingleton<BracketsController>("BracketsController", BracketsController);
container.registerSingleton<PredictionController>("PredictionController", PredictionController);
container.registerSingleton<CommentController>("CommentController", CommentController);

export { container };
