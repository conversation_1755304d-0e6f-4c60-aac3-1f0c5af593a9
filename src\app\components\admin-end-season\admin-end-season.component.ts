import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SeasonAchievementService, SeasonEndPreview, SeasonEndRequest } from '../../services/season-achievement.service';
import { LeagueService } from '../../services/league.service';
import { NotificationService } from '../../services/notification.service';
import { ILeague } from '../../shared/models/league-table.model';

@Component({
  selector: 'app-admin-end-season',
  templateUrl: './admin-end-season.component.html',
  styleUrls: ['./admin-end-season.component.scss']
})
export class AdminEndSeasonComponent implements OnInit {
  leagues: ILeague[] = [];
  selectedLeagueId: string = '';
  selectedSeasonNumber: number = 0;
  
  preview: SeasonEndPreview | null = null;
  isLoadingPreview: boolean = false;
  isEndingSeason: boolean = false;
  
  showConfirmDialog: boolean = false;
  forceEnd: boolean = false;
  
  constructor(
    private seasonAchievementService: SeasonAchievementService,
    private leagueService: LeagueService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  async ngOnInit(): Promise<void> {
    await this.loadLeagues();
  }

  async loadLeagues(): Promise<void> {
    try {
      this.leagues = await this.leagueService.getAllLeagues();
      if (this.leagues.length > 0) {
        this.selectedLeagueId = this.leagues[0].id;
        await this.onLeagueChange();
      }
    } catch (error) {
      console.error('Error loading leagues:', error);
      this.notificationService.error('Failed to load leagues');
    }
  }

  async onLeagueChange(): Promise<void> {
    if (!this.selectedLeagueId) {
      this.preview = null;
      return;
    }

    const selectedLeague = this.leagues.find(league => league.id === this.selectedLeagueId);
    if (selectedLeague) {
      // Get current season number from league (you may need to add this to the league model)
      this.selectedSeasonNumber = 1; // Default to 1, should be fetched from league
      await this.loadPreview();
    }
  }

  async loadPreview(): Promise<void> {
    if (!this.selectedLeagueId || !this.selectedSeasonNumber) {
      return;
    }

    this.isLoadingPreview = true;
    try {
      this.preview = await this.seasonAchievementService.previewSeasonEnd(
        this.selectedLeagueId,
        this.selectedSeasonNumber
      );
    } catch (error) {
      console.error('Error loading season preview:', error);
      this.notificationService.error('Failed to load season preview');
      this.preview = null;
    } finally {
      this.isLoadingPreview = false;
    }
  }

  async refreshPreview(): Promise<void> {
    await this.loadPreview();
  }

  showEndSeasonDialog(): void {
    if (!this.preview) {
      this.notificationService.warning('Please load season preview first');
      return;
    }

    this.showConfirmDialog = true;
  }

  cancelEndSeason(): void {
    this.showConfirmDialog = false;
    this.forceEnd = false;
  }

  async confirmEndSeason(): Promise<void> {
    if (!this.preview) {
      this.notificationService.error('No preview data available');
      return;
    }

    if (!this.preview.isSeasonComplete && !this.forceEnd) {
      this.notificationService.warning('Season is not complete. Check "Force End" to proceed anyway.');
      return;
    }

    this.isEndingSeason = true;
    try {
      const request: SeasonEndRequest = {
        leagueId: this.selectedLeagueId,
        seasonNumber: this.selectedSeasonNumber,
        endDate: new Date(),
        forceEnd: this.forceEnd
      };

      const result = await this.seasonAchievementService.endSeason(request);
      
      this.notificationService.success(result.message);
      this.showConfirmDialog = false;
      this.forceEnd = false;
      
      // Refresh preview to show that season has ended
      await this.loadPreview();
      
    } catch (error: any) {
      console.error('Error ending season:', error);
      this.notificationService.error(error.error?.message || 'Failed to end season');
    } finally {
      this.isEndingSeason = false;
    }
  }

  getAchievementIcon(achievementType: string): string {
    switch (achievementType) {
      case 'Championship Winner':
        return 'fas fa-trophy';
      case 'Finalist':
        return 'fas fa-medal';
      case 'Third Place':
        return 'fas fa-award';
      case 'Top Scorer':
        return 'fas fa-futbol';
      case 'Top Assist Provider':
        return 'fas fa-hands-helping';
      case 'Best Goalkeeper':
        return 'fas fa-hand-paper';
      case 'Best Center Back':
      case 'Best Defensive Midfielder':
        return 'fas fa-shield-alt';
      case 'Best Midfielder':
      case 'Best Attacking Midfielder':
        return 'fas fa-running';
      case 'Best Winger':
        return 'fas fa-arrows-alt-h';
      case 'Best Striker':
        return 'fas fa-crosshairs';
      default:
        return 'fas fa-star';
    }
  }

  getRankSuffix(rank: number): string {
    const suffixes = ["th", "st", "nd", "rd"];
    const v = rank % 100;
    return rank + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/Player.png';
    }
  }

  onTeamImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/Team.png';
    }
  }

  goBack(): void {
    this.router.navigate(['/admin/cms-dashboard']);
  }

  getGoalsPerGameRatio(scorer: any): string {
    if (!scorer.games || scorer.games === 0) return '0.00';
    return (scorer.goals / scorer.games).toFixed(2);
  }

  getAssistsPerGameRatio(assister: any): string {
    if (!assister.games || assister.games === 0) return '0.00';
    return (assister.assists / assister.games).toFixed(2);
  }
}
