import { inject, injectable } from "tsyringe";
import { ILeagueRepository } from "../../interfaces/league/league-repository.interface";
import { ITeamRepository } from "../../interfaces/team/team-repository.interface";
import { IGameRepository } from "../../interfaces/game/game-repository.interface";
import { INewsService } from "../../interfaces/news/news-service.interface";
import { ITeamOfTheWeekService } from "../../interfaces/wrapper-services/team-of-the-week-service.interface";
import { ITeamOfTheSeasonService } from "../../interfaces/wrapper-services/team-of-the-season-service.interface";
import { ISeasonCompletionService } from "../../interfaces/wrapper-services/season-completion-service.interface";
import logger from "../../config/logger";
import { ClientSession } from "mongoose";
import { transactionService } from "../util-services/transaction-service";
import { GAME_STATUS } from "@pro-clubs-manager/shared-dtos";

export interface SeasonCompletionResult {
  finalStandings: {
    teamId: string;
    teamName: string;
    position: number;
    points: number;
    goalDifference: number;
  }[];
  champion: {
    teamId: string;
    teamName: string;
  };
  seasonStats: {
    totalGames: number;
    totalGoals: number;
    averageGoalsPerGame: number;
  };
}

@injectable()
export class SeasonCompletionService implements ISeasonCompletionService {
  constructor(
    @inject("ILeagueRepository") private leagueRepository: ILeagueRepository,
    @inject("ITeamRepository") private teamRepository: ITeamRepository,
    @inject("IGameRepository") private gameRepository: IGameRepository,
    @inject("INewsService") private newsService: INewsService,
    @inject("ITeamOfTheWeekService") private teamOfTheWeekService: ITeamOfTheWeekService,
    @inject("ITeamOfTheSeasonService") private teamOfTheSeasonService: ITeamOfTheSeasonService
  ) {}

  async completeSeasonIfFinal(leagueId: string, session?: ClientSession): Promise<SeasonCompletionResult | null> {
    logger.info(`SeasonCompletionService: Checking if season is complete for league ${leagueId}`);

    const league = await this.leagueRepository.getLeagueById(leagueId);
    
    // Check if all games in the season are completed
    const isSeasonComplete = await this.checkIfSeasonComplete(leagueId, league.currentSeason.seasonNumber);
    
    if (!isSeasonComplete) {
      logger.info(`Season ${league.currentSeason.seasonNumber} is not yet complete`);
      return null;
    }

    logger.info(`Season ${league.currentSeason.seasonNumber} is complete! Processing final rankings...`);

    return await transactionService.withTransaction(async (txSession) => {
      // Calculate final standings
      const finalStandings = await this.calculateFinalStandings(leagueId, league.currentSeason.seasonNumber, txSession);
      
      // Update team final positions
      await this.updateTeamFinalPositions(finalStandings, league.currentSeason.seasonNumber, txSession);
      
      // Update league winner
      const champion = finalStandings[0];
      league.currentSeason.winner = champion.teamId as any;
      await league.save({ session: txSession });
      
      // Generate season completion news
      await this.generateSeasonCompletionNews(league.currentSeason.seasonNumber, finalStandings, txSession);

      // Generate Team of the Season for all supported formations
      await this.generateTeamsOfTheSeason(leagueId, league.currentSeason.seasonNumber, txSession);

      // Calculate season stats
      const seasonStats = await this.calculateSeasonStats(leagueId, league.currentSeason.seasonNumber);
      
      return {
        finalStandings,
        champion: {
          teamId: champion.teamId,
          teamName: champion.teamName
        },
        seasonStats
      };
    });
  }

  private async checkIfSeasonComplete(leagueId: string, seasonNumber: number): Promise<boolean> {
    // Get all games for the current season
    const allSeasonGames = await this.gameRepository.getAllGames();

    // Check if all games are played
    const unplayedGames = allSeasonGames.filter(game => game.status !== GAME_STATUS.PLAYED);
    
    return unplayedGames.length === 0 && allSeasonGames.length > 0;
  }

  private async calculateFinalStandings(leagueId: string, seasonNumber: number, session: ClientSession) {
    const teams = await this.teamRepository.getTeamsByLeagueId(leagueId, session);
    
    const standings = teams.map(team => {
      const stats = team.currentSeason!.stats;
      const points = stats.wins * 3 + stats.draws;
      const goalDifference = stats.goalsScored - stats.goalsConceded;
      const gamesPlayed = stats.wins + stats.losses + stats.draws;
      
      return {
        teamId: team.id,
        teamName: team.name,
        points,
        goalDifference,
        goalsScored: stats.goalsScored,
        goalsConceded: stats.goalsConceded,
        wins: stats.wins,
        draws: stats.draws,
        losses: stats.losses,
        gamesPlayed,
        position: 0 // Will be set after sorting
      };
    });

    // Sort by points (desc), then goal difference (desc), then goals scored (desc)
    standings.sort((a, b) => {
      if (b.points !== a.points) return b.points - a.points;
      if (b.goalDifference !== a.goalDifference) return b.goalDifference - a.goalDifference;
      return b.goalsScored - a.goalsScored;
    });

    // Assign positions
    standings.forEach((team, index) => {
      team.position = index + 1;
    });

    return standings;
  }

  private async updateTeamFinalPositions(standings: any[], seasonNumber: number, session: ClientSession): Promise<void> {
    for (const standing of standings) {
      const team = await this.teamRepository.getTeamById(standing.teamId, session);
      if (team.currentSeason) {
        team.currentSeason.finalPosition = standing.position;
        await team.save({ session });
      }
    }
  }

  private async generateTeamsOfTheSeason(leagueId: string, seasonNumber: number, session: ClientSession): Promise<void> {
    logger.info(`Generating Teams of the Season for season ${seasonNumber}`);

    try {
      const supportedFormations = this.teamOfTheSeasonService.getSupportedFormations();

      for (const formation of supportedFormations) {
        try {
          const tots = await this.teamOfTheSeasonService.generateTeamOfTheSeason(
            leagueId,
            seasonNumber,
            formation,
            session
          );

          logger.info(`Generated TOTS for formation ${formation}: ${tots.players.length} players`);

          // Generate news for TOTS
          await this.newsService.addNews({
            type: 'General',
            title: `⭐ Team of the Season (${formation}) - Season ${seasonNumber}`,
            content: `The Team of the Season has been announced for Season ${seasonNumber} using the ${formation} formation! Featuring the best performing players across all positions based on their season-long performances.`,
            createdBy: 'League System',
            date: new Date()
          });

        } catch (error) {
          logger.error(`Failed to generate TOTS for formation ${formation}:`, error);
          // Continue with other formations
        }
      }
    } catch (error) {
      logger.error('Error generating Teams of the Season:', error);
      // Don't throw - TOTS generation is not critical for season completion
    }
  }

  private async generateSeasonCompletionNews(seasonNumber: number, standings: any[], session: ClientSession): Promise<void> {
    const champion = standings[0];
    const runnerUp = standings[1];
    const thirdPlace = standings[2];

    // Generate championship news
    await this.newsService.addNews({
      type: 'General',
      title: `🏆 Season ${seasonNumber} Champions: ${champion.teamName}!`,
      content: `Congratulations to ${champion.teamName} for winning Season ${seasonNumber}! They finished with ${champion.points} points, ${champion.goalDifference > 0 ? '+' : ''}${champion.goalDifference} goal difference. Runner-up: ${runnerUp.teamName} (${runnerUp.points} pts). Third place: ${thirdPlace.teamName} (${thirdPlace.points} pts).`,
      createdBy: 'League System',
      date: new Date()
    });

    // Generate final standings news
    const standingsText = standings.slice(0, 5).map(team => 
      `${team.position}. ${team.teamName} - ${team.points} pts (${team.goalDifference > 0 ? '+' : ''}${team.goalDifference} GD)`
    ).join('\n');

    await this.newsService.addNews({
      type: 'General',
      title: `📊 Season ${seasonNumber} Final Standings`,
      content: `Season ${seasonNumber} has concluded! Here are the final standings:\n\n${standingsText}\n\nThank you to all teams for an exciting season!`,
      createdBy: 'League System',
      date: new Date()
    });
  }

  private async calculateSeasonStats(leagueId: string, seasonNumber: number) {
    const allSeasonGames = await this.gameRepository.getAllGames();

    const playedGames = allSeasonGames.filter(game => game.status === GAME_STATUS.PLAYED && game.result);
    
    const totalGoals = playedGames.reduce((sum, game) => {
      return sum + (game.result?.homeTeamGoals || 0) + (game.result?.awayTeamGoals || 0);
    }, 0);

    return {
      totalGames: playedGames.length,
      totalGoals,
      averageGoalsPerGame: playedGames.length > 0 ? totalGoals / playedGames.length : 0
    };
  }

  async getTeamSeasonAchievements(teamId: string): Promise<string[]> {
    const team = await this.teamRepository.getTeamById(teamId);
    const achievements: string[] = [];

    // Check historical seasons for achievements
    for (const season of team.seasonsHistory) {
      if (season.finalPosition) {
        switch (season.finalPosition) {
          case 1:
            achievements.push(`🏆 Season ${season.seasonNumber} Champions`);
            break;
          case 2:
            achievements.push(`🥈 Season ${season.seasonNumber} Runner-up`);
            break;
          case 3:
            achievements.push(`🥉 Season ${season.seasonNumber} Third Place`);
            break;
          default:
            if (season.finalPosition <= 5) {
              achievements.push(`🏅 Season ${season.seasonNumber} Top 5 Finish (${season.finalPosition}${this.getOrdinalSuffix(season.finalPosition)})`);
            }
        }
      }
    }

    return achievements;
  }

  private getOrdinalSuffix(num: number): string {
    const j = num % 10;
    const k = num % 100;
    if (j === 1 && k !== 11) return 'st';
    if (j === 2 && k !== 12) return 'nd';
    if (j === 3 && k !== 13) return 'rd';
    return 'th';
  }
}
