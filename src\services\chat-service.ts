import { injectable } from "tsyringe";
import { ClientSession } from "mongoose";
import ChatMessage, { IChatMessage } from "../models/chat";
import { IUser } from "../models/user";
import logger from "../config/logger";

export interface CreateMessageRequest {
  content: string;
  replyTo?: string;
}

export interface EditMessageRequest {
  messageId: string;
  content: string;
}

export interface AddReactionRequest {
  messageId: string;
  emoji: string;
}

export interface ChatMessageResponse {
  id: string;
  content: string;
  author: {
    id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    role: 'user' | 'admin';
  };
  timestamp: Date;
  edited: boolean;
  editedAt?: Date;
  replyTo?: {
    id: string;
    content: string;
    author: {
      firstName: string;
      lastName: string;
    };
  };
  reactions: {
    emoji: string;
    count: number;
    userReacted: boolean;
  }[];
  canEdit: boolean;
  canDelete: boolean;
}

@injectable()
export class ChatService {
  
  async getMessages(userId: string, page: number = 1, limit: number = 50): Promise<{
    messages: ChatMessageResponse[];
    totalPages: number;
    currentPage: number;
    hasMore: boolean;
  }> {
    try {
      const skip = (page - 1) * limit;
      
      const totalMessages = await ChatMessage.countDocuments({ isDeleted: false });
      const totalPages = Math.ceil(totalMessages / limit);
      
      const messages = await ChatMessage
        .find({ isDeleted: false })
        .populate('replyTo', 'content author.firstName author.lastName')
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      const formattedMessages = messages.map(message => this.formatMessage(message, userId));
      
      return {
        messages: formattedMessages.reverse(), // Reverse to show oldest first in the current page
        totalPages,
        currentPage: page,
        hasMore: page < totalPages
      };
    } catch (error) {
      logger.error("Error fetching chat messages:", error);
      throw new Error("Failed to fetch chat messages");
    }
  }

  async createMessage(user: IUser, request: CreateMessageRequest, session?: ClientSession): Promise<ChatMessageResponse> {
    try {
      const messageData = {
        content: request.content.trim(),
        author: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          profilePicture: user.profilePicture,
          role: user.role
        },
        replyTo: request.replyTo || undefined,
        reactions: [],
        isDeleted: false
      };

      const message = new ChatMessage(messageData);
      await message.save({ session });

      // Populate replyTo if it exists
      if (request.replyTo) {
        await message.populate('replyTo', 'content author.firstName author.lastName');
      }

      logger.info(`Chat message created by user ${user.id}`);
      return this.formatMessage(message.toObject(), user.id);
    } catch (error) {
      logger.error("Error creating chat message:", error);
      throw new Error("Failed to create chat message");
    }
  }

  async editMessage(userId: string, request: EditMessageRequest, session?: ClientSession): Promise<ChatMessageResponse> {
    try {
      const message = await ChatMessage.findById(request.messageId);
      
      if (!message) {
        throw new Error("Message not found");
      }

      if (message.author.id !== userId) {
        throw new Error("You can only edit your own messages");
      }

      if (message.isDeleted) {
        throw new Error("Cannot edit deleted message");
      }

      // Check if message is older than 15 minutes (edit time limit)
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
      if (message.timestamp < fifteenMinutesAgo) {
        throw new Error("Messages can only be edited within 15 minutes of posting");
      }

      message.content = request.content.trim();
      message.edited = true;
      message.editedAt = new Date();
      
      await message.save({ session });
      await message.populate('replyTo', 'content author.firstName author.lastName');

      logger.info(`Chat message ${request.messageId} edited by user ${userId}`);
      return this.formatMessage(message.toObject(), userId);
    } catch (error) {
      logger.error("Error editing chat message:", error);
      throw error;
    }
  }

  async deleteMessage(userId: string, userRole: string, messageId: string, session?: ClientSession): Promise<void> {
    try {
      const message = await ChatMessage.findById(messageId);
      
      if (!message) {
        throw new Error("Message not found");
      }

      if (message.isDeleted) {
        throw new Error("Message already deleted");
      }

      // Only message author or admin can delete
      if (message.author.id !== userId && userRole !== 'admin') {
        throw new Error("You can only delete your own messages");
      }

      message.isDeleted = true;
      message.deletedAt = new Date();
      message.deletedBy = userId;
      
      await message.save({ session });

      logger.info(`Chat message ${messageId} deleted by user ${userId}`);
    } catch (error) {
      logger.error("Error deleting chat message:", error);
      throw error;
    }
  }

  async addReaction(userId: string, request: AddReactionRequest, session?: ClientSession): Promise<ChatMessageResponse> {
    try {
      const message = await ChatMessage.findById(request.messageId);
      
      if (!message || message.isDeleted) {
        throw new Error("Message not found");
      }

      const existingReaction = message.reactions.find(r => r.emoji === request.emoji);
      
      if (existingReaction) {
        const userIndex = existingReaction.users.indexOf(userId);
        if (userIndex > -1) {
          // Remove reaction
          existingReaction.users.splice(userIndex, 1);
          if (existingReaction.users.length === 0) {
            message.reactions = message.reactions.filter(r => r.emoji !== request.emoji);
          }
        } else {
          // Add reaction
          existingReaction.users.push(userId);
        }
      } else {
        // Create new reaction
        message.reactions.push({
          emoji: request.emoji,
          users: [userId]
        });
      }

      await message.save({ session });
      await message.populate('replyTo', 'content author.firstName author.lastName');

      return this.formatMessage(message.toObject(), userId);
    } catch (error) {
      logger.error("Error adding reaction to chat message:", error);
      throw error;
    }
  }

  private formatMessage(message: any, currentUserId: string): ChatMessageResponse {
    return {
      id: message._id.toString(),
      content: message.content,
      author: message.author,
      timestamp: message.timestamp,
      edited: message.edited,
      editedAt: message.editedAt,
      replyTo: message.replyTo ? {
        id: message.replyTo._id.toString(),
        content: message.replyTo.content,
        author: {
          firstName: message.replyTo.author.firstName,
          lastName: message.replyTo.author.lastName
        }
      } : undefined,
      reactions: message.reactions.map((reaction: any) => ({
        emoji: reaction.emoji,
        count: reaction.users.length,
        userReacted: reaction.users.includes(currentUserId)
      })),
      canEdit: message.author.id === currentUserId && !message.isDeleted,
      canDelete: message.author.id === currentUserId || currentUserId === 'admin'
    };
  }
}
