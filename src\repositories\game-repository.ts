import { ClientSession, Types } from "mongoose";
import { IGameRepository } from "../interfaces/game/game-repository.interface";
import { BadRequestError, NotFoundError, QueryFailedError } from "../errors";
import logger from "../config/logger";
import { GAME_STATUS } from "@pro-clubs-manager/shared-dtos";
import Game, { IGame, AddGameData, PopulatedPlayerGameData } from "../models/game/game";

export class GameRepository implements IGameRepository {
  async createGame(
    fixtureId: string | Types.ObjectId,
    leagueId: Types.ObjectId,
    seasonNumber: number,
    addGameData: AddGameData,
    session?: ClientSession
  ): Promise<IGame> {
    const { homeTeam, awayTeam, date, round, isPlayoff, playoffStage, matchNumber } = addGameData;
    try {
      const game = new Game({
        fixture: fixtureId,
        homeTeam,
        awayTeam,
        date,
        league: leagueId,
        seasonNumber,
        round: round || 1,
        isPlayoff: isPlayoff || false,
        playoffStage: playoffStage,
        matchNumber: matchNumber,
      });
      await game.save({ session });
      return game;
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to create game`);
    }
  }

  async createGames(
    fixtureId: string | Types.ObjectId,
    leagueId: Types.ObjectId,
    seasonNumber: number,
    gamesData: AddGameData[],
    session?: ClientSession
  ): Promise<IGame[]> {
    const gamesWithFixtureId = gamesData.map((game) => ({
      ...game,
      fixture: fixtureId,
      league: leagueId,
      seasonNumber,
      round: game.round || 1,
      isPlayoff: game.isPlayoff || false,
      playoffStage: game.playoffStage,
    }));

    try {
      const games = await Game.insertMany(gamesWithFixtureId, { session });
      return games;
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to create games`);
    }
  }

  async getGameById(id: string | Types.ObjectId, session?: ClientSession): Promise<IGame> {
    try {
      logger.info(`GameRepository: attempting to get game by id ${id} (type: ${typeof id})`);

      // Validate ObjectId format if it's a string
      if (typeof id === 'string' && !Types.ObjectId.isValid(id)) {
        throw new BadRequestError(`Invalid game ID format: ${id}`);
      }

      // Try to find the game with detailed logging
      logger.info(`GameRepository: executing Game.findById with id: ${id}`);

      // First, let's try a direct query to see if the game exists
      const gameCount = await Game.countDocuments({ _id: id });
      logger.info(`GameRepository: found ${gameCount} games with id ${id}`);

      if (gameCount === 0) {
        logger.warn(`Game with id ${id} not found in database (count query)`);
        throw new NotFoundError(`Game with id ${id} not found`);
      }

      const game = await Game.findById(id, {}, { session });

      if (!game) {
        logger.error(`GameRepository: Game.findById returned null despite count query finding ${gameCount} games`);
        throw new NotFoundError(`Game with id ${id} not found`);
      }

      logger.info(`GameRepository: successfully retrieved game ${id} with status: ${game.status}`);
      return game;
    } catch (e: any) {
      if (e instanceof NotFoundError || e instanceof BadRequestError) {
        throw e;
      } else {
        logger.error(`GameRepository: Error getting game by id ${id}:`, e);
        logger.error(`Error details: ${e.message}`);
        logger.error(`Error stack: ${e.stack}`);

        // Try to provide more specific error information
        if (e.name === 'CastError') {
          throw new BadRequestError(`Invalid ObjectId format: ${id}`);
        }

        throw new QueryFailedError(`Failed to get game by id ${id}: ${e.message}`);
      }
    }
  }
  async getGamesByIds(ids: string | Types.ObjectId[], session?: ClientSession): Promise<IGame[]> {
    try {
      const games = await Game.find({ _id: { $in: ids } }, {}, { session });
      if (games.length !== ids.length) {
        throw new BadRequestError(`Failed to get some of the games`);
      }
      return games;
    } catch (e: any) {
      if (e instanceof NotFoundError) {
        throw e;
      } else {
        logger.error(e.message);
        throw new QueryFailedError(`Failed to get games by ids ${ids}`);
      }
    }
  }

  async getAllGames(session?: ClientSession): Promise<IGame[]> {
    try {
      const games = await Game.find({}, {}, { session }).populate("homeTeam awayTeam");

      // Filter out games with null teams (orphaned references)
      const validGames = games.filter(game => {
        const hasValidTeams = game.homeTeam && game.awayTeam;
        if (!hasValidTeams) {
          logger.warn(`Game ${game.id} has null teams - homeTeam: ${!!game.homeTeam}, awayTeam: ${!!game.awayTeam}. Filtering out.`);
        }
        return hasValidTeams;
      });

      if (validGames.length !== games.length) {
        logger.warn(`Filtered out ${games.length - validGames.length} games with null team references`);
      }

      return validGames;
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to get all games`);
    }
  }

  async getTopAvgRatingByPosition(position: string, minimumGames: number, session?: ClientSession): Promise<TopAvgRatingByPosition[]> {
    try {
      const pipeline: any[] = [
        { $match: { seasonNumber: 6 } },
  
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          },
        },
  
        { $unwind: "$players" },
      ];
  
      // if the position is any -> return all the players
      if (position.toLowerCase() !== 'any') {
        pipeline.push({ $match: { "players.positionPlayed": position } });
      }
  
      pipeline.push(
        {
          $group: {
            _id: {
              playerId: "$players.playerId",
              position: "$players.positionPlayed"
            },
            totalRating: { $sum: "$players.rating" },
            games: { $sum: 1 },
            goals: { $sum: "$players.goals" },
            assists: { $sum: "$players.assists" },
            cleanSheets: {
              $sum: { $cond: { if: "$players.cleanSheet", then: 1, else: 0 } }
            }
          }
        },
        {
          $project: {
            playerId: "$_id.playerId",
            position: "$_id.position",
            avgRating: { $divide: ["$totalRating", "$games"] },
            goals: 1,
            games: 1,
            assists: 1,
            cleanSheets: 1
          }
        },
        { $match: { games: { $gte: minimumGames } } },
        {
          $lookup: {
            from: "players",
            localField: "playerId",
            foreignField: "_id",
            as: "player"
          }
        },
        { $unwind: "$player" },
        {
          $lookup: {
            from: "teams",
            localField: "player.team",
            foreignField: "_id",
            as: "team"
          }
        },
        { $unwind: "$team" },
        {
          $project: {
            playerId: 1,
            playerName: "$player.name",
            playerImgUrl: "$player.imgUrl",
            teamId: "$team._id",
            teamName: "$team.name",
            teamImgUrl: "$team.imgUrl",
            goals: 1,
            position: 1,
            assists: 1,
            avgRating: 1,
            games: 1,
            cleanSheets: 1
          }
        },
        { $sort: { avgRating: -1 } },
        { $limit: 10 }
      );
  
      const topPlayers = await Game.aggregate(pipeline, { session });
  
      return topPlayers;
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to calculate top players for position ${position}`);
    }
  }

  async getLeagueSeasonTeamGames(teamId: string, leagueId: string, seasonNumber: number, limit: number = 100): Promise<IGame[]> {
    try {
      const games = await Game.find({ league: leagueId, seasonNumber, $or: [{ homeTeam: teamId }, { awayTeam: teamId }] })
        .populate("homeTeam awayTeam")
        .sort({ round: 1 })
        .limit(limit)
        .exec();

      // Filter out games with null teams (orphaned references)
      const validGames = games.filter(game => {
        const hasValidTeams = game.homeTeam && game.awayTeam;
        if (!hasValidTeams) {
          logger.warn(`Team game ${game.id} has null teams - homeTeam: ${!!game.homeTeam}, awayTeam: ${!!game.awayTeam}. Filtering out.`);
        }
        return hasValidTeams;
      });

      if (validGames.length !== games.length) {
        logger.warn(`Filtered out ${games.length - validGames.length} team games with null team references`);
      }

      return validGames;
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to get games for team ${teamId}`);
    }
  }
  async getPlayedLeagueSeasonTeamGames(teamId: string, leagueId: string | Types.ObjectId, seasonNumber: number, limit: number = 10): Promise < IGame[] > {
  try {
    const games = await Game.find({
      league: leagueId,
      seasonNumber: seasonNumber,
      $or: [{ homeTeam: teamId }, { awayTeam: teamId }],
      status: [GAME_STATUS.COMPLETED, GAME_STATUS.PLAYED],
    })
      .sort({ round: 1 })
      .limit(limit)
      .exec();
    return games;
  } catch(e: any) {
    logger.error(e.message);
    throw new QueryFailedError(`Failed to get played games for team ${teamId}`);
  }
}

  async getPlayerPlayedSeasonGames(playerId: string, league: string | Types.ObjectId, seasonNumber: number, session ?: ClientSession): Promise < IGame[] > {
  try {
    return await Game.find(
      {
        league,
        seasonNumber,
        $or: [{ "homeTeamPlayersPerformance.playerId": playerId }, { "awayTeamPlayersPerformance.playerId": playerId }],
      },
      null,
      { session }
    );
  } catch(e: any) {
    logger.error(e.message);
    throw new QueryFailedError(`Failed to get played games for player ${playerId}`);
  }
}

  async getLeaguePlayedGamesByDate(leagueData: { leagueId: Types.ObjectId; seasonNumber: number }, startDate: Date, endDate: Date): Promise < IGame[] > {
  try {
    return await Game.find({
      league: leagueData.leagueId,
      seasonNumber: leagueData.seasonNumber,
      status: GAME_STATUS.COMPLETED,
      date: { $gte: startDate, $lte: endDate },
    })
      .sort({ date: 1, seasonNumber: 1, round: 1 })
      .exec();
  } catch(e: any) {
    logger.error(e.message);
    throw new QueryFailedError(`Failed to get games between ${startDate} and ${endDate}`);
  }
}

  async getPlayerLastGames(
  playerId: string | Types.ObjectId,
  league: string | Types.ObjectId,
  seasonNumber: number,
  numberOfGames: number
): Promise < PopulatedPlayerGameData[] > {
  try {
    // Fetch the last 5 games for the player with populated teams data of each game
    const games = (await Game.find({
      league,
      seasonNumber,
      $or: [{ "homeTeamPlayersPerformance.playerId": playerId }, { "awayTeamPlayersPerformance.playerId": playerId }],
    })
      .sort({ date: -1, seasonNumber: -1, round: -1, _id: -1 })
      .limit(numberOfGames)
      .populate("homeTeam awayTeam", "name id imgUrl")
      .populate("league", "id name imgUrl")) as any;

    return games;
  } catch(e: any) {
    logger.error(e.message);
    throw new QueryFailedError(`Failed to get last ${numberOfGames} games for player with id ${playerId}`);
  }
}

  async deleteGameById(id: string | Types.ObjectId, session ?: ClientSession): Promise < void> {
  try {
    await Game.findByIdAndDelete(id, { session });
  } catch(e: any) {
    logger.error(e.message);
    throw new QueryFailedError(`Failed to delete game with id: ${id}`);
  }
}

  async getLeagueGamesBySeason(params: { leagueId: string | Types.ObjectId; seasonNumber: number }): Promise<IGame[]> {
    try {
      return await Game.find({
        league: params.leagueId,
        seasonNumber: params.seasonNumber
      })
        .populate("homeTeam awayTeam", "name id imgUrl")
        .populate("fixture", "isPlayoff playoffDetails")
        .sort({ date: 1, round: 1 })
        .exec();
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to get games for league ${params.leagueId} season ${params.seasonNumber}`);
    }
  }

  async getGamesByFixtureId(fixtureId: string | Types.ObjectId): Promise<IGame[]> {
    try {
      return await Game.find({ fixture: fixtureId })
        .populate("homeTeam awayTeam", "name id imgUrl")
        .sort({ matchNumber: 1, date: 1 })
        .exec();
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to get games for fixture ${fixtureId}`);
    }
  }

  async getMaxSeasonNumberForLeague(leagueId: string | Types.ObjectId): Promise<number | null> {
    try {
      logger.info(`Getting max season number for league: ${leagueId}`);

      // Ensure leagueId is properly formatted
      const objectId = typeof leagueId === 'string' ? new Types.ObjectId(leagueId) : leagueId;

      const result = await Game.findOne({ league: objectId })
        .sort({ seasonNumber: -1 })
        .select("seasonNumber")
        .lean()
        .exec();

      const maxSeason = result?.seasonNumber || null;
      logger.info(`Max season number for league ${leagueId}: ${maxSeason}`);

      return maxSeason;
    } catch (e: any) {
      logger.error(`Error getting max season number for league ${leagueId}:`, e);
      // Return null instead of throwing to prevent server crashes
      return null;
    }
  }

  async getTeamVsTeamHistory(
    team1Id: string | Types.ObjectId,
    team2Id: string | Types.ObjectId,
    limit: number = 10,
    session?: ClientSession
  ): Promise<IGame[]> {
    try {
      const query = {
        $or: [
          { homeTeam: team1Id, awayTeam: team2Id },
          { homeTeam: team2Id, awayTeam: team1Id }
        ],
        result: { $exists: true } // Only completed games
      };

      return await Game.find(query, null, { session })
        .populate('homeTeam', 'name imgUrl')
        .populate('awayTeam', 'name imgUrl')
        .sort({ date: -1 }) // Most recent first
        .limit(limit)
        .exec();
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to get team vs team history for teams ${team1Id} and ${team2Id}`);
    }
  }

  async aggregatePlayerStatsForSeason(
    leagueId: string | Types.ObjectId,
    seasonNumber: number
  ): Promise<{
    _id: Types.ObjectId;
    totalGoals: number;
    totalAssists: number;
    totalGames: number;
  }[]> {
    try {
      logger.info(`Aggregating player stats for league ${leagueId}, season ${seasonNumber}`);

      const result = await Game.aggregate([
        {
          $match: {
            league: new Types.ObjectId(leagueId.toString()),
            seasonNumber: seasonNumber,
            result: { $exists: true } // Only completed games
          }
        },
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          }
        },
        { $unwind: "$players" },
        {
          $group: {
            _id: "$players.playerId",
            totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
            totalAssists: { $sum: { $ifNull: ["$players.assists", 0] } },
            totalGames: { $sum: 1 }
          }
        },
        { $sort: { totalGoals: -1, totalGames: 1 } }
      ]);

      logger.info(`Found stats for ${result.length} players in league ${leagueId}, season ${seasonNumber}`);
      return result;
    } catch (e: any) {
      logger.error(`Error aggregating player stats for league ${leagueId}, season ${seasonNumber}:`, e);
      throw new QueryFailedError(`Failed to aggregate player stats for league ${leagueId}, season ${seasonNumber}`);
    }
  }
}

export type TopAvgRatingByPosition = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl: string;
  position: string;
  games: number;
  goals: number;
  assists: number;
  cleanSheets: number;
  avgRating: number;
};

// Export types for all-time statistics
export type AllTimeTopAvgRatingByPosition = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  position: string;
  games: number;
  goals: number;
  assists: number;
  cleanSheets: number;
  avgRating: number;
};

export type MostHattricks = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  position: string;
  games: number;
  totalGoals: number;
  hattricks: number;
  hattricsPerGame: number;
};

export type MostCleanSheets = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  position: string;
  games: number;
  cleanSheets: number;
  cleanSheetsPerGame: number;
  avgRating: number;
};

export type MostWinningPercentageTeam = {
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  games: number;
  wins: number;
  draws: number;
  losses: number;
  winningPercentage: number;
  goalsFor: number;
  goalsAgainst: number;
  goalDifference: number;
};

export type MostWinningPercentagePlayer = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  position: string;
  games: number;
  wins: number;
  draws: number;
  losses: number;
  winningPercentage: number;
  avgRating: number;
  goals: number;
  assists: number;
};
