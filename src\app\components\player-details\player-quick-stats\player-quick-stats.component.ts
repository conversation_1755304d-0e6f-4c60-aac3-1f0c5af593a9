import { Component, Input } from '@angular/core';
import { PlayerDTO } from '@pro-clubs-manager/shared-dtos';
import { PlayerStat } from '../player-stats-card/player-stats-card.component';

@Component({
  selector: 'app-player-quick-stats',
  templateUrl: './player-quick-stats.component.html',
  styleUrls: ['./player-quick-stats.component.scss']
})
export class PlayerQuickStatsComponent {
  @Input() player!: PlayerDTO;

  get isDefender(): boolean {
    return this.player.position === 'CB' || 
           this.player.position === 'LB' || 
           this.player.position === 'RB' ||
           this.player.position === 'LWB' ||
           this.player.position === 'RWB';
  }

  getAVGRatingColor(): string {
    const rating = this.player.stats.avgRating;
    if (rating >= 8.0) {
      return 'text-green';
    } else if (rating >= 7.0) {
      return 'text-yellow';
    } else {
      return 'text-red';
    }
  }

  getPlayerStats(): PlayerStat[] {
    const stats: PlayerStat[] = [
      {
        label: 'Games',
        value: this.player.stats.games,
        icon: 'fas fa-gamepad',
        color: 'primary'
      },
      {
        label: 'Assists',
        value: this.player.stats.assists,
        icon: 'fas fa-hands-helping',
        color: 'info'
      },
      {
        label: 'POTM',
        value: this.player.stats.playerOfTheMatch,
        icon: 'fas fa-trophy',
        color: 'warning'
      },
      {
        label: 'Avg Rating',
        value: this.player.stats.avgRating.toFixed(2),
        icon: 'fas fa-star',
        color: this.getRatingColor()
      }
    ];

    // Add Goals or Clean Sheets based on position
    if (this.isDefender) {
      stats.splice(1, 0, {
        label: 'Clean Sheets',
        value: this.player.stats.cleanSheets,
        icon: 'fas fa-shield-alt',
        color: 'success'
      });
    } else {
      stats.splice(1, 0, {
        label: 'Goals',
        value: this.player.stats.goals,
        icon: 'fas fa-futbol',
        color: 'success'
      });
    }

    return stats;
  }

  private getRatingColor(): 'success' | 'warning' | 'danger' {
    const rating = this.player.stats.avgRating;
    if (rating >= 8.0) {
      return 'success';
    } else if (rating >= 7.0) {
      return 'warning';
    } else {
      return 'danger';
    }
  }
}
