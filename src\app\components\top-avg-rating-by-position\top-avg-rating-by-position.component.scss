/* === TOP RATING CONTAINER === */
.top-rating-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    min-height: 600px;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--secondary), var(--primary));
        opacity: 0.8;
    }
}

/* === HEADER SECTION === */
.header-section {
    .header-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
        padding: var(--spacing-md) 0;
        border-bottom: 1px solid var(--border-primary);

        .header-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: var(--radius-full);
            color: var(--text-inverse);
            font-size: 1.5rem;
            box-shadow: var(--shadow-md);
        }

        .header-text {
            flex: 1;

            .header-title {
                font-size: var(--text-2xl);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
                margin: 0;
                line-height: 1.2;
            }

            .header-subtitle {
                font-size: var(--text-sm);
                color: var(--text-secondary);
                margin: var(--spacing-xs) 0 0 0;
                font-weight: var(--font-weight-medium);
            }
        }
    }
}

/* === CONTROLS SECTION === */
.controls-section {
    .controls-container {
        display: flex;
        align-items: flex-end;
        gap: var(--spacing-lg);
        padding: var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-secondary);

        .control-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
            align-items: flex-start;

            .control-label {
                font-size: var(--text-xs);
                font-weight: var(--font-weight-medium);
                color: var(--text-secondary);
                text-transform: uppercase;
                letter-spacing: 0.05em;
                margin-bottom: 2px;
            }

            .position-selector {
                height: 40px;
                display: flex;
                align-items: center;

                ::ng-deep {
                    .pro-clubs-auto-complete-select {
                        height: 40px;

                        .select-container {
                            background: var(--surface-primary);
                            border: 1px solid var(--border-primary);
                            border-radius: var(--radius-md);
                            transition: all 0.2s ease;
                            height: 40px;
                            display: flex;
                            align-items: center;

                            &:hover {
                                border-color: var(--primary);
                            }
                        }
                    }
                }
            }

            .games-input {
                width: 80px;
                height: 40px;
                padding: var(--spacing-sm);
                background: var(--surface-primary);
                border: 1px solid var(--border-primary);
                border-radius: var(--radius-md);
                color: var(--text-primary);
                font-size: var(--text-sm);
                text-align: center;
                transition: all 0.2s ease;
                box-sizing: border-box;

                &:focus {
                    outline: none;
                    border-color: var(--primary);
                    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
                }

                &:hover {
                    border-color: var(--primary);
                }
            }
        }

        .load-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            height: 40px;
            padding: 0 var(--spacing-lg);
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-sm);
            white-space: nowrap;

            &:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }

            &:active {
                transform: translateY(0);
            }

            i {
                transition: transform 0.2s ease;
            }

            &:hover i {
                transform: rotate(180deg);
            }
        }
    }
}

/* === PLAYERS GRID === */
.players-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
    flex: 1;
}

/* === POSITION LEADERS SECTION === */
.position-leaders-section {
    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-xl);

        .section-title {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-xs) 0;

            i {
                color: var(--primary);
                font-size: var(--text-lg);
            }
        }

        .section-subtitle {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            margin: 0;
            font-weight: var(--font-weight-medium);
        }
    }

    .position-leaders-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-lg);

        @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }

        @media (max-width: 480px) {
            grid-template-columns: 1fr;
        }

        .position-card {
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg,
                    transparent,
                    rgba(99, 102, 241, 0.1),
                    transparent);
                transition: left 0.5s ease;
            }

            &:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-xl);
                border-color: var(--primary);

                &::before {
                    left: 100%;
                }

                .player-avatar img {
                    transform: scale(1.05);
                }

                .position-icon {
                    transform: scale(1.1);
                }
            }

            .position-header {
                display: flex;
                align-items: center;
                gap: var(--spacing-md);
                margin-bottom: var(--spacing-md);
                padding-bottom: var(--spacing-sm);
                border-bottom: 1px solid var(--border-secondary);

                .position-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, var(--primary), var(--secondary));
                    border-radius: var(--radius-lg);
                    color: white;
                    font-size: 1.1rem;
                    transition: all 0.3s ease;
                    box-shadow: var(--shadow-sm);
                }

                .position-info {
                    flex: 1;

                    .position-name {
                        font-size: var(--text-base);
                        font-weight: var(--font-weight-bold);
                        color: var(--text-primary);
                        margin: 0;
                        line-height: 1.2;
                    }

                    .position-code {
                        font-size: var(--text-xs);
                        color: var(--text-secondary);
                        font-weight: var(--font-weight-medium);
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                    }
                }
            }

            .player-section {
                display: flex;
                align-items: center;
                gap: var(--spacing-md);
                margin-bottom: var(--spacing-md);

                .player-avatar {
                    position: relative;
                    flex-shrink: 0;

                    img {
                        width: 60px;
                        height: 60px;
                        border-radius: var(--radius-full);
                        object-fit: cover;
                        border: 2px solid var(--border-primary);
                        transition: all 0.3s ease;
                    }

                    .team-badge {
                        position: absolute;
                        bottom: -3px;
                        right: -3px;
                        width: 24px;
                        height: 24px;
                        background: var(--surface-primary);
                        border-radius: var(--radius-full);
                        border: 2px solid var(--border-primary);
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        img {
                            width: 16px;
                            height: 16px;
                            border: none;
                            border-radius: var(--radius-sm);
                        }
                    }
                }

                .player-details {
                    flex: 1;
                    min-width: 0;

                    .player-name {
                        font-size: var(--text-base);
                        font-weight: var(--font-weight-semibold);
                        color: var(--text-primary);
                        margin: 0 0 2px 0;
                        line-height: 1.2;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .team-name {
                        font-size: var(--text-sm);
                        color: var(--text-secondary);
                        margin: 0;
                        font-weight: var(--font-weight-medium);
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }
            }

            .stats-section {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: var(--spacing-md);

                .rating-display {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 2px;
                    padding: var(--spacing-sm);
                    background: var(--surface-primary);
                    border-radius: var(--radius-lg);
                    border: 2px solid var(--border-primary);
                    min-width: 60px;

                    .rating-value {
                        font-size: var(--text-lg);
                        font-weight: var(--font-weight-black);
                        font-family: var(--font-mono);
                        color: var(--text-primary);

                        &.excellent {
                            color: #10b981;
                        }

                        &.good {
                            color: #3b82f6;
                        }

                        &.average {
                            color: #f59e0b;
                        }

                        &.poor {
                            color: #ef4444;
                        }
                    }

                    .rating-label {
                        font-size: var(--text-xs);
                        font-weight: var(--font-weight-medium);
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                        color: var(--text-secondary);
                    }
                }

                .mini-stats {
                    display: flex;
                    gap: var(--spacing-sm);

                    .stat-item {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: 1px;

                        .stat-value {
                            font-size: var(--text-sm);
                            font-weight: var(--font-weight-bold);
                            color: var(--text-primary);
                            font-family: var(--font-mono);
                        }

                        .stat-label {
                            font-size: 10px;
                            color: var(--text-secondary);
                        }
                    }
                }
            }
        }
    }
}

/* === PLAYERS LIST === */
.players-list {
    .list-header {
        margin-bottom: var(--spacing-lg);

        .list-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0;

            i {
                color: var(--primary);
            }
        }
    }

    .players-list-container {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        max-height: 400px;
        overflow-y: auto;
        padding-right: var(--spacing-xs);

        /* Custom scrollbar */
        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: var(--surface-secondary);
            border-radius: var(--radius-full);
        }

        &::-webkit-scrollbar-thumb {
            background: var(--border-primary);
            border-radius: var(--radius-full);
            transition: background 0.2s ease;

            &:hover {
                background: var(--primary);
            }
        }

        .player-card {
            display: grid;
            grid-template-columns: 50px 50px 2fr 1fr 80px;
            gap: var(--spacing-md);
            align-items: center;
            padding: var(--spacing-md);
            background: var(--surface-secondary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: visible;
            min-height: 70px;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 4px;
                background: var(--border-primary);
                transition: all 0.2s ease;
            }

            &:hover {
                background: var(--surface-hover);
                border-color: var(--primary);
                transform: translateX(4px);
                box-shadow: var(--shadow-md);

                &::before {
                    background: var(--primary);
                    width: 6px;
                }

                .player-avatar-small img {
                    transform: scale(1.1);
                }

                .rating-circle {
                    transform: scale(1.1);
                }
            }

            &.top-three {
                background: linear-gradient(135deg,
                    var(--surface-secondary),
                    rgba(99, 102, 241, 0.05));
                border-color: var(--primary);

                &::before {
                    background: var(--primary);
                }
            }

            .player-rank {
                display: flex;
                align-items: center;
                justify-content: center;

                .rank-number {
                    font-size: var(--text-lg);
                    font-weight: var(--font-weight-black);
                    font-family: var(--font-mono);
                    color: var(--text-primary);

                    &.gold {
                        color: #ffd700;
                        text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
                    }

                    &.silver {
                        color: #c0c0c0;
                        text-shadow: 0 0 10px rgba(192, 192, 192, 0.5);
                    }

                    &.bronze {
                        color: #cd7f32;
                        text-shadow: 0 0 10px rgba(205, 127, 50, 0.5);
                    }
                }
            }

            .player-avatar-small {
                img {
                    width: 40px;
                    height: 40px;
                    border-radius: var(--radius-full);
                    object-fit: cover;
                    border: 2px solid var(--border-primary);
                    transition: all 0.2s ease;
                }
            }

            .player-details {
                display: flex;
                flex-direction: column;
                gap: var(--spacing-xs);
                min-width: 0;
                flex: 1;

                .player-name {
                    font-size: var(--text-base);
                    font-weight: var(--font-weight-semibold);
                    color: var(--text-primary);
                    margin: 0;
                    line-height: 1.3;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 100%;
                }

                .team-name {
                    font-size: var(--text-sm);
                    color: var(--text-secondary);
                    margin: 0;
                    font-weight: var(--font-weight-medium);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 100%;
                }
            }

            .player-stats {
                display: flex;
                gap: var(--spacing-lg);

                .stat-group {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 2px;

                    .stat-value {
                        font-size: var(--text-sm);
                        font-weight: var(--font-weight-bold);
                        color: var(--text-primary);
                        font-family: var(--font-mono);
                    }

                    .stat-label {
                        font-size: var(--text-xs);
                        color: var(--text-secondary);
                        text-transform: uppercase;
                        font-weight: var(--font-weight-medium);
                        letter-spacing: 0.05em;
                    }
                }
            }

            .player-rating {
                .rating-circle {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 50px;
                    height: 50px;
                    border-radius: var(--radius-full);
                    border: 3px solid var(--border-primary);
                    background: var(--surface-primary);
                    transition: all 0.2s ease;
                    position: relative;

                    .rating-value {
                        font-size: var(--text-base);
                        font-weight: var(--font-weight-black);
                        font-family: var(--font-mono);
                    }

                    &.excellent {
                        border-color: #10b981;
                        background: linear-gradient(135deg, #10b981, #34d399);
                        color: white;
                        box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
                    }

                    &.good {
                        border-color: #3b82f6;
                        background: linear-gradient(135deg, #3b82f6, #60a5fa);
                        color: white;
                        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
                    }

                    &.average {
                        border-color: #f59e0b;
                        background: linear-gradient(135deg, #f59e0b, #fbbf24);
                        color: var(--text-inverse);
                        box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
                    }

                    &.poor {
                        border-color: #ef4444;
                        background: linear-gradient(135deg, #ef4444, #f87171);
                        color: var(--text-inverse);
                        box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
                    }
                }
            }
        }
    }
}

/* === LOADING CONTAINER === */
.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
}

/* === ANIMATIONS === */
@keyframes goldGlow {
    0% {
        box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    }
    100% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    }
}

@keyframes silverGlow {
    0% {
        box-shadow: 0 0 10px rgba(192, 192, 192, 0.5);
    }
    100% {
        box-shadow: 0 0 20px rgba(192, 192, 192, 0.8);
    }
}

@keyframes bronzeGlow {
    0% {
        box-shadow: 0 0 10px rgba(205, 127, 50, 0.5);
    }
    100% {
        box-shadow: 0 0 20px rgba(205, 127, 50, 0.8);
    }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1200px) {
    .top-rating-container {
        padding: var(--spacing-md);
        gap: var(--spacing-lg);
    }

    .position-leaders-section .position-leaders-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);

        .position-card {
            padding: var(--spacing-md);

            .player-section .player-avatar img {
                width: 50px;
                height: 50px;
            }

            .stats-section .mini-stats {
                gap: var(--spacing-xs);

                .stat-item .stat-value {
                    font-size: var(--text-xs);
                }
            }
        }
    }

    .players-list .players-list-container .player-card {
        grid-template-columns: 40px 40px 2fr 80px;
        gap: var(--spacing-sm);

        .player-stats {
            display: none;
        }
    }
}

@media (max-width: 768px) {
    .top-rating-container {
        padding: var(--spacing-sm);
        gap: var(--spacing-md);
    }

    .header-section .header-content {
        gap: var(--spacing-md);

        .header-icon {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .header-text {
            .header-title {
                font-size: var(--text-xl);
            }

            .header-subtitle {
                font-size: var(--text-xs);
            }
        }
    }

    .controls-section .controls-container {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;

        .control-group {
            align-items: stretch;

            .position-selector {
                width: 100%;
            }

            .games-input {
                width: 100%;
                max-width: 120px;
                align-self: center;
            }
        }

        .load-button {
            align-self: stretch;
            justify-content: center;
        }
    }

    .position-leaders-section .position-leaders-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);

        .position-card {
            padding: var(--spacing-md);

            .position-header {
                .position-icon {
                    width: 35px;
                    height: 35px;
                    font-size: 1rem;
                }

                .position-info .position-name {
                    font-size: var(--text-sm);
                }
            }

            .player-section .player-avatar img {
                width: 45px;
                height: 45px;
            }

            .stats-section {
                .rating-display {
                    min-width: 50px;

                    .rating-value {
                        font-size: var(--text-base);
                    }
                }

                .mini-stats {
                    gap: var(--spacing-xs);

                    .stat-item {
                        .stat-value {
                            font-size: var(--text-xs);
                        }

                        .stat-label {
                            font-size: 9px;
                        }
                    }
                }
            }
        }
    }

    .players-list .players-list-container {
        max-height: 300px;

        .player-card {
            grid-template-columns: 35px 35px 2fr 60px;
            padding: var(--spacing-sm);

            .player-avatar-small img {
                width: 30px;
                height: 30px;
            }

            .player-details {
                .player-name {
                    font-size: var(--text-sm);
                }

                .team-name {
                    font-size: var(--text-xs);
                }
            }

            .player-rating .rating-circle {
                width: 40px;
                height: 40px;

                .rating-value {
                    font-size: var(--text-sm);
                }
            }
        }
    }
}

@media (max-width: 480px) {
    .top-rating-container {
        margin: 0;
        border-radius: var(--radius-lg);
    }

    .position-leaders-section .position-leaders-grid .position-card {
        padding: var(--spacing-sm);

        .position-header {
            margin-bottom: var(--spacing-sm);

            .position-icon {
                width: 30px;
                height: 30px;
                font-size: 0.9rem;
            }

            .position-info .position-name {
                font-size: var(--text-xs);
            }
        }

        .player-section {
            margin-bottom: var(--spacing-sm);

            .player-avatar img {
                width: 40px;
                height: 40px;
            }

            .player-details {
                .player-name {
                    font-size: var(--text-sm);
                }

                .team-name {
                    font-size: var(--text-xs);
                }
            }
        }

        .stats-section {
            .rating-display {
                min-width: 45px;
                padding: var(--spacing-xs);

                .rating-value {
                    font-size: var(--text-sm);
                }

                .rating-label {
                    font-size: 9px;
                }
            }

            .mini-stats .stat-item {
                .stat-value {
                    font-size: 10px;
                }

                .stat-label {
                    font-size: 8px;
                }
            }
        }
    }

    .players-list .players-list-container .player-card {
        grid-template-columns: 30px 30px 2fr 50px;

        .player-rank .rank-number {
            font-size: var(--text-base);
        }

        .player-avatar-small img {
            width: 25px;
            height: 25px;
        }

        .player-rating .rating-circle {
            width: 35px;
            height: 35px;

            .rating-value {
                font-size: var(--text-xs);
            }
        }
    }
}

/* === EMPTY STATE === */
.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-2xl);
    min-height: 400px;
    width: 100%;

    .empty-state-content {
        text-align: center;
        max-width: 500px;
        background: var(--surface-primary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-2xl);
        padding: var(--spacing-2xl);
        box-shadow: var(--shadow-lg);

        .empty-state-icon {
            width: 80px;
            height: 80px;
            background: var(--surface-secondary);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-lg);
            border: 2px solid var(--border-primary);

            i {
                font-size: 2rem;
                color: var(--primary);
            }
        }

        .empty-state-title {
            font-family: var(--font-sans);
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-md) 0;
        }

        .empty-state-message {
            font-family: var(--font-sans);
            font-size: var(--text-base);
            color: var(--text-secondary);
            margin: 0 0 var(--spacing-lg) 0;
            line-height: 1.6;
        }

        .empty-state-suggestions {
            text-align: left;
            background: var(--surface-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);

            .suggestion-text {
                font-family: var(--font-sans);
                font-size: var(--text-sm);
                font-weight: var(--font-weight-semibold);
                color: var(--text-primary);
                margin: 0 0 var(--spacing-sm) 0;
            }

            .suggestion-list {
                margin: 0;
                padding-left: var(--spacing-lg);
                list-style-type: disc;

                li {
                    font-family: var(--font-sans);
                    font-size: var(--text-sm);
                    color: var(--text-secondary);
                    margin-bottom: var(--spacing-xs);
                    line-height: 1.5;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }

        .retry-button {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            background: var(--primary);
            color: var(--text-inverse);
            border: none;
            border-radius: var(--radius-lg);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: var(--font-sans);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 auto;

            &:hover {
                background: var(--primary-hover);
                transform: translateY(-2px);
                box-shadow: var(--shadow-md);
            }

            &:active {
                transform: translateY(0);
            }

            i {
                font-size: var(--text-sm);
            }
        }

        @media (max-width: 768px) {
            padding: var(--spacing-lg);
            max-width: 100%;

            .empty-state-icon {
                width: 60px;
                height: 60px;

                i {
                    font-size: 1.5rem;
                }
            }

            .empty-state-title {
                font-size: var(--text-lg);
            }

            .empty-state-message {
                font-size: var(--text-sm);
            }
        }
    }
}