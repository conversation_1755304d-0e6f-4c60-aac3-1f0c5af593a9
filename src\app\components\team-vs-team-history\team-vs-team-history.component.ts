import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { GameService } from '../../services/game.service';
import { TeamService } from '../../services/team.service';
import { GameDTO } from '@pro-clubs-manager/shared-dtos';
import { TeamDTO } from '@pro-clubs-manager/shared-dtos';
import { ExtendedGameDTO } from '../../shared/types/extended-game-dto';

export interface TeamVsTeamStats {
  team1Wins: number;
  team2Wins: number;
  draws: number;
  team1Goals: number;
  team2Goals: number;
  totalMatches: number;
  lastMeeting?: GameDTO;
}

@Component({
  selector: 'app-team-vs-team-history',
  templateUrl: './team-vs-team-history.component.html',
  styleUrls: ['./team-vs-team-history.component.scss']
})
export class TeamVsTeamHistoryComponent implements OnInit, OnChanges {
  @Input() team1Id: string = '';
  @Input() team2Id: string = '';
  @Input() limit: number = 10;
  @Input() showHeader: boolean = true;

  team1: TeamDTO | null = null;
  team2: TeamDTO | null = null;
  matchHistory: ExtendedGameDTO[] = [];
  stats: TeamVsTeamStats | null = null;
  isLoading: boolean = false;
  error: string | null = null;

  constructor(
    private gameService: GameService,
    private teamService: TeamService
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['team1Id'] || changes['team2Id'] || changes['limit']) {
      this.loadData();
    }
  }

  private async loadData(): Promise<void> {
    if (!this.team1Id || !this.team2Id) {
      return;
    }

    this.isLoading = true;
    this.error = null;

    try {
      // Load teams and match history in parallel
      const [team1, team2, history] = await Promise.all([
        this.teamService.getTeamById(this.team1Id),
        this.teamService.getTeamById(this.team2Id),
        this.gameService.getTeamVsTeamHistory(this.team1Id, this.team2Id, this.limit)
      ]);

      this.team1 = team1;
      this.team2 = team2;
      this.matchHistory = history as ExtendedGameDTO[];
      this.stats = this.calculateStats(this.matchHistory);
    } catch (error: any) {
      console.error('Error loading team vs team history:', error);
      this.error = 'Failed to load match history. Please try again.';
    } finally {
      this.isLoading = false;
    }
  }

  private calculateStats(games: ExtendedGameDTO[]): TeamVsTeamStats {
    let team1Wins = 0;
    let team2Wins = 0;
    let draws = 0;
    let team1Goals = 0;
    let team2Goals = 0;

    games.forEach(game => {
      if (!game.result) return;

      const isTeam1Home = game.homeTeam.id === this.team1Id;
      const homeGoals = game.result.homeTeamGoals;
      const awayGoals = game.result.awayTeamGoals;

      if (isTeam1Home) {
        team1Goals += homeGoals;
        team2Goals += awayGoals;
        if (homeGoals > awayGoals) team1Wins++;
        else if (awayGoals > homeGoals) team2Wins++;
        else draws++;
      } else {
        team1Goals += awayGoals;
        team2Goals += homeGoals;
        if (awayGoals > homeGoals) team1Wins++;
        else if (homeGoals > awayGoals) team2Wins++;
        else draws++;
      }
    });

    return {
      team1Wins,
      team2Wins,
      draws,
      team1Goals,
      team2Goals,
      totalMatches: games.length,
      lastMeeting: games.length > 0 ? games[0] : undefined
    };
  }

  getMatchResult(game: GameDTO, teamId: string): 'win' | 'draw' | 'loss' | null {
    if (!game.result) return null;

    const isHome = game.homeTeam.id === teamId;
    const homeGoals = game.result.homeTeamGoals;
    const awayGoals = game.result.awayTeamGoals;

    if (homeGoals === awayGoals) return 'draw';
    
    if (isHome) {
      return homeGoals > awayGoals ? 'win' : 'loss';
    } else {
      return awayGoals > homeGoals ? 'win' : 'loss';
    }
  }

  getScoreForTeam(game: GameDTO, teamId: string): { for: number; against: number } {
    if (!game.result) return { for: 0, against: 0 };

    const isHome = game.homeTeam.id === teamId;
    const homeGoals = game.result.homeTeamGoals;
    const awayGoals = game.result.awayTeamGoals;

    return isHome 
      ? { for: homeGoals, against: awayGoals }
      : { for: awayGoals, against: homeGoals };
  }

  formatDate(date: Date | string): string {
    const gameDate = new Date(date);
    return gameDate.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  getWinPercentage(wins: number, total: number): number {
    return total > 0 ? Math.round((wins / total) * 100) : 0;
  }

  refresh(): void {
    this.loadData();
  }

  trackByMatchId(index: number, match: GameDTO): string {
    return match.id || index.toString();
  }
}
