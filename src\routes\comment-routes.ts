import { Router } from "express";
import { CommentController } from "../controllers/comment-controller";
import { container } from "../config/container.config";
import { authenticateToken, optionalAuth } from "../middlewares/auth-middleware";

const router = Router();

const commentController = container.resolve(CommentController);

// Create comment for a game (requires auth)
router.post("/game/:gameId", authenticateToken, (req, res, next) =>
  commentController.createComment(req, res, next)
);

// Update a comment (requires auth)
router.put("/:commentId", authenticateToken, (req, res, next) =>
  commentController.updateComment(req, res, next)
);

// Delete a comment (requires auth)
router.delete("/:commentId", authenticateToken, (req, res, next) =>
  commentController.deleteComment(req, res, next)
);

// Get comments for a game (optional auth for like status)
router.get("/game/:gameId", optionalAuth, (req, res, next) =>
  commentController.getCommentsByGame(req, res, next)
);

// Like a comment (requires auth)
router.post("/:commentId/like", authenticateToken, (req, res, next) =>
  commentController.likeComment(req, res, next)
);

// Unlike a comment (requires auth)
router.delete("/:commentId/like", authenticateToken, (req, res, next) =>
  commentController.unlikeComment(req, res, next)
);

// Get user's comments (requires auth)
router.get("/user/my-comments", authenticateToken, (req, res, next) =>
  commentController.getUserComments(req, res, next)
);

export default router;
