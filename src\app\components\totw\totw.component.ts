import { Component } from '@angular/core';
import { LeagueService } from '../../services/league.service';
import { LEAGUE_ID } from '../../constants/constants';
import { FixtureDTO } from '../../shared/models/game.model';
import { ListOption } from '../../shared/models/list-option.model';
import { TeamFormation, FormationPlayer } from '../mini-pitch-formation/mini-pitch-formation.component';

interface TOTWWeek {
  startDate: Date;
  endDate: Date;
  firstFixture: number;
  lastFixture: number;
}

interface TOTWPlayerData {
  player?: {
    id?: string;
    playerId?: string;
    name?: string;
    imgUrl?: string;
    team?: {
      name?: string;
      imgUrl?: string;
    };
  };
  playerId?: string;
  name?: string;
  imgUrl?: string;
  team?: {
    name?: string;
    imgUrl?: string;
  };
  stats?: {
    avgRating?: number;
    goals?: number;
    assists?: number;
  };
  rating?: number;
  goals?: number;
  assists?: number;
}

@Component({
  selector: 'pro-clubs-totw',
  templateUrl: './totw.component.html',
  styleUrl: './totw.component.scss'
})
export class TotwComponent {
  dateFormat = 'dd/MM/yyyy';
  totw: TOTWPlayerData[] | null = null;
  fixtures: FixtureDTO[] | null = null;
  currentTOTW: number | null = null;
  isLoading = false;
  totwStartDate: Date | null = null;
  totwEndDate: Date | null = null;
  allTOTWWeeks: TOTWWeek[] = [];
  totwOptions: ListOption[] = [];
  defaultOption: ListOption | null = null;
  maxTotwAmount = 0;

  constructor(private leagueService: LeagueService) { }

  ngOnInit() {
    this.loadFixturesData();
  }

  async loadFixturesData() {
    const fixturesResponse = await this.leagueService.getPaginatedLeagueFixturesGames(LEAGUE_ID, 1, 42);
    this.fixtures = fixturesResponse.fixtures;
    this.loadTOTWSDates();
    this.loadTOTWOptions();
    this.getTOTWData();
  }

  private loadTOTWSDates() {
    if (!this.fixtures) return;

    let lastDate: Date | null = null;

    this.fixtures.forEach((fixture, index) => {
      if (lastDate !== fixture.startDate) {
        this.allTOTWWeeks.push({
          firstFixture: index + 1,
          lastFixture: index + 1,
          startDate: fixture.startDate,
          endDate: fixture.endDate
        });
      } else {
        this.allTOTWWeeks[this.allTOTWWeeks.length - 1].lastFixture = index + 1;
      }
      lastDate = fixture.startDate;
    });

    this.maxTotwAmount = this.allTOTWWeeks.length - 1;
    this.currentTOTW = this.maxTotwAmount;
  }

  private loadTOTWOptions() {
    for (let i = 0; i < this.maxTotwAmount; i++) {
      this.totwOptions.push({
        value: (i + 1).toString(),
        displayText: `TOTW ${i + 1}`
      });
    }

    this.defaultOption = this.totwOptions[this.totwOptions.length - 1];
  }

  async getTOTWData() {
    this.isLoading = true;

    if (!this.currentTOTW) {
      this.isLoading = false;
      return;
    }

    this.totwStartDate = this.allTOTWWeeks[this.currentTOTW - 1].startDate;
    this.totwEndDate = this.allTOTWWeeks[this.currentTOTW - 1].endDate;

    const response = await this.leagueService.getTOTW(LEAGUE_ID, this.totwStartDate.toString().slice(0, 10),
      this.totwEndDate.toString().slice(0, 10));

    this.totw = response.teamOfTheWeek as TOTWPlayerData[];
    this.isLoading = false;
  }

  onNextClick() {
    if (!this.currentTOTW || this.currentTOTW >= this.maxTotwAmount) {
      return;
    }

    this.currentTOTW++;
    this.getTOTWData();
  }

  onPrevClick() {
    if (!this.currentTOTW || this.currentTOTW <= 1) {
      return;
    }

    this.currentTOTW--;
    this.getTOTWData();
  }

  onSelectionChange(selectedOption: ListOption): void {
    if (!selectedOption || !selectedOption.value || this.currentTOTW === parseInt(selectedOption.value)) {
      return;
    }

    this.currentTOTW = parseInt(selectedOption.value);
    this.getTOTWData();
  }

  private getGoals(player: TOTWPlayerData): number {
    return player?.stats?.goals ?? player?.goals ?? 0;
  }

  private getAssists(player: TOTWPlayerData): number {
    return player?.stats?.assists ?? player?.assists ?? 0;
  }

  private getPositionByIndex(index: number): string {
    // TOTW positions for 3-5-2 formation
    const positions = ['GK', 'CB', 'CB', 'CB', 'CDM', 'CDM', 'LM', 'RM', 'CAM', 'ST', 'ST'];
    return positions[index] || 'SUB';
  }

  getTotwTeamFormation(): TeamFormation {
    if (!this.totw || this.totw.length === 0) {
      return {
        teamId: 'totw',
        teamName: 'Team of the Week',
        formation: '3-5-2',
        players: []
      };
    }

    const formationPlayers: FormationPlayer[] = this.totw.map((player, index) => {
      const playerId = player.player?.id || player.player?.playerId || player.playerId || `player-${index}`;

      return {
        id: playerId,
        name: player.player?.name || player.name || 'Unknown Player',
        position: this.getPositionByIndex(index),
        rating: player.stats?.avgRating || player.rating || 0,
        goals: this.getGoals(player),
        assists: this.getAssists(player),
        profileImage: player.player?.imgUrl || player.imgUrl,
        teamImage: player.player?.team?.imgUrl || player.team?.imgUrl,
        teamName: player.player?.team?.name || player.team?.name,
        jerseyNumber: index + 1
      };
    });

    return {
      teamId: 'totw',
      teamName: 'Team of the Week',
      formation: '3-5-2',
      players: formationPlayers
    };
  }
}