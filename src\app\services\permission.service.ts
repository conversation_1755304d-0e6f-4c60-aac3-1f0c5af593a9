import { Injectable } from '@angular/core';
import { Observable, of, from } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { AuthService, User } from './auth.service';
import { TeamService } from './team.service';
import { GameService } from './game.service';

@Injectable({
  providedIn: 'root'
})
export class PermissionService {

  constructor(
    private authService: AuthService,
    private teamService: TeamService,
    private gameService: GameService
  ) {}

  /**
   * Check if current user is admin
   */
  isAdmin(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => user?.role === 'admin' || false)
    );
  }

  /**
   * Check if current user can edit their own player profile
   */
  canEditPlayer(playerId: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) return false;
        if (user.role === 'admin') return true;
        return user.associatedPlayers?.includes(playerId) || false;
      })
    );
  }

  /**
   * Check if current user can edit a specific team
   */
  canEditTeam(teamId: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) return of(false);
        if (user.role === 'admin') return of(true);
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) return of(false);

        // Need to check if user is captain of this team
        return this.checkTeamCaptainStatusAsync(teamId, user);
      }),
      switchMap(result => result),
      catchError(() => of(false))
    );
  }

  /**
   * Check if current user can edit game stats
   */
  canEditGame(gameId: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) return of(false);
        if (user.role === 'admin') return of(true);
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) return of(false);

        // Need to check if user is captain of one of the teams in the game
        return this.checkGameEditPermissionAsync(gameId, user);
      }),
      switchMap(result => result),
      catchError(() => of(false))
    );
  }

  /**
   * Check if current user is captain of any team
   */
  isTeamCaptain(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) return of(false);
        if (user.role === 'admin') return of(true);
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) return of(false);

        return this.checkAnyCaptainStatusAsync(user);
      }),
      switchMap(result => result),
      catchError(() => of(false))
    );
  }

  /**
   * Check if current user can manage players (add/remove from teams)
   * Admin or team captain can manage players
   */
  canManagePlayers(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) return of(false);
        if (user.role === 'admin') return of(true);
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) return of(false);

        // Team captains can manage players for their teams
        return this.checkAnyCaptainStatusAsync(user);
      }),
      switchMap(result => result),
      catchError(() => of(false))
    );
  }

  /**
   * Check if current user can create/delete teams
   */
  canManageTeams(): Observable<boolean> {
    return this.isAdmin(); // Only admins can create/delete teams
  }

  /**
   * Check if current user can set team captains
   */
  canSetCaptains(): Observable<boolean> {
    return this.isAdmin(); // Only admins can set captains
  }

  /**
   * Get teams that the current user can edit
   */
  getEditableTeams(): Observable<string[]> {
    return this.authService.currentUser$.pipe(
      map(async user => {
        if (!user) return [];
        if (user.role === 'admin') {
          // Admin can edit all teams
          const allTeams = await this.teamService.getAllTeams();
          return allTeams.map(team => team.id);
        }
        if (!user.associatedPlayers || user.associatedPlayers.length === 0) return [];
        
        // Get teams where user is captain
        const allTeams = await this.teamService.getAllTeams();
        return allTeams
          .filter(team => team.captain && user.associatedPlayers!.includes(team.captain.id))
          .map(team => team.id);
      }),
      catchError(() => of([]))
    );
  }

  private checkTeamCaptainStatusAsync(teamId: string, user: User): Observable<boolean> {
    return from(this.teamService.getTeamById(teamId)).pipe(
      map(team => team.captain && user.associatedPlayers!.includes(team.captain.id)),
      catchError(() => of(false))
    );
  }

  private checkGameEditPermissionAsync(gameId: string, user: User): Observable<boolean> {
    return from(this.gameService.getGameById(gameId)).pipe(
      switchMap(game => {
        return from(Promise.all([
          this.teamService.getTeamById(game.homeTeam.id),
          this.teamService.getTeamById(game.awayTeam.id)
        ])).pipe(
          map(([homeTeam, awayTeam]) => {
            const isHomeCaptain = homeTeam.captain && user.associatedPlayers!.includes(homeTeam.captain.id);
            const isAwayCaptain = awayTeam.captain && user.associatedPlayers!.includes(awayTeam.captain.id);
            return isHomeCaptain || isAwayCaptain;
          })
        );
      }),
      catchError(() => of(false))
    );
  }

  private checkAnyCaptainStatusAsync(user: User): Observable<boolean> {
    return from(this.teamService.getAllTeams()).pipe(
      map(allTeams => allTeams.some(team =>
        team.captain && user.associatedPlayers!.includes(team.captain.id)
      )),
      catchError(() => of(false))
    );
  }
}
