import { Component } from '@angular/core';
import { PlayerService } from '../../services/player.service';
import { ActivatedRoute, Router } from '@angular/router';
import { TeamService } from '../../services/team.service';
import { PlayerSearchResultModel } from '../../shared/models/playerSearchResult.model';
import { PLAYER_SEARCH_RESULTS_COLUMNS } from './player-search.definitions';

@Component({
  selector: 'player-search',
  templateUrl: './player-search.component.html',
  styleUrl: './player-search.component.scss'
})
export class PlayerSearchComponent {
  searchText: string = '';
  isLoading: boolean = false;
  playerSearchResults: PlayerSearchResultModel[] | null = null;
  playerSearchResultsColumns = PLAYER_SEARCH_RESULTS_COLUMNS;

  constructor(private router: Router, private playerService: PlayerService, private teamService: TeamService) { }

  ngOnInit() {
  };

  onArrowBackClick() {
    history.back();
  }

  async onSearchClick() {
    if (this.searchText?.length < 3) {
      return;
    };
    
    this.isLoading = true;
    const response = await this.playerService.playerSearchByText(this.searchText);

    this.playerSearchResults = response.map(player => {
      return {
        playerId: player.id,
        position: player.position, tableIcon: { name: player.name, imgUrl: player.imgUrl!, isTeam: false },
        teamId: player.team?.id!, teamName: player.team?.name ? player.team?.name : 'Free Agent'
      }
    });

    this.searchText = '';
    this.isLoading = false;
  }

  onPlayerClick($playerDetails: PlayerSearchResultModel): void {
    this.router.navigate(['/player-details', { id: $playerDetails.playerId }])
  }
}