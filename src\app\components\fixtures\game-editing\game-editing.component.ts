import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnDestroy } from '@angular/core';
import { GameFixtureData } from '../../../shared/models/game.model';
import { GameService } from '../../../services/game.service';
import { NotificationService } from '../../../services/notification.service';
import { FixtureStateService } from '../../../services/state/fixture-state.service';
import { BackgroundSyncService } from '../../../services/background-sync.service';
import { AuthService } from '../../../services/auth.service';
import { PermissionsService } from '../../../services/permissions.service';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

export interface GameEditEvent {
  type: 'result-edit' | 'result-save' | 'result-cancel' | 'time-edit' | 'time-save' | 'time-cancel';
  game: GameFixtureData;
  data?: any;
}

@Component({
  selector: 'app-game-editing',
  templateUrl: './game-editing.component.html',
  styleUrls: ['./game-editing.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GameEditingComponent implements OnInit, OnDestroy {
  @Input() games: GameFixtureData[] = [];
  @Input() canEdit: boolean = false;
  @Input() isAdmin: boolean = false;
  
  @Output() gameEditEvent = new EventEmitter<GameEditEvent>();
  @Output() gameClicked = new EventEmitter<GameFixtureData>();

  // Editing state
  currentEditedGameId: string | null = null;
  currentEditedTimeGameId: string | null = null;
  homeTeamGoals: number = 0;
  awayTeamGoals: number = 0;
  editingGameTime: string = '';
  editingGameDate: string = '';

  private destroy$ = new Subject<void>();

  constructor(
    private gameService: GameService,
    private notificationService: NotificationService,
    private fixtureState: FixtureStateService,
    private backgroundSync: BackgroundSyncService,
    private authService: AuthService,
    private permissionsService: PermissionsService
  ) {}

  ngOnInit(): void {
    // Component initialization
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Game result editing methods
  onEditGameResultClick(game: GameFixtureData): void {
    // Initialize with existing result if available, otherwise start with 0-0
    if (game.result) {
      this.homeTeamGoals = game.result.homeTeamGoals;
      this.awayTeamGoals = game.result.awayTeamGoals;
    } else {
      this.homeTeamGoals = 0;
      this.awayTeamGoals = 0;
    }

    this.currentEditedGameId = game.id;
    this.gameEditEvent.emit({ type: 'result-edit', game });
  }

  async onSaveClick(game: GameFixtureData): Promise<void> {
    // Store original values for potential rollback
    const originalHomeGoals = game.result?.homeTeamGoals || 0;
    const originalAwayGoals = game.result?.awayTeamGoals || 0;

    // Set loading state for this specific game
    this.fixtureState.setGameUpdateState(game.id, 'updating');

    // Apply optimistic update immediately
    this.fixtureState.addOptimisticUpdate(game.id, this.homeTeamGoals, this.awayTeamGoals);
    
    // Show immediate success feedback
    this.notificationService.success(`Result: ${game.homeTeam.name} ${this.homeTeamGoals} : ${this.awayTeamGoals} ${game.awayTeam.name} updated successfully`);
    
    // Clear editing state immediately for better UX
    this.currentEditedGameId = null;
    const savedHomeGoals = this.homeTeamGoals;
    const savedAwayGoals = this.awayTeamGoals;
    this.homeTeamGoals = 0;
    this.awayTeamGoals = 0;

    try {
      const extendedGame = game as any;
      const isPlayoff = extendedGame.isPlayoff || false;
      
      // Perform server update in background
      await this.gameService.updateGameResult(game.id, savedHomeGoals, savedAwayGoals, new Date(), isPlayoff);
      
      // Mark as successfully updated
      this.fixtureState.setGameUpdateState(game.id, 'success');
      
      // Remove optimistic update since server confirmed the change
      this.fixtureState.removeOptimisticUpdate(game.id);
      
      // Trigger background sync for all related data
      this.backgroundSync.syncAfterGameUpdate(game.id);
      
      // Clear success state after a delay
      setTimeout(() => {
        this.fixtureState.setGameUpdateState(game.id, null);
      }, 2000);

      this.gameEditEvent.emit({ type: 'result-save', game, data: { homeGoals: savedHomeGoals, awayGoals: savedAwayGoals } });
      
    } catch (error) {
      console.error('Error updating game result:', error);
      
      // Rollback optimistic update
      this.fixtureState.rollbackOptimisticUpdate(game.id, originalHomeGoals, originalAwayGoals);
      
      // Set error state
      this.fixtureState.setGameUpdateState(game.id, 'error');
      
      // Show error notification
      this.notificationService.error('Failed to update game result. Changes have been reverted.');
      
      // Clear error state after a delay
      setTimeout(() => {
        this.fixtureState.setGameUpdateState(game.id, null);
      }, 3000);
    }
  }

  onCancelClick(): void {
    this.currentEditedGameId = null;
    this.homeTeamGoals = 0;
    this.awayTeamGoals = 0;
    this.gameEditEvent.emit({ type: 'result-cancel', game: this.getCurrentEditingGame()! });
  }

  // Game time editing methods
  onEditTimeClick(game: GameFixtureData): void {
    this.currentEditedTimeGameId = game.id;

    // Initialize with current game time or default to current time
    if (game.date) {
      const gameDate = new Date(game.date);
      this.editingGameDate = gameDate.toISOString().split('T')[0];
      this.editingGameTime = gameDate.toTimeString().split(' ')[0].substring(0, 5);
    } else {
      const now = new Date();
      this.editingGameDate = now.toISOString().split('T')[0];
      this.editingGameTime = now.toTimeString().split(' ')[0].substring(0, 5);
    }

    this.gameEditEvent.emit({ type: 'time-edit', game });
  }

  async onSaveTimeClick(game: GameFixtureData): Promise<void> {
    try {
      const combinedDateTime = new Date(`${this.editingGameDate}T${this.editingGameTime}:00`);
      await this.gameService.updateGameDate(game.id, combinedDateTime);

      // Update the game object
      game.date = combinedDateTime;
      this.notificationService.success('Game time updated successfully');
      this.onCancelTimeEdit();

      this.gameEditEvent.emit({ type: 'time-save', game, data: { date: combinedDateTime } });
    } catch (error) {
      console.error('Error updating game time:', error);
      this.notificationService.error('Failed to update game time');
    }
  }

  onCancelTimeEdit(): void {
    this.currentEditedTimeGameId = null;
    this.editingGameTime = '';
    this.editingGameDate = '';
    this.gameEditEvent.emit({ type: 'time-cancel', game: this.getCurrentTimeEditingGame()! });
  }

  // Helper methods
  onGameClick(game: GameFixtureData): void {
    this.gameClicked.emit(game);
  }

  isCurrentlyEditing(gameId: string): boolean {
    return this.currentEditedGameId === gameId;
  }

  isCurrentlyEditingTime(gameId: string): boolean {
    return this.currentEditedTimeGameId === gameId;
  }

  getGameUpdateState(gameId: string): 'updating' | 'success' | 'error' | null {
    return this.fixtureState.currentState.gameUpdateStates.get(gameId) || null;
  }

  canEditGame(gameId: string): Observable<boolean> {
    return this.permissionsService.canEditGame(gameId);
  }

  onHomeGoalsChange(value: number): void {
    this.homeTeamGoals = value;
  }

  onAwayGoalsChange(value: number): void {
    this.awayTeamGoals = value;
  }

  onEditingGameDateChange(value: string): void {
    this.editingGameDate = value;
  }

  onEditingGameTimeChange(value: string): void {
    this.editingGameTime = value;
  }

  private getCurrentEditingGame(): GameFixtureData | null {
    return this.games.find(game => game.id === this.currentEditedGameId) || null;
  }

  private getCurrentTimeEditingGame(): GameFixtureData | null {
    return this.games.find(game => game.id === this.currentEditedTimeGameId) || null;
  }

  trackByGameId(_index: number, game: GameFixtureData): string {
    return game.id;
  }
}
