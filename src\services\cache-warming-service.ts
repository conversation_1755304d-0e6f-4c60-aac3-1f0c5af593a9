import { inject, injectable } from "tsyringe";
import logger from "../config/logger";
import { IDashboardService } from "./dashboard-service";
import { ILeagueService } from "../interfaces/league/league-service.interface";
import { ILeagueRepository } from "../interfaces/league/league-repository.interface";
import { IGameService } from "../interfaces/game/game-service.interface";

@injectable()
export class CacheWarmingService {
  private warmingInterval: NodeJS.Timeout | null = null;
  private isWarming = false;

  constructor(
    @inject("IDashboardService") private dashboardService: IDashboardService,
    @inject("ILeagueService") private leagueService: ILeagueService,
    @inject("ILeagueRepository") private leagueRepository: ILeagueRepository,
    @inject("IGameService") private gameService: IGameService
  ) {}

  /**
   * Start the cache warming service
   * Warms cache immediately and then every 4 hours
   */
  start(): void {
    logger.info("CacheWarmingService: Starting cache warming service");

    // Warm cache immediately on startup
    this.warmDashboardCache();

    // Schedule cache warming every 4 hours (4 * 60 * 60 * 1000 ms)
    const FOUR_HOURS_MS = 4 * 60 * 60 * 1000;

    this.warmingInterval = setInterval(() => {
      this.warmDashboardCache();
    }, FOUR_HOURS_MS);

    logger.info("CacheWarmingService: Scheduled cache warming every 4 hours");
  }

  /**
   * Stop the cache warming service
   */
  stop(): void {
    if (this.warmingInterval) {
      clearInterval(this.warmingInterval);
      this.warmingInterval = null;
      logger.info("CacheWarmingService: Cache warming service stopped");
    }
  }

  /**
   * Manually trigger cache warming
   */
  async warmCacheNow(): Promise<void> {
    await this.warmDashboardCache();
  }

  /**
   * Warm all caches in the background
   */
  private async warmDashboardCache(): Promise<void> {
    if (this.isWarming) {
      logger.info("CacheWarmingService: Cache warming already in progress, skipping");
      return;
    }

    this.isWarming = true;
    logger.info("CacheWarmingService: Starting cache warming");

    try {
      // Add timeout to prevent infinite loops
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Cache warming timeout after 60 seconds')), 60000);
      });

      // Race between cache warming and timeout
      await Promise.race([
        this.warmAllCaches(),
        timeoutPromise
      ]);

      logger.info("CacheWarmingService: All caches warmed successfully");
    } catch (error) {
      logger.error("CacheWarmingService: Error warming caches:", error);
      // Don't throw error to prevent server crash
    } finally {
      this.isWarming = false;
    }
  }

  /**
   * Warm all caches including dashboard and league-specific caches
   */
  private async warmAllCaches(): Promise<void> {
    // First warm the dashboard cache
    await this.dashboardService.refreshDashboardCache();
    logger.info("CacheWarmingService: Dashboard cache warmed");

    // Warm global games cache
    await this.gameService.getAllGames();
    logger.info("CacheWarmingService: Games cache warmed");

    // Get all leagues to warm their individual caches
    const leagues = await this.leagueRepository.getAllLeagues();
    logger.info(`CacheWarmingService: Warming caches for ${leagues.length} leagues`);

    // Warm league-specific caches for each league
    for (const league of leagues) {
      try {
        await Promise.all([
          // Current season stats
          this.leagueService.getLeagueTable(league.id),
          this.leagueService.getTopScorers(league.id),
          this.leagueService.getTopAssists(league.id),

          // All-time stats
          this.leagueService.getAllTimeTopScorers(league.id),
          this.leagueService.getAllTimeTopAssisters(league.id),
          this.leagueService.getMostHattricks(league.id),
          this.leagueService.getMostCleanSheets(league.id),
          this.leagueService.getMostWinningPercentageTeams(league.id),
          this.leagueService.getMostWinningPercentagePlayers(league.id)
        ]);

        // Warm all-time top avg rating by position for common positions
        const commonPositions = ['ST', 'CAM', 'CM', 'CB', 'GK', 'Any'];
        const minimumGames = 50;

        await Promise.all(
          commonPositions.map(position =>
            this.leagueService.getAllTimeTopAvgRatingByPosition(league.id, position, minimumGames)
          )
        );

        logger.info(`CacheWarmingService: Warmed all caches for league ${league.name}`);
      } catch (error) {
        logger.error(`CacheWarmingService: Error warming caches for league ${league.name}:`, error);
        // Continue with other leagues
      }
    }
  }

  /**
   * Get cache warming status
   */
  getStatus(): {
    isRunning: boolean;
    isWarming: boolean;
    nextWarmingIn: number | null;
  } {
    const nextWarmingIn = this.warmingInterval ? 4 * 60 * 60 * 1000 : null;

    return {
      isRunning: this.warmingInterval !== null,
      isWarming: this.isWarming,
      nextWarmingIn
    };
  }
}

export interface ICacheWarmingService {
  start(): void;
  stop(): void;
  warmCacheNow(): Promise<void>;
  getStatus(): {
    isRunning: boolean;
    isWarming: boolean;
    nextWarmingIn: number | null;
  };
}
