<div class="pro-clubs-autocomplete-container" [ngStyle]="{width: width}">
    <div class="autocomplete-wrapper" [class.focused]="isFocused" [class.has-value]="selectedOption.value">
        <div class="input-container">
            <input
                type="text"
                [placeholder]="placeholder"
                [formControl]="selectedOption"
                (focus)="onFocus()"
                (blur)="onBlur()"
                [matAutocomplete]="auto"
                class="autocomplete-input"
                [attr.aria-label]="placeholder">

            <div class="input-icon" *ngIf="!isFocused && !selectedOption.value">
                <i class="fas fa-chevron-down"></i>
            </div>

            <div class="input-icon clear-icon" *ngIf="selectedOption.value" (click)="clearInput(); $event.stopPropagation()">
                <i class="fas fa-times"></i>
            </div>
        </div>

        <mat-autocomplete #auto="matAutocomplete" class="pro-clubs-autocomplete-panel">
            <mat-option
                *ngFor="let option of filteredOptions | async"
                [value]="option"
                class="autocomplete-option">
                <div class="option-content">
                    <i class="fas fa-check option-check" *ngIf="selectedOption.value === option"></i>
                    <span class="option-text">{{option}}</span>
                </div>
            </mat-option>
        </mat-autocomplete>
    </div>
</div>