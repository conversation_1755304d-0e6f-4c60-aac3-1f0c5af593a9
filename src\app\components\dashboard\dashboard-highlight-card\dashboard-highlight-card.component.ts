import { Component, Input } from '@angular/core';

export interface HighlightCardData {
  type: 'player' | 'team';
  title: string;
  name: string;
  imageUrl?: string;
  stats: {
    primary: {
      label: string;
      value: string | number;
    };
    secondary?: {
      label: string;
      value: string;
    };
  };
  badge?: {
    icon: string;
    color: string;
  };
}

@Component({
  selector: 'app-dashboard-highlight-card',
  templateUrl: './dashboard-highlight-card.component.html',
  styleUrl: './dashboard-highlight-card.component.scss'
})
export class DashboardHighlightCardComponent {
  @Input() data!: HighlightCardData;
  @Input() animationDelay: number = 0;

  getDefaultImage(): string {
    return this.data.type === 'player' ? 'assets/Icons/User.jpg' : 'assets/Icons/Team.jpg';
  }
}
