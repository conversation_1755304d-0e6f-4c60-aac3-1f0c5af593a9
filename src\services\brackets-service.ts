import { injectable, inject } from "tsyringe";
import { ClientSession, Types } from "mongoose";
import { IGameRepository } from "../interfaces/game";
import { ITeamRepository } from "../interfaces/team";
import { IFixtureRepository } from "../interfaces/fixture";
import { IGame, PLAYOFF_STAGE } from "../models/game/game";
import { ITeam } from "../models/team";
import logger from "../config/logger";

export interface BracketMatch {
  id: string;
  homeTeam: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  awayTeam: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  result?: {
    homeTeamGoals: number;
    awayTeamGoals: number;
  };
  status: string;
  date?: Date;
  playoffStage: PLAYOFF_STAGE;
  round?: number;
  matchNumber?: number;
  seriesFormat?: string;
  broadcast?: {
    streamUrl: string;
    broadcastingTeam: string;
  };
  // Add individual games for series matches
  matches?: BracketMatch[];
}

export interface BracketStage {
  stage: PLAYOFF_STAGE;
  displayName: string;
  matches: BracketMatch[];
  order: number;
  seriesFormat?: string;
}

export interface PlayoffBracket {
  seasonNumber: number;
  leagueId: string;
  stages: BracketStage[];
  isComplete: boolean;
  champion?: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  runnerUp?: {
    id: string;
    name: string;
    imgUrl?: string;
  };
  thirdPlace?: {
    id: string;
    name: string;
    imgUrl?: string;
  };
}

export interface IBracketsService {
  getPlayoffBracket(leagueId: string, seasonNumber?: number): Promise<PlayoffBracket>;
  getPlayoffMatches(leagueId: string, seasonNumber?: number): Promise<BracketMatch[]>;
}

@injectable()
export class BracketsService implements IBracketsService {
  constructor(
    @inject("IGameRepository") private gameRepository: IGameRepository,
    @inject("ITeamRepository") private teamRepository: ITeamRepository,
    @inject("IFixtureRepository") private fixtureRepository: IFixtureRepository
  ) {}

  async getPlayoffBracket(leagueId: string, seasonNumber?: number): Promise<PlayoffBracket> {
    console.log(`\n🏆 Getting playoff bracket for league ${leagueId}, season ${seasonNumber}...`);
    try {
      const matches = await this.getPlayoffMatches(leagueId, seasonNumber);
      const stages = this.organizeBracket(matches);
      
      // Determine championship results
      const finalMatch = matches.find(m => m.playoffStage === PLAYOFF_STAGE.FINAL && m.result);
      const thirdPlaceMatch = matches.find(m => m.playoffStage === PLAYOFF_STAGE.THIRD_PLACE && m.result);
      
      let champion, runnerUp, thirdPlace;
      
      if (finalMatch?.result) {
        const { homeTeamGoals, awayTeamGoals } = finalMatch.result;
        if (homeTeamGoals > awayTeamGoals) {
          champion = finalMatch.homeTeam;
          runnerUp = finalMatch.awayTeam;
        } else if (awayTeamGoals > homeTeamGoals) {
          champion = finalMatch.awayTeam;
          runnerUp = finalMatch.homeTeam;
        }
      }
      
      if (thirdPlaceMatch?.result) {
        const { homeTeamGoals, awayTeamGoals } = thirdPlaceMatch.result;
        if (homeTeamGoals > awayTeamGoals) {
          thirdPlace = thirdPlaceMatch.homeTeam;
        } else if (awayTeamGoals > homeTeamGoals) {
          thirdPlace = thirdPlaceMatch.awayTeam;
        }
      }

      const isComplete = this.calculateBracketCompletion(matches) === 100;
      const currentSeason = seasonNumber || new Date().getFullYear();

      return {
        seasonNumber: currentSeason,
        leagueId,
        stages,
        isComplete,
        champion,
        runnerUp,
        thirdPlace
      };
    } catch (error) {
      logger.error("Error getting playoff bracket:", error);
      throw error;
    }
  }

  async getPlayoffMatches(leagueId: string, seasonNumber?: number): Promise<BracketMatch[]> {
    try {
      const currentSeason = seasonNumber || new Date().getFullYear();

      // Get playoff fixtures instead of individual games
      const fixtures = await this.fixtureRepository.getPlayoffFixtures(leagueId, currentSeason);

      // Convert fixtures to bracket matches (series)
      const matches: BracketMatch[] = [];

      for (const fixture of fixtures) {
        if (fixture.isPlayoff && fixture.playoffDetails) {
          // Get all games for this fixture
          const fixtureGames = await this.gameRepository.getGamesByFixtureId((fixture._id as any).toString());

          if (fixtureGames.length > 0) {
            // Filter only completed games for series calculation
            const completedGames = fixtureGames.filter(game =>
              game.status === 'Played' || game.status === 'Completed'
            );

            // Calculate series score from completed games
            const seriesScore = this.calculateSeriesScore(completedGames);

            // Create a series-level bracket match using all fixture games (not just completed ones)
            const seriesMatch = this.mapFixtureToBracketMatch(fixture, fixtureGames, seriesScore);
            matches.push(seriesMatch);
          }
        }
      }

      logger.info(`Found ${matches.length} playoff series for league ${leagueId}, season ${currentSeason}`);
      return matches;
    } catch (error) {
      logger.error("Error getting playoff matches:", error);
      throw error;
    }
  }



  private mapFixtureToBracketMatch(fixture: any, games: any[], seriesScore: { homeWins: number; awayWins: number }): BracketMatch {
    const firstGame = games[0];

    return {
      id: fixture._id?.toString(),
      homeTeam: {
        id: firstGame.homeTeam._id?.toString() || firstGame.homeTeam.id,
        name: firstGame.homeTeam.name || 'Unknown Team',
        imgUrl: firstGame.homeTeam.imgUrl
      },
      awayTeam: {
        id: firstGame.awayTeam._id?.toString() || firstGame.awayTeam.id,
        name: firstGame.awayTeam.name || 'Unknown Team',
        imgUrl: firstGame.awayTeam.imgUrl
      },
      result: {
        homeTeamGoals: seriesScore.homeWins,
        awayTeamGoals: seriesScore.awayWins
      },
      status: this.getSeriesStatus(games, seriesScore),
      date: firstGame.date,
      playoffStage: fixture.playoffDetails?.stage || PLAYOFF_STAGE.QUARTER_FINAL,
      round: fixture.round,
      seriesFormat: fixture.playoffDetails?.format,
      broadcast: firstGame.broadcast,
      // Add individual games to the series match - this is the key fix!
      matches: games.map(game => ({
        id: game._id?.toString() || game.id,
        homeTeam: {
          id: game.homeTeam._id?.toString() || game.homeTeam.id,
          name: game.homeTeam.name || 'Unknown Team',
          imgUrl: game.homeTeam.imgUrl
        },
        awayTeam: {
          id: game.awayTeam._id?.toString() || game.awayTeam.id,
          name: game.awayTeam.name || 'Unknown Team',
          imgUrl: game.awayTeam.imgUrl
        },
        result: game.result ? {
          homeTeamGoals: game.result.homeTeamGoals || 0,
          awayTeamGoals: game.result.awayTeamGoals || 0
        } : undefined,
        status: game.status || 'Scheduled',
        date: game.date,
        playoffStage: game.playoffStage,
        round: game.round,
        broadcast: game.broadcast
      }))
    };
  }

  private calculateSeriesScore(games: any[]): { homeWins: number; awayWins: number } {
    if (games.length === 0) {
      return { homeWins: 0, awayWins: 0 };
    }

    // Get the series teams from the first game
    const firstGame = games[0];
    const seriesHomeTeamId = firstGame.homeTeam._id?.toString() || firstGame.homeTeam.id;
    const seriesAwayTeamId = firstGame.awayTeam._id?.toString() || firstGame.awayTeam.id;

    let homeWins = 0;
    let awayWins = 0;

    games.forEach((game) => {
      if (game.result && (game.status === 'Played' || game.status === 'Completed')) {
        const gameHomeTeamId = game.homeTeam._id?.toString() || game.homeTeam.id;
        const gameAwayTeamId = game.awayTeam._id?.toString() || game.awayTeam.id;

        // Determine which team won this game (including penalty shootouts)
        let winningTeamId = null;
        if (game.result.homeTeamGoals > game.result.awayTeamGoals) {
          winningTeamId = gameHomeTeamId;
        } else if (game.result.awayTeamGoals > game.result.homeTeamGoals) {
          winningTeamId = gameAwayTeamId;
        } else if (game.result.homeTeamGoals === game.result.awayTeamGoals) {
          // Handle penalty shootouts for draws
          if (game.result.penalties) {
            if (game.result.penalties.homeTeamPenalties > game.result.penalties.awayTeamPenalties) {
              winningTeamId = gameHomeTeamId;
            } else if (game.result.penalties.awayTeamPenalties > game.result.penalties.homeTeamPenalties) {
              winningTeamId = gameAwayTeamId;
            }
          }
          // If no penalty data, it remains a draw (no winner)
        }

        // Count the win for the correct series team
        if (winningTeamId === seriesHomeTeamId) {
          homeWins++;
        } else if (winningTeamId === seriesAwayTeamId) {
          awayWins++;
        }
      }
    });

    return { homeWins, awayWins };
  }

  private getSeriesStatus(games: any[], seriesScore: { homeWins: number; awayWins: number }): string {
    // Determine wins needed based on series format
    // For best-of-3: need 2 wins, for best-of-5: need 3 wins, for best-of-7: need 4 wins
    const winsNeeded = Math.ceil(games.length / 2);

    // Check if series is completed (one team has enough wins)
    if (seriesScore.homeWins >= winsNeeded || seriesScore.awayWins >= winsNeeded) {
      return 'Completed';
    }

    // Check if any games have been played
    const playedGames = games.filter(game =>
      game.status === 'Played' || game.status === 'Completed'
    ).length;

    if (playedGames > 0) {
      return 'In Progress';
    }

    return 'Scheduled';
  }

  private organizeBracket(matches: BracketMatch[]): BracketStage[] {
    const stageMap = new Map<PLAYOFF_STAGE, BracketMatch[]>();

    // Group matches by stage
    matches.forEach(match => {
      if (!stageMap.has(match.playoffStage)) {
        stageMap.set(match.playoffStage, []);
      }
      stageMap.get(match.playoffStage)!.push(match);
    });

    // Convert to bracket stages and sort
    const stages: BracketStage[] = Array.from(stageMap.entries()).map(([stage, stageMatches]) => {
      // Sort matches by match number within each stage
      const sortedMatches = stageMatches.sort((a, b) => (a.matchNumber || 0) - (b.matchNumber || 0));

      // Get series format from the first match in the stage
      const seriesFormat = sortedMatches[0]?.seriesFormat;

      return {
        stage,
        displayName: this.getStageDisplayName(stage),
        matches: sortedMatches,
        order: this.getStageOrder(stage),
        seriesFormat
      };
    });

    return stages.sort((a, b) => a.order - b.order);
  }

  private getStageOrder(stage: PLAYOFF_STAGE): number {
    const stageOrder = {
      [PLAYOFF_STAGE.PLAY_IN_ROUND]: 1,
      [PLAYOFF_STAGE.QUARTER_FINAL]: 2,
      [PLAYOFF_STAGE.SEMI_FINAL]: 3,
      [PLAYOFF_STAGE.THIRD_PLACE]: 4,
      [PLAYOFF_STAGE.FINAL]: 5,
      [PLAYOFF_STAGE.PROMOTION_PLAYOFF]: 6,
      [PLAYOFF_STAGE.RELEGATION_PLAYOFF]: 7
    };
    return stageOrder[stage] || 0;
  }

  private getStageDisplayName(stage: PLAYOFF_STAGE): string {
    const stageNames = {
      [PLAYOFF_STAGE.PLAY_IN_ROUND]: 'Play-in Round',
      [PLAYOFF_STAGE.QUARTER_FINAL]: 'Quarter-Finals',
      [PLAYOFF_STAGE.SEMI_FINAL]: 'Semi-Finals',
      [PLAYOFF_STAGE.THIRD_PLACE]: '3rd Place Match',
      [PLAYOFF_STAGE.FINAL]: 'Final',
      [PLAYOFF_STAGE.PROMOTION_PLAYOFF]: 'Promotion Playoff',
      [PLAYOFF_STAGE.RELEGATION_PLAYOFF]: 'Relegation Playoff'
    };
    return stageNames[stage] || stage;
  }

  private calculateBracketCompletion(matches: BracketMatch[]): number {
    const totalMatches = matches.length;
    const completedMatches = matches.filter(match => 
      match.status === 'COMPLETED' && match.result
    ).length;

    return totalMatches > 0 ? Math.round((completedMatches / totalMatches) * 100) : 0;
  }
}
