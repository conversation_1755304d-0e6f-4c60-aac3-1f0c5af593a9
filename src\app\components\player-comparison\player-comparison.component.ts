import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PlayerService } from '../../services/player.service';
import { TeamService } from '../../services/team.service';
import { PlayerComparisonData } from '../../shared/models/player-comparison.model';
import { PlayerDTO, TeamDTO } from '@pro-clubs-manager/shared-dtos';
import { LEAGUE_ID } from '../../constants/constants';

@Component({
  selector: 'app-player-comparison',
  templateUrl: './player-comparison.component.html',
  styleUrls: ['./player-comparison.component.scss']
})
export class PlayerComparisonComponent implements OnInit {
  player1Id!: string;
  player2Id?: string;
  
  teams: TeamDTO[] = [];
  selectedTeam?: TeamDTO;
  teamPlayers: PlayerDTO[] = [];
  selectedPlayer?: PlayerDTO;
  
  comparisonData?: PlayerComparisonData;
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // Step tracking
  currentStep: 'selectTeam' | 'selectPlayer' | 'comparison' = 'selectTeam';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private playerService: PlayerService,
    private teamService: TeamService
  ) {}

  async ngOnInit(): Promise<void> {
    this.route.params.subscribe(params => {
      this.player1Id = params['player1Id'];
      this.player2Id = params['player2Id'];
      
      if (this.player1Id && this.player2Id) {
        this.currentStep = 'comparison';
        this.loadComparison();
      } else if (this.player1Id) {
        this.currentStep = 'selectTeam';
        this.loadTeams();
      }
    });
  }

  async loadTeams(): Promise<void> {
    try {
      this.isLoading = true;
      this.hasError = false;
      this.teams = await this.teamService.getTeamsByLeagueId(LEAGUE_ID);
    } catch (error) {
      console.error('Error loading teams:', error);
      this.hasError = true;
      this.errorMessage = 'Failed to load teams';
    } finally {
      this.isLoading = false;
    }
  }

  async onTeamSelect(team: TeamDTO): Promise<void> {
    try {
      this.isLoading = true;
      this.hasError = false;
      this.selectedTeam = team;
      this.teamPlayers = await this.teamService.getPlayersByTeam(team.id);
      
      // Filter out the current player
      this.teamPlayers = this.teamPlayers.filter(player => player.id !== this.player1Id);
      
      this.currentStep = 'selectPlayer';
    } catch (error) {
      console.error('Error loading team players:', error);
      this.hasError = true;
      this.errorMessage = 'Failed to load team players';
    } finally {
      this.isLoading = false;
    }
  }

  onPlayerSelect(player: PlayerDTO): void {
    this.selectedPlayer = player;
    this.player2Id = player.id;
    this.router.navigate(['/player-comparison', this.player1Id, this.player2Id]);
  }

  async loadComparison(): Promise<void> {
    if (!this.player1Id || !this.player2Id) return;

    try {
      this.isLoading = true;
      this.hasError = false;
      this.comparisonData = await this.playerService.comparePlayersById(this.player1Id, this.player2Id);
    } catch (error) {
      console.error('Error loading comparison:', error);
      this.hasError = true;
      this.errorMessage = 'Failed to load player comparison';
    } finally {
      this.isLoading = false;
    }
  }

  goBack(): void {
    if (this.currentStep === 'selectPlayer') {
      this.currentStep = 'selectTeam';
      this.selectedTeam = undefined;
      this.teamPlayers = [];
    } else if (this.currentStep === 'comparison') {
      history.back();
    } else {
      history.back();
    }
  }

  startNewComparison(): void {
    this.currentStep = 'selectTeam';
    this.selectedTeam = undefined;
    this.teamPlayers = [];
    this.selectedPlayer = undefined;
    this.player2Id = undefined;
    this.comparisonData = undefined;
    this.loadTeams();
  }

  getDefaultPlayerImage(): string {
    return 'assets/Icons/User.jpg';
  }

  getDefaultTeamImage(): string {
    return 'assets/Icons/TeamLogo.jpg';
  }

  getDifferenceClass(difference: number): string {
    if (difference > 0) return 'positive';
    if (difference < 0) return 'negative';
    return 'neutral';
  }

  getDifferenceIcon(difference: number): string {
    if (difference > 0) return 'fas fa-arrow-up';
    if (difference < 0) return 'fas fa-arrow-down';
    return 'fas fa-equals';
  }

  formatDifference(difference: number, decimals: number = 0): string {
    const formatted = Math.abs(difference).toFixed(decimals);
    if (difference > 0) return `+${formatted}`;
    if (difference < 0) return `-${formatted}`;
    return formatted;
  }
}
