import { Router } from "express";
import { container } from "tsyringe";
import multer from "multer";
import { AIController } from "../controllers/ai-controller";
import { authenticateToken } from "../middlewares/auth-middleware";

const router = Router();
const aiController = container.resolve(AIController);

// Configure multer for image upload
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Routes
router.post(
  "/analyze-player-stats",
  authenticateToken,
  upload.single('image'),
  aiController.analyzePlayerStatsImage.bind(aiController)
);

router.get(
  "/capabilities",
  authenticateToken,
  aiController.getAnalysisCapabilities.bind(aiController)
);

export { router as aiRoutes };
