"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const mongoose_1 = require("mongoose");
const errors_1 = require("../../src/errors");
const mock_fixture_repository_1 = require("../../src/mocks/repositories/mock-fixture-repository");
const services_1 = require("../../src/services");
const mock_game_service_1 = require("../../src/mocks/services/mock-game-service");
describe("FixtureService", () => {
    let fixtureService;
    let fixtureRepository;
    let gameService;
    let session;
    beforeEach(() => {
        fixtureRepository = new mock_fixture_repository_1.MockFixtureRepository();
        gameService = new mock_game_service_1.MockGameService();
        fixtureService = new services_1.FixtureService(fixtureRepository, gameService);
        session = {};
    });
    describe("getFixtureById", () => {
        it("should return a fixture DTO", () => __awaiter(void 0, void 0, void 0, function* () {
            const fixtureId = new mongoose_1.Types.ObjectId();
            const fixture = { _id: fixtureId, games: [] };
            fixtureRepository.getFixtureById.mockResolvedValue(fixture);
            const result = yield fixtureService.getFixtureById(fixtureId.toString());
            expect(fixtureRepository.getFixtureById).toHaveBeenCalledWith(fixtureId);
            expect(result).toEqual(expect.objectContaining({ _id: fixtureId }));
        }));
    });
    describe("getPaginatedLeagueFixturesGames", () => {
        it("should return paginated fixtures", () => __awaiter(void 0, void 0, void 0, function* () {
            const leagueId = "60d5ec49b4dcd204d8e8bc17";
            const page = 1;
            const pageSize = 10;
            const totalFixtures = 20;
            const fixtures = [{ _id: new mongoose_1.Types.ObjectId() }];
            fixtureRepository.countFixturesByLeague.mockResolvedValue(totalFixtures);
            fixtureRepository.getFixturesByLeagueWithPagination.mockResolvedValue(fixtures);
            const result = yield fixtureService.getPaginatedLeagueFixturesGames(leagueId, page, pageSize);
            expect(fixtureRepository.countFixturesByLeague).toHaveBeenCalledWith(leagueId);
            expect(fixtureRepository.getFixturesByLeagueWithPagination).toHaveBeenCalledWith(leagueId, page, pageSize);
            expect(result).toEqual({
                fixtures: expect.any(Array),
                currentPage: page,
                totalPages: Math.ceil(totalFixtures / pageSize),
                totalFixtures: totalFixtures,
            });
        }));
        it("should throw BadRequestError for invalid page or pageSize", () => __awaiter(void 0, void 0, void 0, function* () {
            yield expect(fixtureService.getPaginatedLeagueFixturesGames("60d5ec49b4dcd204d8e8bc17", 0, 10)).rejects.toThrow(errors_1.BadRequestError);
            yield expect(fixtureService.getPaginatedLeagueFixturesGames("60d5ec49b4dcd204d8e8bc17", 1, 0)).rejects.toThrow(errors_1.BadRequestError);
        }));
        it("should throw NotFoundError if page exceeds total pages", () => __awaiter(void 0, void 0, void 0, function* () {
            fixtureRepository.countFixturesByLeague.mockResolvedValue(10);
            yield expect(fixtureService.getPaginatedLeagueFixturesGames("60d5ec49b4dcd204d8e8bc17", 2, 10)).rejects.toThrow(errors_1.NotFoundError);
        }));
    });
    describe("generateFixture", () => {
        it("should create a fixture and its games", () => __awaiter(void 0, void 0, void 0, function* () {
            const fixtureData = {
                leagueId: new mongoose_1.Types.ObjectId(),
                seasonNumber: 1,
                round: 1,
                startDate: new Date(),
                endDate: new Date(),
                gamesData: [],
            };
            const fixture = { _id: new mongoose_1.Types.ObjectId(), games: [] };
            const games = [{ _id: new mongoose_1.Types.ObjectId() }];
            fixtureRepository.createFixture.mockResolvedValue(fixture);
            gameService.createFixtureGames.mockResolvedValue(games);
            const result = yield fixtureService.generateFixture(fixtureData, session);
            expect(fixtureRepository.createFixture).toHaveBeenCalledWith(fixtureData.leagueId, fixtureData.seasonNumber, fixtureData.startDate, fixtureData.endDate, fixtureData.round, session);
            expect(gameService.createFixtureGames).toHaveBeenCalledWith(fixture._id, fixtureData.leagueId, fixtureData.seasonNumber, fixtureData.gamesData, session);
            expect(result).toEqual(expect.objectContaining({ games: games.map((game) => game._id) }));
        }));
    });
    describe("getLeagueFixtureGames", () => {
        it("should return games for a league fixture", () => __awaiter(void 0, void 0, void 0, function* () {
            const leagueId = "60d5ec49b4dcd204d8e8bc17";
            const round = 1;
            const fixture = { games: [new mongoose_1.Types.ObjectId()] };
            const games = [{ _id: new mongoose_1.Types.ObjectId() }];
            fixtureRepository.getLeagueFixture.mockResolvedValue(fixture);
            gameService.getGamesByIds.mockResolvedValue(games);
            const result = yield fixtureService.getLeagueFixtureGames(leagueId, round);
            expect(fixtureRepository.getLeagueFixture).toHaveBeenCalledWith(leagueId, round);
            expect(gameService.getGamesByIds).toHaveBeenCalledWith(fixture.games);
            expect(result).toEqual(games);
        }));
    });
    describe("deleteFixtures", () => {
        it("should delete fixtures and their games", () => __awaiter(void 0, void 0, void 0, function* () {
            const fixturesIds = [new mongoose_1.Types.ObjectId(), new mongoose_1.Types.ObjectId()];
            yield fixtureService.deleteFixtures(fixturesIds, session);
            //   expect(gameService.deleteFixturesGames).toHaveBeenCalledWith(fixturesIds, session);
            expect(fixtureRepository.deleteFixtures).toHaveBeenCalledWith(fixturesIds, session);
        }));
    });
});
