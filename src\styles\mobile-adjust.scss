/* === MODERN RESPONSIVE DESIGN === */

/* Desktop Styles */
@media (min-width: 769px) {
    .table-container {
        width: 60%;
    }

    .team-image-size {
        width: 4rem;
        height: 4rem;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 2px solid var(--border-primary);
    }

    .player-image-size {
        width: 3rem;
        height: 3rem;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 2px solid var(--border-primary);
    }

    .stat-icon-size {
        font-size: 1.25rem;
    }

    .player-photo-size {
        width: 8rem;
        height: 8rem;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 3px solid var(--border-primary);
    }

    .avatar-image {
        width: 120px !important;
        height: 120px !important;
    }
}

/* Tablet Styles */
@media (max-width: 768px) and (min-width: 481px) {
    .table-container {
        width: 100%;
    }

    .team-image-size {
        width: 3rem;
        height: 3rem;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 2px solid var(--border-primary);
    }

    .player-image-size {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 2px solid var(--border-primary);
    }

    .stat-icon-size {
        font-size: 1rem;
    }

    .player-photo-size {
        width: 6rem;
        height: 6rem;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 3px solid var(--border-primary);
    }

    .avatar-image {
        width: 100px !important;
        height: 100px !important;
    }
}

/* Mobile Styles */
@media (max-width: 480px) {
    .table-container {
        width: 100%;
    }

    .team-image-size {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 2px solid var(--border-primary);
    }

    .player-image-size {
        width: 2rem;
        height: 2rem;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 1px solid var(--border-primary);
    }

    .stat-icon-size {
        font-size: 0.875rem;
    }

    .player-photo-size {
        width: 5rem;
        height: 5rem;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 2px solid var(--border-primary);
    }

    .avatar-image {
        width: 80px !important;
        height: 80px !important;
    }

    /* Mobile-specific adjustments */
    .games-stats-row {
        grid-template-columns: 1fr 1fr !important;
        gap: var(--spacing-sm) !important;
    }

    .stat-card {
        padding: var(--spacing-md) !important;
        min-height: 80px !important;
    }

    .team-overall-area {
        gap: var(--spacing-md) !important;
    }

    .player-profile-card {
        padding: var(--spacing-md) !important;
    }
}