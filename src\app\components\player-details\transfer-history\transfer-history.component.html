<!-- === BEAUTIFUL TRANSFER HISTORY COMPONENT === -->
<div class="transfer-history-container">
  
  <!-- Section Header -->
  <div class="section-header">
    <div class="header-content">
      <div class="header-icon">
        <i class="fas fa-history"></i>
      </div>
      <div class="header-text">
        <h3 class="section-title">Transfer History</h3>
        <p class="section-subtitle">Complete career journey</p>
      </div>
    </div>
    <div class="header-badge" *ngIf="!isLoading && !hasError">
      <span class="transfer-count">{{transferHistory.length}}</span>
      <span class="transfer-label">Transfer{{transferHistory.length !== 1 ? 's' : ''}}</span>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-state" *ngIf="isLoading">
    <div class="loading-spinner">
      <i class="fas fa-spinner fa-spin"></i>
    </div>
    <p class="loading-text">Loading transfer history...</p>
  </div>

  <!-- Error State -->
  <div class="error-state" *ngIf="hasError && !isLoading">
    <div class="error-icon">
      <i class="fas fa-exclamation-triangle"></i>
    </div>
    <p class="error-text">Unable to load transfer history</p>
    <button class="retry-btn" (click)="loadTransferHistory()">
      <i class="fas fa-redo"></i>
      <span>Try Again</span>
    </button>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!isLoading && !hasError && transferHistory.length === 0">
    <div class="empty-icon">
      <i class="fas fa-user-clock"></i>
    </div>
    <p class="empty-text">No transfer history available</p>
    <p class="empty-subtext">This player hasn't made any transfers yet</p>
  </div>

  <!-- Transfer Timeline -->
  <div class="transfer-timeline" *ngIf="!isLoading && !hasError && transferHistory.length > 0">
    
    <div class="timeline-item" 
         *ngFor="let transfer of transferHistory; let i = index; let isLast = last"
         [ngClass]="'transfer-' + transfer.transferType">
      
      <!-- Timeline Connector -->
      <div class="timeline-connector" *ngIf="!isLast">
        <div class="connector-line"></div>
      </div>
      
      <!-- Transfer Card -->
      <div class="transfer-card">
        
        <!-- Transfer Header -->
        <div class="transfer-header">
          <div class="transfer-type-badge" [ngClass]="'badge-' + transfer.transferType">
            <i [class]="getTransferIcon(transfer.transferType)"></i>
            <span>{{getTransferTypeLabel(transfer.transferType)}}</span>
          </div>
          <div class="transfer-date">
            <span class="date-primary">{{transfer.formattedDate}}</span>
            <span class="date-secondary">{{transfer.timeAgo}}</span>
          </div>
        </div>

        <!-- Transfer Content -->
        <div class="transfer-content">
          
          <!-- Signing: Free Agent → Team -->
          <div class="transfer-flow" *ngIf="transfer.transferType === 'signing'">
            <div class="transfer-entity from-entity free-agent">
              <div class="entity-avatar">
                <img [src]="getFreeAgentImage()" alt="Free Agent" class="team-logo">
              </div>
              <div class="entity-info">
                <span class="entity-name">Free Agent</span>
                <span class="entity-type">Available</span>
              </div>
            </div>
            
            <div class="transfer-arrow signing-arrow">
              <div class="arrow-line"></div>
              <div class="arrow-icon">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
            
            <div class="transfer-entity to-entity team-entity" 
                 (click)="onTeamClick(transfer.toTeam!.id)">
              <div class="entity-avatar">
                <img [src]="transfer.toTeam!.imgUrl || getDefaultTeamImage()" 
                     [alt]="transfer.toTeam!.name" class="team-logo">
              </div>
              <div class="entity-info">
                <span class="entity-name">{{transfer.toTeam!.name}}</span>
                <span class="entity-type">Signed to</span>
              </div>
            </div>
          </div>

          <!-- Transfer: Team → Team -->
          <div class="transfer-flow" *ngIf="transfer.transferType === 'transfer'">
            <div class="transfer-entity from-entity team-entity" 
                 (click)="onTeamClick(transfer.fromTeam!.id)">
              <div class="entity-avatar">
                <img [src]="transfer.fromTeam!.imgUrl || getDefaultTeamImage()" 
                     [alt]="transfer.fromTeam!.name" class="team-logo">
              </div>
              <div class="entity-info">
                <span class="entity-name">{{transfer.fromTeam!.name}}</span>
                <span class="entity-type">From</span>
              </div>
            </div>
            
            <div class="transfer-arrow transfer-arrow-main">
              <div class="arrow-line"></div>
              <div class="arrow-icon">
                <i class="fas fa-exchange-alt"></i>
              </div>
            </div>
            
            <div class="transfer-entity to-entity team-entity" 
                 (click)="onTeamClick(transfer.toTeam!.id)">
              <div class="entity-avatar">
                <img [src]="transfer.toTeam!.imgUrl || getDefaultTeamImage()" 
                     [alt]="transfer.toTeam!.name" class="team-logo">
              </div>
              <div class="entity-info">
                <span class="entity-name">{{transfer.toTeam!.name}}</span>
                <span class="entity-type">To</span>
              </div>
            </div>
          </div>

          <!-- Release: Team → Free Agent -->
          <div class="transfer-flow" *ngIf="transfer.transferType === 'release'">
            <div class="transfer-entity from-entity team-entity" 
                 (click)="onTeamClick(transfer.fromTeam!.id)">
              <div class="entity-avatar">
                <img [src]="transfer.fromTeam!.imgUrl || getDefaultTeamImage()" 
                     [alt]="transfer.fromTeam!.name" class="team-logo">
              </div>
              <div class="entity-info">
                <span class="entity-name">{{transfer.fromTeam!.name}}</span>
                <span class="entity-type">Released from</span>
              </div>
            </div>
            
            <div class="transfer-arrow release-arrow">
              <div class="arrow-line"></div>
              <div class="arrow-icon">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
            
            <div class="transfer-entity to-entity free-agent">
              <div class="entity-avatar">
                <img [src]="getFreeAgentImage()" alt="Free Agent" class="team-logo">
              </div>
              <div class="entity-info">
                <span class="entity-name">Free Agent</span>
                <span class="entity-type">Available</span>
              </div>
            </div>
          </div>

        </div>

        <!-- Transfer Footer -->
        <div class="transfer-footer">
          <div class="season-info">
            <i class="fas fa-calendar-alt"></i>
            <span>Season {{transfer.seasonNumber}}</span>
          </div>
        </div>

      </div>
    </div>
  </div>

</div>
