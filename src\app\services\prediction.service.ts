import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

export enum PREDICTION_OUTCOME {
  HOME_WIN = "HOME_WIN",
  AWAY_WIN = "AWAY_WIN", 
  DRAW = "DRAW"
}

export interface PredictionDTO {
  id: string;
  gameId: string;
  userId: string;
  predictedOutcome: PREDICTION_OUTCOME;
  confidence: number;
  createdAt: Date;
  updatedAt: Date;
  user?: {
    id: string;
    name: string;
    profileImage?: string;
  };
}

export interface PredictionDistribution {
  gameId: string;
  totalPredictions: number;
  homeWinCount: number;
  awayWinCount: number;
  drawCount: number;
  homeWinPercentage: number;
  awayWinPercentage: number;
  drawPercentage: number;
  userPrediction?: {
    id: string;
    outcome: PREDICTION_OUTCOME;
    confidence: number;
    createdAt: Date;
  };
}

@Injectable({
  providedIn: 'root'
})
export class PredictionService {
  private readonly PREDICTIONS_CONTROLLER_URL = 'predictions';

  constructor(private apiService: ApiService) {}

  async createOrUpdatePrediction(
    gameId: string, 
    predictedOutcome: PREDICTION_OUTCOME, 
    confidence: number = 3
  ): Promise<PredictionDTO> {
    const response = await this.apiService.post<PredictionDTO>(
      `${this.PREDICTIONS_CONTROLLER_URL}/game/${gameId}`,
      { predictedOutcome, confidence }
    );
    return response.data;
  }

  async deletePrediction(predictionId: string): Promise<void> {
    await this.apiService.delete(`${this.PREDICTIONS_CONTROLLER_URL}/${predictionId}`);
  }

  async getPredictionDistribution(gameId: string): Promise<PredictionDistribution> {
    const response = await this.apiService.get<PredictionDistribution>(
      `${this.PREDICTIONS_CONTROLLER_URL}/game/${gameId}/distribution`
    );
    return response.data;
  }

  async getUserPredictions(limit?: number): Promise<PredictionDTO[]> {
    const params = limit ? `?limit=${limit}` : '';
    const response = await this.apiService.get<PredictionDTO[]>(
      `${this.PREDICTIONS_CONTROLLER_URL}/user/my-predictions${params}`
    );
    return response.data;
  }

  async getPredictionById(predictionId: string): Promise<PredictionDTO> {
    const response = await this.apiService.get<PredictionDTO>(
      `${this.PREDICTIONS_CONTROLLER_URL}/${predictionId}`
    );
    return response.data;
  }

  getPredictionOutcomeLabel(outcome: PREDICTION_OUTCOME): string {
    switch (outcome) {
      case PREDICTION_OUTCOME.HOME_WIN:
        return 'Home Win';
      case PREDICTION_OUTCOME.AWAY_WIN:
        return 'Away Win';
      case PREDICTION_OUTCOME.DRAW:
        return 'Draw';
      default:
        return 'Unknown';
    }
  }

  getPredictionOutcomeIcon(outcome: PREDICTION_OUTCOME): string {
    switch (outcome) {
      case PREDICTION_OUTCOME.HOME_WIN:
        return 'fas fa-home';
      case PREDICTION_OUTCOME.AWAY_WIN:
        return 'fas fa-plane';
      case PREDICTION_OUTCOME.DRAW:
        return 'fas fa-handshake';
      default:
        return 'fas fa-question';
    }
  }

  getConfidenceLabel(confidence: number): string {
    switch (confidence) {
      case 1:
        return 'Very Low';
      case 2:
        return 'Low';
      case 3:
        return 'Medium';
      case 4:
        return 'High';
      case 5:
        return 'Very High';
      default:
        return 'Medium';
    }
  }
}
