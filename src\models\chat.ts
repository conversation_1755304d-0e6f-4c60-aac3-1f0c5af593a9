import mongoose, { Document, Schema } from 'mongoose';

export interface IChatMessage extends Document {
  content: string;
  author: {
    id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    role: 'user' | 'admin';
  };
  timestamp: Date;
  edited: boolean;
  editedAt?: Date;
  replyTo?: string; // ID of message being replied to
  reactions: {
    emoji: string;
    users: string[]; // User IDs who reacted
  }[];
  isDeleted: boolean;
  deletedAt?: Date;
  deletedBy?: string;
}

const chatMessageSchema = new Schema<IChatMessage>(
  {
    content: { 
      type: String, 
      required: true,
      maxlength: 1000,
      trim: true
    },
    author: {
      id: { type: String, required: true },
      firstName: { type: String, required: true },
      lastName: { type: String, required: true },
      profilePicture: { type: String },
      role: { 
        type: String, 
        enum: ['user', 'admin'], 
        required: true 
      }
    },
    timestamp: { 
      type: Date, 
      default: Date.now,
      required: true
    },
    edited: { 
      type: Boolean, 
      default: false 
    },
    editedAt: { 
      type: Date 
    },
    replyTo: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: 'ChatMessage' 
    },
    reactions: [{
      emoji: { type: String, required: true },
      users: [{ type: String, required: true }]
    }],
    isDeleted: { 
      type: Boolean, 
      default: false 
    },
    deletedAt: { 
      type: Date 
    },
    deletedBy: { 
      type: String 
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    id: true
  }
);

// Index for efficient querying
chatMessageSchema.index({ timestamp: -1 });
chatMessageSchema.index({ 'author.id': 1 });
chatMessageSchema.index({ isDeleted: 1 });

const ChatMessage = mongoose.model<IChatMessage>('ChatMessage', chatMessageSchema);

export default ChatMessage;
