/* === NEWS EDIT MODAL === */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--spacing-lg);
    backdrop-filter: blur(4px);
}

.modal-container {
    background: var(--surface-primary);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-2xl);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* === MODAL HEADER === */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    background: var(--surface-secondary);
    border-bottom: 1px solid var(--border-primary);

    .modal-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;

        i {
            color: var(--primary-500);
        }
    }

    .close-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border: none;
        border-radius: var(--radius-lg);
        background: var(--surface-tertiary);
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background: var(--error-100);
            color: var(--error-600);
            transform: scale(1.05);
        }
    }
}

/* === MODAL BODY === */
.modal-body {
    padding: var(--spacing-xl);
    max-height: 60vh;
    overflow-y: auto;
}

/* === FORM STYLES === */
.form-group {
    margin-bottom: var(--spacing-lg);

    .form-label {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        font-size: var(--text-sm);

        i {
            color: var(--primary-500);
            font-size: var(--text-xs);
        }
    }

    .form-input,
    .form-select,
    .form-textarea {
        width: 100%;
        padding: var(--spacing-md);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        background: var(--surface-secondary);
        color: var(--text-primary);
        font-size: var(--text-sm);
        transition: all 0.2s ease;
        font-family: var(--font-sans);

        &:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background: var(--surface-primary);
        }

        &.error {
            border-color: var(--error-500);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        &::placeholder {
            color: var(--text-tertiary);
        }
    }

    .form-textarea {
        resize: vertical;
        min-height: 120px;
        line-height: 1.5;
    }

    .error-message {
        margin-top: var(--spacing-xs);
        color: var(--error-500);
        font-size: var(--text-xs);
        font-weight: var(--font-weight-medium);
    }
}

/* === MODAL FOOTER === */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    background: var(--surface-secondary);
    border-top: 1px solid var(--border-primary);

    .btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        border: none;
        border-radius: var(--radius-lg);
        font-weight: var(--font-weight-semibold);
        font-size: var(--text-sm);
        cursor: pointer;
        transition: all 0.2s ease;
        min-width: 120px;
        justify-content: center;

        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        &.btn-secondary {
            background: var(--surface-tertiary);
            color: var(--text-secondary);
            border: 1px solid var(--border-primary);

            &:hover:not(:disabled) {
                background: var(--surface-hover);
                color: var(--text-primary);
                transform: translateY(-1px);
            }
        }

        &.btn-primary {
            background: var(--primary-500);
            color: white;

            &:hover:not(:disabled) {
                background: var(--primary-600);
                transform: translateY(-1px);
                box-shadow: var(--shadow-lg);
            }
        }
    }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .modal-overlay {
        padding: var(--spacing-md);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-lg);
    }

    .modal-title {
        font-size: var(--text-lg);
    }

    .modal-footer {
        flex-direction: column;
        gap: var(--spacing-sm);

        .btn {
            width: 100%;
        }
    }
}
