<div class="prediction-voting" [class.compact]="compact">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <i class="fas fa-spinner fa-spin"></i>
    <span>Loading predictions...</span>
  </div>

  <!-- Prediction Content -->
  <div *ngIf="!isLoading" class="prediction-content">
    
    <!-- Header -->
    <div class="prediction-header" [class.hidden]="compact">
      <h3 class="prediction-title">
        <i class="fas fa-crystal-ball"></i>
        Match Predictions
      </h3>
      <div *ngIf="predictionDistribution" class="total-predictions">
        {{ predictionDistribution.totalPredictions }} prediction{{ predictionDistribution.totalPredictions !== 1 ? 's' : '' }}
      </div>
    </div>

    <!-- Toggle between Vote and Results -->
    <div class="view-toggle">
      <button class="toggle-btn"
              [class.active]="currentView === 'vote'"
              (click)="currentView = 'vote'">
        <i class="fas fa-vote-yea"></i>
        Vote
      </button>
      <button class="toggle-btn"
              [class.active]="currentView === 'results'"
              (click)="currentView = 'results'">
        <i class="fas fa-chart-bar"></i>
        Results
      </button>
    </div>

    <!-- Vote View -->
    <div *ngIf="currentView === 'vote' && game?.status === GAME_STATUS.SCHEDULED" class="voting-section">
      <div class="vote-count">{{ predictionDistribution?.totalPredictions || 0 }} vote{{ (predictionDistribution?.totalPredictions || 0) !== 1 ? 's' : '' }}</div>

      <div class="voting-buttons">
        <!-- Home Win -->
        <button class="vote-btn home-win"
                [class.selected]="getUserPredictionOutcome() === PREDICTION_OUTCOME.HOME_WIN"
                [disabled]="isVoting || !isAuthenticated"
                (click)="vote(PREDICTION_OUTCOME.HOME_WIN)"
                [title]="getVoteButtonTitle(PREDICTION_OUTCOME.HOME_WIN)">
          <img [src]="getTeamLogo(true)" [alt]="getTeamName(true)" class="team-logo">
        </button>

        <!-- Draw -->
        <button class="vote-btn draw"
                [class.selected]="getUserPredictionOutcome() === PREDICTION_OUTCOME.DRAW"
                [disabled]="isVoting || !isAuthenticated"
                (click)="vote(PREDICTION_OUTCOME.DRAW)"
                [title]="getVoteButtonTitle(PREDICTION_OUTCOME.DRAW)">
          <i class="fas fa-handshake"></i>
        </button>

        <!-- Away Win -->
        <button class="vote-btn away-win"
                [class.selected]="getUserPredictionOutcome() === PREDICTION_OUTCOME.AWAY_WIN"
                [disabled]="isVoting || !isAuthenticated"
                (click)="vote(PREDICTION_OUTCOME.AWAY_WIN)"
                [title]="getVoteButtonTitle(PREDICTION_OUTCOME.AWAY_WIN)">
          <img [src]="getTeamLogo(false)" [alt]="getTeamName(false)" class="team-logo">
        </button>
      </div>
    </div>

    <!-- Not Authenticated Message -->
    <div *ngIf="!isAuthenticated && game?.status === GAME_STATUS.SCHEDULED" class="auth-message">
      <i class="fas fa-sign-in-alt"></i>
      <span>Log in to make your prediction!</span>
    </div>

    <!-- Game Already Started Message -->
    <div *ngIf="game?.status !== GAME_STATUS.SCHEDULED" class="game-status-message">
      <i class="fas fa-lock"></i>
      <span>Predictions are closed for this match</span>
    </div>

    <!-- Results Section -->
    <div *ngIf="currentView === 'results' && predictionDistribution"
         class="results-section">
      <h4 class="results-title" *ngIf="!compact">Prediction Results</h4>
      <h4 class="results-title" *ngIf="compact">Results</h4>

      <!-- Show results bars when there are predictions -->
      <div *ngIf="predictionDistribution.totalPredictions > 0" class="results-bars">
        <!-- Home Win -->
        <div class="result-bar">
          <div class="result-header">
            <div class="team-info">
              <img [src]="getTeamLogo(true)" [alt]="getTeamName(true)" class="team-logo-small">
              <span class="team-name">{{ getTeamName(true) }} Win</span>
            </div>
            <span class="percentage">{{ predictionDistribution.homeWinPercentage }}%</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill home-win" 
                 [style.width.%]="predictionDistribution.homeWinPercentage">
            </div>
          </div>
          <div class="vote-count">{{ predictionDistribution.homeWinCount }} votes</div>
        </div>

        <!-- Draw -->
        <div class="result-bar">
          <div class="result-header">
            <div class="team-info">
              <i class="fas fa-handshake"></i>
              <span class="team-name">Draw</span>
            </div>
            <span class="percentage">{{ predictionDistribution.drawPercentage }}%</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill draw" 
                 [style.width.%]="predictionDistribution.drawPercentage">
            </div>
          </div>
          <div class="vote-count">{{ predictionDistribution.drawCount }} votes</div>
        </div>

        <!-- Away Win -->
        <div class="result-bar">
          <div class="result-header">
            <div class="team-info">
              <img [src]="getTeamLogo(false)" [alt]="getTeamName(false)" class="team-logo-small">
              <span class="team-name">{{ getTeamName(false) }} Win</span>
            </div>
            <span class="percentage">{{ predictionDistribution.awayWinPercentage }}%</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill away-win" 
                 [style.width.%]="predictionDistribution.awayWinPercentage">
            </div>
          </div>
          <div class="vote-count">{{ predictionDistribution.awayWinCount }} votes</div>
        </div>
      </div>

      <!-- No Predictions Yet -->
      <div *ngIf="predictionDistribution.totalPredictions === 0"
           class="no-predictions">
        <i class="fas fa-vote-yea"></i>
        <span *ngIf="!compact">No predictions yet. Be the first to predict!</span>
        <span *ngIf="compact">Be the first to vote!</span>
      </div>
    </div>

  </div>
</div>
