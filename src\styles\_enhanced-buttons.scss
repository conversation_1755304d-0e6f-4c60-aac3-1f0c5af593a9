/* === ENHANCED BUTTON STYLES === */

/* Base Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-weight: var(--font-weight-semibold);
    font-size: var(--text-sm);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    user-select: none;
    white-space: nowrap;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    &:hover::before {
        left: 100%;
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.3);
    }

    i {
        font-size: var(--text-sm);
    }
}

/* Primary Button */
.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-600) 100%);
    color: white;
    box-shadow: var(--shadow-lg);

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }

    &:active {
        transform: translateY(0);
        box-shadow: var(--shadow-md);
    }
}

/* Secondary Button */
.btn-secondary {
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--surface-tertiary) 0%, var(--surface-quaternary) 100%);
        border-color: var(--primary-300);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    &:active {
        transform: translateY(0);
        box-shadow: var(--shadow-sm);
    }
}

/* Success Button */
.btn-success {
    background: linear-gradient(135deg, var(--success) 0%, var(--success-600) 100%);
    color: white;
    box-shadow: var(--shadow-lg);

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--success-600) 0%, var(--success-700) 100%);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }

    &:active {
        transform: translateY(0);
        box-shadow: var(--shadow-md);
    }
}

/* Warning Button */
.btn-warning {
    background: linear-gradient(135deg, var(--warning) 0%, var(--warning-600) 100%);
    color: white;
    box-shadow: var(--shadow-lg);

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-700) 100%);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }

    &:active {
        transform: translateY(0);
        box-shadow: var(--shadow-md);
    }
}

/* Danger Button */
.btn-danger {
    background: linear-gradient(135deg, var(--danger) 0%, var(--danger-600) 100%);
    color: white;
    box-shadow: var(--shadow-lg);

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-700) 100%);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }

    &:active {
        transform: translateY(0);
        box-shadow: var(--shadow-md);
    }
}

/* Info Button */
.btn-info {
    background: linear-gradient(135deg, var(--info) 0%, var(--info-600) 100%);
    color: white;
    box-shadow: var(--shadow-lg);

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--info-600) 0%, var(--info-700) 100%);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }

    &:active {
        transform: translateY(0);
        box-shadow: var(--shadow-md);
    }
}

/* Outline Variants */
.btn-outline-primary {
    background: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);

    &:hover:not(:disabled) {
        background: var(--primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
}

.btn-outline-secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 2px solid var(--border-primary);

    &:hover:not(:disabled) {
        background: var(--surface-secondary);
        color: var(--text-primary);
        border-color: var(--primary);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
}

/* Size Variants */
.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-xs);
    border-radius: var(--radius-md);

    i {
        font-size: var(--text-xs);
    }
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--text-base);
    border-radius: var(--radius-xl);

    i {
        font-size: var(--text-base);
    }
}

.btn-xl {
    padding: var(--spacing-xl) var(--spacing-2xl);
    font-size: var(--text-lg);
    border-radius: var(--radius-2xl);

    i {
        font-size: var(--text-lg);
    }
}

/* Special Effects */
.btn-glow {
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.4), var(--shadow-lg);

    &:hover:not(:disabled) {
        box-shadow: 0 0 30px rgba(var(--primary-rgb), 0.6), var(--shadow-xl);
    }
}

.btn-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(var(--primary-rgb), 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
    }
}

/* Loading State */
.btn-loading {
    position: relative;
    color: transparent !important;

    &::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        color: white;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--text-xs);
    }

    .btn-lg {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-sm);
    }

    .btn-xl {
        padding: var(--spacing-lg) var(--spacing-xl);
        font-size: var(--text-base);
    }
}
