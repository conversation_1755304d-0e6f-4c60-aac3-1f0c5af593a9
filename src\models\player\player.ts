import mongoose, { Schema, Document } from "mongoose";
import { PopulatedPlayerWithTeam } from "./player-types";
import { ACHIEVEMENT_TYPE } from "../season-achievement";

export interface IPlayerStats {
  games: number;
  goals: number;
  assists: number;
  cleanSheets: number;
  playerOfTheMatch: number;
  avgRating: number;
}

export interface IPlayerSeason {
  seasonNumber: number;
  league: mongoose.Types.ObjectId;
  team: mongoose.Types.ObjectId;
  stats: IPlayerStats;
}

export interface ITransferHistoryEntry {
  fromTeam: {
    id: string;
    name: string;
    imgUrl?: string;
  } | null; // null for free agent
  toTeam: {
    id: string;
    name: string;
    imgUrl?: string;
  } | null; // null when player becomes free agent
  transferDate: Date;
  seasonNumber: number;
  league: mongoose.Types.ObjectId;
}

export interface IPlayerAchievementHistoryEntry {
  seasonNumber: number;
  league: mongoose.Types.ObjectId;
  leagueName: string;
  achievementType: ACHIEVEMENT_TYPE;
  rank?: number; // 1st, 2nd, 3rd for rankings
  teamId: mongoose.Types.ObjectId;
  teamName: string;
  stats: {
    goals?: number;
    assists?: number;
    cleanSheets?: number;
    avgRating?: number;
    games?: number;
    playerOfTheMatch?: number;
  };
  description?: string;
  achievedDate: Date;
}

export interface IPlayer extends Document {
  id: string;
  team: mongoose.Types.ObjectId | null;
  phone?: Number;
  email?: string;
  name: string;
  age: number;
  imgUrl?: string;
  position: string;
  playablePositions: string[];
  currentSeason?: IPlayerSeason;
  seasonsHistory: IPlayerSeason[];
  transferHistory: ITransferHistoryEntry[];
  achievementHistory: IPlayerAchievementHistoryEntry[];
}

const playerStatsSchema = new Schema(
  {
    games: { type: Number, default: 0, required: true },
    goals: { type: Number, default: 0, required: true },
    assists: { type: Number, default: 0, required: true },
    cleanSheets: { type: Number, default: 0, required: true },
    playerOfTheMatch: { type: Number, default: 0, required: true },
    avgRating: { type: Number, default: 0.0, required: true },
  },
  { _id: false }
); // Disable _id for this subdocument

const playerSeasonStatsSchema = new Schema(
  {
    seasonNumber: { type: Number, required: true },
    league: { type: mongoose.Types.ObjectId, ref: "League", required: true },
    team: { type: mongoose.Types.ObjectId, ref: "Team", required: true },
    stats: { type: playerStatsSchema },
  },
  { _id: false }
);

const transferHistorySchema = new Schema(
  {
    fromTeam: {
      type: {
        id: { type: String, required: true },
        name: { type: String, required: true },
        imgUrl: { type: String }
      },
      required: false,
      default: null
    },
    toTeam: {
      type: {
        id: { type: String, required: true },
        name: { type: String, required: true },
        imgUrl: { type: String }
      },
      required: false,
      default: null
    },
    transferDate: { type: Date, required: true, default: Date.now },
    seasonNumber: { type: Number, required: true },
    league: { type: mongoose.Types.ObjectId, ref: "League", required: true }
  },
  { _id: false }
);

const playerAchievementHistorySchema = new Schema(
  {
    seasonNumber: { type: Number, required: true },
    league: { type: mongoose.Types.ObjectId, ref: "League", required: true },
    leagueName: { type: String, required: true },
    achievementType: { type: String, enum: Object.values(ACHIEVEMENT_TYPE), required: true },
    rank: { type: Number, required: false },
    teamId: { type: mongoose.Types.ObjectId, ref: "Team", required: true },
    teamName: { type: String, required: true },
    stats: {
      goals: { type: Number, required: false },
      assists: { type: Number, required: false },
      cleanSheets: { type: Number, required: false },
      avgRating: { type: Number, required: false },
      games: { type: Number, required: false },
      playerOfTheMatch: { type: Number, required: false },
    },
    description: { type: String, required: false },
    achievedDate: { type: Date, required: true, default: Date.now },
  },
  { _id: false }
);

const playerSchema: Schema = new Schema(
  {
    team: { type: mongoose.Schema.Types.ObjectId, ref: "Team" },
    email: { type: String },
    phone: { type: Number },
    name: { type: String, required: true },
    age: { type: Number, required: true },
    position: { type: String, required: true },
    playablePositions: [{ type: String, required: true }],
    imgUrl: { type: String },
    currentSeason: playerSeasonStatsSchema,
    seasonsHistory: [playerSeasonStatsSchema],
    transferHistory: [transferHistorySchema],
    achievementHistory: [playerAchievementHistorySchema],
  },
  {
    toJSON: { virtuals: true },
    id: true, // Use 'id' instead of '_id'
  }
);

const Player = mongoose.model<IPlayer>("Player", playerSchema);

export { PopulatedPlayerWithTeam };

export default Player;
