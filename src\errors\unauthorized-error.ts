import { StatusCodes } from "http-status-codes";

export class UnauthorizedError extends Error {
  statusCode: number;

  constructor(message: string = "Unauthorized") {
    super(message);
    this.statusCode = StatusCodes.UNAUTHORIZED;
    this.name = "UnauthorizedError";

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, UnauthorizedError);
    }
  }
}
