.all-time-avg-rating-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .header-section {
    text-align: center;
    margin-bottom: 30px;

    .header-content {
      .header-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;

        i {
          color: var(--accent-color);
        }
      }

      .header-subtitle {
        font-size: 1.1rem;
        opacity: 0.8;
        margin: 0;
      }
    }
  }

  .controls-section, .filters-section {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--card-background);
    border-radius: 12px;
    border: 1px solid var(--border-color);

    .control-group, .filter-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-width: 200px;

      .control-label, .filter-label {
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--text-color);

        i {
          color: var(--accent-color);
        }
      }

      .control-select, .filter-select, .filter-input {
        padding: 12px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        background: var(--input-background);
        color: var(--text-color);
        font-size: 1rem;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: var(--accent-color);
          box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb), 0.1);
        }
      }
    }
  }

  .top-players-grid {
    margin-bottom: 40px;

    .grid-header {
      margin-bottom: 20px;

      .grid-title {
        font-size: 1.8rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 0;

        i {
          color: var(--accent-color);
        }
      }
    }

    .position-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;

      .position-card {
        background: var(--card-background);
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        cursor: pointer;

        &.has-player:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
          border-color: var(--accent-color);
        }

        .position-header {
          text-align: center;
          margin-bottom: 15px;

          .position-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--accent-color);
          }
        }

        .player-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;

          .player-image {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 15px;
            border: 3px solid var(--accent-color);
          }

          .player-info {
            .player-name {
              font-size: 1.1rem;
              font-weight: bold;
              margin: 0 0 5px 0;
              color: var(--text-color);
            }

            .team-name {
              font-size: 0.9rem;
              opacity: 0.8;
              margin: 0 0 10px 0;
            }

            .rating-badge {
              background: var(--accent-color);
              color: white;
              padding: 8px 16px;
              border-radius: 20px;
              font-weight: bold;
              font-size: 1.1rem;
            }
          }
        }

        .no-player {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 120px;
          opacity: 0.5;

          i {
            font-size: 2rem;
            margin-bottom: 10px;
          }
        }
      }
    }
  }

  .rankings-section {
    .rankings-header {
      margin-bottom: 25px;

      .rankings-title {
        font-size: 1.8rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 0 0 5px 0;

        i {
          color: var(--accent-color);
        }
      }

      .rankings-subtitle {
        opacity: 0.8;
        margin: 0;
      }
    }

    .players-list {
      display: flex;
      flex-direction: column;
      gap: 15px;

      .player-row {
        display: grid;
        grid-template-columns: 80px 1fr 200px 300px;
        gap: 20px;
        align-items: center;
        padding: 20px;
        background: var(--card-background);
        border: 2px solid var(--border-color);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
          border-color: var(--accent-color);
        }

        &.first-place {
          border-color: #FFD700;
          background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), var(--card-background));
        }

        &.second-place {
          border-color: #C0C0C0;
          background: linear-gradient(135deg, rgba(192, 192, 192, 0.1), var(--card-background));
        }

        &.third-place {
          border-color: #CD7F32;
          background: linear-gradient(135deg, rgba(205, 127, 50, 0.1), var(--card-background));
        }

        .rank-section {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .rank {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--accent-color);
          }

          .crown-icon {
            position: absolute;
            top: -10px;
            right: -5px;
            color: #FFD700;
            font-size: 1.2rem;
          }
        }

        .player-info {
          display: flex;
          align-items: center;
          gap: 15px;

          .player-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--accent-color);
          }

          .player-details {
            .player-name {
              font-size: 1.2rem;
              font-weight: bold;
              margin: 0 0 5px 0;
              color: var(--text-color);
            }

            .player-position {
              font-size: 0.9rem;
              opacity: 0.8;
              margin: 0;
              color: var(--accent-color);
            }
          }
        }

        .team-info {
          .team-name {
            font-weight: 600;
            color: var(--text-color);
            cursor: pointer;
            transition: color 0.3s ease;

            &:hover {
              color: var(--accent-color);
            }
          }
        }

        .stats-section {
          display: flex;
          align-items: center;
          gap: 20px;

          .primary-stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 15px;
            background: var(--accent-color);
            color: white;
            border-radius: 8px;

            .stat-value {
              font-size: 1.4rem;
              font-weight: bold;
            }

            .stat-label {
              font-size: 0.8rem;
              opacity: 0.9;
            }
          }

          .secondary-stats {
            display: flex;
            gap: 15px;

            .stat-item {
              display: flex;
              flex-direction: column;
              align-items: center;

              .stat-value {
                font-size: 1.1rem;
                font-weight: bold;
                color: var(--text-color);
              }

              .stat-label {
                font-size: 0.8rem;
                opacity: 0.7;
              }
            }
          }
        }
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-color);
    opacity: 0.7;

    i {
      font-size: 4rem;
      margin-bottom: 20px;
      color: var(--accent-color);
    }

    h3 {
      font-size: 1.5rem;
      margin: 0 0 10px 0;
    }

    p {
      margin: 0;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .all-time-avg-rating-container {
    padding: 15px;

    .header-section .header-content .header-title {
      font-size: 2rem;
    }

    .controls-section, .filters-section {
      flex-direction: column;
      gap: 15px;

      .control-group, .filter-group {
        min-width: auto;
      }
    }

    .top-players-grid .position-cards {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .rankings-section .players-list .player-row {
      grid-template-columns: 1fr;
      gap: 15px;
      text-align: center;

      .stats-section {
        justify-content: center;
      }
    }
  }
}
