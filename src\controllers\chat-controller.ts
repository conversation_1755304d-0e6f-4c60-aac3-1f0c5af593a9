import { Request, Response, NextFunction } from "express";
import { inject, injectable } from "tsyringe";
import { ChatService, CreateMessageRequest, EditMessageRequest, AddReactionRequest } from "../services/chat-service";
import { transactionService } from "../services/util-services/transaction-service";
import logger from "../config/logger";

@injectable()
export default class ChatController {
  constructor(
    @inject(ChatService) private chatService: ChatService
  ) {}

  async getMessages(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;

      const result = await this.chatService.getMessages(req.user.id, page, limit);

      res.json({
        success: true,
        data: result
      });
    } catch (error: any) {
      logger.error("Error in getMessages:", error);
      next(error);
    }
  }

  async createMessage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { content, replyTo } = req.body;

      if (!content || content.trim().length === 0) {
        res.status(400).json({ message: "Message content is required" });
        return;
      }

      if (content.trim().length > 1000) {
        res.status(400).json({ message: "Message content cannot exceed 1000 characters" });
        return;
      }

      const request: CreateMessageRequest = {
        content: content.trim(),
        replyTo
      };

      const message = await transactionService.withTransaction(async (session) => {
        return await this.chatService.createMessage(req.user!, request, session);
      });

      res.status(201).json({
        success: true,
        data: message
      });
    } catch (error: any) {
      logger.error("Error in createMessage:", error);
      next(error);
    }
  }

  async editMessage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { messageId } = req.params;
      const { content } = req.body;

      if (!content || content.trim().length === 0) {
        res.status(400).json({ message: "Message content is required" });
        return;
      }

      if (content.trim().length > 1000) {
        res.status(400).json({ message: "Message content cannot exceed 1000 characters" });
        return;
      }

      const request: EditMessageRequest = {
        messageId,
        content: content.trim()
      };

      const message = await transactionService.withTransaction(async (session) => {
        return await this.chatService.editMessage(req.user!.id, request, session);
      });

      res.json({
        success: true,
        data: message
      });
    } catch (error: any) {
      logger.error("Error in editMessage:", error);
      next(error);
    }
  }

  async deleteMessage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { messageId } = req.params;

      await transactionService.withTransaction(async (session) => {
        await this.chatService.deleteMessage(req.user!.id, req.user!.role, messageId, session);
      });

      res.json({
        success: true,
        message: "Message deleted successfully"
      });
    } catch (error: any) {
      logger.error("Error in deleteMessage:", error);
      next(error);
    }
  }

  async addReaction(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ message: "Authentication required" });
        return;
      }

      const { messageId } = req.params;
      const { emoji } = req.body;

      if (!emoji || emoji.trim().length === 0) {
        res.status(400).json({ message: "Emoji is required" });
        return;
      }

      // Basic emoji validation (you might want to use a more comprehensive emoji library)
      const emojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]$/u;
      if (!emojiRegex.test(emoji)) {
        res.status(400).json({ message: "Invalid emoji" });
        return;
      }

      const request: AddReactionRequest = {
        messageId,
        emoji: emoji.trim()
      };

      const message = await transactionService.withTransaction(async (session) => {
        return await this.chatService.addReaction(req.user!.id, request, session);
      });

      res.json({
        success: true,
        data: message
      });
    } catch (error: any) {
      logger.error("Error in addReaction:", error);
      next(error);
    }
  }
}
