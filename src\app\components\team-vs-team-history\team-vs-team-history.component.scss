/* === TEAM VS TEAM HISTORY COMPONENT === */
.team-vs-team-history {
    width: 100%;
    background: var(--surface-primary);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

/* === HEADER === */
.history-header {
    background: var(--surface-secondary);
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-primary);
}

.teams-matchup {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);

    .team-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        flex: 1;
        max-width: 200px;

        &:first-child {
            justify-content: flex-end;
        }

        &:last-child {
            justify-content: flex-start;
        }

        .team-logo {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-lg);
            object-fit: cover;
            border: 2px solid var(--border-primary);
        }

        .team-name {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
        }
    }

    .vs-divider {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        border-radius: 50%;
        box-shadow: var(--shadow-md);

        .vs-text {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            color: white;
        }
    }
}

.overall-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);

    .stat-card {
        background: var(--surface-primary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        text-align: center;
        transition: all 0.2s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stat-value {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
        }

        &.team1-stat {
            border-color: var(--primary-300);
            background: var(--primary-50);

            .stat-value {
                color: var(--primary-600);
            }
        }

        &.team2-stat {
            border-color: var(--secondary-300);
            background: var(--secondary-50);

            .stat-value {
                color: var(--secondary-600);
            }
        }

        &.draw-stat {
            border-color: var(--warning-300);
            background: var(--warning-50);

            .stat-value {
                color: var(--warning-600);
            }
        }
    }
}

/* === LOADING & ERROR STATES === */
.loading-state,
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
    color: var(--text-primary);

    .loading-spinner,
    .error-icon {
        font-size: 3rem;
        margin-bottom: var(--spacing-lg);
        color: var(--primary);
    }

    .error-icon {
        color: var(--error-500);
    }

    p {
        font-size: var(--text-lg);
        margin-bottom: var(--spacing-lg);
        color: var(--text-primary);
    }

    .retry-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        background: var(--primary-500);
        color: white;
        border: none;
        border-radius: var(--radius-lg);
        font-weight: var(--font-weight-semibold);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background: var(--primary-600);
            transform: translateY(-1px);
        }
    }
}

/* === NO MATCHES === */
.no-matches {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;

    .no-matches-icon {
        font-size: 4rem;
        color: var(--text-tertiary);
        margin-bottom: var(--spacing-lg);
    }

    h3 {
        font-size: var(--text-xl);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    p {
        color: var(--text-secondary);
        font-size: var(--text-base);
    }
}

/* === MATCH HISTORY === */
.match-history {
    padding: var(--spacing-xl);
}

.matches-header {
    margin-bottom: var(--spacing-lg);

    h3 {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;

        i {
            color: var(--primary-500);
        }
    }
}

.matches-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.match-item {
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    transition: all 0.2s ease;

    &:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
        border-color: var(--primary-300);
    }

    &.latest-match {
        border-color: var(--primary-500);
        background: var(--primary-50);
        box-shadow: var(--shadow-md);
    }

    .match-date {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin-bottom: var(--spacing-md);

        i {
            color: var(--primary-500);
        }
    }

    .match-teams {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        align-items: center;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-md);

        .home-team,
        .away-team {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            transition: all 0.2s ease;

            &.winner {
                color: var(--success-600);
                font-weight: var(--font-weight-bold);
            }

            &.loser {
                color: var(--text-tertiary);
                opacity: 0.7;
            }

            .team-logo-small {
                width: 32px;
                height: 32px;
                border-radius: var(--radius-md);
                object-fit: cover;
                border: 1px solid var(--border-primary);
            }

            .team-name-small {
                font-size: var(--text-base);
                font-weight: var(--font-weight-medium);
            }
        }

        .away-team {
            flex-direction: row-reverse;
        }

        .match-score {
            text-align: center;

            .score-display {
                font-size: var(--text-xl);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
                padding: var(--spacing-sm) var(--spacing-md);
                background: var(--surface-primary);
                border-radius: var(--radius-lg);
                border: 1px solid var(--border-primary);
            }

            .no-result {
                font-size: var(--text-sm);
                color: var(--text-tertiary);
                font-style: italic;
            }
        }
    }

    .match-details {
        margin-bottom: var(--spacing-sm);

        .playoff-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            background: var(--warning-100);
            color: var(--warning-700);
            border-radius: var(--radius-md);
            font-size: var(--text-xs);
            font-weight: var(--font-weight-bold);
            border: 1px solid var(--warning-300);

            i {
                font-size: 10px;
            }
        }
    }

    .result-indicators {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);

        .team-result {
            text-align: center;
            padding: var(--spacing-xs);
            border-radius: var(--radius-md);
            font-size: var(--text-xs);
            font-weight: var(--font-weight-bold);
            text-transform: uppercase;

            &.win {
                background: var(--success-100);
                color: var(--success-700);
                border: 1px solid var(--success-300);
            }

            &.draw {
                background: var(--warning-100);
                color: var(--warning-700);
                border: 1px solid var(--warning-300);
            }

            &.loss {
                background: var(--error-100);
                color: var(--error-700);
                border: 1px solid var(--error-300);
            }
        }
    }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .history-header {
        padding: var(--spacing-lg);
    }

    .teams-matchup {
        flex-direction: column;
        gap: var(--spacing-lg);

        .team-info {
            max-width: none;
            justify-content: center !important;

            .team-name {
                font-size: var(--text-base);
            }
        }

        .vs-divider {
            width: 50px;
            height: 50px;

            .vs-text {
                font-size: var(--text-base);
            }
        }
    }

    .overall-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .match-history {
        padding: var(--spacing-lg);
    }

    .match-item {
        padding: var(--spacing-md);

        .match-teams {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
            text-align: center;

            .home-team,
            .away-team {
                justify-content: center;
            }

            .away-team {
                flex-direction: row;
            }
        }

        .result-indicators {
            grid-template-columns: 1fr;
        }
    }
}
