import { Component, OnInit, <PERSON><PERSON><PERSON>roy, HostListener } from '@angular/core';
import { NavItem } from '../../shared/models/nav-item.model';
import { NAVBAR_ITEMS, ADMIN_NAVBAR_ITEMS } from './navbar.definitions';
import { ConfigurationService } from '../../services/configuration.service';
import { ThemeService, Theme } from '../../services/theme.service';
import { AuthService, User } from '../../services/auth.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss']
})
export class NavbarComponent implements OnInit, OnDestroy {
  navbarItems: NavItem[] = NAVBAR_ITEMS;
  isDarkMode: boolean = true;
  currentTheme: Theme = 'dark';
  currentUser: User | null = null;
  isAuthenticated = false;
  isMobileMenuOpen = false;
  isUserDropdownOpen = false;
  openDropdownIndex: number | null = null;
  linkedPlayer: any = null;

  private themeSubscription?: Subscription;
  private userSubscription?: Subscription;
  private authSubscription?: Subscription;

  constructor(
    private themeService: ThemeService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeSubscription = this.themeService.currentTheme$.subscribe(theme => {
      this.currentTheme = theme;
      this.isDarkMode = theme === 'dark';
    });

    // Subscribe to authentication state
    this.authSubscription = this.authService.isAuthenticated$.subscribe(isAuth => {
      this.isAuthenticated = isAuth;
    });

    // Subscribe to current user
    this.userSubscription = this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      this.updateNavbarItems();
      if (user) {
        this.loadLinkedPlayer();
      } else {
        this.linkedPlayer = null;
      }
    });

    // Listen to system theme changes
    this.themeService.listenToSystemThemeChanges();
  }

  ngOnDestroy(): void {
    this.themeSubscription?.unsubscribe();
    this.userSubscription?.unsubscribe();
    this.authSubscription?.unsubscribe();
  }

  /**
   * Update navbar items based on user role
   */
  private updateNavbarItems(): void {
    if (this.currentUser?.role === 'admin') {
      this.navbarItems = [...NAVBAR_ITEMS, ...ADMIN_NAVBAR_ITEMS];
    } else {
      this.navbarItems = NAVBAR_ITEMS;
    }
  }

  /**
   * Toggle mobile menu
   */
  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  /**
   * Toggle user dropdown
   */
  toggleUserDropdown(): void {
    this.isUserDropdownOpen = !this.isUserDropdownOpen;
    // Close nav dropdowns when user dropdown opens
    if (this.isUserDropdownOpen) {
      this.openDropdownIndex = null;
    }
  }

  /**
   * Toggle navigation dropdown
   */
  toggleNavDropdown(index: number): void {
    if (this.openDropdownIndex === index) {
      this.openDropdownIndex = null;
    } else {
      this.openDropdownIndex = index;
      // Close user dropdown when nav dropdown opens
      this.isUserDropdownOpen = false;
    }
  }

  /**
   * Check if dropdown is open
   */
  isDropdownOpen(index: number): boolean {
    return this.openDropdownIndex === index;
  }

  /**
   * Close all dropdowns
   */
  closeAllDropdowns(): void {
    this.openDropdownIndex = null;
    this.isUserDropdownOpen = false;
  }

  /**
   * Close mobile menu (for navigation)
   */
  closeMobileMenu(): void {
    this.isMobileMenuOpen = false;
    this.closeAllDropdowns();
  }

  /**
   * Handle clicks outside to close dropdowns
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const navbar = target.closest('.navbar');

    if (!navbar) {
      this.closeAllDropdowns();
      this.isMobileMenuOpen = false;
    }
  }

  /**
   * Toggle between dark and light mode
   */
  toggleDarkMode(): void {
    this.themeService.toggleTheme();
  }

  /**
   * Set light theme
   */
  setLightTheme(): void {
    this.themeService.setLightTheme();
  }

  /**
   * Set dark theme
   */
  setDarkTheme(): void {
    this.themeService.setDarkTheme();
  }

  /**
   * Reset to system theme
   */
  resetToSystemTheme(): void {
    this.themeService.resetToSystemTheme();
  }

  /**
   * Logout user
   */
  logout(): void {
    this.authService.logout();
  }

  /**
   * Check if user is admin
   */
  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  /**
   * Load the user's linked player
   */
  private async loadLinkedPlayer(): Promise<void> {
    if (!this.currentUser || !this.currentUser.associatedPlayers || this.currentUser.associatedPlayers.length === 0) {
      this.linkedPlayer = null;
      return;
    }

    try {
      const associatedPlayers = await this.authService.getAssociatedPlayers();
      // For now, we'll take the first associated player as the "linked" player
      // In the future, this could be enhanced to allow users to select their primary player
      this.linkedPlayer = associatedPlayers.length > 0 ? associatedPlayers[0] : null;
    } catch (error) {
      console.error('Error loading linked player:', error);
      this.linkedPlayer = null;
    }
  }

  /**
   * Navigate to linked player profile
   */
  navigateToLinkedPlayer(): void {
    if (this.linkedPlayer) {
      this.closeMobileMenu();
      // Navigate to player details page
      window.location.href = `/player/${this.linkedPlayer.id}`;
    }
  }

  /**
   * Handle image error by setting default avatar
   */
  onImageError(event: Event): void {
    const target = event.target as HTMLImageElement;
    target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTQiIGZpbGw9IiM0Nzc1NjkiLz4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxMSIgcj0iNCIgZmlsbD0iI2Y5ZmFmYiIvPgo8cGF0aCBkPSJNNiAyMmMwLTQuNCAzLjYtOCA4LThzOCAzLjYgOCA4IiBmaWxsPSIjZjlmYWZiIi8+Cjwvc3ZnPgo=';
  }
}
