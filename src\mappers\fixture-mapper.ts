import { FixtureDTO, GameFixtureData } from "@pro-clubs-manager/shared-dtos";
import { IFixture } from "../models/fixture";
import { PlayoffDetails } from "../models/game/game";

// Extended FixtureDTO to include playoff fields
interface ExtendedFixtureDTO extends FixtureDTO {
  isPlayoff?: boolean;
  playoffDetails?: PlayoffDetails;
}

// type GameFixtureData = {

// }

export class FixtureMapper {
  static async mapToDto(fixture: IFixture): Promise<ExtendedFixtureDTO> {
    if (!fixture) {
      throw new Error("fixture object is null or undefined");
    }

    const { games } = await fixture.populate<{ games: GameFixtureData[] }>({
      path: "games",
      select: "id homeTeam awayTeam result status date isPlayoff playoffStage matchNumber",
      populate: {
        path: "homeTeam awayTeam",
        select: "id name imgUrl",
      },
    });

    return {
      id: fixture.id,
      round: fixture.round,
      leagueId: fixture.league._id.toString(),
      startDate: fixture.startDate,
      endDate: fixture.endDate,
      isPlayoff: fixture.isPlayoff,
      playoffDetails: fixture.playoffDetails,
      games: games.map((game) => {
        const gameWithPlayoff = game as any; // Cast to access playoff properties
        return {
          id: game.id,
          homeTeam: {
            id: game.homeTeam.id,
            name: game.homeTeam.name,
            imgUrl: game.homeTeam.imgUrl,
          },
          awayTeam: {
            id: game.awayTeam.id,
            name: game.awayTeam.name,
            imgUrl: game.awayTeam.imgUrl,
          },
          result: game.result && { homeTeamGoals: game.result?.homeTeamGoals, awayTeamGoals: game.result?.awayTeamGoals },
          status: game.status,
          date: game.date,
          isPlayoff: gameWithPlayoff.isPlayoff,
          playoffStage: gameWithPlayoff.playoffStage,
          matchNumber: gameWithPlayoff.matchNumber,
        };
      }),
    } as ExtendedFixtureDTO;
  }

  static async mapToDtos(fixtures: IFixture[]): Promise<ExtendedFixtureDTO[]> {
    return await Promise.all(fixtures.map((fixture) => this.mapToDto(fixture)));
  }
}
