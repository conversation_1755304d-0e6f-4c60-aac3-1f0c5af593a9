import { NextFunction, Request, Response } from "express";
import CustomError from "../errors/custom-error";
import logger from "../config/logger";
import { LoggerUtils } from "../utils/logger-utils";

export default function errorHandlerMiddleware(err: Error, req: Request, res: Response, next: NextFunction) {
  let statusCode = 500;
  let message = "Internal Server Error";

  // Check if it's a custom error
  if (err instanceof CustomError) {
    statusCode = err.statusCode;
    message = err.message;
  }

  // Log error with user context and request details
  LoggerUtils.errorWithRequest(`API Error: ${message}`, req, {
    error: err.message,
    stack: err.stack,
    statusCode,
    url: req.originalUrl,
    method: req.method
  });

  res.status(statusCode).send({ message });
}
