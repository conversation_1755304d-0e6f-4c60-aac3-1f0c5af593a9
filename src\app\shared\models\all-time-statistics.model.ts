// All-time statistics types for overall league statistics

export type AllTimeTopAvgRatingByPosition = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  position: string;
  games: number;
  goals: number;
  assists: number;
  cleanSheets: number;
  avgRating: number;
  tableIcon?: {
    name: string;
    imgUrl: string;
    isTeam: boolean;
  }
};

export type MostHattricks = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  position: string;
  games: number;
  totalGoals: number;
  hattricks: number;
  hattricsPerGame: number;
  tableIcon?: {
    name: string;
    imgUrl: string;
    isTeam: boolean;
  }
};

export type MostCleanSheets = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  position: string;
  games: number;
  cleanSheets: number;
  cleanSheetsPerGame: number;
  avgRating: number;
  tableIcon?: {
    name: string;
    imgUrl: string;
    isTeam: boolean;
  }
};

// Additional statistics that could be added in the future
export type MostPlayerOfTheMatch = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  position: string;
  games: number;
  playerOfTheMatchAwards: number;
  potmPerGame: number;
  avgRating: number;
  tableIcon?: {
    name: string;
    imgUrl: string;
    isTeam: boolean;
  }
};

export type HighestSingleGameRating = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  position: string;
  highestRating: number;
  gameDate: Date;
  opponent: string;
  goals: number;
  assists: number;
  tableIcon?: {
    name: string;
    imgUrl: string;
    isTeam: boolean;
  }
};

export type MostWinningPercentageTeam = {
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  games: number;
  wins: number;
  draws: number;
  losses: number;
  winningPercentage: number;
  goalsFor: number;
  goalsAgainst: number;
  goalDifference: number;
  tableIcon?: {
    name: string;
    imgUrl: string;
    isTeam: boolean;
  }
};

export type MostWinningPercentagePlayer = {
  playerId: string;
  playerName: string;
  playerImgUrl?: string;
  teamId: string;
  teamName: string;
  teamImgUrl?: string;
  position: string;
  games: number;
  wins: number;
  draws: number;
  losses: number;
  winningPercentage: number;
  avgRating: number;
  goals: number;
  assists: number;
  tableIcon?: {
    name: string;
    imgUrl: string;
    isTeam: boolean;
  }
};

// Enum for statistic types to help with filtering and organization
export enum AllTimeStatisticType {
  TOP_AVG_RATING_BY_POSITION = 'topAvgRatingByPosition',
  MOST_HATTRICKS = 'mostHattricks',
  MOST_CLEAN_SHEETS = 'mostCleanSheets',
  MOST_PLAYER_OF_THE_MATCH = 'mostPlayerOfTheMatch',
  HIGHEST_SINGLE_GAME_RATING = 'highestSingleGameRating',
  MOST_WINNING_PERCENTAGE_TEAM = 'mostWinningPercentageTeam',
  MOST_WINNING_PERCENTAGE_PLAYER = 'mostWinningPercentagePlayer'
}

// Union type for all statistics
export type AllTimeStatistic =
  | AllTimeTopAvgRatingByPosition
  | MostHattricks
  | MostCleanSheets
  | MostPlayerOfTheMatch
  | HighestSingleGameRating
  | MostWinningPercentageTeam
  | MostWinningPercentagePlayer;
