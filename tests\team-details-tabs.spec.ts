import { test, expect } from '@playwright/test';

test.describe('Team Details Tab Styling', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');

    // Wait for the application to load
    await page.waitForLoadState('networkidle');
  });

  test('should display tabs with proper styling in light mode', async ({ page }) => {
    // Navigate directly to team-details page
    await page.goto('/team-details');
    await page.waitForLoadState('networkidle');

    // Check if there are tabs on the page
    const tabGroup = page.locator('mat-tab-group');
    const tabGroupExists = await tabGroup.count() > 0;

    if (tabGroupExists) {
      await expect(tabGroup).toBeVisible();

      // Take screenshot of tabs in light mode
      await page.screenshot({ path: 'tests/screenshots/tabs-light-mode.png' });

      // Check tab visibility and styling
      const tabs = page.locator('.mdc-tab');
      const tabCount = await tabs.count();
      expect(tabCount).toBeGreaterThan(0);

      // Check that tabs are visible (not white on white)
      for (let i = 0; i < tabCount; i++) {
        const tab = tabs.nth(i);
        await expect(tab).toBeVisible();

        // Check that tab text is visible
        const tabText = tab.locator('.mdc-tab__text-label');
        await expect(tabText).toBeVisible();
      }

      console.log(`Found ${tabCount} tabs on the page`);
    } else {
      console.log('No tab group found on team-details page');
      // Take screenshot anyway to see what's on the page
      await page.screenshot({ path: 'tests/screenshots/team-details-no-tabs.png' });
    }
  });

  test('should toggle dark mode and update tab styling', async ({ page }) => {
    // Navigate directly to team-details page
    await page.goto('/team-details');
    await page.waitForLoadState('networkidle');

    // Check if there are tabs on the page
    const tabGroup = page.locator('mat-tab-group');
    const tabGroupExists = await tabGroup.count() > 0;

    if (tabGroupExists) {
      await expect(tabGroup).toBeVisible();

      // Take screenshot before dark mode toggle
      await page.screenshot({ path: 'tests/screenshots/tabs-before-dark-toggle.png' });

      // Find and click the dark mode toggle in navbar - look for the theme toggle button
      const themeToggle = page.locator('.theme-toggle');

      if (await themeToggle.count() > 0) {
        await themeToggle.click();
        console.log('Clicked theme toggle button');
      } else {
        // Fallback: look for any button with moon or sun emoji
        const darkModeToggle = page.locator('button:has-text("🌙"), button:has-text("☀️")');
        if (await darkModeToggle.count() > 0) {
          await darkModeToggle.click();
          console.log('Clicked emoji-based dark mode toggle');
        } else {
          console.log('No dark mode toggle found');
        }
      }

      // Wait for theme change to apply
      await page.waitForTimeout(1000);

      // Take screenshot after dark mode toggle
      await page.screenshot({ path: 'tests/screenshots/tabs-after-dark-toggle.png' });

      // Check that the document has dark theme attribute
      const htmlElement = page.locator('html');
      const dataTheme = await htmlElement.getAttribute('data-theme');
      console.log('Data theme attribute:', dataTheme);

      // Verify tabs are still visible and properly styled in dark mode
      const tabs = page.locator('.mdc-tab');
      const tabCount = await tabs.count();

      for (let i = 0; i < tabCount; i++) {
        const tab = tabs.nth(i);
        await expect(tab).toBeVisible();

        // Check that tab text is still visible in dark mode
        const tabText = tab.locator('.mdc-tab__text-label');
        await expect(tabText).toBeVisible();
      }

      // Test clicking on different tabs to ensure they work
      if (tabCount > 1) {
        await tabs.nth(1).click();
        await page.waitForTimeout(300);

        // Verify the second tab is now active
        const activeTab = page.locator('.mdc-tab--active');
        await expect(activeTab).toBeVisible();
      }
    } else {
      console.log('No tab group found for dark mode testing');
      // Take screenshot anyway to see what's on the page
      await page.screenshot({ path: 'tests/screenshots/team-details-no-tabs-dark-test.png' });
    }
  });

  test('should have proper contrast in both light and dark modes', async ({ page }) => {
    // This test will check computed styles to ensure proper contrast
    await page.goto('/team-details');
    await page.waitForLoadState('networkidle');

    // Check if there are tabs to test
    const tabGroup = page.locator('mat-tab-group');
    if (await tabGroup.count() > 0) {
      const tabs = page.locator('.mdc-tab');

      if (await tabs.count() > 0) {
        // Get computed styles for the first tab
        const tabStyles = await tabs.first().evaluate((el) => {
          const styles = window.getComputedStyle(el);
          const rootStyles = window.getComputedStyle(document.documentElement);
          return {
            backgroundColor: styles.backgroundColor,
            color: styles.color,
            borderColor: styles.borderColor,
            cssVars: {
              bgPrimary: rootStyles.getPropertyValue('--bg-primary').trim(),
              textSecondary: rootStyles.getPropertyValue('--text-secondary').trim(),
              surfaceHover: rootStyles.getPropertyValue('--surface-hover').trim()
            }
          };
        });

        console.log('Tab styles in light mode:', tabStyles);

        // Toggle dark mode if possible
        const themeToggle = page.locator('.theme-toggle');
        if (await themeToggle.count() > 0) {
          await themeToggle.click();
          await page.waitForTimeout(1000);

          // Get styles after dark mode toggle
          const darkTabStyles = await tabs.first().evaluate((el) => {
            const styles = window.getComputedStyle(el);
            const rootStyles = window.getComputedStyle(document.documentElement);
            return {
              backgroundColor: styles.backgroundColor,
              color: styles.color,
              borderColor: styles.borderColor,
              cssVars: {
                bgPrimary: rootStyles.getPropertyValue('--bg-primary').trim(),
                textSecondary: rootStyles.getPropertyValue('--text-secondary').trim(),
                surfaceHover: rootStyles.getPropertyValue('--surface-hover').trim()
              }
            };
          });

          console.log('Tab styles in dark mode:', darkTabStyles);

          // Verify that styles actually changed
          expect(darkTabStyles.backgroundColor).not.toBe(tabStyles.backgroundColor);
        } else {
          console.log('No theme toggle found for contrast testing');
        }
      }
    } else {
      console.log('No tabs found for contrast testing');
    }
  });
});
