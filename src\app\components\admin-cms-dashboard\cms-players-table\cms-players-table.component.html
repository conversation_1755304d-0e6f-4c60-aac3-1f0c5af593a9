<div class="cms-table-container">
    <div class="table-header">
        <h3>
            <i class="fas fa-users"></i>
            Players Management
        </h3>
        <div class="table-actions">
            <button class="action-btn create-btn" routerLink="/create-player">
                <i class="fas fa-user-plus"></i>
                Add Player
            </button>
        </div>
    </div>

    <div class="table-wrapper">
        <table class="cms-table">
            <thead>
                <tr>
                    <th>Player</th>
                    <th>Position</th>
                    <th>Team</th>
                    <th>Stats</th>
                    <th>Email</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let player of players" class="table-row">
                    <td class="player-cell">
                        <div class="player-info">
                            <img [src]="player.imgUrl || 'assets/Icons/User.jpg'" 
                                 [alt]="player.name"
                                 (error)="onImageError($event)"
                                 class="player-avatar">
                            <div class="player-details">
                                <span class="player-name">{{ player.name }}</span>
                                <span class="player-id">ID: {{ player.id }}</span>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="position-badge" [class]="'position-' + getPlayerPosition(player).toLowerCase()">
                            {{ getPlayerPosition(player) }}
                        </span>
                    </td>
                    <td>
                        <span class="team-name" [class.free-agent]="!player.team">
                            {{ getPlayerTeamName(player) }}
                        </span>
                    </td>
                    <td>
                        <span class="stats-text">{{ getPlayerStats(player) }}</span>
                    </td>
                    <td>
                        <span class="email-text">{{ player.email || 'N/A' }}</span>
                    </td>
                    <td class="actions-cell">
                        <div class="action-buttons">
                            <button class="action-btn view-btn" 
                                    (click)="viewPlayerDetails(player.id)"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn edit-btn" 
                                    (click)="editPlayer(player.id)"
                                    title="Edit Player">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-btn" 
                                    (click)="deletePlayer(player)"
                                    [disabled]="isPlayerDeleting(player.id)"
                                    title="Delete Player">
                                <i class="fas fa-trash" *ngIf="!isPlayerDeleting(player.id)"></i>
                                <i class="fas fa-spinner fa-spin" *ngIf="isPlayerDeleting(player.id)"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="players.length === 0">
            <div class="empty-icon">
                <i class="fas fa-users"></i>
            </div>
            <h4>No Players Found</h4>
            <p>No players match your current search criteria.</p>
            <button class="action-btn create-btn" routerLink="/create-player">
                <i class="fas fa-user-plus"></i>
                Create First Player
            </button>
        </div>
    </div>
</div>
