/* === MODERN NEWS LIST COMPONENT === */

.news-list-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    font-family: var(--font-sans);
    position: relative;

    /* Add subtle background pattern */
    &::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.02) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    @media (max-width: 768px) {
        gap: var(--spacing-md);
    }

    @media (max-width: 480px) {
        gap: var(--spacing-sm);
    }
}

/* === MODERN NEWS HEADER === */
.news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow:
        var(--shadow-lg),
        0 0 0 1px rgba(99, 102, 241, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    min-height: 80px;
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Modern gradient border */
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(
            90deg,
            var(--primary-500) 0%,
            var(--primary-400) 25%,
            var(--accent-primary) 50%,
            var(--primary-400) 75%,
            var(--primary-500) 100%
        );
        background-size: 200% 100%;
        animation: gradientShift 3s ease-in-out infinite;
    }

    /* Subtle inner glow */
    &::after {
        content: '';
        position: absolute;
        top: 4px;
        left: 1px;
        right: 1px;
        bottom: 1px;
        background: linear-gradient(
            135deg,
            rgba(99, 102, 241, 0.02) 0%,
            transparent 50%,
            rgba(255, 215, 0, 0.01) 100%
        );
        border-radius: var(--radius-xl);
        pointer-events: none;
    }

    &:hover {
        transform: translateY(-2px);
        box-shadow:
            var(--shadow-xl),
            0 0 0 1px rgba(99, 102, 241, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
        min-height: auto;
    }
}

.header-content {
    flex: 1;

    .news-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0;

        i {
            color: var(--primary);
            font-size: var(--text-base);
        }

        .highlight {
            color: var(--primary);
        }

        @media (max-width: 768px) {
            font-size: var(--text-lg);
        }
    }
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    @media (max-width: 768px) {
        width: 100%;
        justify-content: flex-start;
    }
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: linear-gradient(135deg, var(--surface-secondary), var(--surface-tertiary));
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: var(--text-sm);
    position: relative;
    overflow: hidden;

    /* Subtle inner highlight */
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    }

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--surface-tertiary), var(--surface-secondary));
        transform: translateY(-2px);
        box-shadow:
            var(--shadow-lg),
            0 0 20px rgba(99, 102, 241, 0.1);
        border-color: var(--primary-400);
    }

    &:active:not(:disabled) {
        transform: translateY(0);
        transition: transform 0.1s ease;
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .spinning {
        animation: spin 1s linear infinite;
    }

    i {
        transition: transform 0.3s ease;
    }

    &:hover:not(:disabled) i {
        transform: rotate(180deg);
    }
}

.action-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: var(--text-sm);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow:
        var(--shadow-md),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;

    /* Shimmer effect */
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
        );
        transition: left 0.5s ease;
    }

    &:hover {
        background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
        transform: translateY(-3px);
        box-shadow:
            var(--shadow-xl),
            0 0 30px rgba(99, 102, 241, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.2) inset;

        &::before {
            left: 100%;
        }
    }

    &:active {
        transform: translateY(-1px);
        transition: transform 0.1s ease;
    }

    i {
        font-size: var(--text-base);
        transition: transform 0.3s ease;
    }

    &:hover i {
        transform: scale(1.1);
    }
}

/* === MODERN NEWS FILTERS === */
.news-filters {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    padding: var(--spacing-lg);
    box-shadow:
        var(--shadow-lg),
        0 0 0 1px rgba(99, 102, 241, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;

    @media (max-width: 768px) {
        padding: var(--spacing-md);
        border-radius: var(--radius-lg);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-sm);
        border-radius: var(--radius-md);
    }

    /* Subtle background gradient */
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
            135deg,
            rgba(99, 102, 241, 0.02) 0%,
            transparent 50%,
            rgba(255, 215, 0, 0.01) 100%
        );
        pointer-events: none;
    }
}

.filter-tabs {
    display: flex;
    gap: var(--spacing-xs);
    overflow-x: auto;
    padding-bottom: var(--spacing-xs);

    @media (max-width: 768px) {
        gap: var(--spacing-xs);
    }
}

.filter-tab {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: var(--text-sm);
    white-space: nowrap;
    min-width: fit-content;
    position: relative;
    overflow: hidden;

    /* Subtle inner highlight */
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover {
        background: var(--surface-tertiary);
        color: var(--text-primary);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--primary-400);

        &::before {
            opacity: 1;
        }
    }

    &.active {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        color: white;
        border-color: var(--primary-400);
        box-shadow:
            var(--shadow-lg),
            0 0 20px rgba(99, 102, 241, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        transform: translateY(-1px);

        &::before {
            opacity: 1;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        }
    }

    i {
        font-size: var(--text-base);
        transition: transform 0.3s ease;
    }

    &:hover i,
    &.active i {
        transform: scale(1.1);
    }

    .count {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-full);
        font-size: var(--text-xs);
        font-weight: 700;
        min-width: 24px;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
    }

    &.active .count {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.2);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--text-sm);

        span:not(.count) {
            display: none;
        }

        .count {
            min-width: 20px;
            padding: 2px 6px;
        }
    }
}

/* === NEWS FEED === */
.news-feed {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.news-item {
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow:
        var(--shadow-lg),
        0 0 0 1px rgba(99, 102, 241, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);

    /* Subtle background gradient */
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
            135deg,
            rgba(99, 102, 241, 0.01) 0%,
            transparent 50%,
            rgba(255, 215, 0, 0.005) 100%
        );
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.4s ease;
    }

    &:hover {
        transform: translateY(-4px);
        box-shadow:
            var(--shadow-2xl),
            0 0 40px rgba(99, 102, 241, 0.1),
            0 0 0 1px rgba(99, 102, 241, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        border-color: var(--primary-400);

        &::before {
            opacity: 1;
        }
    }

    &.transfer-news {
        border-left: 4px solid var(--success-500);

        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(
                180deg,
                var(--success-400),
                var(--success-500),
                var(--success-600)
            );
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
        }
    }
}

.news-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    background: var(--surface-secondary);

    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
}

.news-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.news-type-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    /* Shimmer effect */
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
        );
        transition: left 0.5s ease;
    }

    &:hover::before {
        left: 100%;
    }

    &.general-badge {
        background: rgba(59, 130, 246, 0.15);
        color: var(--info-500);
        border-color: rgba(59, 130, 246, 0.3);
        box-shadow: 0 0 10px rgba(59, 130, 246, 0.1);
    }

    &.transfer-badge {
        background: rgba(16, 185, 129, 0.15);
        color: var(--success-500);
        border-color: rgba(16, 185, 129, 0.3);
        box-shadow: 0 0 10px rgba(16, 185, 129, 0.1);
    }

    &.free-agent-badge {
        background: rgba(245, 158, 11, 0.15);
        color: var(--warning-500);
        border-color: rgba(245, 158, 11, 0.3);
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.1);
    }

    &.default-badge {
        background: rgba(107, 114, 128, 0.15);
        color: var(--neutral-400);
        border-color: rgba(107, 114, 128, 0.3);
        box-shadow: 0 0 10px rgba(107, 114, 128, 0.1);
    }

    i {
        transition: transform 0.3s ease;
    }

    &:hover i {
        transform: scale(1.1);
    }
}

.news-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: var(--text-sm);

    i {
        font-size: var(--text-xs);
    }
}

.news-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    @media (max-width: 768px) {
        justify-content: flex-end;
    }

    .like-section {
        display: flex;
        align-items: center;

        .like-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--surface-secondary);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: var(--text-sm);
            min-width: 60px;
            height: 40px;

            &:hover {
                background: var(--error-100);
                color: var(--error-600);
                transform: translateY(-2px) scale(1.05);
                box-shadow: var(--shadow-lg);
                border-color: var(--error-300);
            }

            &.liked {
                background: var(--error-100);
                color: var(--error-600);
                border-color: var(--error-300);

                i {
                    color: var(--error-500);
                }

                &:hover {
                    background: var(--surface-secondary);
                    color: var(--text-secondary);
                    border-color: var(--border-primary);
                }
            }

            .like-count {
                font-weight: var(--font-weight-medium);
                font-size: var(--text-xs);
            }
        }
    }

    .like-display {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
        background: var(--surface-secondary);
        border: 1px solid var(--error-300);
        border-radius: var(--radius-lg);
        color: var(--error-500);
        font-size: var(--text-sm);
        min-width: 60px;
        height: 40px;

        .like-count {
            font-weight: var(--font-weight-medium);
            font-size: var(--text-xs);
        }
    }
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background: var(--surface-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    /* Subtle inner highlight */
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover {
        background: var(--surface-tertiary);
        color: var(--text-primary);
        transform: translateY(-2px) scale(1.05);
        box-shadow: var(--shadow-lg);

        &::before {
            opacity: 1;
        }
    }

    &:active {
        transform: translateY(0) scale(1);
        transition: transform 0.1s ease;
    }

    i {
        transition: transform 0.3s ease;
    }

    &:hover i {
        transform: scale(1.1);
    }

    &.share-btn:hover {
        background: rgba(59, 130, 246, 0.15);
        border-color: var(--info-500);
        color: var(--info-500);
        box-shadow:
            var(--shadow-lg),
            0 0 20px rgba(59, 130, 246, 0.2);
    }

    &.edit-btn:hover {
        background: rgba(245, 158, 11, 0.15);
        border-color: var(--warning-500);
        color: var(--warning-500);
        box-shadow:
            var(--shadow-lg),
            0 0 20px rgba(245, 158, 11, 0.2);
    }

    &.delete-btn:hover {
        background: rgba(239, 68, 68, 0.15);
        border-color: var(--error-500);
        color: var(--error-500);
        box-shadow:
            var(--shadow-lg),
            0 0 20px rgba(239, 68, 68, 0.2);
    }
}

.admin-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.news-item-title {
    padding: 0 var(--spacing-lg);
    margin: var(--spacing-md) 0;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.4;

    @media (max-width: 768px) {
        font-size: var(--text-base);
        padding: 0 var(--spacing-md);
    }
}

/* === LOADING AND EMPTY STATES === */
.loading-container,
.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

.loading-spinner,
.empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--text-secondary);
    text-align: center;

    i {
        font-size: 2rem;
        color: var(--primary);
    }

    h3 {
        margin: 0;
        color: var(--text-primary);
    }

    p {
        margin: 0;
        max-width: 300px;
    }
}

/* === NEWS CONTENT === */
.news-content {
    padding: var(--spacing-lg);

    @media (max-width: 768px) {
        padding: var(--spacing-md);
    }
}

.general-content p {
    margin: 0;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: var(--text-base);
}

/* === TRANSFER CONTENT === */
.transfer-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.transfer-visual {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);

    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-lg);
        padding: var(--spacing-md);
    }
}

.transfer-entity {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);

    &:hover {
        background: var(--surface-tertiary);
        transform: translateY(-2px);
    }
}

.entity-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    overflow: hidden;
    border: 2px solid var(--border-primary);
    background: var(--surface-primary);

    .player-image,
    .team-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.entity-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 2px;
}

.entity-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--text-sm);
}

.entity-label {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 500;
}

.transfer-arrow {
    display: flex;
    align-items: center;
    color: var(--primary);
    font-size: var(--text-lg);

    @media (max-width: 768px) {
        transform: rotate(90deg);
    }
}

.transfer-description p {
    margin: 0;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: var(--text-base);
    text-align: center;
    font-style: italic;
}

/* === FREE AGENT CONTENT === */
.free-agent-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.free-agent-visual {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);

    @media (max-width: 768px) {
        padding: var(--spacing-md);
    }
}

.free-agent-entity {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    transition: all 0.2s ease;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);

    &:hover {
        background: var(--surface-tertiary);
    }

    .entity-info {
        align-items: flex-start;
        text-align: left;
    }

    .entity-details {
        font-size: var(--text-sm);
        color: var(--text-secondary);
    }
}

.free-agent-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    text-align: center;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--text-sm);
}

.status-text {
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.previous-teams {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.previous-teams-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    font-weight: 600;
}

.teams-list {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.team-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background: var(--surface-tertiary);
        transform: translateY(-1px);
    }
}

.team-logo {
    width: 24px;
    height: 24px;
    border-radius: var(--radius-sm);
    object-fit: cover;
}

.team-info {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.team-name {
    font-size: var(--text-xs);
    font-weight: 600;
    color: var(--text-primary);
}

.season {
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

.free-agent-description p {
    margin: 0;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: var(--text-base);
    text-align: center;
    font-style: italic;
}

/* === MODERN ANIMATIONS === */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Apply fade-in animation to news items */
.news-item {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.news-item:nth-child(1) { animation-delay: 0.1s; }
.news-item:nth-child(2) { animation-delay: 0.2s; }
.news-item:nth-child(3) { animation-delay: 0.3s; }
.news-item:nth-child(4) { animation-delay: 0.4s; }
.news-item:nth-child(5) { animation-delay: 0.5s; }

/* Loading state improvements */
.loading-spinner i {
    animation: spin 1s linear infinite;
}

.empty-content {
    animation: fadeInUp 0.8s ease-out;
}
