import { GAME_STATUS } from "@pro-clubs-manager/shared-dtos";
import mongoose, { Document, Schema } from "mongoose";
import { PlayerGamePerformance } from "./game-types";

export enum PLAYOFF_STAGE {
  PLAY_IN_ROUND = "Play-in Round",
  QUARTER_FINAL = "Quarter-Final",
  SEMI_FINAL = "Semi-Final",
  FINAL = "Final",
  THIRD_PLACE = "3rd Place Match",
  PROMOTION_PLAYOFF = "Promotion Playoff",
  RELEGATION_PLAYOFF = "Relegation Playoff"
}

export enum SERIES_FORMAT {
  BEST_OF_1 = "Best of 1",
  BEST_OF_3 = "Best of 3",
  BEST_OF_5 = "Best of 5",
  BEST_OF_7 = "Best of 7"
}

export interface PlayoffDetails {
  stage: PLAYOFF_STAGE;
  format: SERIES_FORMAT;
}

export interface IGame extends Document {
  id: string;
  fixture: mongoose.Types.ObjectId;
  round: number;
  league: mongoose.Types.ObjectId;
  seasonNumber: number;
  homeTeam: mongoose.Types.ObjectId;
  awayTeam: mongoose.Types.ObjectId;
  date?: Date;
  status: GAME_STATUS;
  isPlayoff: boolean;
  playoffStage?: PLAYOFF_STAGE;
  matchNumber?: number;
  technicalLoss?: {
    teamId: mongoose.Types.ObjectId;
    reason: string;
  };
  result?: {
    homeTeamGoals: number;
    awayTeamGoals: number;
    penalties?: {
      homeTeamPenalties: number;
      awayTeamPenalties: number;
    };
  };
  broadcast?: {
    streamUrl: string;
    broadcastingTeam: string;
  };
  homeTeamPlayersPerformance?: PlayerGamePerformance[];
  awayTeamPlayersPerformance?: PlayerGamePerformance[];
}

const playerGameStatsSchema = new Schema(
  {
    playerId: { type: mongoose.Schema.Types.ObjectId, ref: "Player", required: true },
    rating: { type: Number, required: true },
    cleanSheet: { type: Boolean, required: true },
    goals: { type: Number },
    assists: { type: Number },
    playerOfTheMatch: { type: Boolean },
    positionPlayed: { type: String },
  },
  { id: false }
);

const gameSchema = new Schema<IGame>(
  {
    fixture: { type: mongoose.Schema.Types.ObjectId, ref: "Fixture", required: true },
    round: { type: Number, required: true },
    league: { type: mongoose.Schema.Types.ObjectId, ref: "League", required: true },
    seasonNumber: { type: Number, required: true },
    homeTeam: { type: mongoose.Schema.Types.ObjectId, ref: "Team", required: true },
    awayTeam: { type: mongoose.Schema.Types.ObjectId, ref: "Team", required: true },
    date: { type: Date },
    status: { type: String, required: true, default: GAME_STATUS.SCHEDULED, enum: Object.values(GAME_STATUS) },
    isPlayoff: { type: Boolean, required: true, default: false },
    playoffStage: { type: String, enum: Object.values(PLAYOFF_STAGE), required: false },
    matchNumber: { type: Number, required: false },
    technicalLoss: {
      type: {
        reason: { type: String, required: true },
        teamId: { type: mongoose.Schema.Types.ObjectId, ref: "Team", required: true },
      },
      id: false,
      required: false,
    },
    result: {
      type: {
        homeTeamGoals: { type: Number },
        awayTeamGoals: { type: Number },
        penalties: {
          type: {
            homeTeamPenalties: { type: Number },
            awayTeamPenalties: { type: Number },
          },
          required: false,
        },
      },
      required: false,
    },
    broadcast: {
      type: {
        streamUrl: { type: String, required: true },
        broadcastingTeam: { type: String, required: true },
      },
      required: false,
    },
    homeTeamPlayersPerformance: [playerGameStatsSchema],
    awayTeamPlayersPerformance: [playerGameStatsSchema],
  },
  {
    toJSON: { virtuals: true },
    id: true, // Use 'id' instead of '_id'
  }
);

// Define the index
gameSchema.index({ league: 1, seasonNumber: 1, date: 1 });
gameSchema.index({ GAME_STATUS: 1 });

const Game = mongoose.model<IGame>("Game", gameSchema);

export default Game;
export { PlayerGamePerformance, AddGameData, PopulatedPlayerGameData } from "./game-types";
