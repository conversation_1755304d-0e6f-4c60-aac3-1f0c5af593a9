/* === ADMIN TEAM LEAGUE MANAGEMENT === */

.admin-team-league-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  min-height: 100vh;
}

/* === PAGE HEADER === */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-xl);
  color: white;
  box-shadow: var(--shadow-lg);

  .page-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);

    i {
      font-size: var(--text-2xl);
    }
  }

  .page-description {
    font-size: var(--text-lg);
    opacity: 0.9;
    margin: 0;
  }
}

/* === FORM ELEMENTS === */
.form-group {
  margin-bottom: var(--spacing-lg);

  .form-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--text-base);
  }

  .form-select {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background: var(--surface-secondary);
    color: var(--text-primary);
    font-size: var(--text-base);
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(var(--primary-500-rgb), 0.1);
    }

    &:hover {
      border-color: var(--primary-400);
    }
  }
}

/* === BUTTONS === */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-semibold);
  font-size: var(--text-base);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.btn-primary {
    background: var(--primary-500);
    color: white;

    &:hover:not(:disabled) {
      background: var(--primary-600);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }

  &.btn-danger {
    background: var(--danger-500);
    color: white;

    &:hover:not(:disabled) {
      background: var(--danger-600);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }

  &.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-sm);
  }
}

/* === LEAGUE MANAGEMENT === */
.league-management {
  background: var(--surface-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);
}

.league-info {
  margin-bottom: var(--spacing-xl);
  text-align: center;

  .league-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
  }

  .league-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-xs);

      .stat-label {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
      }

      .stat-value {
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--primary-500);
      }
    }
  }
}

/* === SECTIONS === */
.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-primary);
}

.add-team-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);

  .add-team-form {
    display: flex;
    gap: var(--spacing-lg);
    align-items: end;
    flex-wrap: wrap;

    .form-group {
      flex: 1;
      min-width: 250px;
      margin-bottom: 0;
    }
  }

  .no-teams-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--info-100);
    color: var(--info-700);
    border-radius: var(--radius-lg);
    border: 1px solid var(--info-200);
    font-weight: var(--font-weight-medium);
  }
}

/* === TEAMS GRID === */
.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.team-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-300);
  }

  .team-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;

    .team-image {
      width: 50px;
      height: 50px;
      border-radius: var(--radius-full);
      object-fit: cover;
      border: 2px solid var(--border-primary);
    }

    .team-details {
      .team-name {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
      }

      .team-stats {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin: 0;
      }
    }
  }
}

/* === LOADING & EMPTY STATES === */
.loading-container,
.teams-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);

  i {
    font-size: var(--text-2xl);
    margin-bottom: var(--spacing-md);
    color: var(--primary-500);
  }

  p {
    font-size: var(--text-lg);
    margin: 0;
  }
}

.no-teams-in-league {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);

  i {
    font-size: var(--text-3xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-tertiary);
  }

  p {
    font-size: var(--text-lg);
    margin: 0;
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .admin-team-league-container {
    padding: var(--spacing-md);
  }

  .page-header {
    padding: var(--spacing-md);

    .page-title {
      font-size: var(--text-2xl);
      flex-direction: column;
      gap: var(--spacing-sm);
    }
  }

  .league-management {
    padding: var(--spacing-lg);
  }

  .league-stats {
    gap: var(--spacing-lg) !important;
  }

  .add-team-form {
    flex-direction: column;
    align-items: stretch !important;

    .form-group {
      min-width: unset;
    }
  }

  .teams-grid {
    grid-template-columns: 1fr;
  }

  .team-card {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;

    .team-info {
      justify-content: center;
    }
  }
}
