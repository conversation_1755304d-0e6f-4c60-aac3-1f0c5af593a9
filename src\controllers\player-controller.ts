import { CreatePlayerDataRequest } from "@pro-clubs-manager/shared-dtos";
import { NextFunction, Request, Response } from "express";
import { inject, injectable } from "tsyringe";
import { IPlayerController } from "../interfaces/player";
import { IPlayerService } from "../interfaces/player/player-service.interface";
import logger from "../config/logger";
import { IPlayerStatsService } from "../interfaces/wrapper-services";

@injectable()
export default class PlayerController implements IPlayerController {
  constructor(
    @inject("IPlayerService") private playerService: IPlayerService,
    @inject("IPlayerStatsService") private playerStatsService: IPlayerStatsService
  ) { }

  async getPlayerById(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id } = req.params;

    if (!id) {
      res.status(400).send({ message: "no id provided" });
      return;
    }

    try {
      const player = await this.playerService.getPlayerById(id);
      res.json(player);
    } catch (error: any) {
      next(error);
    }
  }

  async getFreeAgents(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const freeAgents = await this.playerService.getFreeAgents();
      res.send(freeAgents);
    } catch (e: any) {
      next(e);
    }
  }

  async createPlayer(req: Request, res: Response, next: NextFunction): Promise<void> {
    // TODO: add validation
    const playerData = req.body as CreatePlayerDataRequest;

    const file = req.file;

    try {
      const player = await this.playerService.createPlayer(playerData);
      if (file) {
        try {
          const imgUrl = await this.playerService.setPlayerImage(player.id, file);
          player.imgUrl = imgUrl;
        } catch (e: any) {
          logger.error(e.message);
        }
      }

      res.status(201).json(player);
    } catch (error: any) {
      next(error);
    }
  }

  async getPlayerStatsByPosition(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id } = req.params;

    if (!id) {
      res.status(400).send({ message: "No playerId provided" });
      return;
    }

    try {
      const playerStats = await this.playerStatsService.getPlayerStatsByPosition(id);
      res.json(playerStats);
    } catch (error: any) {
      next(error);
    }
  }

  async getPlayerForm(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id: playerId } = req.params;
    const numberOfGames = req.query.numGames ? Number(req.query.numGames) : 5;

    try {
      const playerForm = await this.playerStatsService.getLastFiveGamesPerformance(playerId, numberOfGames);
      res.send(playerForm);
    } catch (e) {
      next(e);
    }
  }

  async setPlayerImage(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { playerId } = req.params;
    const file = req.file;

    if (!file || !playerId) {
      res.status(400).send({
        message: "Bad request",
      });
      return;
    }

    try {
      const imgUrl = await this.playerService.setPlayerImage(playerId, file);
      res.json(imgUrl);
    } catch (error: any) {
      next(error);
    }
  }

  async renamePlayer(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { playerId } = req.params;

    const { newName } = req.body;
    if (!playerId || !newName || !newName.length) {
      res.status(400).send({ message: "bad data" });
      return;
    }

    try {
      await this.playerService.renamePlayer(playerId, newName);
      res.status(200).json({ success: true });
    } catch (error: any) {
      next(error);
    }
  }

  async editPlayerAge(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { playerId } = req.params;

    const { age } = req.body;
    if (!playerId || age === undefined || age === null || age < 16 || age > 50) {
      res.status(400).send({ message: "bad data - age must be between 16 and 50" });
      return;
    }

    try {
      await this.playerService.editPlayerAge(playerId, age);
      res.status(200).json({ success: true });
    } catch (error: any) {
      next(error);
    }
  }

  async editPlayerPosition(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { playerId } = req.params;

    const { position } = req.body;
    if (!playerId || !position || !position.length) {
      res.status(400).send({ message: "bad data" });
      return;
    }

    try {
      await this.playerService.editPlayerPosition(playerId, position);
      res.status(200).json({ success: true });
    } catch (error: any) {
      next(error);
    }
  }

  async editPlayerPlayablePositions(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { playerId } = req.params;

    const { playablePositions } = req.body;
    if (!playerId || !playablePositions || !Array.isArray(playablePositions)) {
      res.status(400).send({ message: "bad data" });
      return;
    }

    try {
      await this.playerService.editPlayerPlayablePositions(playerId, playablePositions);
      res.status(200).json({ success: true });
    } catch (error: any) {
      next(error);
    }
  }

  async deletePlayer(req: Request, res: Response, next: NextFunction): Promise<void> {
    // const { id } = req.params;
    // if (!id) {
    //   res.status(400).send({ message: "no id provided" });
    //   return;
    // }
    // try {
    //   await this.playerTeamService.deletePlayer(id);
    //   res.sendStatus(204);
    // } catch (error: any) {
    //   next(error);
  }

  async playerSearchByText(req: Request, res: Response, next: NextFunction): Promise<void> {

    const searchText = req.query.searchText?.toString();

    if (!searchText?.length) {
      res.status(400).send({ message: "bad data" });
      return;
    }

    try {
      const searchResponse = await this.playerService.playerSearchByText(searchText);
      res.send(searchResponse);
    } catch (error: any) {
      next(error);
    }
  }

  async getAllPlayers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const allPlayers = await this.playerService.getAllPlayers();
      res.json(allPlayers);
    } catch (error: any) {
      next(error);
    }
  }

  async getTransferHistoryByPlayerId(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id } = req.params;

    if (!id) {
      res.status(400).send({ message: "no player id provided" });
      return;
    }

    try {
      const transferHistory = await this.playerService.getTransferHistoryByPlayerId(id);
      res.json(transferHistory);
    } catch (error: any) {
      next(error);
    }
  }

  async comparePlayersById(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { player1Id, player2Id } = req.params;

    if (!player1Id || !player2Id) {
      res.status(400).send({ message: "Both player IDs are required" });
      return;
    }

    if (player1Id === player2Id) {
      res.status(400).send({ message: "Cannot compare a player with themselves" });
      return;
    }

    try {
      const comparison = await this.playerService.comparePlayersById(player1Id, player2Id);
      res.json(comparison);
    } catch (error: any) {
      next(error);
    }
  }

  async getPlayerSeasonHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { id } = req.params;

    if (!id) {
      res.status(400).send({ message: "Player ID is required" });
      return;
    }

    try {
      const seasonHistory = await this.playerService.getPlayerSeasonHistory(id);
      res.json(seasonHistory);
    } catch (error: any) {
      next(error);
    }
  }

  async deletePlayerSeasonHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { playerId, seasonNumber } = req.params;

    if (!playerId || !seasonNumber) {
      res.status(400).send({ message: "Player ID and season number are required" });
      return;
    }

    try {
      await this.playerService.deletePlayerSeasonHistory(playerId, parseInt(seasonNumber));
      res.status(200).send({ message: "Season history deleted successfully" });
    } catch (error: any) {
      next(error);
    }
  }
}
