import { Component, Input } from '@angular/core';
import { TeamDTO } from '@pro-clubs-manager/shared-dtos';
import { TeamStat } from '../../../team-stats-card/team-stats-card.component';

@Component({
  selector: 'team-details-overall-stats',
  templateUrl: './team-details-overall-stats.component.html',
  styleUrl: './team-details-overall-stats.component.scss'
})
export class TeamDetailsOverallStatsComponent {
  @Input() chosenTeam: TeamDTO | null = null;

  getTeamStats(): TeamStat[] {
    if (!this.chosenTeam?.stats) {
      return [];
    }

    return [
      {
        label: 'Games',
        value: this.chosenTeam.stats.games || 0,
        icon: 'fas fa-gamepad',
        color: 'primary'
      },
      {
        label: 'Wins',
        value: this.chosenTeam.stats.wins || 0,
        icon: 'fas fa-trophy',
        color: 'success'
      },
      {
        label: 'Draws',
        value: this.chosenTeam.stats.draws || 0,
        icon: 'fas fa-handshake',
        color: 'warning'
      },
      {
        label: 'Losses',
        value: this.chosenTeam.stats.losses || 0,
        icon: 'fas fa-heart-broken',
        color: 'danger'
      }
    ];
  }
}