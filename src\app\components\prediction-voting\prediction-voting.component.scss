.prediction-voting {
  background: var(--surface-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);

  .view-toggle {
    display: flex;
    background: var(--surface-secondary);
    border-radius: var(--radius-md);
    padding: 2px;
    margin-bottom: var(--spacing-sm);

    .toggle-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-xs);
      padding: var(--spacing-xs) var(--spacing-sm);
      background: transparent;
      border: none;
      border-radius: var(--radius-sm);
      font-size: var(--text-sm);
      font-weight: var(--font-weight-medium);
      color: var(--text-secondary);
      cursor: pointer;
      transition: all 0.2s ease;

      i {
        font-size: var(--text-sm);
      }

      &:hover {
        color: var(--text-primary);
        background: var(--surface-tertiary);
      }

      &.active {
        background: var(--primary-500);
        color: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .vote-count {
    text-align: center;
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
  }

  .voting-section,
  .results-section {
    min-height: 120px; // Ensure consistent height between vote and results views
  }

  // Mobile-specific styles for more compact design
  @media (max-width: 768px) {
    padding: var(--spacing-sm);

    .view-toggle {
      padding: 1px;
      margin-bottom: var(--spacing-xs);

      .toggle-btn {
        padding: var(--spacing-xs);
        font-size: var(--text-xs);
      }
    }

    .vote-count {
      font-size: var(--text-xs);
      margin-bottom: var(--spacing-xs);
    }

    .voting-section,
    .results-section {
      min-height: 80px; // Reduced height for mobile
    }
  }

  &.compact {
    padding: var(--spacing-xs);
    min-height: auto;
    max-height: 280px; // Limit height to prevent excessive vertical space

    .prediction-header {
      margin-bottom: var(--spacing-xs);

      &.hidden {
        display: none;
      }
    }

    .compact-header {
      margin-bottom: 4px;
      display: flex;
      flex-direction: column;
      gap: 2px;

      .compact-predictions-count {
        font-size: var(--text-sm);
        color: var(--primary-400);
        display: flex;
        align-items: center;
        gap: 4px;
        line-height: 1;
        font-weight: var(--font-weight-semibold);

        i {
          color: var(--primary-500);
          font-size: var(--text-sm);
        }
      }

      .prediction-count {
        font-size: var(--text-xs);
        color: var(--text-tertiary);
        margin-left: 20px; // Align with icon
      }
    }

    .prediction-title {
      font-size: var(--text-sm);
      margin-bottom: var(--spacing-xs);
    }

    .total-predictions {
      font-size: var(--text-xs);
    }

    .voting-section {
      margin-bottom: 8px; // More space before results section
    }

    .voting-buttons {
      gap: 1px;
      flex-wrap: wrap;
      display: grid;
      grid-template-columns: 1fr auto 1fr; // Ensure proper 3-column layout
      margin-bottom: 6px; // Additional space after voting buttons
    }

    .vote-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-xs);
      min-height: 32px;
      min-width: 32px;
      flex: 1;
      background: var(--surface-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-sm);
      cursor: pointer;
      transition: all 0.2s ease;

      .team-logo {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        object-fit: cover;
      }

      i {
        font-size: 16px;
        color: var(--text-secondary);
      }

      &:hover:not(:disabled) {
        background: var(--surface-tertiary);
        border-color: var(--primary-400);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      &.selected {
        border-color: var(--primary-500);
        background: var(--primary-50);
        color: var(--primary-700);

        // Visual feedback for remove action on hover
        &:hover:not(:disabled) {
          background: var(--error-50);
          border-color: var(--error-400);
          color: var(--error-700);
          transform: scale(0.98);
        }
      }


    }

    .results-section {
      margin-top: 12px; // More separation from voting buttons
      clear: both; // Ensure it appears below voting buttons

      .results-title {
        font-size: var(--text-sm);
        margin-bottom: 6px;
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary); // Darker color for better visibility
        text-align: center;
      }

      .results-bars {
        gap: 4px; // More gap between result bars for better readability
        display: flex;
        flex-direction: column;

        .result-bar {
          margin-bottom: 2px;

          .result-header {
            margin-bottom: 2px;

            .team-info {
              gap: 4px;

              .team-logo-small {
                width: 14px;
                height: 14px;
              }

              .team-name {
                font-size: var(--text-xs);
                line-height: 1.2;
                color: var(--text-primary); // Darker color for better visibility
                font-weight: var(--font-weight-medium);
              }
            }

            .percentage {
              font-size: var(--text-sm);
              line-height: 1.2;
              font-weight: var(--font-weight-bold);
              color: var(--text-primary); // Darker color for better visibility
            }
          }

          .progress-bar {
            height: 4px; // Slightly taller for better visibility
            margin-bottom: 2px;
          }

          .vote-count {
            font-size: var(--text-xs);
            line-height: 1.2;
            margin-bottom: 2px;
            color: var(--text-secondary); // Better contrast than tertiary
            font-weight: var(--font-weight-medium);
          }
        }
      }

      .result-item {
        font-size: var(--text-xs);
        padding: 2px;
        line-height: 1.2;
        color: var(--text-primary);
      }
    }



    // Compact auth and status messages
    .auth-message,
    .game-status-message {
      padding: var(--spacing-xs) var(--spacing-sm); // Reduced padding
      margin-bottom: var(--spacing-xs); // Reduced margin
      font-size: var(--text-xs);

      i {
        font-size: var(--text-xs);
      }
    }
  }

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-secondary);

    i {
      color: var(--primary-500);
      font-size: var(--text-lg);
    }
  }

  .prediction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);

    .prediction-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-size: var(--text-lg);
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      margin: 0;

      i {
        color: var(--primary-500);
      }
    }

    .total-predictions {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      font-weight: var(--font-weight-medium);
    }
  }

  .voting-section {
    margin-bottom: var(--spacing-lg);

    .voting-buttons {
      display: grid;
      grid-template-columns: 1fr auto 1fr;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg); // More space before results

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
      }

      .vote-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-lg);
        min-height: 60px;
        min-width: 60px;
        background: var(--surface-secondary);
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-lg);
        cursor: pointer;
        transition: all 0.3s ease;

        .team-logo {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          object-fit: cover;
        }

        i {
          font-size: 28px;
          color: var(--text-secondary);
        }

        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: var(--shadow-lg);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        &.selected {
          border-color: var(--primary-500);
          background: var(--primary-50);
          color: var(--primary-700);

          // Visual feedback for remove action on hover
          &:hover:not(:disabled) {
            background: var(--error-50);
            border-color: var(--error-400);
            color: var(--error-700);
            transform: scale(0.98);
          }
        }

        &.home-win.selected {
          border-color: var(--success-500);
          background: var(--success-50);
          color: var(--success-700);

          &:hover:not(:disabled) {
            background: var(--error-50);
            border-color: var(--error-400);
            color: var(--error-700);
            transform: scale(0.98);
          }
        }

        &.away-win.selected {
          border-color: var(--warning-500);
          background: var(--warning-50);
          color: var(--warning-700);

          &:hover:not(:disabled) {
            background: var(--error-50);
            border-color: var(--error-400);
            color: var(--error-700);
            transform: scale(0.98);
          }
        }

        &.draw.selected {
          border-color: var(--info-500);
          background: var(--info-50);
          color: var(--info-700);

          &:hover:not(:disabled) {
            background: var(--error-50);
            border-color: var(--error-400);
            color: var(--error-700);
            transform: scale(0.98);
          }
        }


      }
    }


  }

  .auth-message,
  .game-status-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--warning-50);
    border: 1px solid var(--warning-200);
    border-radius: var(--radius-md);
    color: var(--warning-700);
    margin-bottom: var(--spacing-lg);

    i {
      color: var(--warning-500);
    }
  }

  .results-section {
    margin-top: var(--spacing-lg); // More separation from voting buttons
    clear: both; // Ensure it appears below voting buttons

    .results-title {
      font-size: var(--text-base);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
      margin: 0 0 var(--spacing-md) 0;
    }

    .results-bars {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);

      .result-bar {
        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-xs);

          .team-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            .team-logo-small {
              width: 20px;
              height: 20px;
              border-radius: 50%;
              object-fit: cover;
            }

            i {
              color: var(--text-primary); // Better visibility
              font-size: var(--text-base);
            }

            .team-name {
              font-size: var(--text-sm);
              font-weight: var(--font-weight-semibold); // Bolder for better visibility
              color: var(--text-primary);
            }
          }

          .percentage {
            font-size: var(--text-base); // Slightly larger
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
          }
        }

        .progress-bar {
          width: 100%;
          height: 10px; // Slightly taller for better visibility
          background: var(--surface-secondary); // Better contrast
          border-radius: var(--radius-full);
          overflow: hidden;
          margin-bottom: var(--spacing-xs);

          .progress-fill {
            height: 100%;
            border-radius: var(--radius-full);
            transition: width 0.3s ease;

            &.home-win {
              background: linear-gradient(90deg, var(--success-400), var(--success-500));
            }

            &.away-win {
              background: linear-gradient(90deg, var(--warning-400), var(--warning-500));
            }

            &.draw {
              background: linear-gradient(90deg, var(--info-400), var(--info-500));
            }
          }
        }

        .vote-count {
          font-size: var(--text-sm); // Slightly larger
          color: var(--text-primary); // Better visibility
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }

  .no-predictions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    font-style: italic;

    i {
      color: var(--text-tertiary);
    }
  }
}
