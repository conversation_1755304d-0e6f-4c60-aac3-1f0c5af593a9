import { ComponentFixture, TestBed } from '@angular/core/testing';

import { GameDetailsComponent } from './game-details.component';

describe('GameDetailsComponent', () => {
  let component: GameDetailsComponent;
  let fixture: ComponentFixture<GameDetailsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [GameDetailsComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(GameDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('URL conversion for iframe embedding', () => {
    beforeEach(() => {
      // Mock window.location.hostname for Twitch parent parameter
      Object.defineProperty(window, 'location', {
        value: { hostname: 'localhost' },
        writable: true
      });
    });

    it('should convert Twitch URLs to embed format', () => {
      const twitchUrl = 'https://twitch.tv/testchannel';
      const embedUrl = (component as any).convertToEmbedUrl(twitchUrl);
      expect(embedUrl).toBe('https://player.twitch.tv/?channel=testchannel&parent=localhost&autoplay=false');
    });

    it('should convert YouTube watch URLs to embed format', () => {
      const youtubeUrl = 'https://youtube.com/watch?v=dQw4w9WgXcQ';
      const embedUrl = (component as any).convertToEmbedUrl(youtubeUrl);
      expect(embedUrl).toBe('https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=0');
    });

    it('should convert YouTube short URLs to embed format', () => {
      const youtubeShortUrl = 'https://youtu.be/dQw4w9WgXcQ';
      const embedUrl = (component as any).convertToEmbedUrl(youtubeShortUrl);
      expect(embedUrl).toBe('https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=0');
    });

    it('should handle YouTube live channel URLs', () => {
      const youtubeLiveUrl = 'https://youtube.com/channel/UC123456789/live';
      const embedUrl = (component as any).convertToEmbedUrl(youtubeLiveUrl);
      expect(embedUrl).toBe('https://www.youtube.com/embed/live_stream?channel=UC123456789&autoplay=0');
    });

    it('should return null for unsupported URLs', () => {
      const unsupportedUrl = 'https://example.com/stream';
      const embedUrl = (component as any).convertToEmbedUrl(unsupportedUrl);
      expect(embedUrl).toBeNull();
    });

    it('should return null for malformed URLs', () => {
      const malformedUrl = 'not-a-url';
      const embedUrl = (component as any).convertToEmbedUrl(malformedUrl);
      expect(embedUrl).toBeNull();
    });
  });
});
