.cms-table-container {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    
    h3 {
      margin: 0;
      font-size: 1.3rem;
      color: var(--text-primary);
      
      i {
        margin-right: 10px;
        color: var(--accent-color);
      }
    }
    
    .table-actions {
      .coming-soon {
        color: var(--text-secondary);
        font-style: italic;
        font-size: 0.9rem;
      }
    }
  }
  
  .table-wrapper {
    overflow-x: auto;
    
    .cms-table {
      width: 100%;
      border-collapse: collapse;
      
      thead {
        background: var(--table-header-background);
        
        th {
          padding: 15px 12px;
          text-align: left;
          font-weight: 600;
          color: var(--text-primary);
          border-bottom: 2px solid var(--border-color);
          font-size: 0.9rem;
          white-space: nowrap;
        }
      }
      
      tbody {
        .table-row {
          border-bottom: 1px solid var(--border-color);
          transition: background-color 0.2s ease;
          
          &:hover {
            background: var(--hover-background);
          }
          
          td {
            padding: 12px;
            vertical-align: middle;
            
            &.league-cell {
              .league-info {
                display: flex;
                align-items: center;
                gap: 12px;
                
                .league-logo {
                  width: 40px;
                  height: 40px;
                  border-radius: 8px;
                  object-fit: cover;
                  border: 2px solid var(--border-color);
                }
                
                .league-details {
                  display: flex;
                  flex-direction: column;
                  
                  .league-name {
                    font-weight: 600;
                    color: var(--text-primary);
                    font-size: 0.95rem;
                  }
                  
                  .league-id {
                    font-size: 0.8rem;
                    color: var(--text-secondary);
                    font-family: monospace;
                  }
                }
              }
            }
            
            .team-count {
              display: flex;
              align-items: center;
              gap: 6px;
              color: var(--text-primary);
              
              i {
                color: var(--accent-color);
                font-size: 0.9rem;
              }
              
              span {
                font-weight: 500;
              }
            }
            
            .season-text {
              font-weight: 500;
              color: var(--text-primary);
            }
            
            &.actions-cell {
              .action-buttons {
                display: flex;
                gap: 6px;
                
                .action-btn {
                  width: 32px;
                  height: 32px;
                  border: none;
                  border-radius: 6px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 0.8rem;
                  transition: all 0.2s ease;
                  
                  &.view-btn {
                    background: var(--info-color);
                    color: white;
                    
                    &:hover {
                      background: var(--info-hover);
                    }
                  }
                  
                  &.edit-btn {
                    background: var(--warning-color);
                    color: white;
                    
                    &:hover {
                      background: var(--warning-hover);
                    }
                  }
                  
                  &.delete-btn {
                    background: var(--danger-color);
                    color: white;
                    
                    &:hover {
                      background: var(--danger-hover);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      
      .empty-icon {
        font-size: 4rem;
        color: var(--text-secondary);
        margin-bottom: 20px;
        opacity: 0.5;
      }
      
      h4 {
        font-size: 1.5rem;
        margin-bottom: 10px;
        color: var(--text-primary);
      }
      
      p {
        color: var(--text-secondary);
        margin-bottom: 30px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .cms-table-container {
    .table-header {
      flex-direction: column;
      gap: 15px;
      align-items: stretch;
      
      .table-actions {
        text-align: center;
      }
    }
    
    .cms-table {
      font-size: 0.85rem;
      
      th, td {
        padding: 8px 6px;
      }
      
      .league-info {
        .league-logo {
          width: 32px;
          height: 32px;
        }
      }
      
      .action-buttons {
        .action-btn {
          width: 28px;
          height: 28px;
          font-size: 0.7rem;
        }
      }
    }
  }
}
