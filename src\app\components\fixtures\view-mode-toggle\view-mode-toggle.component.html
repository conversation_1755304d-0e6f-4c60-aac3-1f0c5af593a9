<div class="fixtures-header" *ngIf="!hideTitle">
    <h1 class="page-title">
        <i class="fas fa-calendar-check"></i>
        League Fixtures
    </h1>

    <!-- View Toggle -->
    <div class="view-toggle">
        <button class="toggle-btn" 
                [class.active]="isActive('fixtures')" 
                (click)="onViewModeChange('fixtures')">
            <i class="fas fa-list"></i>
            By Fixture
        </button>
        <button class="toggle-btn" 
                [class.active]="isActive('date')" 
                (click)="onViewModeChange('date')">
            <i class="fas fa-calendar-alt"></i>
            By Date
        </button>
        <button class="toggle-btn" 
                [class.active]="isActive('playoffs')" 
                (click)="onViewModeChange('playoffs')">
            <i class="fas fa-trophy"></i>
            Playoffs
        </button>
    </div>
</div>
