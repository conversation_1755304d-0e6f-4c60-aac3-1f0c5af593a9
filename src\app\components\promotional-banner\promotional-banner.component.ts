import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';

export interface PromotionalMessage {
  title: string;
  subtitle: string;
  ctaText: string;
  icon: string;
  variant: 'primary' | 'secondary' | 'accent';
}

@Component({
  selector: 'app-promotional-banner',
  templateUrl: './promotional-banner.component.html',
  styleUrls: ['./promotional-banner.component.scss']
})
export class PromotionalBannerComponent {
  @Input() message: PromotionalMessage = {
    title: 'Connect Your Player Today!',
    subtitle: 'Join the league and track your stats, compare with others, and climb the rankings!',
    ctaText: 'Sign Up Now',
    icon: 'fas fa-user-plus',
    variant: 'primary'
  };

  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() dismissible: boolean = true;
  @Input() showSecondaryAction: boolean = true;

  isDismissed = false;

  constructor(private router: Router) {}

  onSignUp(): void {
    this.router.navigate(['/sign-up']);
  }

  onLogin(): void {
    this.router.navigate(['/login']);
  }

  dismiss(): void {
    this.isDismissed = true;
  }

  // Predefined promotional messages for different contexts
  static readonly MESSAGES = {
    DASHBOARD: {
      title: 'Join the League!',
      subtitle: 'Connect your player profile and see where you rank among the best!',
      ctaText: 'Connect Player',
      icon: 'fas fa-trophy',
      variant: 'primary' as const
    },
    PLAYER_DETAILS: {
      title: 'Track Your Own Stats!',
      subtitle: 'Want to see your performance analytics? Connect your player profile today!',
      ctaText: 'Connect Player',
      icon: 'fas fa-chart-line',
      variant: 'accent' as const
    },
    FIXTURES: {
      title: 'Never Miss a Match!',
      subtitle: 'Get notifications about your team\'s fixtures and results!',
      ctaText: 'Sign Up Now',
      icon: 'fas fa-calendar-check',
      variant: 'secondary' as const
    },
    LEAGUE_TABLE: {
      title: 'See Where You Rank!',
      subtitle: 'Connect your player profile and track your team\'s position in real-time!',
      ctaText: 'Join League',
      icon: 'fas fa-medal',
      variant: 'primary' as const
    },
    TOP_SCORERS: {
      title: 'Race to the Golden Boot!',
      subtitle: 'Think you can make it to the top? Connect your player and start scoring!',
      ctaText: 'Join Competition',
      icon: 'fas fa-futbol',
      variant: 'accent' as const
    },
    GAME_DETAILS: {
      title: 'Be Part of the Action!',
      subtitle: 'Connect your player profile to see detailed match analytics and your performance!',
      ctaText: 'Connect Player',
      icon: 'fas fa-gamepad',
      variant: 'secondary' as const
    }
  };
}
