import { AddNewsRequestModel } from "../../controllers/news-controller";
import { INews } from "../../models/news";

export interface INewsRepository {
    getAllNews(): Promise<INews[]>;

    addNews(newsData: AddNewsRequestModel): Promise<INews>;

    deleteNews(newsId: string): Promise<boolean>;
    updateNews(newsId: string, newsData: Partial<AddNewsRequestModel>): Promise<INews>;
    likeNews(newsId: string, userId: string): Promise<INews>;
    unlikeNews(newsId: string, userId: string): Promise<INews>;
}