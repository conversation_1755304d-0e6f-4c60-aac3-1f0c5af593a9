import { Component, Input, Output, EventEmitter } from '@angular/core';
import { PlayerDTO } from '@pro-clubs-manager/shared-dtos';

@Component({
  selector: 'app-player-header',
  templateUrl: './player-header.component.html',
  styleUrls: ['./player-header.component.scss']
})
export class PlayerHeaderComponent {
  @Input() player!: PlayerDTO;
  @Input() editPlayerMode: boolean = false;
  @Input() isViewOnly: boolean = false;

  @Output() backClick = new EventEmitter<void>();
  @Output() teamNavigate = new EventEmitter<void>();
  @Output() editClick = new EventEmitter<void>();
  @Output() cancelEditClick = new EventEmitter<void>();
  @Output() saveClick = new EventEmitter<void>();

  onBackClick(): void {
    this.backClick.emit();
  }

  onTeamNavigate(): void {
    this.teamNavigate.emit();
  }

  onEditClick(): void {
    this.editClick.emit();
  }

  onCancelEditClick(): void {
    this.cancelEditClick.emit();
  }

  onSaveClick(): void {
    this.saveClick.emit();
  }
}
