.cms-table-container {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    
    h3 {
      margin: 0;
      font-size: 1.3rem;
      color: var(--text-primary);
      
      i {
        margin-right: 10px;
        color: var(--accent-color);
      }
    }
    
    .table-actions {
      .create-btn {
        background: var(--success-color);
        color: white;
        border: none;
        padding: 10px 16px;
        border-radius: 6px;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        
        &:hover {
          background: var(--success-hover);
          transform: translateY(-1px);
        }
      }
    }
  }
  
  .table-wrapper {
    overflow-x: auto;
    
    .cms-table {
      width: 100%;
      border-collapse: collapse;
      
      thead {
        background: var(--table-header-background);
        
        th {
          padding: 15px 12px;
          text-align: left;
          font-weight: 600;
          color: var(--text-primary);
          border-bottom: 2px solid var(--border-color);
          font-size: 0.9rem;
          white-space: nowrap;
        }
      }
      
      tbody {
        .table-row {
          border-bottom: 1px solid var(--border-color);
          transition: background-color 0.2s ease;
          
          &:hover {
            background: var(--hover-background);
          }
          
          td {
            padding: 12px;
            vertical-align: middle;
            
            &.title-cell {
              .news-title {
                display: flex;
                flex-direction: column;
                
                .title-text {
                  font-weight: 600;
                  color: var(--text-primary);
                  font-size: 0.95rem;
                  line-height: 1.3;
                }
                
                .news-id {
                  font-size: 0.8rem;
                  color: var(--text-secondary);
                  font-family: monospace;
                  margin-top: 2px;
                }
              }
            }
            
            .type-badge {
              display: inline-block;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 0.8rem;
              font-weight: 500;
              text-transform: uppercase;
              
              &.type-transfer {
                background: #74b9ff;
                color: white;
              }

              &.type-freeagent {
                background: #00b894;
                color: white;
              }

              &.type-general {
                background: #fdcb6e;
                color: #333;
              }
            }
            
            &.content-cell {
              max-width: 300px;
              
              .content-preview {
                color: var(--text-secondary);
                font-size: 0.9rem;
                line-height: 1.4;
                display: block;
                word-wrap: break-word;
              }
            }
            
            .date-text {
              color: var(--text-primary);
              font-size: 0.9rem;
              white-space: nowrap;
            }
            
            &.actions-cell {
              .action-buttons {
                display: flex;
                gap: 6px;
                
                .action-btn {
                  width: 32px;
                  height: 32px;
                  border: none;
                  border-radius: 6px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 0.8rem;
                  transition: all 0.2s ease;
                  
                  &.view-btn {
                    background: var(--info-color);
                    color: white;
                    
                    &:hover {
                      background: var(--info-hover);
                    }
                  }
                  
                  &.edit-btn {
                    background: var(--warning-color);
                    color: white;
                    
                    &:hover {
                      background: var(--warning-hover);
                    }
                  }
                  
                  &.delete-btn {
                    background: var(--danger-color);
                    color: white;
                    
                    &:hover:not(:disabled) {
                      background: var(--danger-hover);
                    }
                    
                    &:disabled {
                      opacity: 0.6;
                      cursor: not-allowed;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      
      .empty-icon {
        font-size: 4rem;
        color: var(--text-secondary);
        margin-bottom: 20px;
        opacity: 0.5;
      }
      
      h4 {
        font-size: 1.5rem;
        margin-bottom: 10px;
        color: var(--text-primary);
      }
      
      p {
        color: var(--text-secondary);
        margin-bottom: 30px;
      }
      
      .create-btn {
        background: var(--success-color);
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        
        &:hover {
          background: var(--success-hover);
          transform: translateY(-2px);
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .cms-table-container {
    .table-header {
      flex-direction: column;
      gap: 15px;
      align-items: stretch;
      
      .table-actions {
        text-align: center;
      }
    }
    
    .cms-table {
      font-size: 0.85rem;
      
      th, td {
        padding: 8px 6px;
      }
      
      .content-cell {
        max-width: 200px;
      }
      
      .action-buttons {
        .action-btn {
          width: 28px;
          height: 28px;
          font-size: 0.7rem;
        }
      }
    }
  }
}
