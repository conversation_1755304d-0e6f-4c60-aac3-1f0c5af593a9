/* === MODERN ANIMATION SYSTEM === */

/* === KEYFRAME ANIMATIONS === */

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes wiggle {
    0%, 7% {
        transform: rotateZ(0);
    }
    15% {
        transform: rotateZ(-15deg);
    }
    20% {
        transform: rotateZ(10deg);
    }
    25% {
        transform: rotateZ(-10deg);
    }
    30% {
        transform: rotateZ(6deg);
    }
    35% {
        transform: rotateZ(-4deg);
    }
    40%, 100% {
        transform: rotateZ(0);
    }
}

/* === ANIMATION UTILITY CLASSES === */

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.4s ease-out;
}

.animate-slide-in-down {
    animation: slideInDown 0.4s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.3s ease-out;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-wiggle {
    animation: wiggle 1s ease-in-out;
}

/* === STAGGERED ANIMATIONS === */
.stagger-children > * {
    animation: fadeInUp 0.6s ease-out;
}

.stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-children > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-children > *:nth-child(6) { animation-delay: 0.6s; }
.stagger-children > *:nth-child(7) { animation-delay: 0.7s; }
.stagger-children > *:nth-child(8) { animation-delay: 0.8s; }

/* === ENHANCED HOVER ANIMATIONS === */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }
}

.hover-scale {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        transform: scale(1.05);
    }
}

.hover-glow {
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
    }
}

/* === LOADING ANIMATIONS === */
.skeleton-loading {
    background: linear-gradient(90deg, var(--surface-secondary) 25%, var(--surface-tertiary) 50%, var(--surface-secondary) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* === ENTRANCE ANIMATIONS === */
.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* === INTERACTIVE ANIMATIONS === */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;

    &.in-view {
        opacity: 1;
        transform: translateY(0);
    }
}

/* === BUTTON ANIMATIONS === */
.btn-animated {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
    }

    &:hover::before {
        left: 100%;
    }

    &:active {
        transform: scale(0.98);
    }
}

/* === UTILITY ANIMATION CLASSES === */
.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-wiggle {
    animation: wiggle 0.5s ease-in-out;
}

.animate-heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
    40%, 43% { transform: translate3d(0, -30px, 0); }
    70% { transform: translate3d(0, -15px, 0); }
    90% { transform: translate3d(0, -4px, 0); }
}

@keyframes wiggle {
    0%, 7% { transform: rotateZ(0); }
    15% { transform: rotateZ(-15deg); }
    20% { transform: rotateZ(10deg); }
    25% { transform: rotateZ(-10deg); }
    30% { transform: rotateZ(6deg); }
    35% { transform: rotateZ(-4deg); }
    40%, 100% { transform: rotateZ(0); }
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

/* === PERFORMANCE OPTIMIZATIONS === */
.animate-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* === RESPONSIVE ANIMATIONS === */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

.stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-children > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-children > *:nth-child(6) { animation-delay: 0.6s; }
.stagger-children > *:nth-child(7) { animation-delay: 0.7s; }
.stagger-children > *:nth-child(8) { animation-delay: 0.8s; }

/* === HOVER ANIMATIONS === */
.hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
}

.hover-lift-lg {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    
    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }
}

.hover-scale {
    transition: transform 0.2s ease-in-out;
    
    &:hover {
        transform: scale(1.02);
    }
}

.hover-scale-lg {
    transition: transform 0.2s ease-in-out;
    
    &:hover {
        transform: scale(1.05);
    }
}

.hover-glow {
    transition: box-shadow 0.2s ease-in-out;
    
    &:hover {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
    }
}

/* === LOADING ANIMATIONS === */
.loading-shimmer {
    background: linear-gradient(90deg, var(--surface-tertiary) 25%, var(--surface-hover) 50%, var(--surface-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
    
    &::before,
    &::after {
        content: '';
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: var(--text-muted);
        animation: pulse 1.4s ease-in-out infinite;
    }
    
    &::before {
        animation-delay: 0.2s;
    }
    
    &::after {
        animation-delay: 0.4s;
    }
}

/* === TRANSITION UTILITIES === */
.transition-all {
    transition: all 0.2s ease-in-out;
}

.transition-colors {
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.transition-transform {
    transition: transform 0.2s ease-in-out;
}

.transition-opacity {
    transition: opacity 0.2s ease-in-out;
}

/* === REDUCED MOTION === */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
