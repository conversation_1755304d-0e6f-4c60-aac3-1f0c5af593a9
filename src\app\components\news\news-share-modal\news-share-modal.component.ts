import { Component, Input, Output, EventEmitter } from '@angular/core';
import { News } from '../news.model';
import { NewsImageGeneratorService } from '../../../services/news-image-generator.service';
import { NotificationService } from '../../../services/notification.service';

@Component({
  selector: 'app-news-share-modal',
  templateUrl: './news-share-modal.component.html',
  styleUrl: './news-share-modal.component.scss'
})
export class NewsShareModalComponent {
  @Input() show: boolean = false;
  @Input() selectedNews: News | null = null;
  @Output() close = new EventEmitter<void>();

  generatedImageUrl: string | null = null;

  constructor(
    private newsImageGeneratorService: NewsImageGeneratorService,
    private notificationService: NotificationService
  ) { }

  closeModal(): void {
    this.show = false;
    this.selectedNews = null;
    this.generatedImageUrl = null;
    this.close.emit();
  }

  shareViaWhatsApp(): void {
    if (!this.selectedNews) return;

    const text = this.generateShareText();
    const url = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
  }

  shareViaTwitter(): void {
    if (!this.selectedNews) return;

    const text = this.generateShareText();
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
  }

  shareViaFacebook(): void {
    if (!this.selectedNews) return;

    const text = this.generateShareText();
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}&quote=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
  }

  shareViaTelegram(): void {
    if (!this.selectedNews) return;

    const text = this.generateShareText();
    const url = `https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
  }

  async copyToClipboard(): Promise<void> {
    if (!this.selectedNews) return;

    const text = this.generateShareText();

    try {
      await navigator.clipboard.writeText(text);
      this.notificationService.success('Link copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      this.notificationService.error('Failed to copy to clipboard');
    }
  }

  async generateShareImage(): Promise<void> {
    if (!this.selectedNews) return;

    try {
      this.generatedImageUrl = await this.newsImageGeneratorService.generateShareImage(this.selectedNews);
      this.notificationService.success('Share image generated successfully!');
    } catch (error) {
      console.error('Error generating share image:', error);
      this.notificationService.error('Failed to generate image');
    }
  }

  downloadImage(): void {
    if (!this.generatedImageUrl) return;

    const link = document.createElement('a');
    link.download = `news-${this.selectedNews?.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.png`;
    link.href = this.generatedImageUrl;
    link.click();
  }

  shareImageViaWhatsApp(): void {
    if (!this.generatedImageUrl) return;

    // For WhatsApp image sharing, we'll need to download the image first
    // as WhatsApp Web doesn't support direct base64 image sharing
    this.downloadImage();
    this.notificationService.info('Image downloaded! You can now share it via WhatsApp.');
  }

  private generateShareText(): string {
    if (!this.selectedNews) return '';

    const typeLabel = this.getNewsTypeLabel(this.selectedNews.type);
    const date = new Date(this.selectedNews.createdAt).toLocaleDateString();

    return `🏆 ${typeLabel} News - ${this.selectedNews.title}\n\n${this.selectedNews.content}\n\n📅 ${date}\n\n`;
  }

  getNewsTypeLabel(type: string): string {
    return type === 'FreeAgent' ? 'Free Agent' : type;
  }
}
