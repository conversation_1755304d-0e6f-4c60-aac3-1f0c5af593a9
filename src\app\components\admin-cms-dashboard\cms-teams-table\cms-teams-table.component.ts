import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { TeamDTO } from '@pro-clubs-manager/shared-dtos';
import { TeamService } from '../../../services/team.service';
import { NotificationService } from '../../../services/notification.service';

@Component({
  selector: 'app-cms-teams-table',
  templateUrl: './cms-teams-table.component.html',
  styleUrl: './cms-teams-table.component.scss'
})
export class CmsTeamsTableComponent {
  @Input() teams: TeamDTO[] = [];
  @Output() teamDeleted = new EventEmitter<void>();
  @Output() teamUpdated = new EventEmitter<void>();

  deletingTeamIds = new Set<string>();

  constructor(
    private teamService: TeamService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  async deleteTeam(team: TeamDTO): Promise<void> {
    const confirmDelete = confirm(
      `⚠️ Are you sure you want to delete "${team.name}"?\n\n` +
      'This action cannot be undone and will permanently remove:\n' +
      '• Team profile and statistics\n' +
      '• All game history\n' +
      '• Player associations (players will become free agents)\n' +
      '• League participations'
    );

    if (!confirmDelete) {
      return;
    }

    try {
      this.deletingTeamIds.add(team.id);
      await this.teamService.deleteTeam(team.id);
      this.notificationService.success(`${team.name} deleted successfully`);
      this.teamDeleted.emit();
    } catch (error: any) {
      console.error('Error deleting team:', error);
      this.notificationService.error(error.message || 'Failed to delete team');
    } finally {
      this.deletingTeamIds.delete(team.id);
    }
  }

  viewTeamDetails(teamId: string): void {
    this.router.navigate(['/team-details', { id: teamId }]);
  }

  editTeam(teamId: string): void {
    // Navigate to team details where they can edit
    this.viewTeamDetails(teamId);
  }

  getTeamPlayerCount(team: TeamDTO): number {
    return team.players?.length || 0;
  }

  getTeamCaptainName(team: TeamDTO): string {
    return team.captain?.name || 'No Captain';
  }

  getTeamLeague(team: TeamDTO): string {
    return team.leagueId ? 'League Team' : 'No League';
  }

  getTeamStats(team: TeamDTO): string {
    if (!team.stats) {
      return 'No stats';
    }

    const wins = team.stats.wins || 0;
    const draws = team.stats.draws || 0;
    const losses = team.stats.losses || 0;
    const games = team.stats.games || 0;

    return `${wins}W ${draws}D ${losses}L (${games} games)`;
  }

  isTeamDeleting(teamId: string): boolean {
    return this.deletingTeamIds.has(teamId);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/Icons/Team.png';
    }
  }
}
