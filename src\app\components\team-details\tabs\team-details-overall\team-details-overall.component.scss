/* === MODERN TEAM OVERVIEW DESIGN === */

.team-overview-container {
    padding: var(--spacing-lg);
    min-height: 500px;

    @media (max-width: 768px) {
        padding: var(--spacing-md);
    }
}

/* === TEAM PROFILE HEADER === */
.team-profile-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--primary-400), var(--primary-600));
    }

    @media (max-width: 768px) {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }

    .team-logo-section {
        flex-shrink: 0;

        .team-logo {
            width: 120px;
            height: 120px;
            border-radius: var(--radius-2xl);
            object-fit: cover;
            border: 4px solid var(--border-primary);
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease-in-out;

            &:hover {
                border-color: var(--primary);
                box-shadow: 0 0 30px rgba(99, 102, 241, 0.3);
                transform: scale(1.05);
            }

            @media (max-width: 768px) {
                width: 100px;
                height: 100px;
            }

            @media (max-width: 480px) {
                width: 80px;
                height: 80px;
            }
        }
    }

    .team-info-section {
        flex: 1;

        .team-name {
            font-family: var(--font-sans);
            font-size: var(--text-3xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-md) 0;
            line-height: 1.2;

            @media (max-width: 768px) {
                font-size: var(--text-2xl);
            }

            @media (max-width: 480px) {
                font-size: var(--text-xl);
            }
        }

        .team-quick-stats {
            display: flex;
            gap: var(--spacing-lg);
            flex-wrap: wrap;

            @media (max-width: 768px) {
                justify-content: center;
                gap: var(--spacing-md);
            }

            @media (max-width: 480px) {
                flex-direction: column;
                gap: var(--spacing-sm);
            }

            .quick-stat {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                background: var(--surface-secondary);
                border: 1px solid var(--border-primary);
                border-radius: var(--radius-lg);
                padding: var(--spacing-sm) var(--spacing-md);
                font-family: var(--font-sans);
                font-size: var(--text-sm);
                font-weight: var(--font-weight-medium);
                color: var(--text-secondary);
                transition: all 0.2s ease-in-out;

                &:hover {
                    background: var(--surface-hover);
                    border-color: var(--primary);
                    transform: translateY(-1px);
                    box-shadow: var(--shadow-sm);
                }

                i {
                    color: var(--primary);
                    font-size: var(--text-base);
                }

                @media (max-width: 480px) {
                    justify-content: center;
                    padding: var(--spacing-xs) var(--spacing-sm);
                    font-size: var(--text-xs);
                }
            }
        }
    }
}

.overview-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-xl);
    align-items: start;

    @media (max-width: 1200px) {
        gap: var(--spacing-lg);
    }

    @media (max-width: 900px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    @media (max-width: 768px) {
        gap: var(--spacing-md);
    }
}

/* === SECTION STYLING === */
.team-management-section,
.main-content-area {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.team-management-section,
.stats-section,
.squad-section {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.section-header {
    background: var(--surface-secondary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--spacing-md) var(--spacing-lg);

    .section-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-family: var(--font-sans);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        margin: 0;

        i {
            color: var(--primary);
            font-size: var(--text-base);
        }

        @media (max-width: 768px) {
            font-size: var(--text-base);
        }
    }
}

.section-content {
    padding: var(--spacing-lg);

    @media (max-width: 768px) {
        padding: var(--spacing-md);
    }

    &.stats-content {
        min-height: 120px;
        display: flex;
        align-items: center;
    }

    &.squad-content {
        max-height: 723px;
        overflow-y: auto;

        /* Custom scrollbar */
        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: var(--surface-secondary);
            border-radius: var(--radius-sm);
        }

        &::-webkit-scrollbar-thumb {
            background: var(--border-primary);
            border-radius: var(--radius-sm);

            &:hover {
                background: var(--border-secondary);
            }
        }
    }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
    .overview-grid {
        .main-content-area {
            gap: var(--spacing-md);
        }
    }

    .section-header {
        padding: var(--spacing-sm) var(--spacing-md);

        .section-title {
            font-size: var(--text-sm);

            i {
                font-size: var(--text-sm);
            }
        }
    }
}

/* === ANIMATION === */
.team-overview-container {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}