import { Request, Response, NextFunction } from 'express';
import { AuthUtils, JWTPayload } from '../utils/auth';
import User, { IUser } from '../models/user';
import { IUserService } from '../interfaces/user/user-service.interface';
import logger from '../config/logger';
import { LoggerUtils } from '../utils/logger-utils';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: IUser;
      userId?: string;
      captainTeams?: string[];
      userTeamSide?: 'home' | 'away';
    }
  }
}

/**
 * Middleware to authenticate JWT tokens
 */
export const authenticateToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = AuthUtils.extractTokenFromHeader(authHeader);

    if (!token) {
      res.status(401).json({ message: 'Access token required' });
      return;
    }

    // Verify the token
    const payload: JWTPayload = AuthUtils.verifyToken(token);

    // Fetch the user from database to ensure they still exist
    const user = await User.findById(payload.userId);
    if (!user) {
      res.status(401).json({ message: 'User not found' });
      return;
    }

    // Attach user to request object
    req.user = user;
    req.userId = user.id;

    next();
  } catch (error: any) {
    if (error.message === 'Invalid or expired token') {
      res.status(401).json({ message: 'Invalid or expired token' });
    } else {
      res.status(500).json({ message: 'Authentication error' });
    }
  }
};

/**
 * Middleware to check if user is admin
 */
export const requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({ message: 'Authentication required' });
    return;
  }

  console.log('Admin check - User role:', req.user.role, 'User ID:', req.user.id);

  if (req.user.role !== 'admin') {
    res.status(403).json({ message: 'Admin access required', userRole: req.user.role });
    return;
  }

  next();
};

/**
 * Middleware to check if user is team captain or admin
 */
export const requireTeamCaptain = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  if (!req.user) {
    res.status(401).json({ message: 'Authentication required' });
    return;
  }

  // Admin can always perform team operations
  if (req.user.role === 'admin') {
    next();
    return;
  }

  try {
    // Check if user has any associated players that are team captains
    const userPlayers = req.user.associatedPlayers;

    if (userPlayers.length === 0) {
      res.status(403).json({ message: 'You must be associated with a player to perform this action' });
      return;
    }

    // Import Team model to check captain status
    const { default: Team } = await import('../models/team');

    // Check if any of the user's players are team captains
    const teams = await Team.find({ captain: { $in: userPlayers } });

    if (teams.length === 0) {
      res.status(403).json({ message: 'You must be a team captain to perform this action' });
      return;
    }

    // Store the teams where user is captain for potential use in the route handler
    req.captainTeams = teams.map(team => team.id);

    next();
  } catch (error: any) {
    LoggerUtils.errorWithRequest(`Error checking team captain status: ${error.message}`, req, error);
    res.status(500).json({ message: 'Authorization check failed' });
  }
};

/**
 * Middleware to check if user can edit a specific team (admin or team captain)
 */
export const requireTeamEditPermission = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  if (!req.user) {
    res.status(401).json({ message: 'Authentication required' });
    return;
  }

  // Admin can always edit teams
  if (req.user.role === 'admin') {
    next();
    return;
  }

  try {
    const teamId = req.params.id || req.params.teamId;
    if (!teamId) {
      res.status(400).json({ message: 'Team ID required' });
      return;
    }

    // Check if user has any associated players
    const userPlayers = req.user.associatedPlayers;
    if (userPlayers.length === 0) {
      res.status(403).json({ message: 'You must be associated with a player to perform this action' });
      return;
    }

    // Import Team model to check captain status
    const { default: Team } = await import('../models/team');

    // Check if user is captain of this specific team
    const team = await Team.findById(teamId);
    if (!team) {
      res.status(404).json({ message: 'Team not found' });
      return;
    }

    const isCaptain = userPlayers.some(playerId =>
      team.captain && team.captain.toString() === playerId.toString()
    );

    if (!isCaptain) {
      res.status(403).json({ message: 'You must be the captain of this team to perform this action' });
      return;
    }

    next();
  } catch (error: any) {
    LoggerUtils.errorWithRequest(`Error checking team edit permission: ${error.message}`, req, error);
    res.status(500).json({ message: 'Authorization check failed' });
  }
};

/**
 * Middleware to check if user can edit a specific game (admin or captain of one of the teams)
 */
export const requireGameEditPermission = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  if (!req.user) {
    res.status(401).json({ message: 'Authentication required' });
    return;
  }

  // Admin can always edit games
  if (req.user.role === 'admin') {
    next();
    return;
  }

  try {
    const gameId = req.params.id || req.params.gameId;
    if (!gameId) {
      res.status(400).json({ message: 'Game ID required' });
      return;
    }

    // Check if user has any associated players
    const userPlayers = req.user.associatedPlayers;
    if (userPlayers.length === 0) {
      res.status(403).json({ message: 'You must be associated with a player to perform this action' });
      return;
    }

    // Import Game and Team models
    const { default: Game } = await import('../models/game/game');
    const { default: Team } = await import('../models/team');

    // Get the game to find the teams involved
    const game = await Game.findById(gameId).populate('homeTeam awayTeam');
    if (!game) {
      res.status(404).json({ message: 'Game not found' });
      return;
    }

    // Check if user is captain of either team
    const homeTeam = await Team.findById(game.homeTeam);
    const awayTeam = await Team.findById(game.awayTeam);

    const isHomeCaptain = homeTeam?.captain && userPlayers.some(playerId =>
      homeTeam.captain.toString() === playerId.toString()
    );

    const isAwayCaptain = awayTeam?.captain && userPlayers.some(playerId =>
      awayTeam.captain.toString() === playerId.toString()
    );

    if (!isHomeCaptain && !isAwayCaptain) {
      res.status(403).json({ message: 'You must be the captain of one of the teams in this game to perform this action' });
      return;
    }

    // Store which team the user can edit for
    if (isHomeCaptain) req.userTeamSide = 'home';
    if (isAwayCaptain) req.userTeamSide = 'away';

    next();
  } catch (error: any) {
    logger.error(`Error checking game edit permission: ${error.message}`);
    res.status(500).json({ message: 'Authorization check failed' });
  }
};

/**
 * Middleware to check if user can edit a specific player's profile
 */
export const requirePlayerOwnership = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  if (!req.user) {
    res.status(401).json({ message: 'Authentication required' });
    return;
  }

  const playerId = req.params.playerId || req.body.playerId;

  if (!playerId) {
    res.status(400).json({ message: 'Player ID is required' });
    return;
  }

  // Check if user owns this player or is admin
  if (req.user.role === 'admin') {
    next();
    return;
  }

  try {
    // Import the container to get the user service
    const { container } = await import('../config/container.config');
    const userService = container.resolve<IUserService>('IUserService');

    const isOwner = await userService.checkPlayerOwnership(req.user.id, playerId);

    if (!isOwner) {
      res.status(403).json({ message: 'You can only edit players associated with your account' });
      return;
    }

    next();
  } catch (error: any) {
    logger.error(`Error checking player ownership in middleware: ${error.message}`);
    res.status(500).json({ message: 'Internal server error' });
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
export const optionalAuth = async (req: Request, _res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = AuthUtils.extractTokenFromHeader(authHeader);

    if (token) {
      const payload: JWTPayload = AuthUtils.verifyToken(token);
      const user = await User.findById(payload.userId);

      if (user) {
        req.user = user;
        req.userId = user.id;
      }
    }

    next();
  } catch (error) {
    // Silently continue without authentication
    next();
  }
};
