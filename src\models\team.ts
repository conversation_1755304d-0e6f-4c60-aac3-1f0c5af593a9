import mongoose, { Schema, Document } from "mongoose";
import { IPlayer } from "./player/player";
import { ACHIEVEMENT_TYPE } from "./season-achievement";

export type TeamWithPlayers = {
  id: string;
  name: string;
  league: mongoose.Types.ObjectId;
  imgUrl?: string;
  players: IPlayer[];
  captain?: mongoose.Types.ObjectId;
  seasonsHistory: ITeamSeason[];
  currentSeason?: ITeamSeason;
};

export interface ITeamStats {
  wins: number;
  losses: number;
  draws: number;
  goalsScored: number;
  goalsConceded: number;
  cleanSheets: number;
}

export interface ITeamSeason {
  seasonNumber: number;
  league: mongoose.Types.ObjectId;
  stats: ITeamStats;
  finalPosition?: number; // Final league position for completed seasons
}

export interface ITeamAchievementHistoryEntry {
  seasonNumber: number;
  league: mongoose.Types.ObjectId;
  leagueName: string;
  achievementType: ACHIEVEMENT_TYPE;
  rank?: number; // Final position or ranking
  stats: {
    wins?: number;
    losses?: number;
    draws?: number;
    goalsScored?: number;
    goalsConceded?: number;
    points?: number;
    goalDifference?: number;
  };
  description?: string;
  achievedDate: Date;
}

export interface ITeam extends Document {
  id: string;
  name: string;
  imgUrl?: string;
  league: mongoose.Types.ObjectId | null;
  players: mongoose.Types.ObjectId[];
  captain: mongoose.Types.ObjectId;
  seasonsHistory: ITeamSeason[];
  currentSeason?: ITeamSeason;
  achievementHistory: ITeamAchievementHistoryEntry[];
}

const teamStatsSchema = new Schema<ITeamStats>(
  {
    wins: { type: Number, default: 0 },
    losses: { type: Number, default: 0 },
    draws: { type: Number, default: 0 },
    goalsScored: { type: Number, default: 0 },
    goalsConceded: { type: Number, default: 0 },
    cleanSheets: { type: Number, default: 0 },
  },
  { _id: false }
);

const teamSeasonSchema = new Schema<ITeamSeason>(
  {
    seasonNumber: { type: Number, required: true },
    league: { type: mongoose.Schema.Types.ObjectId, ref: "League" },
    stats: teamStatsSchema,
    finalPosition: { type: Number, required: false },
  },
  { _id: false }
);

const teamAchievementHistorySchema = new Schema<ITeamAchievementHistoryEntry>(
  {
    seasonNumber: { type: Number, required: true },
    league: { type: mongoose.Schema.Types.ObjectId, ref: "League", required: true },
    leagueName: { type: String, required: true },
    achievementType: { type: String, enum: Object.values(ACHIEVEMENT_TYPE), required: true },
    rank: { type: Number, required: false },
    stats: {
      wins: { type: Number, required: false },
      losses: { type: Number, required: false },
      draws: { type: Number, required: false },
      goalsScored: { type: Number, required: false },
      goalsConceded: { type: Number, required: false },
      points: { type: Number, required: false },
      goalDifference: { type: Number, required: false },
    },
    description: { type: String, required: false },
    achievedDate: { type: Date, required: true, default: Date.now },
  },
  { _id: false }
);

const teamSchema: Schema<ITeam> = new Schema<ITeam>(
  {
    name: { type: String, required: true, unique: true },
    imgUrl: { type: String },
    league: { type: mongoose.Schema.Types.ObjectId, ref: "League" },
    players: [{ type: mongoose.Schema.Types.ObjectId, ref: "Player" }],
    captain: { type: mongoose.Schema.Types.ObjectId, ref: "Player" },
    seasonsHistory: [teamSeasonSchema],
    currentSeason: teamSeasonSchema,
    achievementHistory: [teamAchievementHistorySchema],
  },
  {
    toJSON: { virtuals: true },
    id: true, // Use 'id' instead of '_id'
  }
);

const Team = mongoose.model<ITeam>("Team", teamSchema);

export default Team;
