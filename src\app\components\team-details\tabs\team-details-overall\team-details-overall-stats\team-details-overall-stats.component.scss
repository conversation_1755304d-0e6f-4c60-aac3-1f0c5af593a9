/* === HOST COMPONENT STYLES === */
:host {
    display: block;
    width: 100%;
    height: 100%;
}

/* === REVOLUTIONARY STATS GRID === */
.quick-stats-section {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
    min-height: 120px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary), var(--primary-400));
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-md);

        @media (max-width: 1200px) {
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
        }

        @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }

        @media (max-width: 480px) {
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
        }

        .stat-card {
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-sm);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            gap: var(--spacing-xs);
            transition: all 0.3s ease-in-out;
            position: relative;
            overflow: hidden;
            min-height: 90px;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
                transition: left 0.5s ease-in-out;
            }

            &:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-xl);
                border-color: var(--primary);

                &::before {
                    left: 100%;
                }
            }

            .stat-icon {
                width: 32px;
                height: 32px;
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                color: var(--text-inverse);
                font-size: 1rem;
                margin-bottom: 2px;

                &.games {
                    background: linear-gradient(135deg, var(--primary), var(--primary-600));
                }

                &.wins {
                    background: linear-gradient(135deg, var(--success-500), var(--success-600));
                }

                &.draws {
                    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
                }

                &.losses {
                    background: linear-gradient(135deg, var(--error-500), var(--error-600));
                }
            }

            .stat-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 2px;

                .stat-value {
                    font-family: var(--font-sans);
                    font-size: var(--text-2xl);
                    font-weight: var(--font-weight-bold);
                    color: var(--text-primary);
                    line-height: 1;
                    margin: 0;

                    @media (max-width: 1200px) {
                        font-size: var(--text-xl);
                    }

                    @media (max-width: 768px) {
                        font-size: var(--text-lg);
                    }
                }

                .stat-label {
                    font-family: var(--font-sans);
                    font-size: 11px;
                    font-weight: var(--font-weight-semibold);
                    color: var(--text-tertiary);
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    line-height: 1;
                    margin: 0;
                    white-space: nowrap;

                    @media (max-width: 768px) {
                        font-size: 10px;
                    }
                }
            }
        }
    }
}
