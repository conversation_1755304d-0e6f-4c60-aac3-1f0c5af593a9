import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { News } from '../../components/news/news.model';

export interface NewsState {
  news: News[];
  lastNewsUpdate: Date | null;
  isNewsLoading: boolean;
  totalPages: number;
  currentPage: number;
}

@Injectable({
  providedIn: 'root'
})
export class NewsStateService {
  private readonly initialState: NewsState = {
    news: [],
    lastNewsUpdate: null,
    isNewsLoading: false,
    totalPages: 0,
    currentPage: 1
  };

  private stateSubject = new BehaviorSubject<NewsState>(this.initialState);
  public state$ = this.stateSubject.asObservable();

  // Cache duration in milliseconds (10 minutes)
  private readonly NEWS_CACHE_DURATION = 10 * 60 * 1000;

  constructor() {
    this.loadStateFromStorage();
  }

  get currentState(): NewsState {
    return this.stateSubject.value;
  }

  updateNews(news: News[], totalPages: number = 0, currentPage: number = 1): void {
    const newState = {
      ...this.currentState,
      news,
      totalPages,
      currentPage,
      lastNewsUpdate: new Date(),
      isNewsLoading: false
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  addNewsItem(newsItem: News): void {
    const newState = {
      ...this.currentState,
      news: [newsItem, ...this.currentState.news]
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  updateNewsItem(updatedNewsItem: News): void {
    const newState = {
      ...this.currentState,
      news: this.currentState.news.map(item =>
        item._id === updatedNewsItem._id ? updatedNewsItem : item
      )
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  removeNewsItem(newsId: string): void {
    const newState = {
      ...this.currentState,
      news: this.currentState.news.filter(item => item._id !== newsId)
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  setNewsLoading(isLoading: boolean): void {
    this.updateState({ isNewsLoading: isLoading });
  }

  isNewsStale(): boolean {
    const lastUpdate = this.currentState.lastNewsUpdate;
    if (!lastUpdate) return true;
    return Date.now() - lastUpdate.getTime() > this.NEWS_CACHE_DURATION;
  }

  forceRefresh(): void {
    const newState = {
      ...this.currentState,
      lastNewsUpdate: null
    };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  clearCache(): void {
    this.stateSubject.next(this.initialState);
    this.clearStateFromStorage();
  }

  private updateState(partialState: Partial<NewsState>): void {
    const newState = { ...this.currentState, ...partialState };
    this.stateSubject.next(newState);
    this.saveStateToStorage(newState);
  }

  private saveStateToStorage(state: NewsState): void {
    try {
      const stateToSave = {
        ...state,
        lastNewsUpdate: state.lastNewsUpdate?.toISOString(),
        news: state.news.map(item => ({
          ...item,
          createdAt: item.createdAt instanceof Date ? item.createdAt.toISOString() : item.createdAt
        }))
      };
      localStorage.setItem('news-state', JSON.stringify(stateToSave));
    } catch (error) {
      console.warn('Failed to save news state to localStorage:', error);
    }
  }

  private loadStateFromStorage(): void {
    try {
      const savedState = localStorage.getItem('news-state');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        const restoredState: NewsState = {
          ...this.initialState,
          ...parsedState,
          lastNewsUpdate: parsedState.lastNewsUpdate ? new Date(parsedState.lastNewsUpdate) : null,
          news: (parsedState.news || []).map((item: any) => ({
            ...item,
            createdAt: new Date(item.createdAt),
            // Preserve transferData and freeAgentData if they exist
            transferData: item.transferData,
            freeAgentData: item.freeAgentData
          })),
          // Reset loading state on app start
          isNewsLoading: false
        };
        this.stateSubject.next(restoredState);
      }
    } catch (error) {
      console.warn('Failed to load news state from localStorage:', error);
    }
  }

  private clearStateFromStorage(): void {
    try {
      localStorage.removeItem('news-state');
    } catch (error) {
      console.warn('Failed to clear news state from localStorage:', error);
    }
  }
}
