<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prediction Results Test - Fixed</title>
    <style>
        :root {
            --surface-primary: #1a1a1a;
            --surface-secondary: #2a2a2a;
            --surface-tertiary: #3a3a3a;
            --border-primary: #444;
            --text-primary: #fff;
            --text-secondary: #ccc;
            --text-tertiary: #999;
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --primary-400: #60a5fa;
            --primary-500: #3b82f6;
            --primary-700: #1d4ed8;
            --success-400: #4ade80;
            --success-500: #22c55e;
            --warning-400: #facc15;
            --warning-500: #eab308;
            --info-400: #60a5fa;
            --info-500: #3b82f6;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --radius-sm: 0.25rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
        }

        body {
            background: #111;
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .test-title {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary-500);
        }

        .prediction-card {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-primary);
            overflow: hidden;
            max-width: 400px;
            margin: 0 auto 2rem;
            padding: var(--spacing-md);
        }

        .prediction-voting.compact {
            padding: var(--spacing-xs);
            max-height: 280px;
        }

        .compact-header {
            margin-bottom: 4px;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .compact-predictions-count {
            font-size: var(--text-sm);
            color: var(--primary-400);
            display: flex;
            align-items: center;
            gap: 4px;
            line-height: 1;
            font-weight: var(--font-weight-semibold);
        }

        .prediction-count {
            font-size: var(--text-xs);
            color: var(--text-tertiary);
            margin-left: 20px;
        }

        .voting-section {
            margin-bottom: 6px;
        }

        .voting-buttons {
            gap: 2px;
            display: grid;
            grid-template-columns: 1fr auto 1fr;
        }

        .vote-btn {
            padding: 3px 6px;
            font-size: var(--text-xs);
            min-height: 32px;
            min-width: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1px;
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-sm);
            cursor: pointer;
            color: var(--text-primary);
        }

        .team-logo {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: var(--primary-500);
            margin-bottom: 2px;
        }

        .team-name {
            font-size: var(--text-xs);
            text-align: center;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .results-section {
            margin-top: 6px;
            max-height: 140px;
            overflow-y: auto;
        }

        .results-title {
            font-size: var(--text-sm);
            margin-bottom: 4px;
            font-weight: var(--font-weight-semibold);
            color: var(--primary-400);
            text-align: center;
        }

        .results-bars {
            gap: 1px;
            display: flex;
            flex-direction: column;
        }

        .result-bar {
            margin-bottom: 1px;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1px;
        }

        .team-info {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .team-logo-small {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary-500);
        }

        .percentage {
            font-size: var(--text-sm);
            line-height: 1;
            font-weight: var(--font-weight-bold);
            color: var(--primary-400);
        }

        .progress-bar {
            width: 100%;
            height: 2px;
            background: var(--surface-tertiary);
            border-radius: var(--radius-sm);
            margin-bottom: 1px;
        }

        .progress-fill {
            height: 100%;
            border-radius: var(--radius-sm);
            transition: width 0.3s ease;
        }

        .progress-fill.home-win {
            background: linear-gradient(90deg, var(--success-400), var(--success-500));
        }

        .progress-fill.away-win {
            background: linear-gradient(90deg, var(--warning-400), var(--warning-500));
        }

        .progress-fill.draw {
            background: linear-gradient(90deg, var(--info-400), var(--info-500));
        }

        .vote-count {
            font-size: var(--text-xs);
            color: var(--text-tertiary);
            margin-bottom: 1px;
        }

        .no-predictions {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            color: var(--text-secondary);
            font-style: italic;
        }

        .status {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: var(--radius-md);
            text-align: center;
        }

        .status.fixed {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }

        .status.issue {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Prediction Results Display - Fixed</h1>
        
        <div class="status fixed">
            ✅ <strong>FIXED:</strong> Results section now shows vote counts and progress bars!
        </div>

        <h2>With Predictions (Shows Results)</h2>
        <div class="prediction-card">
            <div class="prediction-voting compact">
                <div class="compact-header">
                    <span class="compact-predictions-count">
                        🗳️ Who's gonna win? Vote now!
                    </span>
                    <span class="prediction-count">15 votes</span>
                </div>
                
                <div class="voting-section">
                    <div class="voting-buttons">
                        <button class="vote-btn">
                            <div class="team-logo"></div>
                            <span class="team-name">Maccabi N Sara</span>
                        </button>
                        <button class="vote-btn">
                            <span>Draw</span>
                        </button>
                        <button class="vote-btn">
                            <div class="team-logo"></div>
                            <span class="team-name">UTOPIA XI</span>
                        </button>
                    </div>
                </div>
                
                <div class="results-section">
                    <h4 class="results-title">Results</h4>
                    <div class="results-bars">
                        <div class="result-bar">
                            <div class="result-header">
                                <div class="team-info">
                                    <div class="team-logo-small"></div>
                                    <span class="team-name">Maccabi N Sara Win</span>
                                </div>
                                <span class="percentage">60%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill home-win" style="width: 60%"></div>
                            </div>
                            <div class="vote-count">9 votes</div>
                        </div>
                        
                        <div class="result-bar">
                            <div class="result-header">
                                <div class="team-info">
                                    <span class="team-name">Draw</span>
                                </div>
                                <span class="percentage">20%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill draw" style="width: 20%"></div>
                            </div>
                            <div class="vote-count">3 votes</div>
                        </div>
                        
                        <div class="result-bar">
                            <div class="result-header">
                                <div class="team-info">
                                    <div class="team-logo-small"></div>
                                    <span class="team-name">UTOPIA XI Win</span>
                                </div>
                                <span class="percentage">20%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill away-win" style="width: 20%"></div>
                            </div>
                            <div class="vote-count">3 votes</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h2>No Predictions Yet (Shows Encouragement)</h2>
        <div class="prediction-card">
            <div class="prediction-voting compact">
                <div class="compact-header">
                    <span class="compact-predictions-count">
                        🗳️ Who's gonna win? Vote now!
                    </span>
                    <span class="prediction-count">0 votes</span>
                </div>
                
                <div class="voting-section">
                    <div class="voting-buttons">
                        <button class="vote-btn">
                            <div class="team-logo"></div>
                            <span class="team-name">Team A</span>
                        </button>
                        <button class="vote-btn">
                            <span>Draw</span>
                        </button>
                        <button class="vote-btn">
                            <div class="team-logo"></div>
                            <span class="team-name">Team B</span>
                        </button>
                    </div>
                </div>
                
                <div class="results-section">
                    <h4 class="results-title">Results</h4>
                    <div class="no-predictions">
                        <i>🗳️</i>
                        <span>Be the first to vote!</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="status fixed">
            <strong>Changes Made:</strong><br>
            • Results section now always shows when predictionDistribution exists<br>
            • Vote counts and progress bars display when there are predictions<br>
            • Encouragement message shows when no predictions yet<br>
            • Percentages are prominent and colored<br>
            • Better visual hierarchy and spacing
        </div>
    </div>
</body>
</html>
