import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';

// Safari-specific error handling
if (typeof window !== 'undefined') {
  // Handle unhandled promise rejections (common in Safari)
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    // Prevent Safari from showing Hebrew error message
    event.preventDefault();
  });

  // Handle general JavaScript errors
  window.addEventListener('error', (event) => {
    console.error('JavaScript error:', event.error);
    // Log additional Safari-specific information
    if (navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome')) {
      console.warn('Safari-specific error detected. Consider refreshing the page.');
    }
  });
}

platformBrowserDynamic()
  .bootstrapModule(AppModule)
  .catch(err => {
    console.error('Angular bootstrap error:', err);
    // Safari-specific error handling
    if (navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome')) {
      console.warn('Safari detected. If you see recurring errors, try clearing browser cache.');
    }
  });
