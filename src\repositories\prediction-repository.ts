import { ClientSession, Types } from "mongoose";
import { injectable } from "tsyringe";
import logger from "../config/logger";
import { NotFoundError } from "../errors";
import { IPrediction, Prediction, PREDICTION_OUTCOME } from "../models/prediction/prediction";

export interface PredictionDistribution {
  gameId: string;
  totalPredictions: number;
  homeWinCount: number;
  awayWinCount: number;
  drawCount: number;
  homeWinPercentage: number;
  awayWinPercentage: number;
  drawPercentage: number;
}

export interface IPredictionRepository {
  createPrediction(gameId: Types.ObjectId, userId: Types.ObjectId, predictedOutcome: PREDICTION_OUTCOME, confidence?: number, session?: ClientSession): Promise<IPrediction>;
  updatePrediction(predictionId: string, predictedOutcome: PREDICTION_OUTCOME, confidence?: number, session?: ClientSession): Promise<IPrediction>;
  deletePrediction(predictionId: string, session?: ClientSession): Promise<void>;
  getPredictionById(predictionId: string): Promise<IPrediction>;
  getUserPrediction(gameId: string, userId: string): Promise<IPrediction | null>;
  getPredictionsByGame(gameId: string): Promise<IPrediction[]>;
  getPredictionDistribution(gameId: string): Promise<PredictionDistribution>;
  getUserPredictions(userId: string, limit?: number): Promise<IPrediction[]>;
}

@injectable()
export class PredictionRepository implements IPredictionRepository {
  
  async createPrediction(
    gameId: Types.ObjectId, 
    userId: Types.ObjectId, 
    predictedOutcome: PREDICTION_OUTCOME, 
    confidence: number = 3,
    session?: ClientSession
  ): Promise<IPrediction> {
    logger.info(`PredictionRepository: creating prediction for game ${gameId} by user ${userId}`);
    
    const prediction = new Prediction({
      gameId,
      userId,
      predictedOutcome,
      confidence
    });
    
    await prediction.save({ session });
    return prediction;
  }

  async updatePrediction(
    predictionId: string, 
    predictedOutcome: PREDICTION_OUTCOME, 
    confidence?: number,
    session?: ClientSession
  ): Promise<IPrediction> {
    logger.info(`PredictionRepository: updating prediction ${predictionId}`);
    
    const updateData: any = { predictedOutcome };
    if (confidence !== undefined) {
      updateData.confidence = confidence;
    }
    
    const prediction = await Prediction.findByIdAndUpdate(
      predictionId,
      updateData,
      { new: true, session }
    );
    
    if (!prediction) {
      throw new NotFoundError(`Prediction with id ${predictionId} not found`);
    }
    
    return prediction;
  }

  async deletePrediction(predictionId: string, session?: ClientSession): Promise<void> {
    logger.info(`PredictionRepository: deleting prediction ${predictionId}`);
    
    const result = await Prediction.findByIdAndDelete(predictionId, { session });
    
    if (!result) {
      throw new NotFoundError(`Prediction with id ${predictionId} not found`);
    }
  }

  async getPredictionById(predictionId: string): Promise<IPrediction> {
    logger.info(`PredictionRepository: getting prediction ${predictionId}`);

    const prediction = await Prediction.findById(predictionId)
      .populate('userId', 'name profileImage');
      // Note: Not populating gameId to avoid issues with ObjectId conversion

    if (!prediction) {
      throw new NotFoundError(`Prediction with id ${predictionId} not found`);
    }

    logger.info(`PredictionRepository: found prediction with gameId: ${prediction.gameId} (type: ${typeof prediction.gameId})`);

    return prediction;
  }

  async getUserPrediction(gameId: string, userId: string): Promise<IPrediction | null> {
    logger.info(`PredictionRepository: getting user ${userId} prediction for game ${gameId}`);
    
    const prediction = await Prediction.findOne({
      gameId: new Types.ObjectId(gameId),
      userId: new Types.ObjectId(userId)
    });
    
    return prediction;
  }

  async getPredictionsByGame(gameId: string): Promise<IPrediction[]> {
    logger.info(`PredictionRepository: getting all predictions for game ${gameId}`);
    
    const predictions = await Prediction.find({
      gameId: new Types.ObjectId(gameId)
    })
    .populate('userId', 'name profileImage')
    .sort({ createdAt: -1 });
    
    return predictions;
  }

  async getPredictionDistribution(gameId: string): Promise<PredictionDistribution> {
    logger.info(`PredictionRepository: getting prediction distribution for game ${gameId}`);
    
    const pipeline = [
      {
        $match: { gameId: new Types.ObjectId(gameId) }
      },
      {
        $group: {
          _id: "$predictedOutcome",
          count: { $sum: 1 }
        }
      }
    ];
    
    const results = await Prediction.aggregate(pipeline);
    
    let homeWinCount = 0;
    let awayWinCount = 0;
    let drawCount = 0;
    
    results.forEach(result => {
      switch (result._id) {
        case PREDICTION_OUTCOME.HOME_WIN:
          homeWinCount = result.count;
          break;
        case PREDICTION_OUTCOME.AWAY_WIN:
          awayWinCount = result.count;
          break;
        case PREDICTION_OUTCOME.DRAW:
          drawCount = result.count;
          break;
      }
    });
    
    const totalPredictions = homeWinCount + awayWinCount + drawCount;
    
    return {
      gameId,
      totalPredictions,
      homeWinCount,
      awayWinCount,
      drawCount,
      homeWinPercentage: totalPredictions > 0 ? Math.round((homeWinCount / totalPredictions) * 100) : 0,
      awayWinPercentage: totalPredictions > 0 ? Math.round((awayWinCount / totalPredictions) * 100) : 0,
      drawPercentage: totalPredictions > 0 ? Math.round((drawCount / totalPredictions) * 100) : 0
    };
  }

  async getUserPredictions(userId: string, limit: number = 20): Promise<IPrediction[]> {
    logger.info(`PredictionRepository: getting predictions for user ${userId}`);
    
    const predictions = await Prediction.find({
      userId: new Types.ObjectId(userId)
    })
    .populate('gameId')
    .sort({ createdAt: -1 })
    .limit(limit);
    
    return predictions;
  }
}
