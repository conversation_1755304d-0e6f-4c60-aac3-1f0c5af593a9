/* === MODERN TYPOGRAPHY SYSTEM === */

/* Font Imports - Modern, stunning fonts */
@import url('https://fonts.googleapis.com/css2?family=Geist:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100;200;300;400;500;600;700;800;900&display=swap');

:root {
    /* === FONT FAMILIES === */
    --font-sans: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'Geist Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* === FONT WEIGHTS === */
    --font-weight-thin: 100;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
    
    /* === FONT SIZES === */
    --text-xs: 0.75rem;      /* 12px */
    --text-sm: 0.875rem;     /* 14px */
    --text-base: 1rem;       /* 16px */
    --text-lg: 1.125rem;     /* 18px */
    --text-xl: 1.25rem;      /* 20px */
    --text-2xl: 1.5rem;      /* 24px */
    --text-3xl: 1.875rem;    /* 30px */
    --text-4xl: 2.25rem;     /* 36px */
    --text-5xl: 3rem;        /* 48px */
    
    /* === LINE HEIGHTS === */
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    
    /* === LETTER SPACING === */
    --tracking-tight: -0.025em;
    --tracking-normal: 0em;
    --tracking-wide: 0.025em;
}

/* === TYPOGRAPHY CLASSES === */

/* Headings */
.text-h1 {
    font-family: var(--font-sans);
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
}

.text-h2 {
    font-family: var(--font-sans);
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--leading-tight);
}

.text-h3 {
    font-family: var(--font-sans);
    font-size: var(--text-xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--leading-snug);
}

.text-h4 {
    font-family: var(--font-sans);
    font-size: var(--text-lg);
    font-weight: var(--font-weight-semibold);
    line-height: var(--leading-snug);
}

/* Body Text */
.text-body-lg {
    font-family: var(--font-sans);
    font-size: var(--text-lg);
    font-weight: var(--font-weight-normal);
    line-height: var(--leading-relaxed);
}

.text-body {
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--leading-normal);
}

.text-body-sm {
    font-family: var(--font-sans);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-normal);
    line-height: var(--leading-normal);
}

/* Labels */
.text-label {
    font-family: var(--font-sans);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-wide);
    text-transform: uppercase;
}

.text-caption {
    font-family: var(--font-sans);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-normal);
    line-height: var(--leading-normal);
}

/* Utility Classes */
.font-sans { font-family: var(--font-sans); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* Text Colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-inverse { color: var(--text-inverse); }
.text-muted { color: var(--text-muted); }
.text-brand { color: var(--primary); }
.text-success { color: var(--success-500); }
.text-warning { color: var(--warning-500); }
.text-error { color: var(--error-500); }

/* Responsive Typography */
@media (max-width: 640px) {
    .text-h1 { font-size: var(--text-2xl); }
    .text-h2 { font-size: var(--text-xl); }
    .text-h3 { font-size: var(--text-lg); }
}
