import { Component } from '@angular/core';
import { TopAssister } from '@pro-clubs-manager/shared-dtos';
import { LeagueService } from '../../services/league.service';
import { LEAGUE_ID } from '../../constants/constants';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard-topassists',
  templateUrl: './dashboard-topassists.component.html',
  styleUrl: './dashboard-topassists.component.scss'
})
export class DashboardTopassistsComponent {
  topAssists: TopAssister[] | null = null;
  isLoading: boolean = false;
  constructor(private leagueService: LeagueService, private router: Router) { }

  ngOnInit() {
    this.loadTopAssistsData();
  }

  private async loadTopAssistsData() {
    this.isLoading = true;

    const topAssistsResponse = await this.leagueService.getTopAssists(LEAGUE_ID, 5);

    topAssistsResponse.map(topAssist => {
      topAssist.tableIcon = { name: topAssist.playerName, imgUrl: topAssist.playerImgUrl!, isTeam: false };
      if (topAssist.assistsPerGame)
        topAssist.assistsPerGame = parseFloat(topAssist.assistsPerGame.toFixed(2));
    });
    this.topAssists = topAssistsResponse;
    this.isLoading = false;
  };

  public onPlayerClick(player: TopAssister): void {
    if (!player || !player.playerId) {
      return;
    };
    
    this.router.navigate(['/player-details', { id: player.playerId }])
  }
}
