import { ChangeDetectorRef, Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ListOption } from '../../shared/models/list-option.model';
import { TeamService } from '../../services/team.service';
import { LeagueService } from '../../services/league.service';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { GameService } from '../../services/game.service';
import { NotificationService } from '../../services/notification.service';
import { PLAYABLE_POSITIONS_OPTIONS } from '../top-scorers/top-scorers.definitions';
import { GAME_STATUS, PlayerDTO } from '@pro-clubs-manager/shared-dtos';
import { ExtendedGameDTO } from '../../shared/types/extended-game-dto';
import { Formations, FormationsOptions } from '../../shared/models/player.model';
import { Ai<PERSON>tatsService, PlayerMatchResult } from '../../services/ai-stats.service';
import { AuthService } from '../../services/auth.service';
import { PermissionsService } from '../../services/permissions.service';
import { LEAGUE_ID } from '../../constants/constants';
import { Observable, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';

@Component({
  selector: 'modify-game',
  templateUrl: './modify-game.component.html',
  styleUrls: ['./modify-game.component.scss']
})
export class ModifyGameComponent implements OnInit {
  GameStatus = GAME_STATUS;
  allTeamPlayers?: PlayerDTO[];
  playersStatsFormGroup?: FormGroup;
  playersOptions?: ListOption[];
  isLoading = false;
  isSaving = false;
  homeTeamGoalsAmount = 0;
  awayTeamGoalsAmount = 0;
  homeTeamPenalties = 0;
  awayTeamPenalties = 0;
  selectedGame?: ExtendedGameDTO;
  gameDate = '';
  gameTime = '';

  // Getter and setter for ISO date format (YYYY-MM-DD) for date input
  get gameDateISO(): string {
    if (!this.gameDate) return '';
    // Convert from DD/MM/YYYY to YYYY-MM-DD
    const parts = this.gameDate.split('/');
    if (parts.length === 3) {
      return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
    }
    return '';
  }

  set gameDateISO(value: string) {
    if (!value) {
      this.gameDate = '';
      return;
    }
    // Convert from YYYY-MM-DD to DD/MM/YYYY
    const date = new Date(value);
    this.gameDate = this.formatDateToDDMMYYYY(date);
  }
  playablePositionOptions = [...PLAYABLE_POSITIONS_OPTIONS];
  formationsOptions = FormationsOptions;
  streamUrl = '';
  broadcastingTeam = '';
  originalBroadcast?: { streamUrl: string; broadcastingTeam: string };
  technicalLosingTeam: 'home' | 'away' | null = null;
  technicalReason = '';
  isProcessingImage = false;
  selectedImageFile: File | null = null;
  imagePreviewUrl: string | null = null;

  @Input() selectedGameId?: string;
  @Input() team: 'home' | 'away' = 'away';
  @Output() onSaveEvent = new EventEmitter<void>();

  // Permission observables
  canEditGame$: Observable<boolean> = new Observable();
  canEditTeam$: Observable<boolean> = new Observable();
  isAdmin$: Observable<boolean> = new Observable();
  canEditHomeTeam$: Observable<boolean> = new Observable();
  canEditAwayTeam$: Observable<boolean> = new Observable();
  canAccessModifyGame$: Observable<boolean> = new Observable();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private teamService: TeamService,
    private leagueService: LeagueService,
    private formBuilder: FormBuilder,
    private aiStatsService: AiStatsService,
    private gameService: GameService,
    private notificationService: NotificationService,
    private changeDetectorRef: ChangeDetectorRef,
    private authService: AuthService,
    private permissionsService: PermissionsService
  ) {}

  ngOnInit() {
    // Get game ID from route parameters
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.selectedGameId = params['id'];
      }
    });

    // Get query parameters for team and mode
    this.route.queryParams.subscribe(queryParams => {
      if (queryParams['team']) {
        this.team = queryParams['team'];
      }
      if (queryParams['mode']) {
        // Handle different modes if needed
        console.log('Mode:', queryParams['mode']);
      }
    });

    // Load game details if we have an ID
    if (this.selectedGameId) {
      this.loadGameDetails();
    }
  }

  async loadGameDetails() {
    this.selectedGame = await this.gameService.getGameById(this.selectedGameId!);
    this.initializePermissions();
    const game = this.selectedGame!;

    // Set goals
    if (game.status === GAME_STATUS.SCHEDULED) {
      this.homeTeamGoalsAmount = this.awayTeamGoalsAmount = 0;
      this.homeTeamPenalties = this.awayTeamPenalties = 0;
    } else {
      this.homeTeamGoalsAmount = game.result!.homeTeamGoals!;
      this.awayTeamGoalsAmount = game.result!.awayTeamGoals!;
      // Set penalty values if they exist
      const penalties = game.result?.penalties;
      this.homeTeamPenalties = penalties?.homeTeamPenalties || 0;
      this.awayTeamPenalties = penalties?.awayTeamPenalties || 0;
    }

    // Set date/time
    const now = new Date();
    if (game.date) {
      const gameDate = new Date(game.date);
      this.gameDate = this.formatDateToDDMMYYYY(gameDate);
      this.gameTime = gameDate.toTimeString().split(' ')[0].substring(0, 5);
    } else {
      this.gameDate = this.formatDateToDDMMYYYY(now);
      this.gameTime = now.toTimeString().split(' ')[0].substring(0, 5);
    }

    // Set broadcast
    const broadcast = (game as any).broadcast;
    this.streamUrl = broadcast?.streamUrl || '';
    this.broadcastingTeam = broadcast?.broadcastingTeam || '';
    this.originalBroadcast = broadcast ? { ...broadcast } : undefined;

    this.loadPlayersOptions();
    this.initForms();
  }

  async onFileSelected(event: any): Promise<void> {
    const file = event.target.files[0];
    if (!file || !file.type.startsWith('image/') || file.size > 10 * 1024 * 1024) {
      this.notificationService.error(!file ? 'No file selected' :
        !file.type.startsWith('image/') ? 'Please select a valid image file' :
        'Image file too large (max 10MB)');
      return;
    }

    this.selectedImageFile = file;
    this.imagePreviewUrl = URL.createObjectURL(file);
    this.isProcessingImage = true;
    const loadingToast = this.notificationService.loading('🤖 AI is analyzing your screenshot...');

    try {
      // Get the current team ID to filter players for matching
      const teamId = this.team === 'home' ? this.selectedGame!.homeTeam.id : this.selectedGame!.awayTeam.id;
      const result = await this.aiStatsService.analyzePlayerStatsImage(file, teamId);
      loadingToast.dismiss();

      if (result.success && result.players) {
        await this.populateFormWithAIData(result.players);
        this.showAIAnalysisResults(result);
      } else {
        this.notificationService.error(result.error || 'Failed to extract player data from image.');
      }
    } catch (error) {
      loadingToast.dismiss();
      console.error('Error processing image:', error);
      this.notificationService.error('Failed to process image. Please try again.');
    } finally {
      this.isProcessingImage = false;
    }
  }

  async populateFormWithAIData(players: PlayerMatchResult[]): Promise<void> {
    try {
      this.playersToUpdate.clear();

      players.forEach(playerData => {
        // Use matched player if available, otherwise try to find by extracted name
        const matchingPlayer = playerData.matchedPlayer
          ? this.allTeamPlayers?.find(p => p.id === playerData.matchedPlayer!.id)
          : this.findPlayerByName(playerData.extractedName);

        this.playersToUpdate.push(this.formBuilder.group({
          playerId: [matchingPlayer?.id || '', Validators.required],
          positionPlayed: [playerData.position || '', Validators.required],
          rating: [playerData.rating || 7.0, Validators.required], // Use AI extracted rating
          goals: [playerData.goals || 0],
          assists: [playerData.assists || 0],
          playerOfTheMatch: [playerData.isPlayerOfMatch || false]
        }));
      });

      while (this.playersToUpdate.length < 11) this.addPlayer();
      this.changeDetectorRef.detectChanges();
    } catch (error) {
      console.error('Error populating form with AI data:', error);
      this.notificationService.error('Error populating form with extracted data.');
    }
  }

  private findPlayerByName(aiExtractedName: string): PlayerDTO | undefined {
    if (!this.allTeamPlayers || !aiExtractedName) return undefined;

    const normalizedName = aiExtractedName.toLowerCase().trim();

    // Exact match first
    let match = this.allTeamPlayers.find(p => p.name.toLowerCase().trim() === normalizedName);
    if (match) return match;

    // Partial match
    match = this.allTeamPlayers.find(p => {
      const playerName = p.name.toLowerCase().trim();
      return normalizedName.includes(playerName) || playerName.includes(normalizedName);
    });

    if (match) console.log(`Fuzzy matched "${aiExtractedName}" to "${match.name}"`);
    else console.warn(`Could not find player match for "${aiExtractedName}"`);
    return match;
  }

  clearSelectedImage(): void {
    this.selectedImageFile = null;
    if (this.imagePreviewUrl) {
      URL.revokeObjectURL(this.imagePreviewUrl);
      this.imagePreviewUrl = null;
    }
  }

  testSuccessToast() { this.notificationService.success('🎉 This is a beautiful success notification!'); }
  testErrorToast() { this.notificationService.error('❌ This is an enhanced error notification with better styling!'); }
  testInfoToast() { this.notificationService.info('ℹ️ Here\'s some important information with a modern design!'); }
  testAIToast() { this.notificationService.aiSuccess('Player data extracted successfully from your screenshot!'); }

  onFormationChoose(chosenFormation: ListOption) {
    if (!chosenFormation || this.playersToUpdate.length < 11) return;

    const formation = Formations.find(f => f.name === chosenFormation.value);
    for (let i = 0; i < 11; i++) {
      this.onSelectionChange('position', { value: formation!.roles[i], displayText: '' }, i);
    }
  }

  initForms() {
    this.playersStatsFormGroup = this.formBuilder.group({
      playersToUpdate: this.formBuilder.array([]),
      homeTeamGoals: this.formBuilder.array([]),
      awayTeamGoals: this.formBuilder.array([])
    });

    const teamPerformance = this.team === 'home'
      ? this.selectedGame!.homeTeam.playersPerformance
      : this.selectedGame!.awayTeam.playersPerformance;

    if (teamPerformance?.length) {
      this.playersToUpdate.clear();
      teamPerformance.forEach(perf => {
        if (!perf.positionPlayed) perf.positionPlayed = '';
        this.playersToUpdate.push(this.formBuilder.group(perf));
      });
    }
  }

  get playersToUpdate(): FormArray { return this.playersStatsFormGroup!.get('playersToUpdate') as FormArray; }
  get homeTeamGoals(): FormArray { return this.playersStatsFormGroup!.get('homeTeamGoals') as FormArray; }
  get awayTeamGoals(): FormArray { return this.playersStatsFormGroup!.get('awayTeamGoals') as FormArray; }

  addPlayer() {
    const defaultFormation = Formations.find(f => f.name === this.formationsOptions[0].value);
    const currentPosition = defaultFormation?.roles[this.playersToUpdate.length];

    this.playersToUpdate.push(this.formBuilder.group({
      playerId: ['', Validators.required],
      positionPlayed: [currentPosition, Validators.required],
      rating: [0, Validators.required],
      goals: [0],
      assists: [0],
      playerOfTheMatch: [false]
    }));
  }

  addFullTeam() {
    for (let i = this.playersToUpdate.length; i < 11; i++) this.addPlayer();
  }

  removePlayer(index: number) { this.playersToUpdate.removeAt(index); }

  removeAllPlayers() {
    if (this.playersToUpdate.length === 0) {
      this.notificationService.info('No players to remove');
      return;
    }

    const confirmRemove = confirm(
      `⚠️ Are you sure you want to remove ALL ${this.playersToUpdate.length} players from the form?\n\n` +
      'This will clear all player data you have entered.\n\n' +
      'This action cannot be undone.'
    );

    if (!confirmRemove) {
      return;
    }

    this.playersToUpdate.clear();
    this.notificationService.success('All players removed from the form');
  }

  isScoreModified(): boolean {
    const result = this.selectedGame!.result;
    const penalties = result?.penalties;
    const originalHomePenalties = penalties?.homeTeamPenalties || 0;
    const originalAwayPenalties = penalties?.awayTeamPenalties || 0;

    return !result ||
           this.homeTeamGoalsAmount !== result.homeTeamGoals ||
           this.awayTeamGoalsAmount !== result.awayTeamGoals ||
           this.homeTeamPenalties !== originalHomePenalties ||
           this.awayTeamPenalties !== originalAwayPenalties;
  }

  isPlayoffGame(): boolean {
    const extendedGame = this.selectedGame as any;
    return extendedGame?.isPlayoff || false;
  }

  shouldShowPenalties(): boolean {
    return this.isPlayoffGame() && this.homeTeamGoalsAmount === this.awayTeamGoalsAmount && this.homeTeamGoalsAmount > 0;
  }

  isPenaltiesModified(): boolean {
    if (!this.selectedGame?.result) return this.homeTeamPenalties > 0 || this.awayTeamPenalties > 0;

    const penalties = this.selectedGame.result.penalties;
    const originalHomePenalties = penalties?.homeTeamPenalties || 0;
    const originalAwayPenalties = penalties?.awayTeamPenalties || 0;

    return this.homeTeamPenalties !== originalHomePenalties || this.awayTeamPenalties !== originalAwayPenalties;
  }

  isDateModified(): boolean {
    if (!this.selectedGame!.date) return this.gameDate !== '' || this.gameTime !== '';

    const originalDate = new Date(this.selectedGame!.date);
    return this.gameDate !== this.formatDateToDDMMYYYY(originalDate) ||
           this.gameTime !== originalDate.toTimeString().split(' ')[0].substring(0, 5);
  }

  isBroadcastModified(): boolean {
    const current = { streamUrl: this.streamUrl.trim(), broadcastingTeam: this.broadcastingTeam.trim() };

    if (!this.originalBroadcast) return !!(current.streamUrl || current.broadcastingTeam);

    return this.originalBroadcast.streamUrl !== current.streamUrl ||
           this.originalBroadcast.broadcastingTeam !== current.broadcastingTeam;
  }

  async updateGameDate(): Promise<void> {
    if (!this.isDateModified()) return;

    const combinedDateTime = this.parseDDMMYYYYToDate(this.gameDate, this.gameTime);
    await this.gameService.updateGameDate(this.selectedGame!.id, combinedDateTime);
    this.selectedGame!.date = combinedDateTime;
  }

  async updateGameBroadcast(): Promise<void> {
    if (!this.isBroadcastModified()) return;

    await this.gameService.updateGameBroadcast(this.selectedGame!.id, this.streamUrl.trim(), this.broadcastingTeam.trim());

    const extendedGame = this.selectedGame as any;
    const hasData = this.streamUrl.trim() || this.broadcastingTeam.trim();

    if (hasData) {
      extendedGame.broadcast = { streamUrl: this.streamUrl.trim(), broadcastingTeam: this.broadcastingTeam.trim() };
      this.originalBroadcast = { ...extendedGame.broadcast };
    } else {
      extendedGame.broadcast = this.originalBroadcast = undefined;
    }
  }

  async submitForm() {
    if (this.isSaving) return; // Prevent multiple submissions

    const playersToUpdate = this.playersStatsFormGroup!.value.playersToUpdate;

    // Penalty validation for playoff games
    if (this.isPlayoffGame() && this.homeTeamGoalsAmount === this.awayTeamGoalsAmount && this.homeTeamGoalsAmount > 0) {
      if (this.homeTeamPenalties === 0 && this.awayTeamPenalties === 0) {
        this.notificationService.error('Penalty shootout result is required for playoff games that end in a draw.');
        return;
      }
      if (this.homeTeamPenalties === this.awayTeamPenalties) {
        this.notificationService.error('Penalty shootout cannot end in a draw. One team must win.');
        return;
      }
    }

    // Prevent penalty data for non-playoff games or non-draws
    if (!this.isPlayoffGame() && (this.homeTeamPenalties > 0 || this.awayTeamPenalties > 0)) {
      this.notificationService.error('Penalty data can only be added to playoff games.');
      return;
    }

    if (this.homeTeamGoalsAmount !== this.awayTeamGoalsAmount && (this.homeTeamPenalties > 0 || this.awayTeamPenalties > 0)) {
      this.notificationService.error('Penalty data can only be added to games that end in a draw.');
      return;
    }

    // POTM check
    if (playersToUpdate?.length && !playersToUpdate.some((p: any) => p.playerOfTheMatch)) {
      if (!confirm('⚠️ Warning: No potm selected.\n\nContinue without potm?')) return;
    }

    this.isSaving = true; // Set loading state
    try {
      let hasUpdates = false;
      const updates: string[] = [];

      // Update date/broadcast if modified
      if (this.isDateModified()) {
        await this.updateGameDate();
        hasUpdates = true;
        updates.push('date/time');
      }

      if (this.isBroadcastModified()) {
        await this.updateGameBroadcast();
        hasUpdates = true;
        updates.push('broadcast info');
      }

      // Update score
      if (this.isScoreModified()) {
        const dateTime = this.parseDDMMYYYYToDate(this.gameDate, this.gameTime);
        const extendedGame = this.selectedGame as any;
        const isPlayoff = extendedGame.isPlayoff || false;

        // Include penalty data if it exists or if this is a playoff game with tied score
        console.log('ModifyGame: Current penalty values:', {
          homeTeamPenalties: this.homeTeamPenalties,
          awayTeamPenalties: this.awayTeamPenalties,
          isPlayoff: isPlayoff,
          homeGoals: this.homeTeamGoalsAmount,
          awayGoals: this.awayTeamGoalsAmount
        });

        let penalties: { homeTeamPenalties: number; awayTeamPenalties: number } | undefined;
        if (isPlayoff && this.homeTeamGoalsAmount === this.awayTeamGoalsAmount) {
          // For playoff games with tied scores, always include penalty data (even if 0-0)
          penalties = {
            homeTeamPenalties: this.homeTeamPenalties,
            awayTeamPenalties: this.awayTeamPenalties
          };
          console.log('ModifyGame: Including penalties for playoff tie:', penalties);
        } else if (this.homeTeamPenalties > 0 || this.awayTeamPenalties > 0) {
          // For other cases, only include if there are actual penalty values
          penalties = {
            homeTeamPenalties: this.homeTeamPenalties,
            awayTeamPenalties: this.awayTeamPenalties
          };
          console.log('ModifyGame: Including penalties for non-playoff game:', penalties);
        } else {
          console.log('ModifyGame: No penalties to include');
        }

        await this.gameService.updateGameResult(this.selectedGame!.id, this.homeTeamGoalsAmount, this.awayTeamGoalsAmount, dateTime, isPlayoff, penalties);
        hasUpdates = true;
        updates.push(penalties ? 'score & penalties' : 'score');

        // Refresh the game data to get the updated penalty information
        await this.loadGameDetails();

        // Refresh league table when score is updated
        await this.refreshLeagueTable();
      }

      // Update players
      if (playersToUpdate?.length) {
        await this.gameService.updateTeamPlayersPerformance(this.selectedGame!.id, playersToUpdate, this.team === 'home');
        hasUpdates = true;
        updates.push('player stats');
      }

      // Show appropriate success message
      if (hasUpdates) {
        this.notificationService.success(`Game updated successfully! Updated: ${updates.join(', ')}`);
        this.onSaveEvent.emit();
      } else {
        this.notificationService.info('No changes detected to save.');
      }
    } catch (error) {
      console.error('Error updating game:', error);
      this.notificationService.error('Failed to update game. Please try again.');
    } finally {
      this.isSaving = false; // Reset loading state
    }
  }

  async loadPlayersOptions() {
    const teamId = this.team === 'home' ? this.selectedGame!.homeTeam.id : this.selectedGame!.awayTeam.id;
    this.allTeamPlayers = await this.teamService.getPlayersByTeam(teamId);
    this.playersOptions = this.allTeamPlayers.map(p => ({ value: p.id, displayText: p.name }));
    this.isLoading = false;
  }

  onSelectionChange(selectType: string, option: ListOption, index: number) {
    if (!option) return;

    const field = selectType === 'position' ? 'positionPlayed' : 'playerId';
    let value = option.value;

    // Normalize position if it's a position selection
    if (selectType === 'position') {
      value = this.normalizePosition(option.value);
    }

    this.playersToUpdate.at(index).get(field)?.setValue(value);
  }

  private normalizePosition(position: string): string {
    const positionMap: { [key: string]: string } = {
      // Goalkeeper
      'GK': 'GK', 'GOALKEEPER': 'GK',

      // Defenders
      'LB': 'LB', 'LEFT_BACK': 'LB', 'LWB': 'LB',
      'CB': 'CB', 'CENTER_BACK': 'CB', 'CENTRE_BACK': 'CB',
      'RB': 'RB', 'RIGHT_BACK': 'RB', 'RWB': 'RB',
      'LCB': 'CB', 'RCB': 'CB',

      // Midfielders
      'CDM': 'CDM', 'DEFENSIVE_MIDFIELDER': 'CDM',
      'CM': 'CM', 'CENTRAL_MIDFIELDER': 'CM', 'CENTRE_MIDFIELDER': 'CM',
      'CAM': 'CAM', 'ATTACKING_MIDFIELDER': 'CAM',
      'LM': 'LM', 'LEFT_MIDFIELDER': 'LM',
      'RM': 'RM', 'RIGHT_MIDFIELDER': 'RM',
      'LCM': 'CM', 'RCM': 'CM',
      'LDM': 'CDM', 'RDM': 'CDM',
      'LAM': 'CAM', 'RAM': 'CAM',

      // Forwards
      'LW': 'LW', 'LEFT_WINGER': 'LW',
      'RW': 'RW', 'RIGHT_WINGER': 'RW',
      'ST': 'ST', 'STRIKER': 'ST', 'CENTRE_FORWARD': 'ST',
      'CF': 'ST', 'LF': 'LW', 'RF': 'RW',
      'RS': 'ST', 'LS': 'ST' // Map RS and LS to ST
    };

    return positionMap[position.toUpperCase()] || position;
  }

  onCheckboxCheck(index: number) {
    if (this.playersToUpdate.at(index).get('playerOfTheMatch')?.value) {
      this.playersToUpdate.controls.forEach(p => p.get('playerOfTheMatch')?.setValue(false));
      this.playersToUpdate.at(index).get('playerOfTheMatch')?.setValue(true);
    }
  }

  selectTechnicalLosingTeam(team: 'home' | 'away') { this.technicalLosingTeam = team; }

  clearTechnicalResult() {
    this.technicalLosingTeam = null;
    this.technicalReason = '';
  }

  async applyTechnicalResult(): Promise<void> {
    if (!this.technicalLosingTeam || !this.technicalReason.trim()) {
      this.notificationService.error('Please select a losing team and provide a reason');
      return;
    }

    try {
      const losingTeamId = this.technicalLosingTeam === 'home' ? this.selectedGame!.homeTeam.id : this.selectedGame!.awayTeam.id;
      await this.gameService.setTechnicalResult(this.selectedGame!.id, losingTeamId, this.technicalReason.trim());

      this.selectedGame!.result = {
        homeTeamGoals: this.technicalLosingTeam === 'home' ? 0 : 3,
        awayTeamGoals: this.technicalLosingTeam === 'away' ? 0 : 3
      };

      this.homeTeamGoalsAmount = this.selectedGame!.result.homeTeamGoals;
      this.awayTeamGoalsAmount = this.selectedGame!.result.awayTeamGoals;

      const losingTeamName = this.technicalLosingTeam === 'home' ? this.selectedGame!.homeTeam.name : this.selectedGame!.awayTeam.name;
      this.notificationService.success(`Technical result applied: ${losingTeamName} loses 0-3`);

      this.clearTechnicalResult();
      this.onSaveEvent.emit();
    } catch (error) {
      console.error('Error applying technical result:', error);
      this.notificationService.error('Failed to apply technical result');
    }
  }

  private formatDateToDDMMYYYY(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    return `${day}/${month}/${date.getFullYear()}`;
  }

  private parseDDMMYYYYToDate(dateStr: string, timeStr: string): Date {
    const [day, month, year] = dateStr.split('/');
    return new Date(`${year}-${month}-${day}T${timeStr}:00`);
  }

  isAdmin() { return this.authService.isAdmin(); }

  canEditGame(): Observable<boolean> {
    if (!this.selectedGame) {
      return this.permissionsService.isAdmin();
    }
    return this.canEditGame$;
  }

  canAccessModifyGame(): Observable<boolean> {
    if (!this.selectedGame) {
      return this.permissionsService.isAdmin();
    }
    return this.canAccessModifyGame$;
  }

  canEditHomeTeam(): Observable<boolean> {
    if (!this.selectedGame) {
      return this.permissionsService.isAdmin();
    }
    return this.canEditHomeTeam$;
  }

  canEditAwayTeam(): Observable<boolean> {
    if (!this.selectedGame) {
      return this.permissionsService.isAdmin();
    }
    return this.canEditAwayTeam$;
  }

  isAdminUser(): Observable<boolean> {
    return this.isAdmin$;
  }

  initializePermissions() {
    if (this.selectedGame) {
      // Admin permissions
      this.isAdmin$ = this.permissionsService.isAdmin();
      this.canEditGame$ = this.permissionsService.canEditGame(this.selectedGame.id);

      // Team-specific permissions
      this.canEditHomeTeam$ = this.permissionsService.canEditTeam(this.selectedGame.homeTeam.id);
      this.canEditAwayTeam$ = this.permissionsService.canEditTeam(this.selectedGame.awayTeam.id);

      // Debug logging for captain permissions
      this.authService.currentUser$.subscribe(user => {
        if (user) {
          console.log('Current user ID:', user.id, 'Role:', user.role);
          console.log('Associated players:', user.associatedPlayers);
          console.log('Game ID:', this.selectedGame?.id);
        }
      });

      // Overall access permission (admin or captain of either team)
      this.canAccessModifyGame$ = combineLatest([
        this.isAdmin$,
        this.canEditHomeTeam$,
        this.canEditAwayTeam$
      ]).pipe(
        map(([isAdmin, canEditHome, canEditAway]) => {
          console.log('Permission check - Admin:', isAdmin, 'Can edit home:', canEditHome, 'Can edit away:', canEditAway);
          return isAdmin || canEditHome || canEditAway;
        })
      );

      // Initialize team-specific permissions for current team
      if (this.team === 'home') {
        this.canEditTeam$ = this.canEditHomeTeam$;
      } else {
        this.canEditTeam$ = this.canEditAwayTeam$;
      }
    }
  }

  /**
   * Show AI analysis results to user
   */
  showAIAnalysisResults(result: any): void {
    const validation = this.aiStatsService.validateAnalysisResults(result.players);

    if (!validation.isValid) {
      this.notificationService.warning(`AI Analysis Issues: ${validation.issues.join(', ')}`);
    }

    // Show summary of matches
    const matchedCount = result.players.filter((p: any) => p.matchedPlayer).length;
    const unmatchedCount = result.players.length - matchedCount;

    if (unmatchedCount > 0) {
      this.notificationService.info(
        `${matchedCount} players matched automatically. ${unmatchedCount} players need manual selection.`
      );
    }
  }

  private async refreshLeagueTable(): Promise<void> {
    try {
      // Force refresh the league table to get updated standings after game result changes
      const LEAGUE_ID = '6703b5b8b5b8b5b8b5b8b5b8'; // Current league ID
      await this.leagueService.getLeagueTable(LEAGUE_ID, true);
    } catch (error) {
      console.error('Error refreshing league table:', error);
      // Don't show error to user as this is a background operation
    }
  }
}