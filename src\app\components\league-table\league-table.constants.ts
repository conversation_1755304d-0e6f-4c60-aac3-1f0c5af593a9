import { Column, DataType } from "../../shared/models/column.model";

export const LEAGUE_TABLE_DISPLAY_COLUMN: Column[] = [
    {
        fieldName: 'index',
        displayText: '#'
    },
    {
        fieldName: 'tableIcon',
        displayText: 'Name',
        dataType: DataType.TEXT_WITH_ICON
    },
    {
        fieldName: 'gamesPlayed',
        displayText: 'GP'
    },
    {
        fieldName: 'gamesWon',
        displayText: 'W',
        hideInMobile: true
    },
    {
        fieldName: 'draws',
        displayText: 'D',
        hideInMobile: true
    },
    {
        fieldName: 'gamesLost',
        displayText: 'L',
        hideInMobile: true
    },
    {
        fieldName: 'goalsScored',
        displayText: 'GF',
        hideInMobile: true
    },
    {
        fieldName: 'goalsConceded',
        displayText: 'GA',
        hideInMobile: true
    },
    {
        fieldName: 'goalDifference',
        displayText: 'DIF'
    },
    {
        fieldName: 'points',
        displayText: 'P'
    }

]

export const SHORTENED_LEAGUE_TABLE_DISPLAY_COLUMN: Column[] = [
    {
        fieldName: 'index',
        displayText: '#'
    },
    {
        fieldName: 'tableIcon',
        displayText: 'Name',
        dataType: DataType.TEXT_WITH_ICON
    },
    {
        fieldName: 'gamesPlayed',
        displayText: 'GP'
    },
    {
        fieldName: 'gamesWon',
        displayText: 'W'
    },
    {
        fieldName: 'draws',
        displayText: 'D'
    },
    {
        fieldName: 'gamesLost',
        displayText: 'L'
    },
    {
        fieldName: 'goalDifference',
        displayText: 'DIF'
    },
    {
        fieldName: 'points',
        displayText: 'P'
    }

];


export const leagueSortColumns = [
    { column: 'points', direction: 'asc' },
    { column: 'goalDifference', direction: 'asc' },
    { column: 'goalsScored', direction: 'asc' }
];