import { ClientSession, Types } from "mongoose";
import logger from "../config/logger";
import { AddNewsRequestModel } from "../controllers/news-controller";
import { QueryFailedError } from "../errors";
import { INewsRepository } from "../interfaces/news/news-repository.interface";
import News, { INews } from "../models/news";

export class NewsRepository implements INewsRepository {
    async getAllNews(): Promise<INews[]> {
        try {
            // Sort by createdAt in descending order (newest first)
            const allNews = await News.find().sort({ createdAt: -1 });
            return allNews;
        } catch (error: any) {
            logger.error(error.message);
            throw new QueryFailedError(`Failed to get all news`);
        }
    };

    async addNews(addNewsRequestModel: AddNewsRequestModel, session?: ClientSession): Promise<INews> {
        try {


            const news = await News.create([addNewsRequestModel], { session });

            return news[0];
        } catch (error: any) {
            logger.error(error.message);
            throw new QueryFailedError(`Failed to add news`);
        }
    }

    async deleteNews(id: string | Types.ObjectId, session?: ClientSession): Promise<boolean> {
        try {
            const isDeleted = await News.findByIdAndDelete(id, { session });

            console.log(`Repo : ${!!isDeleted}`)
            return !!isDeleted;
        } catch (e: any) {
            logger.error(e.message);
            throw new QueryFailedError(`Failed to delete news with id: ${id}`);
        }
    }

    async likeNews(newsId: string, userId: string, session?: ClientSession): Promise<INews> {
        try {
            const news = await News.findByIdAndUpdate(
                newsId,
                { $addToSet: { likes: userId } }, // $addToSet prevents duplicates
                { new: true, session }
            );

            if (!news) {
                throw new QueryFailedError(`News with id ${newsId} not found`);
            }

            return news;
        } catch (error: any) {
            logger.error("Error liking news:", error);
            throw new QueryFailedError(`Failed to like news with id ${newsId}`);
        }
    }

    async unlikeNews(newsId: string, userId: string, session?: ClientSession): Promise<INews> {
        try {
            const news = await News.findByIdAndUpdate(
                newsId,
                { $pull: { likes: userId } }, // $pull removes the userId from likes array
                { new: true, session }
            );

            if (!news) {
                throw new QueryFailedError(`News with id ${newsId} not found`);
            }

            return news;
        } catch (error: any) {
            logger.error("Error unliking news:", error);
            throw new QueryFailedError(`Failed to unlike news with id ${newsId}`);
        }
    }

    async updateNews(newsId: string, newsData: Partial<AddNewsRequestModel>, session?: ClientSession): Promise<INews> {
        try {
            const news = await News.findByIdAndUpdate(
                newsId,
                {
                    ...newsData,
                    updatedAt: new Date()
                },
                { new: true, session }
            );

            if (!news) {
                throw new QueryFailedError(`News with id ${newsId} not found`);
            }

            return news;
        } catch (error: any) {
            logger.error("Error updating news:", error);
            throw new QueryFailedError(`Failed to update news with id ${newsId}`);
        }
    }
}