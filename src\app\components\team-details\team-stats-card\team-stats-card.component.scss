.team-stats-card {
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary));
        opacity: 0.9;
    }

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-2xl);
        border-color: var(--primary-300);
    }

    &.compact {
        .card-header {
            padding: var(--spacing-md) var(--spacing-lg);
        }

        .stats-grid {
            padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
            gap: var(--spacing-md);
        }

        .stat-item {
            padding: var(--spacing-md);
        }
    }

    .card-header {
        padding: var(--spacing-lg) var(--spacing-xl);
        background: var(--surface-secondary);
        border-bottom: 1px solid var(--border-primary);

        .card-title {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            &::before {
                content: '';
                width: 4px;
                height: 20px;
                background: var(--primary-500);
                border-radius: var(--radius-sm);
            }
        }
    }

    .stats-grid {
        display: grid;
        gap: var(--spacing-xl);
        padding: var(--spacing-xl);
        align-items: stretch;

        @media (max-width: 1024px) {
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
        }

        @media (max-width: 768px) {
            grid-template-columns: 1fr 1fr !important;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
        }

        @media (max-width: 480px) {
            grid-template-columns: 1fr !important;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
        }
    }

    .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-secondary);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        min-height: 120px;
        text-align: center;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--primary-500);
            transition: all 0.3s ease;
        }

        &:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-300);

            &::before {
                height: 6px;
            }

            .stat-icon {
                transform: scale(1.1);
            }
        }

        .stat-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 56px;
            height: 56px;
            background: var(--primary-100);
            border-radius: var(--radius-xl);
            color: var(--primary-600);
            font-size: var(--text-xl);
            flex-shrink: 0;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);
        }

        .stat-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            width: 100%;

            .stat-header {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: var(--spacing-xs);
                width: 100%;

                .stat-label {
                    font-size: var(--text-sm);
                    font-weight: var(--font-weight-semibold);
                    color: var(--text-secondary);
                    text-transform: uppercase;
                    letter-spacing: 0.8px;
                    margin: 0;
                }

                .stat-trend {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: var(--spacing-xs);
                    font-size: var(--text-xs);
                    font-weight: var(--font-weight-semibold);
                    padding: var(--spacing-xs) var(--spacing-sm);
                    border-radius: var(--radius-md);

                    &.trend--up {
                        color: var(--success-600);
                        background: var(--success-50);
                    }

                    &.trend--down {
                        color: var(--danger-600);
                        background: var(--danger-50);
                    }

                    &.trend--neutral {
                        color: var(--text-secondary);
                        background: var(--surface-tertiary);
                    }
                }
            }

            .stat-value {
                font-size: var(--text-3xl);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
                line-height: 1;
                margin: 0;
            }
        }

        // Color variants
        &.stat-item--success {
            &::before {
                background: var(--success-500);
            }

            .stat-icon {
                background: var(--success-100);
                color: var(--success-600);
                border: 2px solid var(--success-200);
            }

            &:hover {
                border-color: var(--success-300);
                background: var(--success-25);
            }
        }

        &.stat-item--warning {
            &::before {
                background: var(--warning-500);
            }

            .stat-icon {
                background: var(--warning-100);
                color: var(--warning-600);
                border: 2px solid var(--warning-200);
            }

            &:hover {
                border-color: var(--warning-300);
                background: var(--warning-25);
            }
        }

        &.stat-item--danger {
            &::before {
                background: var(--danger-500);
            }

            .stat-icon {
                background: var(--danger-100);
                color: var(--danger-600);
                border: 2px solid var(--danger-200);
            }

            &:hover {
                border-color: var(--danger-300);
                background: var(--danger-25);
            }
        }

        &.stat-item--primary {
            &::before {
                background: var(--primary-500);
            }

            .stat-icon {
                background: var(--primary-100);
                color: var(--primary-600);
                border: 2px solid var(--primary-200);
            }

            &:hover {
                border-color: var(--primary-300);
                background: var(--primary-25);
            }
        }

        &.stat-item--info {
            &::before {
                background: var(--info-500);
            }

            .stat-icon {
                background: var(--info-100);
                color: var(--info-600);
                border: 2px solid var(--info-200);
            }

            &:hover {
                border-color: var(--info-300);
                background: var(--info-25);
            }
        }

        @media (max-width: 768px) {
            min-height: 100px;
            padding: var(--spacing-lg);
            gap: var(--spacing-sm);

            .stat-icon {
                width: 48px;
                height: 48px;
                font-size: var(--text-lg);
            }

            .stat-content {
                .stat-value {
                    font-size: var(--text-3xl);
                }
            }
        }

        @media (max-width: 480px) {
            min-height: 80px;
            padding: var(--spacing-md);
            gap: var(--spacing-xs);

            .stat-icon {
                width: 40px;
                height: 40px;
                font-size: var(--text-base);
            }

            .stat-content {
                .stat-value {
                    font-size: var(--text-3xl);
                }

                .stat-header .stat-label {
                    font-size: var(--text-xs);
                }
            }
        }
    }
}