.stats-card {
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--info), var(--primary), var(--success));
        background-size: 200% 100%;
        animation: shimmer 3s ease-in-out infinite;
    }

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-2xl);
    }

    &.compact {
        .card-header {
            padding: var(--spacing-md) var(--spacing-lg);
        }

        .stats-grid {
            padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
            gap: var(--spacing-md);
        }

        .stat-item {
            padding: var(--spacing-md);
        }
    }

    .card-header {
        padding: var(--spacing-lg) var(--spacing-xl);
        background: var(--surface-secondary);
        border-bottom: 1px solid var(--border-primary);

        .card-title {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            &::before {
                content: '';
                width: 4px;
                height: 20px;
                background: var(--primary-500);
                border-radius: var(--radius-sm);
            }
        }
    }

    .stats-grid {
        display: grid;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);

        @media (max-width: 768px) {
            grid-template-columns: 1fr !important;
            gap: var(--spacing-md);
            padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
        }
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
        background: var(--surface-secondary);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-secondary);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-500);
            transition: all 0.3s ease;
        }

        &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-300);

            &::before {
                width: 6px;
            }
        }

        .stat-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--primary-100);
            border-radius: var(--radius-lg);
            color: var(--primary-600);
            font-size: var(--text-lg);
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .stat-content {
            flex: 1;
            min-width: 0;

            .stat-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: var(--spacing-xs);

                .stat-label {
                    font-size: var(--text-sm);
                    font-weight: var(--font-weight-medium);
                    color: var(--text-secondary);
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .stat-trend {
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-xs);
                    font-size: var(--text-xs);
                    font-weight: var(--font-weight-semibold);
                    padding: var(--spacing-xs) var(--spacing-sm);
                    border-radius: var(--radius-md);

                    &.trend--up {
                        color: var(--success-600);
                        background: var(--success-50);
                    }

                    &.trend--down {
                        color: var(--danger-600);
                        background: var(--danger-50);
                    }

                    &.trend--neutral {
                        color: var(--text-secondary);
                        background: var(--surface-tertiary);
                    }
                }
            }

            .stat-value {
                font-size: var(--text-xl);
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
                line-height: 1.2;
            }
        }

        // Color variants
        &.stat-item--success {
            &::before {
                background: var(--success-500);
            }

            .stat-icon {
                background: var(--success-100);
                color: var(--success-600);
            }

            &:hover {
                border-color: var(--success-300);
            }
        }

        &.stat-item--warning {
            &::before {
                background: var(--warning-500);
            }

            .stat-icon {
                background: var(--warning-100);
                color: var(--warning-600);
            }

            &:hover {
                border-color: var(--warning-300);
            }
        }

        &.stat-item--danger {
            &::before {
                background: var(--danger-500);
            }

            .stat-icon {
                background: var(--danger-100);
                color: var(--danger-600);
            }

            &:hover {
                border-color: var(--danger-300);
            }
        }

        &.stat-item--info {
            &::before {
                background: var(--info-500);
            }

            .stat-icon {
                background: var(--info-100);
                color: var(--info-600);
            }

            &:hover {
                border-color: var(--info-300);
            }
        }

        &.stat-item--primary {
            &::before {
                background: var(--primary-500);
            }

            .stat-icon {
                background: var(--primary-100);
                color: var(--primary-600);
            }

            &:hover {
                border-color: var(--primary-300);
            }
        }

        &.stat-item--info {
            &::before {
                background: var(--info-500);
            }

            .stat-icon {
                background: var(--info-100);
                color: var(--info-600);
            }

            &:hover {
                border-color: var(--info-300);
            }
        }

        @media (max-width: 480px) {
            padding: var(--spacing-md);
            gap: var(--spacing-sm);

            .stat-icon {
                width: 32px;
                height: 32px;
                font-size: var(--text-base);
            }

            .stat-content {
                .stat-value {
                    font-size: var(--text-lg);
                }
            }
        }
    }
}

/* === ANIMATIONS === */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
