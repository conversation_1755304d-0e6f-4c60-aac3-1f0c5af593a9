<div class="dashboard-container theme-transition" appThemeAware>
    <!-- Loading State -->
    <pro-clubs-spinner *ngIf="isLoading"></pro-clubs-spinner>

    <!-- Error State -->
    <div class="error-container" *ngIf="error && !isLoading">
        <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Unable to Load Dashboard</h3>
            <p>{{error}}</p>
            <button class="retry-btn" (click)="refreshDashboard()">
                <i class="fas fa-redo"></i>
                Try Again
            </button>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div class="dashboard-content animate-fade-in" *ngIf="dashboardData && !isLoading">
        <!-- Promotional Banner for Non-Authenticated Users -->
        <app-promotional-banner
            *ngIf="!isAuthenticated"
            [message]="promotionalMessage"
            size="large"
            [dismissible]="true"
            [showSecondaryAction]="true">
        </app-promotional-banner>

        <!-- Header Section -->
        <div class="dashboard-header animate-fade-in-down">
            <div class="header-content">
                <h1 class="header-title">
                    <i class="fas fa-tachometer-alt"></i>
                    League Dashboard
                </h1>
                <p class="header-subtitle">Current season overview and statistics</p>
            </div>
            <div class="header-actions">
                <div class="cache-info">
                    <span class="cache-text">{{ getCacheInfo() }}</span>
                </div>
                <button class="force-refresh-btn"
                        [class.disabled]="!canForceRefresh || isLoading"
                        [disabled]="!canForceRefresh || isLoading"
                        (click)="forceRefresh()"
                        [title]="canForceRefresh ? 'Force refresh dashboard data' : 'Please wait before refreshing again'">
                    <i class="fas fa-sync-alt" [class.spinning]="isLoading"></i>
                    <span>Refresh</span>
                </button>
                <button class="view-all-seasons-btn" (click)="navigateToAllSeasons()">
                    <i class="fas fa-history"></i>
                    <span>View All Seasons</span>
                </button>
            </div>
        </div>

        <!-- Top Row: Scorers and Assists -->
        <div class="top-row">
            <!-- Top Scorers Card -->
            <div class="dashboard-card scorers-card" [routerLink]="'/top-scorers'">
                <div class="card-header">
                    <div class="header-info">
                        <h2 class="card-title">
                            <i class="fas fa-futbol"></i>
                            Top Scorers
                        </h2>
                        <p class="card-subtitle">Goal leaders</p>
                    </div>
                    <div class="header-action">
                        <i class="fas fa-external-link-alt"></i>
                    </div>
                </div>
                <div class="card-content">
                    <app-dashboard-topscorers></app-dashboard-topscorers>
                </div>
            </div>

            <!-- Top Assists Card -->
            <div class="dashboard-card assists-card" [routerLink]="'/top-assists'">
                <div class="card-header">
                    <div class="header-info">
                        <h2 class="card-title">
                            <i class="fas fa-hands-helping"></i>
                            Top Assists
                        </h2>
                        <p class="card-subtitle">Playmakers</p>
                    </div>
                    <div class="header-action">
                        <i class="fas fa-external-link-alt"></i>
                    </div>
                </div>
                <div class="card-content">
                    <app-dashboard-topassists></app-dashboard-topassists>
                </div>
            </div>
        </div>

        <!-- Middle Row: League Table -->
        <div class="middle-row">
            <div class="dashboard-card league-table-card" [routerLink]="'/league-table'">
                <div class="card-header">
                    <div class="header-info">
                        <h2 class="card-title">
                            <i class="fas fa-trophy"></i>
                            League Table
                        </h2>
                        <p class="card-subtitle">Current standings</p>
                    </div>
                    <div class="header-action">
                        <i class="fas fa-external-link-alt"></i>
                    </div>
                </div>
                <div class="card-content">
                    <league-table [hideTitle]="true"></league-table>
                </div>
            </div>
        </div>



        <!-- Quick Stats Cards -->
        <div class="stats-section animate-fade-in-up">
            <div class="stats-grid stagger-children">
                <app-dashboard-stats-card
                    icon="fas fa-users"
                    title="Teams"
                    [value]="dashboardData.totalTeams"
                    color="primary">
                </app-dashboard-stats-card>

                <app-dashboard-stats-card
                    icon="fas fa-calendar"
                    title="Fixtures"
                    [value]="dashboardData.totalFixtures"
                    color="info">
                </app-dashboard-stats-card>

                <app-dashboard-stats-card
                    icon="fas fa-futbol"
                    title="Goals Scored"
                    [value]="dashboardData.totalGoals"
                    color="success">
                </app-dashboard-stats-card>

                <app-dashboard-stats-card
                    icon="fas fa-user-friends"
                    title="Players"
                    [value]="dashboardData.totalPlayers"
                    color="warning">
                </app-dashboard-stats-card>

                <app-dashboard-stats-card
                    icon="fas fa-hands-helping"
                    title="Assists"
                    [value]="dashboardData.totalAssists"
                    color="info">
                </app-dashboard-stats-card>

                <app-dashboard-stats-card
                    icon="fas fa-star"
                    title="Avg Rating"
                    [value]="getFormattedRating(dashboardData.averageRating)"
                    color="warning">
                </app-dashboard-stats-card>

                <app-dashboard-stats-card
                    icon="fas fa-shield-alt"
                    title="Clean Sheets"
                    [value]="dashboardData.cleanSheets"
                    color="success">
                </app-dashboard-stats-card>

                <app-dashboard-stats-card
                    icon="fas fa-chart-bar"
                    title="Goals/Game"
                    [value]="getFormattedGoalsPerGame(dashboardData.averageGoalsPerGame)"
                    color="primary">
                </app-dashboard-stats-card>
            </div>
        </div>

        <!-- Performance Highlights -->
        <div class="highlights-section animate-fade-in-up">
            <div class="highlights-grid stagger-children">
                <!-- Top Scorer -->
                <div class="dashboard-card highlight-card golden-boot-card" *ngIf="dashboardData.topScorer"
                (click)="navigateToPlayerDetails()">
                    <div class="card-header golden-boot-header">
                        <h3 class="card-title">
                            <i class="fas fa-trophy golden-boot-icon"></i>
                            RACE TO THE GOLDEN BOOT
                        </h3>
                    </div>
                    <div class="card-content golden-boot-content">
                        <div class="golden-boot-player">
                            <div class="player-avatar-large">
                                <img [src]="dashboardData.topScorer.playerImgUrl || 'assets/Icons/User.jpg'"
                                     [alt]="dashboardData.topScorer.playerName"
                                     class="player-image">
                                <div class="crown-overlay">
                                    <i class="fas fa-crown"></i>
                                </div>
                            </div>
                            <div class="player-details">
                                <span class="player-name-large">{{dashboardData.topScorer.playerName}}</span>
                                <span class="player-team-badge">{{dashboardData.topScorer.teamName}}</span>
                                <div class="goals-display">
                                    <span class="goals-number">{{dashboardData.topScorer.goals}}</span>
                                    <span class="goals-label">GOALS</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- League Leader -->
                <div class="dashboard-card highlight-card league-leader-card" *ngIf="dashboardData.leagueLeader"
                (click)="navigateToTeamDetails()">
                    <div class="card-header league-leader-header">
                        <h3 class="card-title">
                            <i class="fas fa-medal league-leader-icon"></i>
                            LEAGUE LEADER
                        </h3>
                    </div>
                    <div class="card-content league-leader-content">
                        <div class="league-leader-team">
                            <div class="team-avatar-large">
                                <img [src]="dashboardData.leagueLeader.teamImgUrl || 'assets/Icons/Team.jpg'"
                                     [alt]="dashboardData.leagueLeader.teamName"
                                     class="team-image">
                                <div class="leader-badge">
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            <div class="team-details">
                                <span class="team-name-large">{{dashboardData.leagueLeader.teamName}}</span>
                                <span class="team-stats-badge">{{dashboardData.leagueLeader.gamesPlayed}} games
                                    played</span>
                                <div class="points-display">
                                    <span class="points-number">{{dashboardData.leagueLeader.points}}</span>
                                    <span class="points-label">POINTS</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trending Predictions Section -->
            <div class="dashboard-section trending-predictions-section" *ngIf="upcomingGames && upcomingGames.length > 0">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-crystal-ball"></i>
                        Trending Predictions
                    </h2>
                    <p class="section-subtitle">See what fans are predicting for upcoming matches</p>
                </div>

                <div class="predictions-grid">
                    <div class="prediction-card" *ngFor="let game of upcomingGames.slice(0, 3)">
                        <app-prediction-voting
                            [game]="game"
                            [showResults]="true"
                            [compact]="true">
                        </app-prediction-voting>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>