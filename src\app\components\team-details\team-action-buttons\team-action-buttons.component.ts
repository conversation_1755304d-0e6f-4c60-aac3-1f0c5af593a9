import { Component, Input, Output, EventEmitter } from '@angular/core';
import { TeamDTO } from '@pro-clubs-manager/shared-dtos';

@Component({
  selector: 'app-team-action-buttons',
  templateUrl: './team-action-buttons.component.html',
  styleUrl: './team-action-buttons.component.scss'
})
export class TeamActionButtonsComponent {
  @Input() team!: TeamDTO;
  @Input() canEdit: boolean = false;
  @Input() isEditMode: boolean = false;
  @Input() isLoading: boolean = false;

  @Output() editClick = new EventEmitter<void>();
  @Output() saveClick = new EventEmitter<void>();
  @Output() cancelClick = new EventEmitter<void>();
  @Output() deleteClick = new EventEmitter<void>();
  @Output() addPlayerClick = new EventEmitter<void>();
  @Output() removeAllPlayersClick = new EventEmitter<void>();

  onEditClick(): void {
    this.editClick.emit();
  }

  onSaveClick(): void {
    this.saveClick.emit();
  }

  onCancelClick(): void {
    this.cancelClick.emit();
  }

  onDeleteClick(): void {
    this.deleteClick.emit();
  }

  onAddPlayerClick(): void {
    this.addPlayerClick.emit();
  }

  onRemoveAllPlayersClick(): void {
    this.removeAllPlayersClick.emit();
  }
}
