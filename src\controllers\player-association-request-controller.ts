import { Request, Response, NextFunction } from "express";
import { injectable, inject } from "tsyringe";
import { IPlayerAssociationRequestService } from "../services/player-association-request-service";
import logger from "../config/logger";

export interface IPlayerAssociationRequestController {
  createRequest(req: Request, res: Response, next: NextFunction): Promise<void>;
  getUserRequests(req: Request, res: Response, next: NextFunction): Promise<void>;
  getPendingRequests(req: Request, res: Response, next: NextFunction): Promise<void>;
  approveRequest(req: Request, res: Response, next: NextFunction): Promise<void>;
  rejectRequest(req: Request, res: Response, next: NextFunction): Promise<void>;
  cancelRequest(req: Request, res: Response, next: NextFunction): Promise<void>;
}

@injectable()
export class PlayerAssociationRequestController implements IPlayerAssociationRequestController {
  constructor(
    @inject("IPlayerAssociationRequestService") private requestService: IPlayerAssociationRequestService
  ) {}

  async createRequest(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.userId!;
      const { playerId, playerEmail, userMessage } = req.body;

      if (!playerId && !playerEmail) {
        res.status(400).json({ message: "Either playerId or playerEmail must be provided" });
        return;
      }

      const request = await this.requestService.createRequest(userId, playerId, playerEmail, userMessage);
      
      res.status(201).json({
        message: "Player association request created successfully. An admin will review your request.",
        request
      });
    } catch (error: any) {
      next(error);
    }
  }

  async getUserRequests(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.userId!;
      const requests = await this.requestService.getUserRequests(userId);
      
      res.json({ requests });
    } catch (error: any) {
      next(error);
    }
  }

  async getPendingRequests(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const requests = await this.requestService.getPendingRequests();
      
      res.json({ requests });
    } catch (error: any) {
      next(error);
    }
  }

  async approveRequest(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { requestId } = req.params;
      const adminId = req.userId!;

      if (!requestId) {
        res.status(400).json({ message: "Request ID is required" });
        return;
      }

      const request = await this.requestService.approveRequest(requestId, adminId);

      res.status(200).json({
        message: "Player association request approved successfully",
        request
      });
    } catch (error: any) {
      next(error);
    }
  }

  async rejectRequest(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { requestId } = req.params;
      const { reason } = req.body;
      const adminId = req.userId!;

      if (!requestId) {
        res.status(400).json({ message: "Request ID is required" });
        return;
      }

      const request = await this.requestService.rejectRequest(requestId, adminId, reason);
      
      res.json({
        message: "Player association request rejected",
        request
      });
    } catch (error: any) {
      next(error);
    }
  }

  async cancelRequest(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { requestId } = req.params;
      const userId = req.userId!;

      if (!requestId) {
        res.status(400).json({ message: "Request ID is required" });
        return;
      }

      await this.requestService.cancelRequest(requestId, userId);
      
      res.json({
        message: "Player association request cancelled successfully"
      });
    } catch (error: any) {
      next(error);
    }
  }
}
