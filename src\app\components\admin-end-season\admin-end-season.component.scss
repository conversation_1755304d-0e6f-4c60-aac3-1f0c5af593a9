.admin-end-season-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  color: var(--text-color);

  .header {
    margin-bottom: 30px;
    text-align: center;

    .back-btn {
      position: absolute;
      left: 20px;
      top: 20px;
      background: var(--secondary-color);
      color: var(--text-color);
      border: none;
      padding: 10px 15px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--primary-color);
        transform: translateY(-2px);
      }

      i {
        margin-right: 8px;
      }
    }

    h1 {
      color: var(--primary-color);
      margin-bottom: 10px;
      font-size: 2.5rem;
    }

    .subtitle {
      color: var(--text-secondary);
      font-size: 1.1rem;
    }
  }

  .content {
    .selection-section {
      background: var(--card-background);
      padding: 25px;
      border-radius: 12px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

      .form-group {
        margin-bottom: 20px;

        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 600;
          color: var(--text-color);
        }

        .form-control {
          width: 100%;
          padding: 12px;
          border: 2px solid var(--border-color);
          border-radius: 8px;
          background: var(--input-background);
          color: var(--text-color);
          font-size: 1rem;
          transition: border-color 0.3s ease;

          &:focus {
            outline: none;
            border-color: var(--primary-color);
          }
        }
      }

      .refresh-btn {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          background: var(--primary-hover);
          transform: translateY(-2px);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        i {
          margin-right: 8px;
        }
      }
    }

    .loading-section {
      text-align: center;
      padding: 50px;

      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid var(--border-color);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      p {
        color: var(--text-secondary);
        font-size: 1.1rem;
      }
    }

    .preview-section {
      .season-info {
        margin-bottom: 30px;

        h2 {
          color: var(--primary-color);
          margin-bottom: 20px;
          text-align: center;
        }

        .status-cards {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;

          .status-card {
            background: var(--card-background);
            padding: 20px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

            &.complete {
              border-left: 4px solid var(--success-color);
            }

            &.incomplete {
              border-left: 4px solid var(--warning-color);
            }

            i {
              font-size: 2rem;
              margin-right: 15px;

              &.fa-check-circle {
                color: var(--success-color);
              }

              &.fa-exclamation-triangle {
                color: var(--warning-color);
              }
            }

            .status-info {
              h3 {
                margin: 0 0 5px 0;
                color: var(--text-color);
              }

              p {
                margin: 0 0 5px 0;
                font-weight: 600;
              }

              small {
                color: var(--text-secondary);
              }

              ul {
                margin: 0;
                padding-left: 20px;

                li {
                  color: var(--warning-color);
                  margin-bottom: 5px;
                }
              }
            }
          }
        }
      }

      .achievements-section {
        background: var(--card-background);
        padding: 25px;
        border-radius: 12px;
        margin-bottom: 25px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

        h3 {
          color: var(--primary-color);
          margin-bottom: 20px;
          display: flex;
          align-items: center;

          i {
            margin-right: 10px;
          }
        }

        .championship-results {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;

          .champion-card {
            background: var(--secondary-background);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            position: relative;
            transition: transform 0.3s ease;

            &:hover {
              transform: translateY(-5px);
            }

            .rank-badge {
              position: absolute;
              top: -10px;
              right: -10px;
              width: 40px;
              height: 40px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
              color: white;

              &.champion {
                background: linear-gradient(45deg, #ffd700, #ffed4e);
                color: #333;
              }

              &.finalist {
                background: linear-gradient(45deg, #c0c0c0, #e8e8e8);
                color: #333;
              }

              &.third {
                background: linear-gradient(45deg, #cd7f32, #daa520);
              }
            }

            img {
              width: 60px;
              height: 60px;
              border-radius: 50%;
              object-fit: cover;
              margin-bottom: 15px;
            }

            .team-info {
              h4 {
                margin: 0 0 5px 0;
                color: var(--text-color);
              }

              p {
                margin: 0;
                color: var(--text-secondary);
                font-size: 0.9rem;
              }
            }
          }
        }

        .player-achievements {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 15px;

          .player-card {
            background: var(--secondary-background);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            position: relative;
            transition: transform 0.3s ease;
            border: 2px solid transparent;

            &:hover {
              transform: translateY(-3px);
            }

            &.first-place {
              border-color: #fbbf24;
              background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), var(--secondary-background));
              box-shadow: 0 4px 15px rgba(251, 191, 36, 0.2);
            }

            &.second-place {
              border-color: #60a5fa;
              background: linear-gradient(135deg, rgba(96, 165, 250, 0.15), var(--secondary-background));
              box-shadow: 0 4px 15px rgba(96, 165, 250, 0.2);
            }

            &.third-place {
              border-color: #ea580c;
              background: linear-gradient(135deg, rgba(234, 88, 12, 0.15), var(--secondary-background));
              box-shadow: 0 4px 15px rgba(234, 88, 12, 0.2);
            }

            .rank-badge, .position-badge {
              position: absolute;
              top: -8px;
              right: -8px;
              background: var(--primary-color);
              color: white;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 0.8rem;
              font-weight: bold;

              &.gold {
                background: linear-gradient(45deg, #fbbf24, #f59e0b);
                color: white;
                box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
              }

              &.silver {
                background: linear-gradient(45deg, #60a5fa, #3b82f6);
                color: white;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
              }

              &.bronze {
                background: linear-gradient(45deg, #ea580c, #dc2626);
                color: white;
                box-shadow: 0 2px 8px rgba(234, 88, 12, 0.3);
              }
            }

            .position-badge {
              background: var(--accent-color);
            }

            img {
              width: 50px;
              height: 50px;
              border-radius: 50%;
              object-fit: cover;
              margin-bottom: 10px;
            }

            .player-info {
              h4 {
                margin: 0 0 5px 0;
                color: var(--text-color);
                font-size: 0.9rem;
              }

              p {
                margin: 0 0 8px 0;
                color: var(--text-secondary);
                font-size: 0.8rem;
              }

              .stat {
                background: var(--primary-color);
                color: white;
                padding: 2px 6px;
                border-radius: 6px;
                font-size: 0.8rem;
                margin: 2px;
                display: inline-block;
              }

              .stat-ratio {
                background: var(--accent-color);
                color: white;
                padding: 2px 6px;
                border-radius: 6px;
                font-size: 0.75rem;
                margin: 2px;
                display: inline-block;
                opacity: 0.9;
              }
            }
          }
        }
      }

      .action-section {
        text-align: center;
        margin-top: 30px;

        .end-season-btn {
          background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
          color: white;
          border: none;
          padding: 15px 30px;
          border-radius: 12px;
          font-size: 1.1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

          &:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }

          i {
            margin-right: 10px;
          }
        }
      }
    }

    .no-preview {
      text-align: center;
      padding: 50px;
      color: var(--text-secondary);

      i {
        font-size: 3rem;
        margin-bottom: 20px;
        color: var(--primary-color);
      }

      p {
        font-size: 1.1rem;
      }
    }
  }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .modal-content {
    background: var(--card-background);
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    .modal-header {
      padding: 20px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        color: var(--primary-color);
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: var(--text-secondary);
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: var(--text-color);
        }
      }
    }

    .modal-body {
      padding: 20px;

      .warning {
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid var(--warning-color);
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        display: flex;
        align-items: center;

        i {
          color: var(--warning-color);
          margin-right: 10px;
          font-size: 1.2rem;
        }

        p {
          margin: 0;
          color: var(--warning-color);
        }
      }

      .confirmation-options {
        margin: 20px 0;

        .checkbox-label {
          display: flex;
          align-items: center;
          cursor: pointer;

          input[type="checkbox"] {
            margin-right: 10px;
          }
        }
      }

      .action-description {
        font-weight: 600;
        margin: 20px 0 10px 0;
      }

      .action-list {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          color: var(--text-secondary);

          strong {
            color: var(--warning-color);
          }
        }
      }
    }

    .modal-footer {
      padding: 20px;
      border-top: 1px solid var(--border-color);
      display: flex;
      justify-content: flex-end;
      gap: 15px;

      .cancel-btn {
        background: var(--secondary-color);
        color: var(--text-color);
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          background: var(--border-color);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }

      .confirm-btn {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          background: var(--primary-hover);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        i {
          margin-right: 8px;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .admin-end-season-container {
    padding: 15px;

    .header {
      .back-btn {
        position: static;
        margin-bottom: 20px;
      }

      h1 {
        font-size: 2rem;
      }
    }

    .content {
      .selection-section {
        padding: 20px;
      }

      .preview-section {
        .achievements-section {
          padding: 20px;

          .championship-results {
            grid-template-columns: 1fr;
          }

          .player-achievements {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
          }
        }
      }
    }
  }

  .modal-overlay {
    .modal-content {
      width: 95%;
      margin: 10px;
    }
  }
}
