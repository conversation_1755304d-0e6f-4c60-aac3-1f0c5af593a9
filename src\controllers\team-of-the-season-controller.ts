import { NextFunction, Request, Response } from "express";
import { inject, injectable } from "tsyringe";
import { ITeamOfTheSeasonService } from "../interfaces/wrapper-services/team-of-the-season-service.interface";
import logger from "../config/logger";

export interface ITeamOfTheSeasonController {
  getTeamOfTheSeason(req: Request, res: Response, next: NextFunction): Promise<void>;
  getAllTeamsOfTheSeason(req: Request, res: Response, next: NextFunction): Promise<void>;
  getAvailableSeasons(req: Request, res: Response, next: NextFunction): Promise<void>;
  getAvailableFormations(req: Request, res: Response, next: NextFunction): Promise<void>;
  generateTeamOfTheSeason(req: Request, res: Response, next: NextFunction): Promise<void>;
  regenerateTeamOfTheSeason(req: Request, res: Response, next: NextFunction): Promise<void>;
  getSupportedFormations(req: Request, res: Response, next: NextFunction): Promise<void>;
}

@injectable()
export default class TeamOfTheSeasonController implements ITeamOfTheSeasonController {
  constructor(
    @inject("ITeamOfTheSeasonService") private teamOfTheSeasonService: ITeamOfTheSeasonService
  ) {}

  async getTeamOfTheSeason(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { leagueId, seasonNumber, formation = "3-5-2" } = req.params;

    try {
      const tots = await this.teamOfTheSeasonService.getTeamOfTheSeason(
        leagueId,
        parseInt(seasonNumber),
        formation
      );

      if (!tots) {
        res.status(404).json({ message: "Team of the Season not found" });
        return;
      }

      res.json(tots);
    } catch (error: any) {
      next(error);
    }
  }

  async getAllTeamsOfTheSeason(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { leagueId } = req.params;

    try {
      const allTOTS = await this.teamOfTheSeasonService.getAllTeamsOfTheSeason(leagueId);
      res.json(allTOTS);
    } catch (error: any) {
      next(error);
    }
  }

  async getAvailableSeasons(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { leagueId } = req.params;

    try {
      const seasons = await this.teamOfTheSeasonService.getAvailableSeasons(leagueId);
      res.json(seasons);
    } catch (error: any) {
      next(error);
    }
  }

  async getAvailableFormations(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { leagueId, seasonNumber } = req.params;

    try {
      const formations = await this.teamOfTheSeasonService.getAvailableFormations(
        leagueId,
        parseInt(seasonNumber)
      );
      res.json(formations);
    } catch (error: any) {
      next(error);
    }
  }

  async generateTeamOfTheSeason(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { leagueId, seasonNumber } = req.params;
    const { formation = "3-5-2" } = req.body;

    try {
      const tots = await this.teamOfTheSeasonService.generateTeamOfTheSeason(
        leagueId,
        parseInt(seasonNumber),
        formation
      );

      res.status(201).json(tots);
    } catch (error: any) {
      next(error);
    }
  }

  async regenerateTeamOfTheSeason(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { leagueId, seasonNumber } = req.params;
    const { formation = "3-5-2" } = req.body;

    try {
      const tots = await this.teamOfTheSeasonService.regenerateTeamOfTheSeason(
        leagueId,
        parseInt(seasonNumber),
        formation
      );

      res.json(tots);
    } catch (error: any) {
      next(error);
    }
  }

  async getSupportedFormations(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const formations = this.teamOfTheSeasonService.getSupportedFormations();
      res.json(formations);
    } catch (error: any) {
      next(error);
    }
  }
}
