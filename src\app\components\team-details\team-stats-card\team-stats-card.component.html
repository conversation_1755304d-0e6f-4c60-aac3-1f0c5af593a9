<div class="team-stats-card" [class.compact]="compact">
    <div class="card-header">
        <h3 class="card-title">{{ title }}</h3>
    </div>
    
    <div class="stats-grid" [style.grid-template-columns]="getGridColumns()">
        <div class="stat-item" 
             *ngFor="let stat of stats; trackBy: trackByStat"
             [class]="getStatColorClass(stat.color)">
            
            <div class="stat-icon" *ngIf="stat.icon">
                <i [class]="stat.icon"></i>
            </div>
            
            <div class="stat-content">
                <div class="stat-header">
                    <span class="stat-label">{{ stat.label }}</span>
                    <div class="stat-trend" 
                         *ngIf="stat.trend && stat.trendValue"
                         [class]="'trend--' + stat.trend">
                        <i [class]="getTrendIcon(stat.trend)"></i>
                        <span>{{ stat.trendValue }}</span>
                    </div>
                </div>
                <div class="stat-value">{{ stat.value }}</div>
            </div>
        </div>
    </div>
</div>
