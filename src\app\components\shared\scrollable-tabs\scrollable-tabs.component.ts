import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TabItem {
  id: string | number;
  label: string;
  icon?: string;
  count?: number;
  disabled?: boolean;
}

@Component({
  selector: 'app-scrollable-tabs',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './scrollable-tabs.component.html',
  styleUrls: ['./scrollable-tabs.component.scss']
})
export class ScrollableTabsComponent implements AfterViewInit, OnDestroy {
  @Input() tabs: TabItem[] = [];
  @Input() activeTabId: string | number = '';
  @Input() showArrows: boolean = true;
  @Input() showCounts: boolean = true;
  @Input() variant: 'default' | 'pills' | 'underline' = 'default';
  
  @Output() tabChange = new EventEmitter<TabItem>();
  
  @ViewChild('tabsContainer', { static: false }) tabsContainer!: ElementRef;
  @ViewChild('tabsList', { static: false }) tabsList!: ElementRef;

  showLeftArrow: boolean = false;
  showRightArrow: boolean = false;
  private resizeObserver?: ResizeObserver;

  ngAfterViewInit(): void {
    this.checkScrollability();
    this.setupResizeObserver();
    
    // Listen for scroll events
    if (this.tabsContainer) {
      this.tabsContainer.nativeElement.addEventListener('scroll', () => {
        this.updateArrowVisibility();
      });
    }
  }

  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  private setupResizeObserver(): void {
    if (typeof ResizeObserver !== 'undefined' && this.tabsContainer) {
      this.resizeObserver = new ResizeObserver(() => {
        this.checkScrollability();
      });
      this.resizeObserver.observe(this.tabsContainer.nativeElement);
    }
  }

  private checkScrollability(): void {
    if (!this.tabsContainer || !this.tabsList) return;

    const container = this.tabsContainer.nativeElement;
    const list = this.tabsList.nativeElement;
    
    const isScrollable = list.scrollWidth > container.clientWidth;
    
    if (isScrollable && this.showArrows) {
      this.updateArrowVisibility();
    } else {
      this.showLeftArrow = false;
      this.showRightArrow = false;
    }
  }

  private updateArrowVisibility(): void {
    if (!this.tabsContainer) return;

    const container = this.tabsContainer.nativeElement;
    const scrollLeft = container.scrollLeft;
    const maxScrollLeft = container.scrollWidth - container.clientWidth;

    this.showLeftArrow = scrollLeft > 0;
    this.showRightArrow = scrollLeft < maxScrollLeft - 1; // -1 for rounding issues
  }

  onTabClick(tab: TabItem): void {
    if (tab.disabled) return;
    
    this.activeTabId = tab.id;
    this.tabChange.emit(tab);
    this.scrollToActiveTab();
  }

  scrollLeft(): void {
    if (!this.tabsContainer) return;
    
    const container = this.tabsContainer.nativeElement;
    const scrollAmount = container.clientWidth * 0.7; // Scroll 70% of container width
    
    container.scrollBy({
      left: -scrollAmount,
      behavior: 'smooth'
    });
  }

  scrollRight(): void {
    if (!this.tabsContainer) return;
    
    const container = this.tabsContainer.nativeElement;
    const scrollAmount = container.clientWidth * 0.7; // Scroll 70% of container width
    
    container.scrollBy({
      left: scrollAmount,
      behavior: 'smooth'
    });
  }

  private scrollToActiveTab(): void {
    if (!this.tabsContainer || !this.activeTabId) return;

    const container = this.tabsContainer.nativeElement;
    const activeTab = container.querySelector(`[data-tab-id="${this.activeTabId}"]`) as HTMLElement;
    
    if (activeTab) {
      const containerRect = container.getBoundingClientRect();
      const tabRect = activeTab.getBoundingClientRect();
      
      // Check if tab is fully visible
      const isTabVisible = tabRect.left >= containerRect.left && 
                          tabRect.right <= containerRect.right;
      
      if (!isTabVisible) {
        const scrollLeft = activeTab.offsetLeft - (container.clientWidth / 2) + (activeTab.clientWidth / 2);
        
        container.scrollTo({
          left: Math.max(0, scrollLeft),
          behavior: 'smooth'
        });
      }
    }
  }

  getTabClasses(tab: TabItem): string {
    const classes = ['tab-item'];
    
    if (tab.id === this.activeTabId) {
      classes.push('active');
    }
    
    if (tab.disabled) {
      classes.push('disabled');
    }
    
    classes.push(`variant-${this.variant}`);
    
    return classes.join(' ');
  }

  trackByTabId(index: number, tab: TabItem): string | number {
    return tab.id;
  }
}
