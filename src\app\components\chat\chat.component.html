<div class="chat-container">
  <!-- Chat Header -->
  <div class="chat-header">
    <div class="header-content">
      <h2>
        <i class="fas fa-comments"></i>
        League Chat
      </h2>
      <button 
        class="refresh-btn"
        (click)="refreshChat()"
        [disabled]="loading"
        title="Refresh Chat">
        <i class="fas fa-sync-alt" [class.spinning]="loading"></i>
      </button>
    </div>
  </div>

  <!-- Messages Container -->
  <div 
    class="messages-container" 
    #messagesContainer
    (scroll)="onScroll()">
    
    <!-- Load More Button -->
    <div class="load-more-container" *ngIf="hasMore">
      <button 
        class="load-more-btn"
        (click)="loadMoreMessages()"
        [disabled]="loading">
        <i class="fas fa-spinner fa-spin" *ngIf="loading"></i>
        <i class="fas fa-chevron-up" *ngIf="!loading"></i>
        {{ loading ? 'Loading...' : 'Load More Messages' }}
      </button>
    </div>

    <!-- Messages List -->
    <div class="messages-list">
      <div 
        *ngFor="let message of messages; trackBy: trackByMessageId"
        class="message-wrapper"
        [class.own-message]="isCurrentUser(message)"
        [class.admin-message]="message.author.role === 'admin'">
        
        <!-- Reply Reference -->
        <div class="reply-reference" *ngIf="message.replyTo">
          <i class="fas fa-reply"></i>
          <span class="reply-author">{{ message.replyTo.author.firstName }} {{ message.replyTo.author.lastName }}</span>
          <span class="reply-content">{{ message.replyTo.content }}</span>
        </div>

        <!-- Message Content -->
        <div class="message-content">
          <!-- Avatar -->
          <div class="message-avatar" *ngIf="!isCurrentUser(message)">
            <img 
              [src]="getProfilePicture(message)" 
              [alt]="message.author.firstName + ' ' + message.author.lastName"
              onerror="this.src='assets/icons/default-profile.png'">
            <span class="admin-badge" *ngIf="message.author.role === 'admin'">
              <i class="fas fa-crown"></i>
            </span>
          </div>

          <!-- Message Body -->
          <div class="message-body">
            <!-- Author and Time -->
            <div class="message-header" *ngIf="!isCurrentUser(message)">
              <span class="author-name">
                {{ message.author.firstName }} {{ message.author.lastName }}
                <span class="admin-label" *ngIf="message.author.role === 'admin'">Admin</span>
              </span>
              <span class="message-time">{{ getMessageTime(message.timestamp) }}</span>
            </div>

            <!-- Message Text -->
            <div class="message-text">
              {{ message.content }}
              <span class="edited-indicator" *ngIf="message.edited" title="Edited">
                <i class="fas fa-edit"></i>
              </span>
            </div>

            <!-- Own Message Time -->
            <div class="own-message-time" *ngIf="isCurrentUser(message)">
              {{ getMessageTime(message.timestamp) }}
            </div>

            <!-- Reactions -->
            <div class="message-reactions" *ngIf="message.reactions.length > 0">
              <button 
                *ngFor="let reaction of message.reactions"
                class="reaction-btn"
                [class.user-reacted]="reaction.userReacted"
                (click)="addReaction(message.id, reaction.emoji)"
                [title]="reaction.count + ' reaction(s)'">
                {{ reaction.emoji }} {{ reaction.count }}
              </button>
            </div>

            <!-- Message Actions -->
            <div class="message-actions">
              <button 
                class="action-btn reply-btn"
                (click)="startReply(message)"
                title="Reply">
                <i class="fas fa-reply"></i>
              </button>
              
              <button 
                class="action-btn emoji-btn"
                (click)="showEmojiPickerForMessage(message.id)"
                title="Add Reaction">
                <i class="fas fa-smile"></i>
              </button>

              <button 
                *ngIf="canEditMessage(message)"
                class="action-btn edit-btn"
                (click)="startEdit(message)"
                title="Edit">
                <i class="fas fa-edit"></i>
              </button>

              <button 
                *ngIf="canDeleteMessage(message)"
                class="action-btn delete-btn"
                (click)="deleteMessage(message)"
                title="Delete">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <!-- Own Message Avatar -->
          <div class="message-avatar" *ngIf="isCurrentUser(message)">
            <img 
              [src]="getProfilePicture(message)" 
              [alt]="message.author.firstName + ' ' + message.author.lastName"
              onerror="this.src='assets/icons/default-profile.png'">
            <span class="admin-badge" *ngIf="message.author.role === 'admin'">
              <i class="fas fa-crown"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Emoji Picker -->
  <div class="emoji-picker" *ngIf="showEmojiPicker && selectedMessageForEmoji">
    <div class="emoji-picker-content">
      <div class="emoji-picker-header">
        <span>Quick Reactions</span>
        <button class="close-emoji-btn" (click)="hideEmojiPicker()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="emoji-grid">
        <button 
          *ngFor="let emoji of commonEmojis"
          class="emoji-option"
          (click)="addReaction(selectedMessageForEmoji!, emoji)">
          {{ emoji }}
        </button>
      </div>
    </div>
  </div>

  <!-- Reply/Edit Indicator -->
  <div class="reply-indicator" *ngIf="replyingTo">
    <div class="indicator-content">
      <i class="fas fa-reply"></i>
      <span>Replying to <strong>{{ replyingTo.author.firstName }} {{ replyingTo.author.lastName }}</strong></span>
      <button class="cancel-btn" (click)="cancelReply()">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>

  <div class="edit-indicator" *ngIf="editingMessage">
    <div class="indicator-content">
      <i class="fas fa-edit"></i>
      <span>Editing message</span>
      <button class="cancel-btn" (click)="cancelEdit()">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>

  <!-- Message Input -->
  <div class="message-input-container">
    <form [formGroup]="messageForm" (ngSubmit)="editingMessage ? editMessage() : sendMessage()">
      <div class="input-wrapper">
        <textarea
          #messageInput
          formControlName="content"
          class="message-input"
          [placeholder]="editingMessage ? 'Edit your message...' : 'Type your message...'"
          rows="1"
          maxlength="1000"
          (keydown)="onKeyPress($event)"
          [disabled]="loading"></textarea>
        
        <div class="input-actions">
          <span class="character-count">
            {{ messageForm.get('content')?.value?.length || 0 }}/1000
          </span>
          
          <button 
            type="submit"
            class="send-btn"
            [disabled]="messageForm.invalid || loading"
            [title]="editingMessage ? 'Update Message' : 'Send Message'">
            <i class="fas fa-paper-plane" *ngIf="!editingMessage"></i>
            <i class="fas fa-check" *ngIf="editingMessage"></i>
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
