<!-- === BEAUTIFUL PLAYER SEASON HISTORY COMPONENT === -->
<div class="season-history-container">

  <!-- Header -->
  <div class="history-header">
    <button class="back-button" (click)="goBack()">
      <i class="fas fa-arrow-left"></i>
      <span>Back</span>
    </button>
    <div class="header-content">
      <h1 class="page-title">
        <i class="fas fa-history"></i>
        <span>Season History</span>
      </h1>
      <div class="player-info" *ngIf="seasonHistoryData">
        <img [src]="seasonHistoryData.playerImgUrl || getDefaultPlayerImage()" 
             [alt]="seasonHistoryData.playerName" 
             class="player-avatar">
        <span class="player-name">{{seasonHistoryData.playerName}}</span>
      </div>
    </div>
    <div class="header-actions">
      <button class="view-toggle-btn" (click)="toggleViewMode()">
        <i [class]="viewMode === 'table' ? 'fas fa-th-large' : 'fas fa-table'"></i>
        <span>{{viewMode === 'table' ? 'Cards' : 'Table'}}</span>
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-state" *ngIf="isLoading">
    <div class="loading-spinner">
      <i class="fas fa-spinner fa-spin"></i>
    </div>
    <p class="loading-text">Loading season history...</p>
  </div>

  <!-- Error State -->
  <div class="error-state" *ngIf="hasError && !isLoading">
    <div class="error-icon">
      <i class="fas fa-exclamation-triangle"></i>
    </div>
    <p class="error-text">{{errorMessage}}</p>
    <button class="retry-btn" (click)="loadSeasonHistory()">
      <i class="fas fa-redo"></i>
      <span>Try Again</span>
    </button>
  </div>

  <!-- Season History Content -->
  <div class="history-content" *ngIf="seasonHistoryData && !isLoading && !hasError">
    
    <!-- Career Summary -->
    <div class="career-summary">
      <h2 class="section-title">
        <i class="fas fa-trophy"></i>
        <span>Career Summary</span>
      </h2>
      
      <div class="summary-grid">
        <div class="summary-card">
          <div class="summary-icon seasons">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <div class="summary-content">
            <span class="summary-value">{{seasonHistoryData.careerTotals.totalSeasons}}</span>
            <span class="summary-label">Seasons</span>
          </div>
        </div>

        <div class="summary-card">
          <div class="summary-icon games">
            <i class="fas fa-gamepad"></i>
          </div>
          <div class="summary-content">
            <span class="summary-value">{{seasonHistoryData.careerTotals.totalGames}}</span>
            <span class="summary-label">Total Games</span>
          </div>
        </div>

        <div class="summary-card">
          <div class="summary-icon goals">
            <i class="fas fa-futbol"></i>
          </div>
          <div class="summary-content">
            <span class="summary-value">{{seasonHistoryData.careerTotals.totalGoals}}</span>
            <span class="summary-label">Total Goals</span>
          </div>
        </div>

        <div class="summary-card">
          <div class="summary-icon assists">
            <i class="fas fa-hands-helping"></i>
          </div>
          <div class="summary-content">
            <span class="summary-value">{{seasonHistoryData.careerTotals.totalAssists}}</span>
            <span class="summary-label">Total Assists</span>
          </div>
        </div>

        <div class="summary-card">
          <div class="summary-icon rating">
            <i class="fas fa-star"></i>
          </div>
          <div class="summary-content">
            <span class="summary-value" [ngClass]="getRatingClass(seasonHistoryData.careerTotals.careerAvgRating)">
              {{formatStatValue(seasonHistoryData.careerTotals.careerAvgRating, 2)}}
            </span>
            <span class="summary-label">Career Avg Rating</span>
          </div>
        </div>

        <div class="summary-card best-season">
          <div class="summary-icon best">
            <i class="fas fa-crown"></i>
          </div>
          <div class="summary-content">
            <span class="summary-value">{{seasonHistoryData.careerTotals.bestSeason.seasonName}}</span>
            <span class="summary-label">{{seasonHistoryData.careerTotals.bestSeason.reason}}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Season Details -->
    <div class="season-details">
      <div class="details-header">
        <h2 class="section-title">
          <i class="fas fa-chart-line"></i>
          <span>Season by Season</span>
        </h2>
        
        <!-- Sort Controls -->
        <div class="sort-controls" *ngIf="viewMode === 'table'">
          <span class="sort-label">Sort by:</span>
          <button class="sort-btn" 
                  [class.active]="sortBy === 'season'"
                  (click)="setSortBy('season')">
            <span>Season</span>
            <i [class]="getSortIcon('season')"></i>
          </button>
          <button class="sort-btn" 
                  [class.active]="sortBy === 'games'"
                  (click)="setSortBy('games')">
            <span>Games</span>
            <i [class]="getSortIcon('games')"></i>
          </button>
          <button class="sort-btn" 
                  [class.active]="sortBy === 'goals'"
                  (click)="setSortBy('goals')">
            <span>Goals</span>
            <i [class]="getSortIcon('goals')"></i>
          </button>
          <button class="sort-btn" 
                  [class.active]="sortBy === 'assists'"
                  (click)="setSortBy('assists')">
            <span>Assists</span>
            <i [class]="getSortIcon('assists')"></i>
          </button>
          <button class="sort-btn" 
                  [class.active]="sortBy === 'rating'"
                  (click)="setSortBy('rating')">
            <span>Rating</span>
            <i [class]="getSortIcon('rating')"></i>
          </button>
        </div>
      </div>

      <!-- Table View -->
      <div class="table-view" *ngIf="viewMode === 'table'">
        <div class="table-container">
          <table class="seasons-table">
            <thead>
              <tr>
                <th>Season</th>
                <th>Team</th>
                <th>Games</th>
                <th>Goals</th>
                <th>Assists</th>
                <th>Clean Sheets</th>
                <th>POTM</th>
                <th>Avg Rating</th>
                <th>Goals/Game</th>
                <th>Assists/Game</th>
                <th *ngIf="isAdmin()">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let season of getSortedSeasons()" 
                  [class.current-season]="isCurrentSeason(season.seasonId)">
                <td class="season-cell">
                  <div class="season-info">
                    <span class="season-name">{{season.seasonName}}</span>
                    <span class="current-badge" *ngIf="isCurrentSeason(season.seasonId)">Current</span>
                    <span class="best-badge" *ngIf="getBestSeasonBadge(season)">{{getBestSeasonBadge(season)}}</span>
                  </div>
                </td>
                <td class="team-cell">
                  <div class="team-info" *ngIf="season.team">
                    <img [src]="season.team.imgUrl || getDefaultTeamImage()" 
                         [alt]="season.team.name" 
                         class="team-logo">
                    <span class="team-name">{{season.team.name}}</span>
                  </div>
                  <span class="no-team" *ngIf="!season.team">Free Agent</span>
                </td>
                <td class="stat-cell">{{season.stats.games}}</td>
                <td class="stat-cell">{{season.stats.goals}}</td>
                <td class="stat-cell">{{season.stats.assists}}</td>
                <td class="stat-cell">{{season.stats.cleanSheets}}</td>
                <td class="stat-cell">{{season.stats.playerOfTheMatch}}</td>
                <td class="stat-cell">
                  <span [ngClass]="getRatingClass(season.stats.avgRating)">
                    {{formatStatValue(season.stats.avgRating, 2)}}
                  </span>
                </td>
                <td class="stat-cell">{{formatStatValue(getGoalsPerGame(season), 2)}}</td>
                <td class="stat-cell">{{formatStatValue(getAssistsPerGame(season), 2)}}</td>
                <td class="action-cell" *ngIf="isAdmin()">
                  <button class="delete-btn"
                          *ngIf="!isCurrentSeason(season.seasonId)"
                          (click)="deleteSeason(season)"
                          title="Delete Season">
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Cards View -->
      <div class="cards-view" *ngIf="viewMode === 'cards'">
        <div class="season-cards-grid">
          <div class="season-card" 
               *ngFor="let season of getSortedSeasons()"
               [class.current-season]="isCurrentSeason(season.seasonId)">
            
            <div class="card-header">
              <div class="season-info">
                <h3 class="season-name">{{season.seasonName}}</h3>
                <div class="season-badges">
                  <span class="current-badge" *ngIf="isCurrentSeason(season.seasonId)">Current</span>
                  <span class="best-badge" *ngIf="getBestSeasonBadge(season)">{{getBestSeasonBadge(season)}}</span>
                </div>
              </div>

              <div class="card-actions" *ngIf="isAdmin() && !isCurrentSeason(season.seasonId)">
                <button class="delete-btn"
                        (click)="deleteSeason(season)"
                        title="Delete Season">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
              
              <div class="team-info" *ngIf="season.team">
                <img [src]="season.team.imgUrl || getDefaultTeamImage()" 
                     [alt]="season.team.name" 
                     class="team-logo">
                <span class="team-name">{{season.team.name}}</span>
              </div>
              <div class="no-team" *ngIf="!season.team">
                <i class="fas fa-user-free"></i>
                <span>Free Agent</span>
              </div>
            </div>

            <div class="card-stats">
              <div class="stat-row">
                <div class="stat-item">
                  <span class="stat-label">Games</span>
                  <span class="stat-value">{{season.stats.games}}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Goals</span>
                  <span class="stat-value">{{season.stats.goals}}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Assists</span>
                  <span class="stat-value">{{season.stats.assists}}</span>
                </div>
              </div>
              
              <div class="stat-row">
                <div class="stat-item">
                  <span class="stat-label">Clean Sheets</span>
                  <span class="stat-value">{{season.stats.cleanSheets}}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">POTM</span>
                  <span class="stat-value">{{season.stats.playerOfTheMatch}}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Avg Rating</span>
                  <span class="stat-value" [ngClass]="getRatingClass(season.stats.avgRating)">
                    {{formatStatValue(season.stats.avgRating, 2)}}
                  </span>
                </div>
              </div>

              <div class="per-game-stats">
                <div class="per-game-item">
                  <span class="per-game-label">Goals/Game</span>
                  <span class="per-game-value">{{formatStatValue(getGoalsPerGame(season), 2)}}</span>
                </div>
                <div class="per-game-item">
                  <span class="per-game-label">Assists/Game</span>
                  <span class="per-game-value">{{formatStatValue(getAssistsPerGame(season), 2)}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
