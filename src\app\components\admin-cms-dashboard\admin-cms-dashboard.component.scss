.cms-dashboard-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
}

// Header Section
.cms-header {
  margin-bottom: 30px;
  
  .header-content {
    text-align: center;
    margin-bottom: 30px;
    
    .cms-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 10px;
      color: var(--text-primary);
      
      i {
        margin-right: 15px;
        color: var(--accent-color);
      }
    }
    
    .cms-subtitle {
      font-size: 1.1rem;
      color: var(--text-secondary);
      margin: 0;
    }
  }
  
  .quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
    
    .stat-card {
      background: var(--card-background);
      border: 1px solid var(--border-color);
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 15px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: var(--accent-color);
      }
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        background: var(--accent-color);
        display: flex;
        align-items: center;
        justify-content: center;
        
        i {
          font-size: 1.5rem;
          color: white;
        }
      }
      
      .stat-info {
        h3 {
          font-size: 2rem;
          font-weight: 700;
          margin: 0;
          color: var(--text-primary);
        }
        
        p {
          margin: 0;
          color: var(--text-secondary);
          font-size: 0.9rem;
        }
      }
    }
  }
}

// Navigation Tabs
.cms-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 30px;
  border-bottom: 2px solid var(--border-color);
  overflow-x: auto;
  
  .tab-button {
    background: transparent;
    border: none;
    padding: 15px 20px;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
    
    i {
      font-size: 0.9rem;
    }
    
    &:hover {
      background: var(--hover-background);
      color: var(--text-primary);
    }
    
    &.active {
      background: var(--accent-color);
      color: white;
      border-bottom: 2px solid var(--accent-color);
    }
  }
}

// Search Section
.search-section {
  margin-bottom: 30px;
  
  .search-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 10px;
    
    .search-icon {
      position: absolute;
      left: 15px;
      color: var(--text-secondary);
      z-index: 1;
    }
    
    .search-input {
      flex: 1;
      padding: 12px 15px 12px 45px;
      border: 2px solid var(--border-color);
      border-radius: 25px;
      font-size: 1rem;
      background: var(--input-background);
      color: var(--text-primary);
      transition: border-color 0.3s ease;
      
      &:focus {
        outline: none;
        border-color: var(--accent-color);
      }
      
      &::placeholder {
        color: var(--text-secondary);
      }
    }
    
    .refresh-btn {
      background: var(--accent-color);
      border: none;
      border-radius: 50%;
      width: 45px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: var(--accent-hover);
        transform: rotate(180deg);
      }
    }
  }
}

// Loading State
.loading-container {
  text-align: center;
  padding: 60px 20px;
  
  .loading-spinner {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 20px;
  }
  
  p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0;
  }
}

// Overview Content
.overview-content {
  .overview-section {
    text-align: center;
    
    h2 {
      font-size: 2rem;
      margin-bottom: 15px;
      color: var(--text-primary);
    }
    
    p {
      font-size: 1.1rem;
      color: var(--text-secondary);
      margin-bottom: 40px;
    }
    
    .overview-actions {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 40px;
      max-width: 800px;
      margin: 0 auto;
      
      .action-group {
        h3 {
          font-size: 1.3rem;
          margin-bottom: 20px;
          color: var(--text-primary);
        }
        
        .action-btn {
          display: block;
          width: 100%;
          padding: 15px 20px;
          margin-bottom: 10px;
          border: none;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          text-decoration: none;
          text-align: center;
          
          i {
            margin-right: 10px;
          }
          
          &.create-btn {
            background: var(--success-color);
            color: white;
            
            &:hover {
              background: var(--success-hover);
              transform: translateY(-2px);
            }
          }
          
          &.manage-btn {
            background: var(--info-color);
            color: white;

            &:hover {
              background: var(--info-hover);
              transform: translateY(-2px);
            }

            &.end-season-btn {
              background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
              font-weight: 600;
              box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

              &:hover {
                background: linear-gradient(45deg, var(--primary-hover), var(--accent-hover));
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
              }
            }
          }
        }
      }
    }
  }
}

// Data Content
.data-content {
  .data-table-container {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 30px;
  }
}

// Pagination
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  
  .pagination {
    display: flex;
    align-items: center;
    gap: 15px;
    
    .page-btn {
      background: var(--card-background);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover:not(:disabled) {
        background: var(--accent-color);
        color: white;
        border-color: var(--accent-color);
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
    
    .page-info {
      font-weight: 500;
      color: var(--text-primary);
      padding: 0 10px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .cms-dashboard-container {
    padding: 15px;
  }
  
  .cms-header .cms-title {
    font-size: 2rem;
  }
  
  .quick-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .cms-tabs {
    .tab-button {
      padding: 12px 15px;
      font-size: 0.9rem;
    }
  }
  
  .overview-actions {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}
