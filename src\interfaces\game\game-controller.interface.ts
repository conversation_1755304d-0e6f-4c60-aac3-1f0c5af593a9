import { NextFunction, Request, Response } from "express";

export interface IGameController {
  getGameById(req: Request, res: Response, next: NextFunction): Promise<void>;
  getAllGames(req: Request, res: Response, next: NextFunction): Promise<void>;

  getCurrentSeasonTeamGames(req: Request, res: Response, next: NextFunction): Promise<void>;
  getTopAvgRatingByPosition(req: Request, res: Response, next: NextFunction): Promise<void>;

  setTechnicalResult(req: Request, res: Response, next: NextFunction): Promise<void>;
  updateGameResult(req: Request, res: Response, next: NextFunction): Promise<void>;
  updateGameDate(req: Request, res: Response, next: NextFunction): Promise<void>;
  updateGameBroadcast(req: Request, res: Response, next: NextFunction): Promise<void>;
  updateTeamPlayersPerformance(req: Request, res: Response, next: NextFunction): Promise<void>;

  deleteGame(req: Request, res: Response, next: NextFunction): Promise<void>;
}
