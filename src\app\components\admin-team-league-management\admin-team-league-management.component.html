<div class="admin-team-league-container">
  <div class="page-header">
    <h1 class="page-title">
      <i class="fas fa-users-cog"></i>
      Team League Management
    </h1>
    <p class="page-description">
      Assign teams to leagues and manage league compositions
    </p>
  </div>

  <div class="management-content" *ngIf="!isLoading; else loadingTemplate">
    <!-- League Selection -->
    <div class="selection-section">
      <div class="form-group">
        <label for="leagueSelect" class="form-label">
          <i class="fas fa-trophy"></i>
          Select League
        </label>
        <select 
          id="leagueSelect"
          class="form-select"
          [(ngModel)]="selectedLeagueId"
          (change)="onLeagueChange()">
          <option value="">Choose a league...</option>
          <option *ngFor="let league of leagues" [value]="league.id">
            {{ league.name }}
          </option>
        </select>
      </div>
    </div>

    <!-- League Details & Team Management -->
    <div class="league-management" *ngIf="selectedLeagueId">
      <div class="league-info">
        <h2 class="league-title">{{ selectedLeague?.name }}</h2>
        <div class="league-stats">
          <div class="stat-item">
            <span class="stat-label">Teams in League:</span>
            <span class="stat-value">{{ leagueTeams.length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Available Teams:</span>
            <span class="stat-value">{{ availableTeams.length }}</span>
          </div>
        </div>
      </div>

      <!-- Add Team Section -->
      <div class="add-team-section">
        <h3 class="section-title">
          <i class="fas fa-plus-circle"></i>
          Add Team to League
        </h3>
        
        <div class="add-team-form" *ngIf="availableTeams.length > 0; else noTeamsTemplate">
          <div class="form-group">
            <label for="teamSelect" class="form-label">Select Team</label>
            <select 
              id="teamSelect"
              class="form-select"
              [(ngModel)]="selectedTeamId">
              <option value="">Choose a team...</option>
              <option *ngFor="let team of availableTeams" [value]="team.id">
                {{ team.name }}
              </option>
            </select>
          </div>
          
          <button 
            class="btn btn-primary"
            [disabled]="!selectedTeamId"
            (click)="addTeamToLeague()">
            <i class="fas fa-plus"></i>
            Add Team
          </button>
        </div>

        <ng-template #noTeamsTemplate>
          <div class="no-teams-message">
            <i class="fas fa-info-circle"></i>
            All available teams are already assigned to this league
          </div>
        </ng-template>
      </div>

      <!-- Current Teams in League -->
      <div class="current-teams-section">
        <h3 class="section-title">
          <i class="fas fa-list"></i>
          Teams in {{ selectedLeague?.name }}
        </h3>

        <div class="teams-loading" *ngIf="isLoadingTeams">
          <i class="fas fa-spinner fa-spin"></i>
          Loading teams...
        </div>

        <div class="teams-grid" *ngIf="!isLoadingTeams && leagueTeams.length > 0">
          <div class="team-card" *ngFor="let team of leagueTeams">
            <div class="team-info">
              <img 
                [src]="team.imgUrl || 'assets/Icons/Team.png'" 
                [alt]="team.name"
                class="team-image"
                (error)="onImageError($event)">
              <div class="team-details">
                <h4 class="team-name">{{ team.name }}</h4>
                <p class="team-stats">{{ team.players.length || 0 }} players</p>
              </div>
            </div>
            
            <button 
              class="btn btn-danger btn-sm"
              (click)="removeTeamFromLeague(team.id)"
              title="Remove team from league">
              <i class="fas fa-times"></i>
              Remove
            </button>
          </div>
        </div>

        <div class="no-teams-in-league" *ngIf="!isLoadingTeams && leagueTeams.length === 0">
          <i class="fas fa-users-slash"></i>
          <p>No teams assigned to this league yet</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Template -->
  <ng-template #loadingTemplate>
    <div class="loading-container">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading leagues and teams...</p>
    </div>
  </ng-template>
</div>
