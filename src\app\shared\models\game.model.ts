import { GAME_STATUS } from "@pro-clubs-manager/shared-dtos";
import { PlayerStat } from "./player-stat.model";

export enum PLAYOFF_STAGE {
  PLAY_IN_ROUND = "Play-in Round",
  QUARTER_FINAL = "Quarter-Final",
  SEMI_FINAL = "Semi-Final",
  FINAL = "Final",
  THIRD_PLACE = "3rd Place Match",
  PROMOTION_PLAYOFF = "Promotion Playoff",
  RELEGATION_PLAYOFF = "Relegation Playoff"
}

export enum SERIES_FORMAT {
  BEST_OF_1 = "Best of 1",
  BEST_OF_3 = "Best of 3",
  BEST_OF_5 = "Best of 5",
  BEST_OF_7 = "Best of 7"
}

export interface PlayoffDetails {
  stage: PLAYOFF_STAGE;
  format: SERIES_FORMAT;
}

export interface Fixture {
    id: string;
    leagueId: string;
    fixtureNum: number
    startDate: Date | undefined;
    endDate: Date | undefined;
    games: Game[];


}

export interface Game {
    id: string;
    fixtureNum: number;
    homeTeamDetails: TeamStats;
    awayTeamDetails: TeamStats;
    date?: Date | undefined;
    status?: string;
}

export interface TeamStats {
    teamID: string;
    teamName: string;
    teamImgUrl?: string;
    teamGoalsAmount: number;
    teamScorers?: PlayerStat[]; // player id and how many scored
    teamAssists?: PlayerStat[];// player id and how many scored
}

export type FixtureDTO = {
    id: string;
    round: number;
    leagueId: string;
    startDate: Date;
    endDate: Date;
    games: GameFixtureData[];
    isPlayoff?: boolean;
    playoffDetails?: PlayoffDetails;
  };

  export type PaginatedFixtureDTO = {
    fixtures: FixtureDTO[];
    currentPage: number;
    totalPages: number;
    totalFixtures: number;
  };

  export type GameFixtureData = {
    id: string,
    homeTeam: {
      id: string;
      name: string;
      imgUrl?: string;
    };
    awayTeam: {
      id: string;
      name: string;
      imgUrl?: string;
    };
    result?: {
      homeTeamGoals: number;
      awayTeamGoals: number;
      penalties?: {
        homeTeamPenalties: number;
        awayTeamPenalties: number;
      };
    };
    status: GAME_STATUS;
    date?: Date;
    isPlayoff?: boolean;
    playoffStage?: string;
    matchNumber?: number;
    broadcast?: {
      streamUrl: string;
      broadcastingTeam: string;
    };
  };

  type GoalsData = {
    scorerId: string;
    minute?: number;
    assistId?: string;
  }

  export type PlayerGameStatsData = {
    id: string;
    goals?: number;
    assists?: number;
    rating: number;
    playerOfTheMatch?: boolean;
  };
  
  export type UpdatePlayerPerformanceDataRequest = {
    playerId: string;
    goals: number;
    assists: number;
    playerOfTheMatch: boolean;
    positionPlayed: string;
    rating: number;
  };

  export type TeamGameStatsData = {
    playersStats: PlayerGameStatsData[];
    goals?: {
      scorerId: string;
      minute?: number;
      assisterId?: string;
      isOwnGoal?: boolean;
    }[];
  };