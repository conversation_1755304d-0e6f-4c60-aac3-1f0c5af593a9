import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BracketsService, PlayoffBracket, BracketStage, BracketMatch } from '../../services/brackets.service';
import { LeagueService } from '../../services/league.service';
import { NotificationService } from '../../services/notification.service';
import { PLAYOFF_STAGE } from '../../shared/models/game.model';

export interface BracketSeries {
  title: string;
  homeTeam: { id: string; name: string; imgUrl?: string };
  awayTeam: { id: string; name: string; imgUrl?: string };
  matches: BracketMatch[];
}

@Component({
  selector: 'app-brackets',
  templateUrl: './brackets.component.html',
  styleUrls: ['./brackets.component.scss']
})
export class BracketsComponent implements OnInit {
  bracket: PlayoffBracket | null = null;
  loading = false;
  selectedSeason: number | null = null;
  availableSeasons: number[] = [];
  leagueId = '65ecb1eb2f272e434483a821'; // Current league ID

  // View options
  viewMode: 'bracket' | 'list' = 'bracket';
  selectedStage: PLAYOFF_STAGE | null = null;
  constructor(
    private bracketsService: BracketsService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadAvailableSeasons();
    this.loadCurrentBracket();
  }

  private async loadAvailableSeasons(): Promise<void> {
    try {
      // Use season numbers instead of years, with season 7 as current for testing
      this.availableSeasons = [ 6, 5, 4, 3, 2, 1];
      this.selectedSeason = 6; // Current season for testing
    } catch (error) {
      console.error('Error loading seasons:', error);
    }
  }

  private async loadCurrentBracket(forceRefresh: boolean = false): Promise<void> {
    try {
      this.loading = true;
      this.bracket = await this.bracketsService.getPlayoffBracket(this.leagueId, this.selectedSeason || undefined, forceRefresh);
    } catch (error) {
      console.error('Error loading bracket:', error);
      this.notificationService.error('Failed to load playoff bracket');
    } finally {
      this.loading = false;
    }
  }

  async onSeasonChange(season: number): Promise<void> {
    this.selectedSeason = season;
    await this.loadCurrentBracket();
  }

  async refreshBracket(): Promise<void> {
    await this.loadCurrentBracket(true); // Force refresh
    this.notificationService.success('Bracket refreshed');
  }

  onViewModeChange(mode: 'bracket' | 'list'): void {
    this.viewMode = mode;
  }

  onStageSelect(stage: PLAYOFF_STAGE | null): void {
    this.selectedStage = this.selectedStage === stage ? null : stage;
  }

  // Helper methods
  getCompletionPercentage(): number {
    return this.bracket ? this.bracketsService.getBracketCompletionPercentage(this.bracket) : 0;
  }

  getNextMatch(): BracketMatch | null {
    return this.bracket ? this.bracketsService.getNextMatch(this.bracket) : null;
  }

  getMatchWinner(match: BracketMatch): { id: string; name: string; imgUrl?: string } | null {
    return this.bracketsService.getMatchWinner(match);
  }

  getMatchScore(match: BracketMatch): string {
    return this.bracketsService.getMatchScore(match);
  }

  getMatchStatusIcon(match: BracketMatch): string {
    return this.bracketsService.getMatchStatusIcon(match);
  }

  getMatchStatusColor(match: BracketMatch): string {
    return this.bracketsService.getMatchStatusColor(match);
  }

  isMatchCompleted(match: BracketMatch): boolean {
    return this.bracketsService.isMatchCompleted(match);
  }

  // Format status for better display in brackets
  getFormattedStatus(status: string): string {
    switch (status?.toUpperCase()) {
      case 'SCHEDULED':
        return 'TBD';
      case 'PLAYED':
      case 'COMPLETED':
        return 'Final';
      case 'IN_PROGRESS':
        return 'Live';
      default:
        return 'TBD';
    }
  }

  // Check if game should be displayed (hide scheduled games option)
  shouldDisplayGame(game: any): boolean {
    // Option 1: Hide scheduled games completely
    // return game.status !== 'SCHEDULED' && game.status !== 'Scheduled';

    // Option 2: Show all games but with better formatting
    return true;
  }

  // Filter games to show only played/completed games
  getDisplayableGames(games: any[]): any[] {
    if (!games) return [];

    // Option 1: Show only completed games
    // return games.filter(game =>
    //   game.status === 'PLAYED' ||
    //   game.status === 'COMPLETED' ||
    //   game.status === 'Played' ||
    //   game.status === 'Completed'
    // );

    // Option 2: Show all games with better status formatting
    return games;
  }

  formatMatchDate(date: Date): string {
    return this.bracketsService.formatMatchDate(date);
  }

  getTeamImage(team: { imgUrl?: string }): string {
    return this.bracketsService.getTeamImage(team);
  }

  getStageDisplayName(stage: PLAYOFF_STAGE): string {
    return this.bracketsService.getStageDisplayName(stage);
  }

  // Bracket visualization helpers
  getStageWidth(stage: BracketStage): string {
    const matchCount = stage.matches.length;
    if (matchCount <= 1) return '200px';
    if (matchCount <= 2) return '250px';
    if (matchCount <= 4) return '300px';
    return '350px';
  }

  getMatchPosition(matchIndex: number, totalMatches: number): string {
    if (totalMatches === 1) return '50%';
    const spacing = 100 / (totalMatches + 1);
    return `${spacing * (matchIndex + 1)}%`;
  }

  // Navigation helpers
  goToGameDetails(matchId: string): void {
    this.router.navigate(['/game-details', matchId]);
  }

  shareMatch(match: BracketMatch): void {
    const text = `${match.homeTeam.name} vs ${match.awayTeam.name} - ${this.getStageDisplayName(match.playoffStage)}`;
    if (navigator.share) {
      navigator.share({
        title: 'Playoff Match',
        text: text,
        url: window.location.href
      });
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(`${text} - ${window.location.href}`);
      this.notificationService.success('Match details copied to clipboard');
    }
  }



  // Filter methods for list view
  getFilteredStages(): BracketStage[] {
    if (!this.bracket) return [];
    if (!this.selectedStage) return this.bracket.stages;
    return this.bracket.stages.filter(stage => stage.stage === this.selectedStage);
  }

  getStageIcon(stage: PLAYOFF_STAGE): string {
    const stageIcons = {
      [PLAYOFF_STAGE.PLAY_IN_ROUND]: 'fas fa-play',
      [PLAYOFF_STAGE.QUARTER_FINAL]: 'fas fa-trophy',
      [PLAYOFF_STAGE.SEMI_FINAL]: 'fas fa-medal',
      [PLAYOFF_STAGE.FINAL]: 'fas fa-crown',
      [PLAYOFF_STAGE.THIRD_PLACE]: 'fas fa-award',
      [PLAYOFF_STAGE.PROMOTION_PLAYOFF]: 'fas fa-arrow-up',
      [PLAYOFF_STAGE.RELEGATION_PLAYOFF]: 'fas fa-arrow-down'
    };
    return stageIcons[stage] || 'fas fa-futbol';
  }

  getStageColor(stage: PLAYOFF_STAGE): string {
    const stageColors = {
      [PLAYOFF_STAGE.PLAY_IN_ROUND]: 'info',
      [PLAYOFF_STAGE.QUARTER_FINAL]: 'primary',
      [PLAYOFF_STAGE.SEMI_FINAL]: 'warning',
      [PLAYOFF_STAGE.FINAL]: 'success',
      [PLAYOFF_STAGE.THIRD_PLACE]: 'secondary',
      [PLAYOFF_STAGE.PROMOTION_PLAYOFF]: 'success',
      [PLAYOFF_STAGE.RELEGATION_PLAYOFF]: 'danger'
    };
    return stageColors[stage] || 'primary';
  }

  // Championship display helpers
  hasChampionshipResults(): boolean {
    return !!(this.bracket?.champion || this.bracket?.runnerUp || this.bracket?.thirdPlace);
  }

  getChampionshipTitle(): string {
    if (!this.selectedSeason) return 'Championship Results';
    return `Season ${this.selectedSeason} Champions`;
  }

  // Check if bracket has any playoff data
  hasPlayoffData(): boolean {
    return !!(this.bracket && this.bracket.stages && this.bracket.stages.length > 0 &&
              this.bracket.stages.some(stage => stage.matches && stage.matches.length > 0));
  }

  // Tournament tree methods
  getSeriesFormat(stage: string): string | null {
    const stageData = this.bracket?.stages.find(s => s.stage === stage);
    return stageData?.seriesFormat || null;
  }

  getLeftSideQuarterFinals(): BracketSeries[] {
    return this.getQuarterFinalSeries().slice(0, 2); // First 2 series on left
  }

  getRightSideQuarterFinals(): BracketSeries[] {
    return this.getQuarterFinalSeries().slice(2, 4); // Last 2 series on right
  }

  private getQuarterFinalSeries(): BracketSeries[] {
    const quarterFinalStage = this.bracket?.stages.find(stage => stage.stage === PLAYOFF_STAGE.QUARTER_FINAL);
    if (!quarterFinalStage?.matches) return [];

    // Group matches by teams to create series
    const seriesMap = new Map<string, BracketMatch[]>();

    quarterFinalStage.matches.forEach(match => {
      const key = `${match.homeTeam.id}-${match.awayTeam.id}`;
      const reverseKey = `${match.awayTeam.id}-${match.homeTeam.id}`;

      if (seriesMap.has(key)) {
        seriesMap.get(key)!.push(match);
      } else if (seriesMap.has(reverseKey)) {
        seriesMap.get(reverseKey)!.push(match);
      } else {
        seriesMap.set(key, [match]);
      }
    });

    // Convert to BracketSeries array
    const series: BracketSeries[] = [];
    let seriesIndex = 1;

    seriesMap.forEach((matches) => {
      if (matches.length > 0) {
        const firstMatch = matches[0];
        series.push({
          title: `Series ${seriesIndex}`,
          homeTeam: firstMatch.homeTeam,
          awayTeam: firstMatch.awayTeam,
          matches: matches.sort((a, b) => (a.matchNumber || 0) - (b.matchNumber || 0))
        });
        seriesIndex++;
      }
    });

    return series;
  }

  trackBySeries(_index: number, series: BracketSeries): string {
    return `${series.homeTeam.id}-${series.awayTeam.id}`;
  }

  isSeriesCompleted(series: BracketSeries): boolean {
    const completedMatches = series.matches.filter(m =>
      m.status === 'Played' || m.status === 'Completed'
    );
    return completedMatches.length >= Math.ceil(series.matches.length / 2);
  }

  getSeriesScore(series: BracketSeries): string | null {
    // Use the series result from the backend (first match contains the series score)
    const seriesMatch = series.matches[0];
    if (seriesMatch?.result) {
      const { homeTeamGoals, awayTeamGoals } = seriesMatch.result;
      if (homeTeamGoals === 0 && awayTeamGoals === 0) return null;
      return `${homeTeamGoals}-${awayTeamGoals}`;
    }
    return null;
  }

  getSeriesWinner(series: BracketSeries): string | null {
    // Use the series result from the backend (first match contains the series score)
    const seriesMatch = series.matches[0];
    if (seriesMatch?.result && seriesMatch.status === 'Completed') {
      const { homeTeamGoals, awayTeamGoals } = seriesMatch.result;

      // For a series, the team with more wins is the winner
      // No need to calculate winsNeeded, just compare the scores
      if (homeTeamGoals > awayTeamGoals) return series.homeTeam.id;
      if (awayTeamGoals > homeTeamGoals) return series.awayTeam.id;
    }
    return null;
  }

  getIndividualGames(series: BracketSeries): BracketMatch[] {
    // Return the individual games from the series match
    const seriesMatch = series.matches[0];
    const games = seriesMatch?.matches || [];

    // Apply filtering to show only displayable games
    return this.getDisplayableGames(games);
  }

  getGameResult(game: BracketMatch, seriesHomeTeamId: string): string {
    if (!game.result) return 'vs';

    // Determine the score from the perspective of the series teams
    const gameHomeTeamId = game.homeTeam.id;
    const isSeriesHomeTeamPlayingAtHome = gameHomeTeamId === seriesHomeTeamId;

    let regularScore: string;
    let penaltyScore: string = '';

    if (isSeriesHomeTeamPlayingAtHome) {
      regularScore = `${game.result.homeTeamGoals}-${game.result.awayTeamGoals}`;
      if (game.result.penalties) {
        penaltyScore = ` (${game.result.penalties.homeTeamPenalties}-${game.result.penalties.awayTeamPenalties} pens)`;
      }
    } else {
      regularScore = `${game.result.awayTeamGoals}-${game.result.homeTeamGoals}`;
      if (game.result.penalties) {
        penaltyScore = ` (${game.result.penalties.awayTeamPenalties}-${game.result.penalties.homeTeamPenalties} pens)`;
      }
    }

    return regularScore + penaltyScore;
  }
}
