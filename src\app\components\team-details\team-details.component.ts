import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TeamService } from '../../services/team.service';
import { NotificationService } from '../../services/notification.service';
import { ConfigurationService } from '../../services/configuration.service';
import { TeamDTO } from '@pro-clubs-manager/shared-dtos';
import { Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-team-details',
  templateUrl: './team-details.component.html',
  styleUrls: ['./team-details.component.scss']
})
export class TeamDetailsComponent implements OnInit, OnDestroy {

  teamID: string = '';
  chosenTeam: TeamDTO | null = null;
  editTeamMode: boolean = false;
  editedTeamName: string | null = null;
  editTeamPhotoModel: FormData | null = null;
  isLoading: boolean = false;
  private routeSubscription: Subscription = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private teamService: TeamService,
    private notificationService: NotificationService,
    private authService: AuthService
  ) { }

  ngOnInit() {
    // Subscribe to route parameter changes to handle navigation between different teams
    this.routeSubscription = this.route.paramMap.subscribe(params => {
      const newTeamId = params.get('id');
      if (newTeamId && newTeamId !== this.teamID) {
        this.teamID = newTeamId;
        this.loadTeamDetails();
      }
    });

    // Also check query parameters as fallback
    this.route.queryParamMap.subscribe(queryParams => {
      const queryTeamId = queryParams.get('id');
      if (queryTeamId && queryTeamId !== this.teamID && !this.route.snapshot.paramMap.get('id')) {
        this.teamID = queryTeamId;
        this.loadTeamDetails();
      }
    });

    // Initial load
    this.loadTeamDetails();
  }

  ngOnDestroy() {
    // Clean up subscriptions to prevent memory leaks
    this.routeSubscription.unsubscribe();
  }

  async loadTeamDetails(): Promise<void> {
    // If no teamID is set yet, try to get it from route snapshot (for initial load)
    if (!this.teamID) {
      this.teamID = this.route.snapshot.paramMap.get('id') ||
                    this.route.snapshot.queryParamMap.get('id') ||
                    '';
    }

    if (this.teamID) {
      this.isLoading = true;
      this.chosenTeam = null; // Reset team while loading
      try {
        this.chosenTeam = await this.teamService.getTeamById(this.teamID);
      } catch (error) {
        console.error('Error loading team details:', error);
        this.chosenTeam = null;
      } finally {
        this.isLoading = false;
      }
    } else {
      // No team ID provided, not loading
      this.isLoading = false;
      this.chosenTeam = null;
    }
  }

  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  // === EDIT FUNCTIONALITY ===

  onEditClick(): void {
    this.editedTeamName = this.chosenTeam?.name || '';
    this.editTeamMode = true;
  }

  onCancelEditClick(): void {
    this.editTeamMode = false;
    this.editedTeamName = null;
    this.editTeamPhotoModel = null;
  }

  async onSaveEditClick(): Promise<void> {
    if (!this.chosenTeam) return;

    try {
      // Update team name if changed
      if (this.editedTeamName && this.editedTeamName !== this.chosenTeam.name) {
        // Call team service to update name
        // await this.teamService.updateTeamName(this.chosenTeam.id, this.editedTeamName);
        this.chosenTeam.name = this.editedTeamName;
      }

      // Update team logo if changed
      if (this.editTeamPhotoModel) {
        // Call team service to update logo
        // await this.teamService.updateTeamLogo(this.chosenTeam.id, this.editTeamPhotoModel);
      }

      this.notificationService.success('Team updated successfully');
      this.editTeamMode = false;
      this.editedTeamName = null;
      this.editTeamPhotoModel = null;
    } catch (error) {
      console.error('Error updating team:', error);
      // Since NotificationService only has success method, we'll just log the error
    }
  }

  onFileSelected(event: any): void {
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    const file: File = event.target.files[0];
    this.setTeamImage(file);
  }

  private setTeamImage(file: File): void {
    this.editTeamPhotoModel = new FormData();
    this.editTeamPhotoModel.append('file', file);
  }

  onArrowBackClick(): void {
    history.back();
  }

  navigateToLeagueTable(): void {
    this.router.navigate(['/league-table']);
  }
}