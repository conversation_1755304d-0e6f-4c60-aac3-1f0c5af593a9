<div class="cms-dashboard-container">
    <!-- Header -->
    <div class="cms-header">
        <div class="header-content">
            <h1 class="cms-title">
                <i class="fas fa-cogs"></i>
                Admin CMS Dashboard
            </h1>
            <p class="cms-subtitle">Comprehensive database management system</p>
        </div>
        
        <!-- Quick Stats -->
        <div class="quick-stats" *ngIf="activeTab === 'overview'">
            <div class="stat-card" (click)="switchTab('players')">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-info">
                    <h3>{{ stats.totalPlayers }}</h3>
                    <p>Players</p>
                </div>
            </div>
            <div class="stat-card" (click)="switchTab('teams')">
                <div class="stat-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="stat-info">
                    <h3>{{ stats.totalTeams }}</h3>
                    <p>Teams</p>
                </div>
            </div>
            <div class="stat-card" (click)="switchTab('games')">
                <div class="stat-icon">
                    <i class="fas fa-futbol"></i>
                </div>
                <div class="stat-info">
                    <h3>{{ stats.totalGames }}</h3>
                    <p>Games</p>
                </div>
            </div>
            <div class="stat-card" (click)="switchTab('leagues')">
                <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-info">
                    <h3>{{ stats.totalLeagues }}</h3>
                    <p>Leagues</p>
                </div>
            </div>
            <div class="stat-card" (click)="switchTab('news')">
                <div class="stat-icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div class="stat-info">
                    <h3>{{ stats.totalNews }}</h3>
                    <p>News</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="cms-tabs">
        <button class="tab-button" 
                [class.active]="activeTab === 'overview'"
                (click)="switchTab('overview')">
            <i class="fas fa-chart-pie"></i>
            Overview
        </button>
        <button class="tab-button" 
                [class.active]="activeTab === 'players'"
                (click)="switchTab('players')">
            <i class="fas fa-users"></i>
            Players
        </button>
        <button class="tab-button" 
                [class.active]="activeTab === 'teams'"
                (click)="switchTab('teams')">
            <i class="fas fa-shield-alt"></i>
            Teams
        </button>
        <button class="tab-button" 
                [class.active]="activeTab === 'games'"
                (click)="switchTab('games')">
            <i class="fas fa-futbol"></i>
            Games
        </button>
        <button class="tab-button" 
                [class.active]="activeTab === 'leagues'"
                (click)="switchTab('leagues')">
            <i class="fas fa-trophy"></i>
            Leagues
        </button>
        <button class="tab-button" 
                [class.active]="activeTab === 'news'"
                (click)="switchTab('news')">
            <i class="fas fa-newspaper"></i>
            News
        </button>
    </div>

    <!-- Search Bar (for data tabs) -->
    <div class="search-section" *ngIf="activeTab !== 'overview'">
        <div class="search-container">
            <i class="fas fa-search search-icon"></i>
            <input type="text" 
                   class="search-input" 
                   [(ngModel)]="searchTerm"
                   placeholder="Search {{ activeTab }}...">
            <button class="refresh-btn" (click)="switchTab(activeTab)">
                <i class="fas fa-sync-alt"></i>
            </button>
        </div>
    </div>

    <!-- Loading State -->
    <div class="loading-container" *ngIf="isLoading">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>Loading {{ activeTab }}...</p>
    </div>

    <!-- Overview Content -->
    <div class="overview-content" *ngIf="activeTab === 'overview' && !isLoading">
        <div class="overview-section">
            <h2>Database Overview</h2>
            <p>Welcome to the Admin CMS Dashboard. Here you can manage all aspects of your Pro Clubs database.</p>
            
            <div class="overview-actions">
                <div class="action-group">
                    <h3>Quick Actions</h3>
                    <button class="action-btn create-btn" routerLink="/create-player">
                        <i class="fas fa-user-plus"></i>
                        Create Player
                    </button>
                    <button class="action-btn create-btn" routerLink="/create-team">
                        <i class="fas fa-plus-circle"></i>
                        Create Team
                    </button>
                    <button class="action-btn create-btn" routerLink="/add-fixture">
                        <i class="fas fa-calendar-plus"></i>
                        Add Fixture
                    </button>
                    <button class="action-btn create-btn" routerLink="/add-news">
                        <i class="fas fa-newspaper"></i>
                        Add News
                    </button>
                </div>
                
                <div class="action-group">
                    <h3>Management</h3>
                    <button class="action-btn manage-btn" routerLink="/admin/team-league-management">
                        <i class="fas fa-cog"></i>
                        Team & League Management
                    </button>
                    <button class="action-btn manage-btn" routerLink="/admin/player-requests">
                        <i class="fas fa-user-check"></i>
                        Player Requests
                    </button>
                    <button class="action-btn manage-btn" routerLink="/assign-player-to-team">
                        <i class="fas fa-user-plus"></i>
                        Assign Player to Team
                    </button>
                    <button class="action-btn manage-btn end-season-btn" routerLink="/admin/end-season">
                        <i class="fas fa-flag-checkered"></i>
                        End Season
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Tables Content -->
    <div class="data-content" *ngIf="activeTab !== 'overview' && !isLoading">
        <!-- Players Table -->
        <div class="data-table-container" *ngIf="activeTab === 'players'">
            <app-cms-players-table 
                [players]="paginatedItems"
                (playerDeleted)="loadPlayers()"
                (playerUpdated)="loadPlayers()">
            </app-cms-players-table>
        </div>

        <!-- Teams Table -->
        <div class="data-table-container" *ngIf="activeTab === 'teams'">
            <app-cms-teams-table 
                [teams]="paginatedItems"
                (teamDeleted)="loadTeams()"
                (teamUpdated)="loadTeams()">
            </app-cms-teams-table>
        </div>

        <!-- Games Table -->
        <div class="data-table-container" *ngIf="activeTab === 'games'">
            <app-cms-games-table 
                [games]="paginatedItems"
                (gameDeleted)="loadGames()"
                (gameUpdated)="loadGames()">
            </app-cms-games-table>
        </div>

        <!-- Leagues Table -->
        <div class="data-table-container" *ngIf="activeTab === 'leagues'">
            <app-cms-leagues-table 
                [leagues]="paginatedItems"
                (leagueDeleted)="loadLeagues()"
                (leagueUpdated)="loadLeagues()">
            </app-cms-leagues-table>
        </div>

        <!-- News Table -->
        <div class="data-table-container" *ngIf="activeTab === 'news'">
            <app-cms-news-table 
                [news]="paginatedItems"
                (newsDeleted)="loadNews()"
                (newsUpdated)="loadNews()">
            </app-cms-news-table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container" *ngIf="totalPages > 1">
            <div class="pagination">
                <button class="page-btn" 
                        [disabled]="currentPage === 1"
                        (click)="previousPage()">
                    <i class="fas fa-chevron-left"></i>
                </button>
                
                <span class="page-info">
                    Page {{ currentPage }} of {{ totalPages }}
                </span>
                
                <button class="page-btn" 
                        [disabled]="currentPage === totalPages"
                        (click)="nextPage()">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>
