<div class="squad-container">
    <div class="squad-header">
        <h3 class="squad-title">
            <i class="fas fa-users"></i>
            {{ title }}
            <span class="player-count">({{ players.length }})</span>
        </h3>
    </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="isLoading">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
        <span>Loading players...</span>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="!isLoading && players.length === 0">
        <div class="empty-icon">
            <i class="fas fa-user-slash"></i>
        </div>
        <h4>No Players</h4>
        <p>This team doesn't have any players yet.</p>
    </div>

    <!-- Players List -->
    <div class="players-grid" *ngIf="!isLoading && players.length > 0">
        <div class="player-card" 
             *ngFor="let player of players; trackBy: trackByPlayerId"
             (click)="onPlayerClick(player)">
            
            <div class="player-avatar">
                <img [src]="player.imgUrl || 'assets/Icons/User.jpg'" 
                     [alt]="player.name"
                     class="avatar-image">
                <div class="position-badge" [class]="getPositionColor(player.position)">
                    {{ player.position }}
                </div>
            </div>

            <div class="player-info">
                <h4 class="player-name">{{ player.name }}</h4>
                <div class="player-stats">
                    <div class="stat-item">
                        <i class="fas fa-gamepad"></i>
                        <span>{{ player.stats.games || 0 }}</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-futbol"></i>
                        <span>{{ player.stats.goals || 0 }}</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-hands-helping"></i>
                        <span>{{ player.stats.assists || 0 }}</span>
                    </div>
                    <div class="stat-item rating">
                        <i class="fas fa-star"></i>
                        <span>{{ (player.stats.avgRating || 0).toFixed(1) }}</span>
                    </div>
                </div>
            </div>

            <div class="player-actions" *ngIf="canEdit">
                <button class="remove-btn" 
                        (click)="onRemovePlayerClick($event, player)"
                        title="Remove from team">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</div>
