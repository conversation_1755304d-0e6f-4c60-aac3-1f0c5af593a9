.promotional-banner {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
  border-radius: 12px;
  padding: 20px;
  margin: 16px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
  }

  &.dismissed {
    opacity: 0;
    transform: translateY(-20px);
    pointer-events: none;
  }

  // Size variants
  &.small {
    padding: 12px 16px;
    
    .banner-content {
      flex-direction: row;
      align-items: center;
      gap: 12px;
    }
    
    .banner-icon {
      font-size: 1.2rem;
      width: 32px;
      height: 32px;
    }
    
    .banner-title {
      font-size: 1rem;
      margin-bottom: 2px;
    }
    
    .banner-subtitle {
      font-size: 0.8rem;
    }
    
    .banner-actions {
      flex-direction: row;
      gap: 8px;
      
      .btn {
        padding: 6px 12px;
        font-size: 0.8rem;
      }
    }
  }

  &.medium {
    padding: 20px;
  }

  &.large {
    padding: 32px;
    
    .banner-icon {
      font-size: 2.5rem;
      width: 64px;
      height: 64px;
    }
    
    .banner-title {
      font-size: 1.8rem;
    }
    
    .banner-subtitle {
      font-size: 1.1rem;
    }
  }

  // Color variants
  &.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  }

  &.secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
  }

  &.accent {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  }
}

.banner-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  z-index: 1;
  padding-right: 48px; // Add space for dismiss button

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  // Adjust padding for small banners
  .promotional-banner.small & {
    padding-right: 40px;
  }
}

.banner-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.banner-text {
  flex: 1;
  color: white;

  .banner-title {
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0 0 8px 0;
    line-height: 1.2;
  }

  .banner-subtitle {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
    line-height: 1.4;
  }
}

.banner-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex-shrink: 0;

  @media (min-width: 768px) {
    flex-direction: row;
    gap: 16px;
  }

  .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;

    &.btn-primary {
      background: white;
      color: var(--primary-color);

      &:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
    }

    &.btn-secondary {
      background: transparent;
      color: white;
      border: 2px solid rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

.dismiss-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10; // Ensure it's above other content

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }

  i {
    font-size: 0.9rem;
  }

  // Adjust positioning for small banners to avoid overlap
  .promotional-banner.small & {
    top: 8px;
    right: 8px;
    width: 28px;
    height: 28px;

    i {
      font-size: 0.8rem;
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// Dark mode adjustments
[data-theme="dark"] {
  .promotional-banner {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

// Light mode adjustments
[data-theme="light"] {
  .promotional-banner {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}
