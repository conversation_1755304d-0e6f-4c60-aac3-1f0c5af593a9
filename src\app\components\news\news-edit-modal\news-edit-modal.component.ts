import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { News } from '../news.model';

@Component({
  selector: 'app-news-edit-modal',
  templateUrl: './news-edit-modal.component.html',
  styleUrls: ['./news-edit-modal.component.scss']
})
export class NewsEditModalComponent implements OnInit {
  @Input() show: boolean = false;
  @Input() news: News | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<{ newsId: string, newsData: any }>();

  editForm: FormGroup;
  isLoading: boolean = false;

  constructor(private formBuilder: FormBuilder) {
    this.editForm = this.formBuilder.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      content: ['', [Validators.required, Validators.minLength(10)]],
      type: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    if (this.news) {
      this.editForm.patchValue({
        title: this.news.title,
        content: this.news.content,
        type: this.news.type
      });
    }
  }

  ngOnChanges(): void {
    if (this.news && this.editForm) {
      this.editForm.patchValue({
        title: this.news.title,
        content: this.news.content,
        type: this.news.type
      });
    }
  }

  onClose(): void {
    this.close.emit();
  }

  onSave(): void {
    if (this.editForm.valid && this.news) {
      this.isLoading = true;
      const formData = this.editForm.value;
      this.save.emit({ newsId: this.news._id, newsData: formData });
    }
  }

  stopLoading(): void {
    this.isLoading = false;
  }

  get title() { return this.editForm.get('title'); }
  get content() { return this.editForm.get('content'); }
  get type() { return this.editForm.get('type'); }
}
