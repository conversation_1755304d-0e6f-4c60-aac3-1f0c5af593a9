import { AdvancedPlayersStats, TopAssister, TopAvgRating, TopScorer } from "@pro-clubs-manager/shared-dtos";
import { Types } from "mongoose";
import { inject, injectable } from "tsyringe";
import logger from "../../config/logger";
import { IPlayerRepository } from "../../interfaces/player";
import { ITeamRepository } from "../../interfaces/team";
import { ILeagueService } from "../../interfaces/league";
import { ILeagueStatsService } from "../../interfaces/wrapper-services/league-stats-service.interface";
import { IGameRepository } from "../../interfaces/game";
import { AllTimeTopAvgRatingByPosition, MostHattricks, MostCleanSheets, MostWinningPercentageTeam, MostWinningPercentagePlayer } from "../../repositories/game-repository";

@injectable()
export class LeagueStatsService implements ILeagueStatsService {
  private playerRepository: IPlayerRepository;
  private teamRepository: ITeamRepository;
  private leagueService: ILeagueService;
  private gameRepository: IGameRepository;

  constructor(
    @inject("IPlayerRepository") playerRepository: IPlayerRepository,
    @inject("ITeamRepository") teamRepository: ITeamRepository,
    @inject("ILeagueService") leagueService: ILeagueService,
    @inject("IGameRepository") gameRepository: IGameRepository
  ) {
    this.playerRepository = playerRepository;
    this.teamRepository = teamRepository;
    this.leagueService = leagueService;
    this.gameRepository = gameRepository;
  }

  async getLeagueTopScorers(leagueId: string | Types.ObjectId, limit: number = 10): Promise<TopScorer[]> {
    logger.info(`LeagueStatsService: getting top scorers for league ${leagueId} with game-by-game aggregation`);

    try {
      // Get current season number
      const currentSeasonNumber = await this.gameRepository.getMaxSeasonNumberForLeague(leagueId.toString());
      if (!currentSeasonNumber) {
        logger.warn(`No games found for league ${leagueId}`);
        return [];
      }

      logger.info(`Using season ${currentSeasonNumber} for league ${leagueId}`);

      // Use game-by-game aggregation to ensure ALL goals from current season are counted
      // This prevents data loss when players transfer teams mid-season
      const result = await this.gameRepository.aggregatePlayerStatsForSeason(leagueId.toString(), currentSeasonNumber);

      logger.info(`Found ${result.length} players with stats in season ${currentSeasonNumber}`);

      const topScorers: TopScorer[] = [];

      for (const playerStats of result) {
        if (playerStats.totalGames > 0) {
          // Get player details
          const player = await this.playerRepository.getPlayerById(playerStats._id.toString());
          if (!player) {
            logger.warn(`Player not found: ${playerStats._id}`);
            continue;
          }

          topScorers.push({
            playerId: player.id,
            playerName: player.name,
            position: player.position,
            teamId: player.currentSeason?.team?.toString() || "",
            teamName: "",
            playerImgUrl: player.imgUrl,
            goals: playerStats.totalGoals,
            goalsPerGame: playerStats.totalGoals / playerStats.totalGames,
            games: playerStats.totalGames,
          });
        }
      }

      // Sort by goals (descending), then by fewer games (better ratio)
      topScorers.sort((playerA, playerB) => {
        // Primary sort: by goals (descending)
        if (playerB.goals !== playerA.goals) {
          return playerB.goals - playerA.goals;
        }
        // Tiebreaker: if same goals, player with fewer games is ranked higher (better ratio)
        return playerA.games - playerB.games;
      });

      // Apply limit
      if (limit && topScorers.length > limit) {
        topScorers.length = limit;
      }

      await this.populateTeamNamesForTopPlayers(topScorers);

      logger.info(`Returning ${topScorers.length} top scorers for league ${leagueId}`);
      console.log('service - fixed with game aggregation', topScorers.slice(0, 3)); // Only log top 3

      return topScorers;
    } catch (error) {
      logger.error(`Error getting top scorers for league ${leagueId}:`, error);
      throw error;
    }
  }

  async getLeagueTopAssisters(leagueId: string | Types.ObjectId, limit: number = 10): Promise<TopAssister[]> {
    logger.info(`LeagueStatsService: getting top assisters for league ${leagueId}`);

    // Get current season number
    const currentSeasonNumber = await this.gameRepository.getMaxSeasonNumberForLeague(leagueId.toString());
    if (!currentSeasonNumber) {
      logger.warn(`No games found for league ${leagueId}`);
      return [];
    }

    // Use game-by-game aggregation to ensure ALL assists from current season are counted
    // This prevents data loss when players transfer teams mid-season
    const result = await this.gameRepository.aggregatePlayerStatsForSeason(leagueId.toString(), currentSeasonNumber);

    const topAssisters: TopAssister[] = [];

    for (const playerStats of result) {
      if (playerStats.totalGames > 0) {
        // Get player details
        const player = await this.playerRepository.getPlayerById(playerStats._id.toString());
        if (!player) continue;

        topAssisters.push({
          playerId: player.id,
          playerName: player.name,
          position: player.position,
          teamId: player.currentSeason?.team?.toString() || "",
          teamName: "",
          playerImgUrl: player.imgUrl,
          assists: playerStats.totalAssists,
          assistsPerGame: playerStats.totalAssists / playerStats.totalGames,
          games: playerStats.totalGames,
        });
      }
    }

    // Sort by assists (descending), then by fewer games (better ratio)
    topAssisters.sort((playerA, playerB) => {
      // Primary sort: by assists (descending)
      if (playerB.assists !== playerA.assists) {
        return playerB.assists - playerA.assists;
      }
      // Tiebreaker: if same assists, player with fewer games is ranked higher (better ratio)
      return playerA.games - playerB.games;
    });

    // Apply limit
    if (limit && topAssisters.length > limit) {
      topAssisters.length = limit;
    }

    await this.populateTeamNamesForTopPlayers(topAssisters);

    console.log('assists service - fixed with game aggregation', topAssisters);

    return topAssisters;
  }

  async getAllTimeTopScorers(leagueId: string | Types.ObjectId, limit: number = 50): Promise<TopScorer[]> {
    logger.info(`LeagueStatsService: getting all-time top scorers for league ${leagueId}`);
    return await this.leagueService.getAllTimeTopScorers(leagueId.toString(), limit);
  }

  async getAllTimeTopAssisters(leagueId: string | Types.ObjectId, limit: number = 50): Promise<TopAssister[]> {
    logger.info(`LeagueStatsService: getting all-time top assisters for league ${leagueId}`);
    return await this.leagueService.getAllTimeTopAssisters(leagueId.toString(), limit);
  }

  async getLeagueTopAvgRatingPlayers(leagueId: string | Types.ObjectId, limit: number = 10): Promise<TopAvgRating[]> {
    logger.info(`LeagueStatsService: getting top average rating for league ${leagueId}`);
    const players = await this.playerRepository.getPlayersByLeague(leagueId);

    const topAvgRating: TopAvgRating[] = [];
    const seasonNumber = players[0]?.currentSeason!.seasonNumber;

    players.forEach((player) => {
      let totalRating = 0;
      let totalGames = 0;

      // Check current season
      if (player.currentSeason?.league.equals(leagueId) && player.currentSeason.seasonNumber === seasonNumber) {
        totalRating += player.currentSeason.stats.avgRating * player.currentSeason.stats.games;
        totalGames += player.currentSeason.stats.games;
      }

      // Check season history
      player.seasonsHistory
        .filter((season) => season.league.equals(leagueId) && season.seasonNumber === seasonNumber)
        .forEach((season) => {
          totalRating += season.stats.avgRating * season.stats.games;
          totalGames += season.stats.games;
        });

      if (totalGames > 0) {
        topAvgRating.push({
          playerId: player.id,
          playerName: player.name,
          position: player.position,
          teamId: player.currentSeason!.team.toString(),
          teamName: "",
          playerImgUrl: player.imgUrl,
          avgRating: totalGames > 0 ? totalRating / totalGames : 0,
          games: totalGames,
        });
      }
    });
    topAvgRating.sort((playerA, playerB) => playerB.avgRating - playerA.avgRating);

    if (limit) {
      topAvgRating.length = limit;
    }

    await this.populateTeamNamesForTopPlayers(topAvgRating);

    return topAvgRating;
  }

  async getAdvancedLeaguePlayersStats(leagueId: string | Types.ObjectId, limit: number = 10): Promise<AdvancedPlayersStats> {
    logger.info(`LeagueStatsService: getting advanced stats for league ${leagueId}`);
    const players = await this.playerRepository.getPlayersByLeague(leagueId);

    const topScorers: TopScorer[] = [];
    const topAssisters: TopAssister[] = [];
    const topAvgRating: TopAvgRating[] = [];
    const seasonNumber = players[0]?.currentSeason!.seasonNumber;

    players.forEach((player) => {
      let totalGoals = 0;
      let totalAssists = 0;
      let totalRating = 0;
      let totalGames = 0;

      // Check current season
      if (player.currentSeason?.league.equals(leagueId) && player.currentSeason.seasonNumber === seasonNumber) {
        totalGoals += player.currentSeason.stats.goals;
        totalAssists += player.currentSeason.stats.assists;
        totalRating += player.currentSeason.stats.avgRating * player.currentSeason.stats.games;
        totalGames += player.currentSeason.stats.games;
      }

      // Check season history
      player.seasonsHistory
        .filter((season) => season.league.equals(leagueId) && season.seasonNumber === seasonNumber)
        .forEach((season) => {
          totalGoals += season.stats.goals;
          totalAssists += season.stats.assists;
          totalRating += season.stats.avgRating * season.stats.games;
          totalGames += season.stats.games;
        });

      if (totalGames > 0) {
        topScorers.push({
          playerId: player.id,
          playerName: player.name,
          position: player.position,
          teamId: player.currentSeason!.team.toString(),
          teamName: "",
          playerImgUrl: player.imgUrl,
          goals: totalGoals,
          goalsPerGame: totalGoals / totalGames,
          games: totalGames,
        });

        topAssisters.push({
          playerId: player.id,
          playerName: player.name,
          position: player.position,
          teamId: player.currentSeason!.team.toString(),
          teamName: "",
          playerImgUrl: player.imgUrl,
          assists: totalAssists,
          assistsPerGame: totalAssists / totalGames,
          games: totalGames,
        });

        topAvgRating.push({
          playerId: player.id,
          playerName: player.name,
          position: player.position,
          teamId: player.currentSeason!.team.toString(),
          teamName: "",
          playerImgUrl: player.imgUrl,
          avgRating: totalGames > 0 ? totalRating / totalGames : 0,
          games: totalGames,
        });
      }
    });

    topScorers.sort((playerA, playerB) => {
      // Primary sort: by goals (descending)
      if (playerB.goals !== playerA.goals) {
        return playerB.goals - playerA.goals;
      }
      // Tiebreaker: if same goals, player with fewer games is ranked higher (better ratio)
      return playerA.games - playerB.games;
    });
    topAssisters.sort((playerA, playerB) => playerB.assists - playerA.assists);
    topAvgRating.sort((playerA, playerB) => playerB.avgRating - playerA.avgRating);

    if (limit) {
      topScorers.length = limit;
      topAssisters.length = limit;
      topAvgRating.length = limit;
    }

    await Promise.all([
      this.populateTeamNamesForTopPlayers(topScorers),
      this.populateTeamNamesForTopPlayers(topAssisters),
      this.populateTeamNamesForTopPlayers(topAvgRating),
    ]);

    return { topScorers, topAssisters, topAvgRating };
  }

  async getAdvancedLeagueTeamStats(leagueId: string | Types.ObjectId): Promise<any> {
    logger.info(`LeagueStatsService: getting advanced team stats for league with id ${leagueId}`);
  }

  async getAllTimeTopAvgRatingByPosition(leagueId: string | Types.ObjectId, position: string, minimumGames: number, limit: number = 50): Promise<AllTimeTopAvgRatingByPosition[]> {
    logger.info(`LeagueStatsService: getting all-time top avg rating by position for league ${leagueId}, position: ${position}`);
    return await this.leagueService.getAllTimeTopAvgRatingByPosition(leagueId.toString(), position, minimumGames, limit);
  }

  async getMostHattricks(leagueId: string | Types.ObjectId, limit: number = 50): Promise<MostHattricks[]> {
    logger.info(`LeagueStatsService: getting most hattricks for league ${leagueId}`);
    return await this.leagueService.getMostHattricks(leagueId.toString(), limit);
  }

  async getMostCleanSheets(leagueId: string | Types.ObjectId, limit: number = 50): Promise<MostCleanSheets[]> {
    logger.info(`LeagueStatsService: getting most clean sheets for league ${leagueId}`);
    return await this.leagueService.getMostCleanSheets(leagueId.toString(), limit);
  }

  async getMostWinningPercentageTeams(leagueId: string | Types.ObjectId, minimumGames: number = 10, limit: number = 50): Promise<MostWinningPercentageTeam[]> {
    logger.info(`LeagueStatsService: getting most winning percentage teams for league ${leagueId}`);
    return await this.leagueService.getMostWinningPercentageTeams(leagueId.toString(), minimumGames, limit);
  }

  async getMostWinningPercentagePlayers(leagueId: string | Types.ObjectId, minimumGames: number = 10, limit: number = 50): Promise<MostWinningPercentagePlayer[]> {
    logger.info(`LeagueStatsService: getting most winning percentage players for league ${leagueId}`);
    return await this.leagueService.getMostWinningPercentagePlayers(leagueId.toString(), minimumGames, limit);
  }

  private async populateTeamNamesForTopPlayers(topPlayers: { teamId: string; teamName: string }[]): Promise<void> {
    const teamsIds = topPlayers.map((player) => player.teamId).filter((id) => id);
    const teams = await this.teamRepository.getTeamsByIds(teamsIds);

    const teamMap = new Map(teams.map((team) => [team.id, team.name]));

    topPlayers.forEach((topPlayer) => {
      if (topPlayer.teamId) {
        topPlayer.teamName = teamMap.get(topPlayer.teamId) || "";
      }
    });
  }
}
