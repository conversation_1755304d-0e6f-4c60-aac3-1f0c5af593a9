import { Injectable } from '@angular/core';

export interface FormationPlayer {
  position: string;
  x: number;
  y: number;
}

export interface PlayerPosition {
  x: number;
  y: number;
}

@Injectable({
  providedIn: 'root'
})
export class FormationTemplateService {

  constructor() { }

  /**
   * Get formation template based on formation name
   */
  getFormationTemplate(formation: string): FormationPlayer[] {
    switch (formation) {
      case '4-3-3':
        return this.get433Formation();
      case '4-4-2':
        return this.get442Formation();
      case '3-5-2':
        return this.get352Formation();
      case '3-4-1-2':
        return this.get3412Formation();
      case '4-2-3-1':
        return this.get4231Formation();
      case '4-1-4-1':
        return this.get4141Formation();
      case '5-3-2':
        return this.get532Formation();
      case '3-4-3':
        return this.get343Formation();
      default:
        return this.get433Formation(); // Default fallback
    }
  }

  /**
   * Get player position within formation
   */
  getPlayerPosition(position: string, formation: string, isHome: boolean = true): PlayerPosition {
    const formationTemplate = this.getFormationTemplate(formation);
    
    // Normalize position names
    const normalizedPosition = this.normalizePosition(position);

    // Find a player with matching position in the template
    const templatePlayer = formationTemplate.find(player =>
      this.normalizePosition(player.position) === normalizedPosition
    );

    if (templatePlayer) {
      return { x: templatePlayer.x, y: templatePlayer.y };
    }

    // Fallback: try to find similar position
    const similarPositions = this.getSimilarPositions(normalizedPosition);
    for (const similarPos of similarPositions) {
      const similarPlayer = formationTemplate.find(player =>
        this.normalizePosition(player.position) === similarPos
      );
      if (similarPlayer) {
        return { x: similarPlayer.x, y: similarPlayer.y };
      }
    }

    // Final fallback to default position based on side
    const baseX = isHome ? 25 : 75;
    return { x: baseX, y: 50 };
  }

  /**
   * Adjust player positions to avoid overlapping
   */
  adjustPlayerPositions(players: any[]): any[] {
    if (!players || players.length === 0) return [];

    // Group players by position to handle multiple players in same position
    const positionGroups: { [position: string]: any[] } = {};

    players.forEach(player => {
      const normalizedPosition = this.normalizePosition(player.position);
      if (!positionGroups[normalizedPosition]) {
        positionGroups[normalizedPosition] = [];
      }
      positionGroups[normalizedPosition].push(player);
    });

    const adjustedPlayers: any[] = [];
    const minDistance = 12; // Increased minimum distance for better spacing

    // Process each position group
    Object.keys(positionGroups).forEach(position => {
      const playersInPosition = positionGroups[position];
      
      if (playersInPosition.length === 1) {
        // Single player, no adjustment needed
        adjustedPlayers.push(playersInPosition[0]);
      } else {
        // Multiple players in same position, spread them out
        const basePlayer = playersInPosition[0];
        const baseX = basePlayer.x;
        const baseY = basePlayer.y;
        
        // Calculate spread positions
        const spreadDistance = minDistance;
        const totalWidth = (playersInPosition.length - 1) * spreadDistance;
        const startX = baseX - totalWidth / 2;
        
        playersInPosition.forEach((player, index) => {
          const adjustedPlayer = { ...player };
          if (index === 0) {
            // Keep first player at original position
            adjustedPlayer.x = baseX;
            adjustedPlayer.y = baseY;
          } else {
            // Spread other players horizontally
            adjustedPlayer.x = startX + (index * spreadDistance);
            adjustedPlayer.y = baseY + (index % 2 === 0 ? -3 : 3); // Slight vertical offset
          }
          
          // Ensure positions stay within bounds
          adjustedPlayer.x = Math.max(5, Math.min(95, adjustedPlayer.x));
          adjustedPlayer.y = Math.max(5, Math.min(95, adjustedPlayer.y));
          
          adjustedPlayers.push(adjustedPlayer);
        });
      }
    });

    return adjustedPlayers;
  }

  /**
   * Detect formation from team data
   */
  detectFormation(teamData: any): string {
    if (!teamData || !teamData.players) {
      return '4-3-3'; // Default formation
    }

    // Count players by position
    const positionCounts: { [key: string]: number } = {};
    
    teamData.players.forEach((player: any) => {
      const normalizedPosition = this.normalizePosition(player.position);
      positionCounts[normalizedPosition] = (positionCounts[normalizedPosition] || 0) + 1;
    });

    // Analyze formation based on position counts
    const defenders = (positionCounts['LB'] || 0) + (positionCounts['CB'] || 0) + (positionCounts['RB'] || 0);
    const midfielders = (positionCounts['CDM'] || 0) + (positionCounts['CM'] || 0) + (positionCounts['CAM'] || 0) + 
                       (positionCounts['LM'] || 0) + (positionCounts['RM'] || 0);
    const forwards = (positionCounts['LW'] || 0) + (positionCounts['RW'] || 0) + (positionCounts['ST'] || 0);

    // Formation detection logic
    if (defenders === 4 && midfielders === 3 && forwards === 3) {
      return '4-3-3';
    } else if (defenders === 4 && midfielders === 4 && forwards === 2) {
      return '4-4-2';
    } else if (defenders === 3 && midfielders === 5 && forwards === 2) {
      return '3-5-2';
    } else if (defenders === 3 && midfielders === 4 && forwards === 3) {
      return '3-4-3';
    } else if (defenders === 5 && midfielders === 3 && forwards === 2) {
      return '5-3-2';
    }

    // Default fallback
    return '4-3-3';
  }

  // Formation Templates

  private get433Formation(): FormationPlayer[] {
    return [
      // Goalkeeper
      { position: 'GK', x: 10, y: 50 },
      
      // Defense (4)
      { position: 'LB', x: 25, y: 20 },
      { position: 'CB', x: 25, y: 40 },
      { position: 'CB', x: 25, y: 60 },
      { position: 'RB', x: 25, y: 80 },
      
      // Midfield (3)
      { position: 'CDM', x: 45, y: 35 },
      { position: 'CM', x: 45, y: 50 },
      { position: 'CDM', x: 45, y: 65 },
      
      // Attack (3)
      { position: 'LW', x: 70, y: 25 },
      { position: 'ST', x: 70, y: 50 },
      { position: 'RW', x: 70, y: 75 }
    ];
  }

  private get442Formation(): FormationPlayer[] {
    return [
      // Goalkeeper
      { position: 'GK', x: 10, y: 50 },
      
      // Defense (4)
      { position: 'LB', x: 25, y: 20 },
      { position: 'CB', x: 25, y: 40 },
      { position: 'CB', x: 25, y: 60 },
      { position: 'RB', x: 25, y: 80 },
      
      // Midfield (4)
      { position: 'LM', x: 50, y: 25 },
      { position: 'CM', x: 50, y: 42 },
      { position: 'CM', x: 50, y: 58 },
      { position: 'RM', x: 50, y: 75 },
      
      // Attack (2)
      { position: 'ST', x: 75, y: 42 },
      { position: 'ST', x: 75, y: 58 }
    ];
  }

  private get352Formation(): FormationPlayer[] {
    return [
      // Goalkeeper
      { position: 'GK', x: 10, y: 50 },
      
      // Defense (3)
      { position: 'CB', x: 25, y: 30 },
      { position: 'CB', x: 25, y: 50 },
      { position: 'CB', x: 25, y: 70 },
      
      // Midfield (5)
      { position: 'LM', x: 45, y: 15 },
      { position: 'CDM', x: 45, y: 35 },
      { position: 'CAM', x: 55, y: 50 },
      { position: 'CDM', x: 45, y: 65 },
      { position: 'RM', x: 45, y: 85 },
      
      // Attack (2)
      { position: 'ST', x: 75, y: 42 },
      { position: 'ST', x: 75, y: 58 }
    ];
  }

  private get3412Formation(): FormationPlayer[] {
    return [
      // Goalkeeper
      { position: 'GK', x: 10, y: 50 },
      
      // Defense (3)
      { position: 'CB', x: 25, y: 30 },
      { position: 'CB', x: 25, y: 50 },
      { position: 'CB', x: 25, y: 70 },
      
      // Midfield (4)
      { position: 'LM', x: 45, y: 20 },
      { position: 'CDM', x: 45, y: 40 },
      { position: 'CDM', x: 45, y: 60 },
      { position: 'RM', x: 45, y: 80 },
      
      // Attacking Mid (1)
      { position: 'CAM', x: 60, y: 50 },
      
      // Attack (2)
      { position: 'ST', x: 75, y: 42 },
      { position: 'ST', x: 75, y: 58 }
    ];
  }

  private get4231Formation(): FormationPlayer[] {
    return [
      // Goalkeeper
      { position: 'GK', x: 10, y: 50 },

      // Defense (4)
      { position: 'LB', x: 25, y: 20 },
      { position: 'CB', x: 25, y: 40 },
      { position: 'CB', x: 25, y: 60 },
      { position: 'RB', x: 25, y: 80 },

      // Defensive Midfield (2)
      { position: 'CDM', x: 45, y: 42 },
      { position: 'CDM', x: 45, y: 58 },

      // Attacking Midfield (3)
      { position: 'CAM', x: 60, y: 30 },
      { position: 'CAM', x: 60, y: 50 },
      { position: 'CAM', x: 60, y: 70 },

      // Attack (1)
      { position: 'ST', x: 75, y: 50 }
    ];
  }

  private get4141Formation(): FormationPlayer[] {
    return [
      // Goalkeeper
      { position: 'GK', x: 10, y: 50 },

      // Defense (4)
      { position: 'LB', x: 25, y: 20 },
      { position: 'CB', x: 25, y: 40 },
      { position: 'CB', x: 25, y: 60 },
      { position: 'RB', x: 25, y: 80 },

      // Defensive Midfield (1)
      { position: 'CDM', x: 40, y: 50 },

      // Midfield (4)
      { position: 'LM', x: 55, y: 25 },
      { position: 'CM', x: 55, y: 42 },
      { position: 'CM', x: 55, y: 58 },
      { position: 'RM', x: 55, y: 75 },

      // Attack (1)
      { position: 'ST', x: 75, y: 50 }
    ];
  }

  private get532Formation(): FormationPlayer[] {
    return [
      // Goalkeeper
      { position: 'GK', x: 10, y: 50 },

      // Defense (5)
      { position: 'LB', x: 25, y: 15 },
      { position: 'CB', x: 25, y: 35 },
      { position: 'CB', x: 25, y: 50 },
      { position: 'CB', x: 25, y: 65 },
      { position: 'RB', x: 25, y: 85 },

      // Midfield (3)
      { position: 'CM', x: 50, y: 35 },
      { position: 'CM', x: 50, y: 50 },
      { position: 'CM', x: 50, y: 65 },

      // Attack (2)
      { position: 'ST', x: 75, y: 42 },
      { position: 'ST', x: 75, y: 58 }
    ];
  }

  private get343Formation(): FormationPlayer[] {
    return [
      // Goalkeeper
      { position: 'GK', x: 10, y: 50 },

      // Defense (3)
      { position: 'CB', x: 25, y: 30 },
      { position: 'CB', x: 25, y: 50 },
      { position: 'CB', x: 25, y: 70 },

      // Midfield (4)
      { position: 'LM', x: 45, y: 20 },
      { position: 'CM', x: 45, y: 42 },
      { position: 'CM', x: 45, y: 58 },
      { position: 'RM', x: 45, y: 80 },

      // Attack (3)
      { position: 'LW', x: 70, y: 30 },
      { position: 'ST', x: 70, y: 50 },
      { position: 'RW', x: 70, y: 70 }
    ];
  }

  // Helper Methods

  private getSimilarPositions(position: string): string[] {
    const similarityMap: { [key: string]: string[] } = {
      'CB': ['LCB', 'RCB'],
      'LCB': ['CB', 'RCB'],
      'RCB': ['CB', 'LCB'],
      'CM': ['LCM', 'RCM', 'CAM', 'CDM'],
      'LCM': ['CM', 'RCM', 'LM'],
      'RCM': ['CM', 'LCM', 'RM'],
      'CAM': ['CM', 'LAM', 'RAM'],
      'CDM': ['CM', 'LDM', 'RDM'],
      'LM': ['LCM', 'LW'],
      'RM': ['RCM', 'RW'],
      'LW': ['LM', 'LF'],
      'RW': ['RM', 'RF'],
      'ST': ['CF', 'LF', 'RF']
    };

    return similarityMap[position] || [];
  }

  private normalizePosition(position: string): string {
    const positionMap: { [key: string]: string } = {
      // Goalkeeper
      'GK': 'GK', 'GOALKEEPER': 'GK',

      // Defenders
      'LB': 'LB', 'LEFT_BACK': 'LB', 'LWB': 'LB',
      'CB': 'CB', 'CENTER_BACK': 'CB', 'CENTRE_BACK': 'CB',
      'RB': 'RB', 'RIGHT_BACK': 'RB', 'RWB': 'RB',
      'LCB': 'CB', 'RCB': 'CB',

      // Midfielders
      'CDM': 'CDM', 'DEFENSIVE_MIDFIELDER': 'CDM',
      'CM': 'CM', 'CENTRAL_MIDFIELDER': 'CM', 'CENTRE_MIDFIELDER': 'CM',
      'CAM': 'CAM', 'ATTACKING_MIDFIELDER': 'CAM',
      'LM': 'LM', 'LEFT_MIDFIELDER': 'LM',
      'RM': 'RM', 'RIGHT_MIDFIELDER': 'RM',
      'LCM': 'CM', 'RCM': 'CM',
      'LDM': 'CDM', 'RDM': 'CDM',
      'LAM': 'CAM', 'RAM': 'CAM',

      // Forwards
      'LW': 'LW', 'LEFT_WINGER': 'LW',
      'RW': 'RW', 'RIGHT_WINGER': 'RW',
      'ST': 'ST', 'STRIKER': 'ST', 'CENTRE_FORWARD': 'ST',
      'CF': 'ST', 'LF': 'LW', 'RF': 'RW'
    };

    return positionMap[position.toUpperCase()] || position;
  }
}