/* === MODERN LOGIN DESIGN === */

.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg,
        var(--bg-primary) 0%,
        var(--surface-primary) 50%,
        var(--bg-primary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl) 5px;
    font-family: var(--font-sans);
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg) 5px;
    }

    @media (max-width: 480px) {
        padding: var(--spacing-md) 5px;
    }
}

.login-card {
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-3xl);
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    padding: var(--spacing-3xl);
    width: 100%;
    max-width: 500px;
    position: relative;
    backdrop-filter: blur(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary), var(--primary));
        background-size: 200% 100%;
        animation: shimmer 3s ease-in-out infinite;
        border-radius: var(--radius-3xl) var(--radius-3xl) 0 0;
    }

    &:hover {
        transform: translateY(-4px);
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.5),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    backdrop-filter: blur(20px);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary));
        border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    }

    @media (max-width: 768px) {
        padding: var(--spacing-xl);
        max-width: 400px;
    }

    @media (max-width: 480px) {
        padding: var(--spacing-lg);
        max-width: 100%;
    }
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);

    .login-title {
        font-size: var(--text-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;
        background: linear-gradient(135deg, var(--primary), var(--accent-primary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        @media (max-width: 768px) {
            font-size: var(--text-2xl);
        }
    }

    .login-subtitle {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin: 0;
        font-weight: var(--font-weight-medium);
    }
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    .form-label {
        font-size: var(--text-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);

        .required-indicator {
            color: var(--error);
            font-size: var(--text-xs);
        }
    }

    .form-input {
        background: var(--surface-secondary);
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-base);
        color: var(--text-primary);
        transition: all 0.3s ease;
        font-family: var(--font-sans);

        &::placeholder {
            color: var(--text-tertiary);
        }

        &:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
            background: var(--surface-primary);
        }

        &:hover:not(:focus) {
            border-color: var(--border-secondary);
        }

        &.error {
            border-color: var(--error);
            box-shadow: 0 0 0 3px rgba(var(--error-rgb), 0.1);
        }
    }
}

.submit-button {
    background: linear-gradient(135deg, var(--primary), var(--accent-primary));
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--text-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-inverse);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: var(--spacing-md);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

        &::before {
            left: 100%;
        }
    }

    &:active:not(:disabled) {
        transform: translateY(0);
    }

    &:disabled {
        background: var(--surface-tertiary);
        color: var(--text-tertiary);
        cursor: not-allowed;
        opacity: 0.6;
    }
}

.divider {
    display: flex;
    align-items: center;
    margin: var(--spacing-xl) 0;
    color: var(--text-tertiary);
    font-size: var(--text-sm);

    &::before,
    &::after {
        content: '';
        flex: 1;
        height: 1px;
        background: var(--border-primary);
    }

    &::before {
        margin-right: var(--spacing-md);
    }

    &::after {
        margin-left: var(--spacing-md);
    }
}

.google-signin-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;

    &:hover:not(:disabled) {
        background: var(--surface-hover);
        border-color: var(--border-secondary);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &:active {
        transform: translateY(0);
    }

    &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
    }

    .google-icon {
        width: 20px;
        height: 20px;
    }
}

.signup-link {
    text-align: center;
    margin-top: var(--spacing-xl);

    p {
        color: var(--text-secondary);
        font-size: var(--text-sm);
        margin: 0;
    }

    .link {
        color: var(--primary);
        text-decoration: none;
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }
    }
}

.browse-link {
    text-align: center;
    margin-top: var(--spacing-md);

    p {
        color: var(--text-tertiary);
        font-size: var(--text-sm);
        margin: 0;
    }

    .link {
        color: var(--accent-primary);
        text-decoration: none;
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
            color: var(--accent-hover);
            text-decoration: underline;
        }
    }
}

/* === ANIMATIONS === */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-card {
    animation: fadeInUp 0.6s ease-out;
}

.form-group {
    animation: fadeInUp 0.6s ease-out;

    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
    .login-card,
    .form-group {
        animation: none !important;
    }

    .submit-button {
        &:hover:not(:disabled) {
            transform: none;
        }
    }
}

/* === GOOGLE SIGN-IN === */
.google-signin-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 0;

    // Override Google's default button styles to match our theme
    :global(div[role="button"]) {
        width: 100% !important;
        border-radius: 8px !important;
        border: 1px solid var(--border-primary) !important;

        &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }
    }
}