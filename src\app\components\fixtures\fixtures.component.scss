/* === FIXTURES COMPONENT === */

.fixtures-container {
    padding: var(--spacing-xl) 5px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
    animation: fadeIn 0.5s ease-out;

    @media (max-width: 768px) {
        padding: var(--spacing-lg) var(--spacing-md);
        max-width: 100vw;
        overflow-x: hidden;
    }
}

.date-view {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;

    @media (max-width: 768px) {
        padding: 0;
        margin: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* === VIEW MODE ANIMATIONS === */
.fixtures-view {
    animation: slideInFromLeft 0.6s ease-out;
}

.date-view {
    animation: slideInFromLeft 0.6s ease-out;
}

.playoffs-view {
    animation: slideInFromLeft 0.6s ease-out;
}

/* === FIXTURE TITLE === */
.fixture-title-section {
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
    text-align: center;

    .fixture-title {
        margin: 0;
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);

        i {
            color: var(--primary);
        }
    }

    @media (max-width: 768px) {
        padding: var(--spacing-md);

        .fixture-title {
            font-size: var(--text-xl);
        }
    }
}

/* === HEADER === */
.fixtures-header {
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);

    h1 {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin: 0;
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);

        i {
            color: var(--primary);
        }
    }

    @media (max-width: 768px) {
        padding: var(--spacing-lg);

        h1 {
            font-size: var(--text-xl);
        }
    }
}

/* === FILTERS === */
.fixtures-filters {
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);

    .filter-controls {
        display: flex;
        gap: var(--spacing-md);
        align-items: center;
        flex-wrap: wrap;

        @media (max-width: 768px) {
            flex-direction: column;
            align-items: stretch;
        }
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);

        label {
            font-size: var(--text-sm);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }

        select {
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: var(--text-sm);

            &:focus {
                outline: none;
                border-color: var(--primary);
                box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
            }
        }
    }
}

/* === VIEW TOGGLE === */
.view-toggle {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    border: 1px solid var(--border-primary);

    .toggle-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-lg);
        background: transparent;
        border: none;
        border-radius: var(--radius-md);
        color: var(--text-secondary);
        font-size: var(--text-sm);
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;

        i {
            font-size: var(--text-sm);
        }

        &:hover {
            background: var(--surface-hover);
            color: var(--text-primary);
            transform: translateY(-1px);
        }

        &.active {
            background: var(--primary-500);
            color: white;
            box-shadow: var(--shadow-sm);

            &:hover {
                background: var(--primary-600);
                color: white;
            }
        }

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
    }

    @media (max-width: 768px) {
        .toggle-btn {
            padding: var(--spacing-xs) var(--spacing-md);
            font-size: var(--text-xs);
        }
    }
}

/* === FIXTURES HEADER === */
.fixtures-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }
}

/* === FIXTURE CONTROL PANEL === */
.fixture-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);

    .fixture-info {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);

        .fixture-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin: 0;
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);

            i {
                color: var(--primary-500);
            }
        }

        .fixture-stats {
            display: flex;
            gap: var(--spacing-lg);

            .stat {
                font-size: var(--text-sm);
                color: var(--text-secondary);
                font-weight: var(--font-weight-medium);
            }
        }
    }

    .fixture-selector {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);

        label {
            font-size: var(--text-sm);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }
    }

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);

        .fixture-stats {
            flex-direction: column;
            gap: var(--spacing-xs);
        }
    }
}

/* === FIXTURES GRID === */
.fixtures-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
    width: 100%;

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: 0 var(--spacing-md);
        justify-items: stretch;
    }
}

/* === GAMES GRID === */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(420px, 1fr)); // Increased min width to prevent 4 columns
    gap: var(--spacing-sm); // Reduced gap for tighter spacing
    margin-bottom: var(--spacing-xl);
    justify-items: stretch; // Changed to stretch for better card utilization
    padding: 0 5px; // Added small padding to prevent edge cutoff

    // Ensure maximum of 3 columns on large screens
    @media (min-width: 1400px) {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md); // Slightly larger gap on very large screens
    }

    @media (max-width: 1200px) {
        grid-template-columns: repeat(2, 1fr); // Force 2 columns on medium screens
        gap: var(--spacing-sm);
    }

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        justify-items: stretch;
        padding: 0;
    }
}

/* === MATCHES GRID === */
.matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(420px, 1fr)); // Increased min width to prevent 4 columns
    gap: var(--spacing-sm); // Reduced gap for tighter spacing
    width: 100%;
    padding: 0 5px;
    box-sizing: border-box;
    justify-items: stretch; // Changed to stretch for better card utilization

    // Ensure maximum of 3 columns on large screens
    @media (min-width: 1400px) {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md); // Slightly larger gap on very large screens
    }

    @media (max-width: 1200px) {
        grid-template-columns: repeat(2, 1fr); // Force 2 columns on medium screens
        gap: var(--spacing-sm);
    }

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        justify-items: stretch;
        padding: 0;
    }

    @media (max-width: 480px) {
        gap: var(--spacing-xs); // Even tighter on very small screens
    }
}

/* === DATE VIEW === */
.date-view {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.date-section {
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;

    &:hover {
        box-shadow: var(--shadow-md);
    }

    &.date-today {
        border-color: var(--primary);
        box-shadow: 0 0 0 1px var(--primary), var(--shadow-md);

        .date-header {
            background: linear-gradient(135deg, var(--primary-50) 0%, var(--surface-secondary) 100%);
        }
    }

    &.date-past {
        opacity: 0.8;

        .date-header {
            background: var(--surface-tertiary);
        }
    }

    &.date-future {
        .date-header {
            background: linear-gradient(135deg, var(--success-50) 0%, var(--surface-secondary) 100%);
        }
    }
}

.date-header {
    background: var(--surface-secondary);
    padding: var(--spacing-lg);
    margin: 0;
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--text-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    position: relative;

    i {
        color: var(--primary);
        font-size: var(--text-xl);
    }

    .date-text {
        flex: 1;
    }

    .games-count {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
        background: var(--surface-tertiary);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-full);
    }

    .today-indicator {
        color: var(--warning-color);
        font-size: var(--text-lg);
        animation: pulse 2s infinite;
    }

    &.today-header {
        color: var(--primary);
        font-weight: var(--font-weight-bold);

        .date-text {
            color: var(--primary);
        }
    }

    @media (max-width: 768px) {
        padding: var(--spacing-md);
        font-size: var(--text-base);
        flex-wrap: wrap;

        .games-count {
            margin-top: var(--spacing-xs);
        }
    }
}

/* === LOAD MORE TRIGGERS === */
.load-more-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border: 2px dashed var(--border-primary);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: var(--font-weight-medium);

    &:hover {
        background: var(--surface-tertiary);
        border-color: var(--primary);
        color: var(--primary);
        transform: translateY(-1px);
    }

    &.past-trigger {
        margin-bottom: var(--spacing-md);

        i {
            animation: bounce-up 1.5s infinite;
        }
    }

    &.future-trigger {
        margin-top: var(--spacing-md);

        i {
            animation: bounce-down 1.5s infinite;
        }
    }

    @media (max-width: 768px) {
        padding: var(--spacing-md);
        font-size: var(--text-sm);
    }
}

/* === DATE LOADING === */
.date-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);

    .loading-spinner {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);

        i {
            font-size: var(--text-lg);
            color: var(--primary);
        }
    }

    &.past-loading {
        margin-bottom: var(--spacing-md);
    }

    &.future-loading {
        margin-top: var(--spacing-md);
    }
}

/* === EMPTY STATE === */
.empty-dates {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3xl);
    text-align: center;
    color: var(--text-secondary);

    i {
        font-size: 4rem;
        color: var(--border-primary);
        margin-bottom: var(--spacing-lg);
    }

    h3 {
        margin: 0 0 var(--spacing-sm) 0;
        color: var(--text-primary);
        font-size: var(--text-xl);
    }

    p {
        margin: 0;
        font-size: var(--text-base);
        max-width: 400px;
    }
}

/* === ANIMATIONS === */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce-up {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

@keyframes bounce-down {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(3px); }
}

/* === LOADING STATE === */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
    padding: var(--spacing-xl);

    i {
        font-size: 3rem;
        color: var(--primary);
        margin-bottom: var(--spacing-lg);
        animation: spin 1s linear infinite;
    }

    h3 {
        margin: 0 0 var(--spacing-md) 0;
        color: var(--text-primary);
    }

    p {
        color: var(--text-secondary);
    }
}

/* === ERROR STATE === */
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
    padding: var(--spacing-xl);

    i {
        font-size: 3rem;
        color: var(--danger);
        margin-bottom: var(--spacing-lg);
    }

    h3 {
        margin: 0 0 var(--spacing-md) 0;
        color: var(--text-primary);
    }

    p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
    }

    .retry-btn {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-lg);
        background: var(--primary);
        color: white;
        border: none;
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background: var(--primary-600);
            transform: translateY(-1px);
        }
    }
}

/* === FIXTURES NAVIGATION === */
.fixtures-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    margin-top: var(--spacing-xl);
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;

    /* Material Paginator Styling */
    ::ng-deep .mat-mdc-paginator {
        background: transparent !important;
        color: var(--text-primary) !important;
        border-radius: var(--radius-md);

        .mat-mdc-paginator-container {
            padding: 0 !important;
        }

        .mat-mdc-paginator-range-label {
            color: var(--text-primary) !important;
            font-size: var(--text-sm) !important;
            font-weight: var(--font-weight-medium) !important;
        }

        .mat-mdc-icon-button {
            background: var(--surface-secondary) !important;
            border: 1px solid var(--border-primary) !important;
            border-radius: var(--radius-md) !important;
            color: var(--text-primary) !important;
            margin: 0 var(--spacing-xs) !important;
            transition: all 0.2s ease !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;

            .mat-mdc-button-touch-target {
                width: 100% !important;
                height: 100% !important;
            }

            .mdc-button__label {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            svg,
            .mat-mdc-paginator-icon {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                margin: 0 !important;
            }

            &:hover:not([disabled]) {
                background: var(--surface-hover) !important;
                border-color: var(--primary-500) !important;
                transform: translateY(-1px) !important;
            }

            &[disabled] {
                background: var(--surface-disabled) !important;
                color: var(--text-disabled) !important;
                border-color: var(--border-secondary) !important;
                opacity: 0.5 !important;
            }

            .mat-mdc-button-ripple {
                border-radius: var(--radius-md) !important;
            }
        }

        .mat-mdc-paginator-icon {
            fill: var(--text-primary) !important;
        }
    }

    .quick-nav {
        display: flex;
        gap: var(--spacing-sm);

        .nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: var(--text-sm);

            &:hover:not(:disabled) {
                background: var(--surface-hover);
                border-color: var(--primary-500);
                color: var(--primary-500);
                transform: translateY(-1px);
            }

            &:disabled {
                background: var(--surface-disabled);
                color: var(--text-disabled);
                border-color: var(--border-secondary);
                cursor: not-allowed;
                opacity: 0.5;
                transform: none;
            }
        }
    }

    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        max-width: calc(100vw - 2rem);

        .quick-nav {
            order: -1;
            justify-content: center;
            width: 100%;
        }

        ::ng-deep .mat-mdc-paginator {
            width: 100%;

            .mat-mdc-paginator-container {
                justify-content: center !important;
                flex-direction: column !important;
                align-items: center !important;
                gap: var(--spacing-sm) !important;
                padding: var(--spacing-sm) !important;
            }

            .mat-mdc-paginator-range-label {
                order: -1;
                width: 100%;
                text-align: center;
                margin-bottom: var(--spacing-sm);
                font-size: var(--text-sm) !important;
                font-weight: var(--font-weight-medium) !important;
            }

            .mat-mdc-paginator-range-actions {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: var(--spacing-sm);
            }

            .mat-mdc-icon-button {
                width: 40px !important;
                height: 40px !important;
                margin: 0 !important;
                border-radius: var(--radius-md) !important;
                background: var(--surface-secondary) !important;
                border: 1px solid var(--border-primary) !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;

                .mat-mdc-button-touch-target {
                    width: 100% !important;
                    height: 100% !important;
                }

                .mdc-button__label {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                }

                svg,
                .mat-mdc-paginator-icon {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    margin: 0 !important;
                }

                &:hover:not([disabled]) {
                    background: var(--surface-hover) !important;
                    border-color: var(--primary-500) !important;
                }

                &[disabled] {
                    opacity: 0.5 !important;
                    background: var(--surface-disabled) !important;
                }
            }
        }
    }
}

/* === EMPTY STATE === */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 40vh;
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);

    i {
        font-size: 4rem;
        color: var(--primary);
        margin-bottom: var(--spacing-lg);
    }

    h3 {
        margin: 0 0 var(--spacing-md) 0;
        color: var(--text-primary);
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
    }

    p {
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }

    .empty-actions {
        display: flex;
        gap: var(--spacing-md);
        flex-wrap: wrap;
        justify-content: center;

        .btn-secondary {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-lg);
            background: var(--surface-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;

            &:hover {
                background: var(--surface-hover);
                border-color: var(--primary-500);
                color: var(--primary-500);
                transform: translateY(-1px);
            }

            i {
                font-size: var(--text-sm);
            }
        }
    }
}

/* === PLAYOFFS VIEW === */
.playoffs-view {
    padding: var(--spacing-xl);
    background: var(--surface-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    margin-top: var(--spacing-xl);
}

.playoffs-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }
}

.season-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    label {
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
        white-space: nowrap;
    }

    .form-select {
        padding: var(--spacing-sm) var(--spacing-md);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-md);
        background: var(--surface-secondary);
        color: var(--text-primary);
        font-size: var(--text-sm);
        min-width: 120px;

        &:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
        }
    }
}

.bracket-view-toggle {
    display: flex;
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    border: 1px solid var(--border-primary);
    gap: var(--spacing-xs);
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);

    &:hover:not(:disabled) {
        background: var(--primary-hover);
        transform: translateY(-1px);
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .spinning {
        animation: spin 1s linear infinite;
    }
}

.bracket-loading,
.no-bracket-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3xl);
    text-align: center;
    color: var(--text-primary);

    i {
        font-size: 4rem;
        color: var(--primary);
        margin-bottom: var(--spacing-lg);
    }

    h3 {
        margin: 0 0 var(--spacing-md) 0;
        color: var(--text-primary);
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
    }

    p {
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }
}

.bracket-content {
    min-height: 400px;
}

/* Progress Overview */
.progress-overview {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
    }
}

.progress-card {
    background: var(--surface-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);

    h3 {
        margin: 0 0 var(--spacing-md) 0;
        color: var(--text-primary);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: var(--surface-tertiary);
        border-radius: var(--radius-full);
        overflow: hidden;
        margin-bottom: var(--spacing-sm);

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary), var(--primary-hover));
            transition: width 0.3s ease;
        }
    }

    p {
        margin: 0;
        color: var(--text-secondary);
        font-size: var(--text-sm);
        font-weight: var(--font-weight-medium);
    }
}

.next-match-card {
    background: var(--surface-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    min-width: 300px;

    h3 {
        margin: 0 0 var(--spacing-md) 0;
        color: var(--text-primary);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
    }

    .next-match-info {
        .teams {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);

            .team {
                font-weight: var(--font-weight-semibold);
                color: var(--text-primary);
            }

            .vs {
                color: var(--primary);
                font-size: var(--text-sm);
                font-weight: var(--font-weight-semibold);
            }
        }

        .match-details {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);

            .stage {
                color: var(--primary);
                font-weight: var(--font-weight-semibold);
                font-size: var(--text-sm);
            }

            .date {
                color: var(--info-500);
                font-size: var(--text-sm);
                font-weight: var(--font-weight-medium);
            }
        }
    }
}

/* Championship Results */
.championship-results {
    background: var(--surface-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--border-primary);

    h2 {
        text-align: center;
        margin: 0 0 var(--spacing-xl) 0;
        color: var(--text-primary);
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-bold);
    }

    .podium {
        display: flex;
        justify-content: center;
        align-items: end;
        gap: var(--spacing-lg);
        flex-wrap: wrap;

        .podium-position {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            min-width: 150px;

            &.champion {
                background: linear-gradient(135deg, var(--warning-100), var(--warning-200));
                border: 2px solid var(--warning-500);
                order: 2;
            }

            &.runner-up {
                background: linear-gradient(135deg, var(--neutral-100), var(--neutral-200));
                border: 2px solid var(--neutral-400);
                order: 1;
            }

            &.third-place {
                background: linear-gradient(135deg, var(--warning-50), var(--warning-100));
                border: 2px solid var(--warning-300);
                order: 3;
            }

            .position-icon {
                font-size: var(--text-2xl);
                margin-bottom: var(--spacing-sm);

                .fa-crown {
                    color: var(--warning-600);
                }

                .fa-medal {
                    color: var(--neutral-600);
                }

                .fa-award {
                    color: var(--warning-500);
                }
            }

            .team-logo {
                width: 60px;
                height: 60px;
                border-radius: var(--radius-full);
                margin-bottom: var(--spacing-sm);
                border: 2px solid var(--border-primary);
            }

            h3 {
                margin: 0 0 var(--spacing-xs) 0;
                color: var(--text-primary);
                font-size: var(--text-lg);
                font-weight: var(--font-weight-bold);
            }

            .position-label {
                font-size: var(--text-sm);
                color: var(--text-secondary);
                font-weight: var(--font-weight-medium);
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }
        }
    }
}

/* Bracket View */
.bracket-view {
    margin-bottom: var(--spacing-xl);

    // Tournament Tree Styles
    .tournament-tree {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 1.5rem;
        padding: 2rem;
        position: relative;
        min-height: 600px;
        overflow: hidden;
        background: var(--surface-secondary);
        border-radius: var(--radius-lg);
        margin: 2rem 0;
        border: 2px solid var(--border-primary);

        .bracket-column {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            flex: 1;
            position: relative;

            &.quarter-finals-left,
            &.quarter-finals-right {
                flex: 1.2;
            }

            &.semi-finals-left,
            &.semi-finals-right {
                flex: 0.8;
            }

            &.final {
                flex: 1;
            }

            .column-header {
                text-align: center;
                margin-bottom: 1rem;

                h3 {
                    color: var(--primary);
                    font-size: 1.2rem;
                    font-weight: 600;
                    margin: 0;
                }

                .series-info {
                    color: var(--text-secondary);
                    font-size: 0.9rem;
                    font-style: italic;
                }
            }

            .matches-column {
                display: flex;
                flex-direction: column;
                gap: 2rem;
                width: 100%;
                align-items: center;
            }

            .bracket-match {
                background: var(--surface-primary);
                border: 2px solid var(--border-primary);
                border-radius: var(--radius-lg);
                padding: 1.25rem;
                min-width: 200px;
                max-width: 250px;
                width: 100%;
                box-shadow: var(--shadow-md);
                transition: all 0.3s ease;
                position: relative;
                z-index: 2;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: var(--shadow-lg);
                    border-color: var(--primary);
                }

                &.completed {
                    border-color: var(--success);
                    background: linear-gradient(135deg, var(--surface-primary) 0%, rgba(var(--success-rgb), 0.05) 100%);
                }

                &.final-match {
                    border-color: var(--warning);
                    background: linear-gradient(135deg, var(--surface-primary) 0%, rgba(var(--warning-rgb), 0.1) 100%);

                    .trophy-icon {
                        position: absolute;
                        top: -10px;
                        right: -10px;
                        background: var(--warning);
                        color: white;
                        border-radius: 50%;
                        width: 30px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 0.8rem;
                    }
                }

                .series-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 0.75rem;

                    .series-title {
                        font-weight: 600;
                        color: var(--primary);
                        font-size: 0.9rem;
                    }

                    .series-score {
                        background: var(--primary);
                        color: white;
                        padding: 0.25rem 0.5rem;
                        border-radius: var(--radius-sm);
                        font-weight: 600;
                        font-size: 0.8rem;
                    }
                }

                .series-teams {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    margin-bottom: 1rem;

                    .team {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                        padding: 0.5rem;
                        border-radius: var(--radius-md);
                        transition: all 0.3s ease;

                        &.winner {
                            background: rgba(var(--success-rgb), 0.1);
                            border: 1px solid var(--success);
                        }

                        .team-logo {
                            width: 24px;
                            height: 24px;
                            border-radius: 50%;
                            object-fit: cover;
                        }

                        .team-name {
                            font-weight: 500;
                            color: var(--text-primary);
                            font-size: 0.9rem;
                        }
                    }

                    .team-placeholder {
                        padding: 0.5rem;
                        text-align: center;
                        color: var(--text-secondary);
                        font-style: italic;
                        background: var(--surface-secondary);
                        border-radius: var(--radius-md);
                        border: 1px dashed var(--border-primary);
                    }

                    .vs-separator {
                        text-align: center;
                        color: var(--text-secondary);
                        font-weight: 500;
                        font-size: 0.8rem;
                    }
                }

                .series-games {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 0.5rem;
                    justify-content: center;

                    .game-result {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: 0.25rem;
                        padding: 0.5rem;
                        background: var(--surface-secondary);
                        border-radius: var(--radius-sm);
                        cursor: pointer;
                        transition: all 0.3s ease;
                        min-width: 40px;

                        &:hover {
                            background: var(--primary);
                            color: white;
                        }

                        &.completed {
                            background: rgba(var(--success-rgb), 0.1);
                            border: 1px solid var(--success);
                        }

                        .game-label {
                            font-size: 0.7rem;
                            font-weight: 600;
                            color: var(--text-secondary);
                        }

                        .game-score {
                            font-size: 0.8rem;
                            font-weight: 600;
                            color: var(--text-primary);
                        }

                        &:hover .game-label,
                        &:hover .game-score {
                            color: white;
                        }
                    }
                }
            }
        }
    }
}

.bracket-stages {
    display: flex;
    gap: var(--spacing-lg);
    overflow-x: auto;
    padding: var(--spacing-lg);
    min-height: 600px;

    @media (max-width: 768px) {
        flex-direction: column;
        overflow-x: visible;
    }
}

.bracket-stage {
    flex: 1;
    min-width: 250px;
    position: relative;

    .stage-header {
        text-align: center;
        margin-bottom: var(--spacing-lg);
        padding: var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-primary);

        h3 {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
            font-size: var(--text-lg);
            font-weight: var(--font-weight-bold);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);

            i {
                color: var(--primary);
            }
        }

        .match-count {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
        }
    }

    .stage-matches {
        position: relative;
        height: 500px;
    }
}

.bracket-match {
    position: absolute;
    width: 100%;
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all 0.2s ease;
    transform: translateY(-50%);

    &:hover {
        border-color: var(--primary);
        box-shadow: var(--shadow-md);
        transform: translateY(-50%) translateY(-2px);
    }

    &.completed {
        border-color: var(--success-500);
        background: var(--success-50);
    }

    .match-teams {
        margin-bottom: var(--spacing-sm);

        .team {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-xs) 0;

            &.winner {
                font-weight: var(--font-weight-bold);
                color: var(--success-600);
            }

            .team-logo {
                width: 24px;
                height: 24px;
                border-radius: var(--radius-sm);
            }

            .team-name {
                flex: 1;
                font-size: var(--text-sm);
                color: var(--text-primary);
            }

            .team-score {
                font-weight: var(--font-weight-bold);
                color: var(--text-primary);
                min-width: 20px;
                text-align: center;
            }
        }

        .match-separator {
            text-align: center;
            padding: var(--spacing-xs) 0;

            .vs-text,
            .score-separator {
                font-size: var(--text-xs);
                color: var(--text-secondary);
                font-weight: var(--font-weight-medium);
            }
        }
    }

    .match-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);

        .match-status {
            font-size: var(--text-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: var(--font-weight-medium);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);

            &.status-success {
                background: var(--success-100);
                color: var(--success-700);
            }

            &.status-primary {
                background: var(--primary-100);
                color: var(--primary-700);
            }

            &.status-warning {
                background: var(--warning-100);
                color: var(--warning-700);
            }
        }

        .match-date {
            font-size: var(--text-xs);
            color: var(--text-secondary);
        }
    }

    .match-actions {
        display: flex;
        gap: var(--spacing-xs);

        .action-btn {
            background: transparent;
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-sm);
            padding: var(--spacing-xs);
            cursor: pointer;
            color: var(--text-secondary);
            transition: all 0.2s ease;

            &:hover {
                background: var(--primary);
                color: var(--text-inverse);
                border-color: var(--primary);
            }

            i {
                font-size: var(--text-xs);
            }
        }
    }
}

/* List View */
.list-view {
    margin-bottom: var(--spacing-xl);
}

.stage-filter {
    margin-bottom: var(--spacing-xl);

    h3 {
        margin: 0 0 var(--spacing-md) 0;
        color: var(--text-primary);
        font-size: var(--text-lg);
        font-weight: var(--font-weight-semibold);
    }

    .filter-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-sm);

        .filter-btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-full);
            background: var(--surface-secondary);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);

            &:hover {
                border-color: var(--primary);
                background: var(--primary-50);
            }

            &.active {
                background: var(--primary);
                color: var(--text-inverse);
                border-color: var(--primary);
            }
        }
    }
}

.matches-list {
    .stage-section {
        margin-bottom: var(--spacing-xl);

        .stage-title {
            margin: 0 0 var(--spacing-lg) 0;
            color: var(--text-primary);
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            padding-bottom: var(--spacing-sm);
            border-bottom: 2px solid var(--border-primary);
        }

        .list-match {
            background: var(--surface-primary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                border-color: var(--primary);
                box-shadow: var(--shadow-md);
                transform: translateY(-1px);
            }

            &.completed {
                border-color: var(--success-500);
                background: var(--success-25);
            }

            .match-teams {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: var(--spacing-md);

                .team {
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-sm);
                    flex: 1;

                    &.winner {
                        font-weight: var(--font-weight-bold);
                        color: var(--success-600);
                    }

                    .team-logo {
                        width: 32px;
                        height: 32px;
                        border-radius: var(--radius-sm);
                    }

                    .team-name {
                        font-size: var(--text-base);
                        color: var(--text-primary);
                    }
                }

                .match-score {
                    font-size: var(--text-lg);
                    font-weight: var(--font-weight-bold);
                    color: var(--text-primary);
                    padding: 0 var(--spacing-lg);
                    text-align: center;
                    min-width: 80px;
                }
            }

            .match-details {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .match-status {
                    font-size: var(--text-sm);
                    padding: var(--spacing-xs) var(--spacing-sm);
                    border-radius: var(--radius-sm);
                    font-weight: var(--font-weight-medium);
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-xs);

                    &.status-success {
                        background: var(--success-100);
                        color: var(--success-700);
                    }

                    &.status-primary {
                        background: var(--primary-100);
                        color: var(--primary-700);
                    }

                    &.status-warning {
                        background: var(--warning-100);
                        color: var(--warning-700);
                    }
                }

                .match-date {
                    font-size: var(--text-sm);
                    color: var(--text-secondary);
                    font-weight: var(--font-weight-medium);
                }
            }
        }
    }
}

/* === ANIMATIONS === */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* === RESPONSIVE === */
@media (max-width: 480px) {
    .fixtures-container {
        padding: var(--spacing-md);
    }

    .fixtures-header {
        padding: var(--spacing-md);
    }

    .fixtures-filters {
        padding: var(--spacing-md);
    }

    .fixtures-grid {
        align-items: stretch;
        gap: var(--spacing-sm);
        padding: 0 var(--spacing-sm);
    }
}

.fixture-card-size {
    @media (min-width: 768px) {
       width: 100%;
    }
}