/* === PLAYER HEADER COMPONENT === */
.player-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-xl);

    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        align-items: stretch;
    }

    .header-navigation {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);

        @media (max-width: 768px) {
            justify-content: space-between;
        }

        .back-button {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            background: linear-gradient(135deg, var(--info-500), var(--info-600));
            border: 1px solid var(--info-500);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-md);
            color: white;
            font-size: var(--text-sm);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);

            &:hover {
                background: linear-gradient(135deg, var(--info-600), var(--info-700));
                transform: translateX(-2px) translateY(-1px);
                border-color: var(--info-600);
                box-shadow: 0 4px 8px rgba(59, 130, 246, 0.25);
            }

            .back-text {
                @media (max-width: 480px) {
                    display: none;
                }
            }
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--text-sm);

            .breadcrumb-link {
                background: none;
                border: none;
                color: var(--text-secondary);
                cursor: pointer;
                font-size: var(--text-sm);
                font-weight: 500;
                transition: color 0.2s ease;

                &:hover {
                    color: var(--primary-500);
                }
            }

            .breadcrumb-separator {
                color: var(--text-tertiary);
                font-size: var(--text-xs);
            }

            .breadcrumb-current {
                color: var(--text-primary);
                font-weight: 600;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                @media (max-width: 480px) {
                    max-width: 120px;
                }
            }
        }
    }

    .header-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        @media (max-width: 768px) {
            justify-content: center;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-lg);
            font-size: var(--text-sm);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;

            &.edit-btn {
                background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
                color: white;
                border-color: var(--primary-500);
                box-shadow: 0 2px 4px rgba(99, 102, 241, 0.15);

                &:hover {
                    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.25);
                    border-color: var(--primary-600);
                }
            }

            &.cancel-btn {
                background: linear-gradient(135deg, var(--error-500), var(--error-600));
                color: white;
                border-color: var(--error-500);
                box-shadow: 0 2px 4px rgba(239, 68, 68, 0.15);

                &:hover {
                    background: linear-gradient(135deg, var(--error-600), var(--error-700));
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.25);
                    border-color: var(--error-600);
                }
            }

            &.save-btn {
                background: linear-gradient(135deg, var(--success-500), var(--success-600));
                color: white;
                border-color: var(--success-500);
                box-shadow: 0 2px 4px rgba(34, 197, 94, 0.15);

                &:hover {
                    background: linear-gradient(135deg, var(--success-600), var(--success-700));
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(34, 197, 94, 0.25);
                    border-color: var(--success-600);
                }
            }

            span {
                @media (max-width: 480px) {
                    display: none;
                }
            }
        }
    }
}
