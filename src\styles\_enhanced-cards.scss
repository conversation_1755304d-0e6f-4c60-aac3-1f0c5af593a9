/* === ENHANCED CARD STYLES === */

/* Base Card */
.card {
    background: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary), var(--accent-primary));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-2xl);
        border-color: var(--primary-300);

        &::before {
            opacity: 1;
        }
    }
}

/* Card Header */
.card-header {
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border-bottom: 1px solid var(--border-primary);
    position: relative;

    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: var(--spacing-xl);
        right: var(--spacing-xl);
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--primary), transparent);
    }

    .card-title {
        margin: 0;
        font-size: var(--text-xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .card-subtitle {
        margin: var(--spacing-xs) 0 0 0;
        font-size: var(--text-sm);
        color: var(--text-secondary);
    }
}

/* Card Body */
.card-body {
    padding: var(--spacing-xl);
}

/* Card Footer */
.card-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--surface-secondary);
    border-top: 1px solid var(--border-primary);
}

/* Card Variants */
.card-elevated {
    box-shadow: var(--shadow-2xl);
    
    &:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: var(--shadow-3xl);
    }
}

.card-interactive {
    cursor: pointer;
    
    &:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-2xl);
    }

    &:active {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
}

.card-glow {
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.2), var(--shadow-lg);

    &:hover {
        box-shadow: 0 0 30px rgba(var(--primary-rgb), 0.3), var(--shadow-2xl);
    }
}

/* Color Variants */
.card-primary {
    border-color: var(--primary-300);
    
    &::before {
        background: linear-gradient(90deg, var(--primary), var(--primary-600));
        opacity: 0.8;
    }

    .card-header {
        background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    }
}

.card-success {
    border-color: var(--success-300);
    
    &::before {
        background: linear-gradient(90deg, var(--success), var(--success-600));
        opacity: 0.8;
    }

    .card-header {
        background: linear-gradient(135deg, var(--success-50) 0%, var(--success-100) 100%);
    }
}

.card-warning {
    border-color: var(--warning-300);
    
    &::before {
        background: linear-gradient(90deg, var(--warning), var(--warning-600));
        opacity: 0.8;
    }

    .card-header {
        background: linear-gradient(135deg, var(--warning-50) 0%, var(--warning-100) 100%);
    }
}

.card-danger {
    border-color: var(--danger-300);
    
    &::before {
        background: linear-gradient(90deg, var(--danger), var(--danger-600));
        opacity: 0.8;
    }

    .card-header {
        background: linear-gradient(135deg, var(--danger-50) 0%, var(--danger-100) 100%);
    }
}

.card-info {
    border-color: var(--info-300);
    
    &::before {
        background: linear-gradient(90deg, var(--info), var(--info-600));
        opacity: 0.8;
    }

    .card-header {
        background: linear-gradient(135deg, var(--info-50) 0%, var(--info-100) 100%);
    }
}

/* Special Effects */
.card-shimmer {
    position: relative;
    overflow: hidden;

    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }
}

.card-pulse {
    animation: cardPulse 2s ease-in-out infinite;
}

/* Stats Card */
.stats-card {
    @extend .card;
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: var(--spacing-lg);
        padding: var(--spacing-xl);
    }

    .stat-item {
        text-align: center;
        padding: var(--spacing-lg);
        background: var(--surface-tertiary);
        border-radius: var(--radius-lg);
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-2px);
            background: var(--surface-quaternary);
        }

        .stat-value {
            font-size: var(--text-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }
}

/* Animations */
@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes cardPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .card-header,
    .card-body,
    .card-footer {
        padding: var(--spacing-lg);
    }

    .card-header .card-title {
        font-size: var(--text-lg);
    }

    .stats-card .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
    }

    .stats-card .stat-item {
        padding: var(--spacing-md);

        .stat-value {
            font-size: var(--text-xl);
        }
    }
}

@media (max-width: 480px) {
    .card-header,
    .card-body,
    .card-footer {
        padding: var(--spacing-md);
    }

    .stats-card .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }
}
