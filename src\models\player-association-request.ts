import mongoose, { Schema, Document } from "mongoose";

export interface IPlayerAssociationRequest extends Document {
  id: string;
  userId: mongoose.Types.ObjectId;
  playerId: mongoose.Types.ObjectId;
  playerEmail?: string;
  status: 'pending' | 'approved' | 'rejected';
  requestedAt: Date;
  processedAt?: Date;
  processedBy?: mongoose.Types.ObjectId; // Admin who processed the request
  reason?: string; // Reason for rejection
  userMessage?: string; // Message from user explaining why they want this player
}

const playerAssociationRequestSchema: Schema<IPlayerAssociationRequest> = new Schema<IPlayerAssociationRequest>(
  {
    userId: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: "User", 
      required: true 
    },
    playerId: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: "Player", 
      required: true 
    },
    playerEmail: { 
      type: String 
    },
    status: { 
      type: String, 
      enum: ['pending', 'approved', 'rejected'], 
      default: 'pending' 
    },
    requestedAt: { 
      type: Date, 
      default: Date.now 
    },
    processedAt: { 
      type: Date 
    },
    processedBy: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: "User" 
    },
    reason: { 
      type: String 
    },
    userMessage: { 
      type: String 
    }
  },
  {
    timestamps: true,
    toJSON: { 
      virtuals: true,
      transform: function(doc, ret) {
        return ret;
      }
    },
    id: true,
  }
);

// Index for performance
playerAssociationRequestSchema.index({ userId: 1 });
playerAssociationRequestSchema.index({ playerId: 1 });
playerAssociationRequestSchema.index({ status: 1 });

// Compound index to prevent duplicate pending requests
playerAssociationRequestSchema.index({ userId: 1, playerId: 1, status: 1 }, { unique: true });

const PlayerAssociationRequest = mongoose.model<IPlayerAssociationRequest>("PlayerAssociationRequest", playerAssociationRequestSchema);

export default PlayerAssociationRequest;
