<div class="create-player-container">
    <div class="create-player-card">
        <div class="create-player-header">
            <h1 class="create-player-title">
                <i class="fas fa-user-plus"></i>
                Add Player
            </h1>
            <p class="create-player-subtitle">Create a new player profile</p>
        </div>

        <form [formGroup]="addPlayerFormGroup" (ngSubmit)="onSubmit()" class="create-player-form">
            <div class="form-group" *ngFor="let control of formControls">
                <label class="form-label">
                    {{ control.displayText }}
                    <span class="required-indicator" *ngIf="isRequiredForm(control.control)">*</span>
                </label>

                <input
                    *ngIf="control.type === 'text-input'"
                    [formControlName]="control.field"
                    [maxlength]="control.maxLength!"
                    [placeholder]="'Enter ' + control.displayText.toLowerCase()"
                    class="form-input"
                    [class.error]="control.control.invalid && control.control.touched">

                <pro-clubs-auto-complete-select
                    *ngIf="control.type === 'select'"
                    [placeholder]="'Select ' + control.displayText.toLowerCase()"
                    (selectionChange)="onSelectionChange($event)"
                    [selectOptions]="playablePositionOptions">
                </pro-clubs-auto-complete-select>

                <pro-clubs-multiple-select
                    *ngIf="control.type === 'multi-select'"
                    [placeholder]="'Select ' + control.displayText.toLowerCase()"
                    (selectionChange)="onMultipleSelectionChange($event)"
                    [selectOptions]="playablePositionOptions">
                </pro-clubs-multiple-select>
            </div>

            <div class="file-upload-section">
                <label class="file-upload-label">Player Photo</label>
                <div class="file-upload-button" (click)="fileInput.click()">
                    <i class="fas fa-camera upload-icon"></i>
                    <div class="upload-text">Upload Player Photo</div>
                    <div class="upload-hint">Click to select an image file</div>
                </div>
                <input
                    type="file"
                    #fileInput
                    id="urlPhoto"
                    class="file-input"
                    (change)="onFileSelected($event)"
                    accept="image/*">
            </div>

            <button
                type="submit"
                class="submit-button"
                [disabled]="addPlayerFormGroup.invalid">
                <i class="fas fa-user-check"></i>
                Create Player
            </button>
        </form>
    </div>
</div>