import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnDestroy } from '@angular/core';
import { FixtureDTO, GameFixtureData } from '../../../shared/models/game.model';
import { PermissionsService } from '../../../services/permissions.service';
import { Subject, Observable } from 'rxjs';

@Component({
  selector: 'app-date-view',
  templateUrl: './date-view.component.html',
  styleUrls: ['./date-view.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DateViewComponent implements OnInit, OnDestroy {
  @Input() fixtures: FixtureDTO[] = [];
  @Input() gamesByDate: { [date: string]: GameFixtureData[] } = {};
  
  @Output() gameClicked = new EventEmitter<GameFixtureData>();
  @Output() loadMorePast = new EventEmitter<void>();
  @Output() loadMoreFuture = new EventEmitter<void>();

  // Date view properties for time-centered navigation
  visibleDates: string[] = [];
  todayDateKey: string = '';
  currentCenterDate: string = '';
  dateViewInitialized: boolean = false;
  isLoadingPastDates: boolean = false;
  isLoadingFutureDates: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(private permissionsService: PermissionsService) {}

  ngOnInit(): void {
    this.initializeDateView();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeDateView(): void {
    this.organizeGamesByDate();
    this.setupTimeCenteredView();
  }

  private organizeGamesByDate(): void {
    // This method is now handled by the parent component
    // The gamesByDate input should already be organized
  }

  private setupTimeCenteredView(): void {
    // Set today as the reference point
    const today = new Date();
    this.todayDateKey = today.toDateString();
    this.currentCenterDate = this.todayDateKey;
    this.dateViewInitialized = true;
  }

  loadMorePastDates(): void {
    if (this.isLoadingPastDates || this.visibleDates.length === 0) return;

    this.isLoadingPastDates = true;

    // Get all available dates
    const allDates = Object.keys(this.gamesByDate).sort((a, b) =>
      new Date(a).getTime() - new Date(b).getTime()
    );

    // Find the current earliest visible date
    const earliestVisible = this.visibleDates[0];
    const earliestIndex = allDates.indexOf(earliestVisible);

    if (earliestIndex > 0) {
      // Add 3 more dates before the current earliest
      const newStartIndex = Math.max(0, earliestIndex - 3);
      const newDates = allDates.slice(newStartIndex, earliestIndex);
      this.visibleDates = [...newDates, ...this.visibleDates];
    }

    // Simulate loading delay
    setTimeout(() => {
      this.isLoadingPastDates = false;
    }, 300);

    this.loadMorePast.emit();
  }

  loadMoreFutureDates(): void {
    if (this.isLoadingFutureDates || this.visibleDates.length === 0) return;

    this.isLoadingFutureDates = true;

    // Get all available dates
    const allDates = Object.keys(this.gamesByDate).sort((a, b) =>
      new Date(a).getTime() - new Date(b).getTime()
    );

    // Find the current latest visible date
    const latestVisible = this.visibleDates[this.visibleDates.length - 1];
    const latestIndex = allDates.indexOf(latestVisible);

    if (latestIndex < allDates.length - 1) {
      // Add 3 more dates after the current latest
      const newEndIndex = Math.min(allDates.length, latestIndex + 4);
      const newDates = allDates.slice(latestIndex + 1, newEndIndex);
      this.visibleDates = [...this.visibleDates, ...newDates];
    }

    // Simulate loading delay
    setTimeout(() => {
      this.isLoadingFutureDates = false;
    }, 300);

    this.loadMoreFuture.emit();
  }

  get sortedDates(): string[] {
    // Always sort all dates with today and future dates first
    const allDates = Object.keys(this.gamesByDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const futureDates = allDates.filter(date => new Date(date).getTime() >= today.getTime());
    const pastDates = allDates.filter(date => new Date(date).getTime() < today.getTime());

    // Sort future dates ascending (today first, then future), past dates descending (most recent first)
    futureDates.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    pastDates.sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    // Return future dates first (starting with today), then past dates
    return [...futureDates, ...pastDates];
  }

  formatDateHeader(dateString: string): string {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    // Check if it's today, tomorrow, or yesterday
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    }

    // Format as "Monday, January 15"
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    });
  }

  isToday(dateString: string): boolean {
    return dateString === this.todayDateKey;
  }

  getDateDisplayClass(dateString: string): string {
    if (this.isToday(dateString)) {
      return 'date-today';
    }

    const date = new Date(dateString);
    const today = new Date();

    if (date < today) {
      return 'date-past';
    } else if (date > today) {
      return 'date-future';
    }

    return '';
  }

  onGameClick(game: GameFixtureData): void {
    this.gameClicked.emit(game);
  }

  trackByDate(_index: number, date: string): string {
    return date;
  }

  canEditGame(gameId: string): Observable<boolean> {
    return this.permissionsService.canEditGame(gameId);
  }

  trackByGameId(_index: number, game: GameFixtureData): string {
    return game.id;
  }
}
