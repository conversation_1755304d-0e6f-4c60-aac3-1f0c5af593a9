"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const tsyringe_1 = require("tsyringe");
const mongoose_1 = require("mongoose");
const mock_game_repository_1 = require("../../src/mocks/repositories/mock-game-repository");
const services_1 = require("../../src/mocks/services");
const services_2 = require("../../src/services");
const game_mapper_1 = require("../../src/mappers/game-mapper");
const errors_1 = require("../../src/errors");
describe("GameService", () => {
    let gameService;
    let mockGameRepository;
    let mockTeamService;
    let mockPlayerService;
    beforeAll(() => {
        mockGameRepository = new mock_game_repository_1.MockGameRepository();
        mockTeamService = new services_1.MockTeamService();
        mockPlayerService = new services_1.MockPlayerService();
        tsyringe_1.container.registerInstance("IGameRepository", mockGameRepository);
        tsyringe_1.container.registerInstance("ITeamService", mockTeamService);
        tsyringe_1.container.registerInstance("IPlayerService", mockPlayerService);
        gameService = tsyringe_1.container.resolve(services_2.GameService);
    });
    describe("getCurrentSeasonTeamGames", () => {
        let teamId;
        let mockGames;
        let mockTeam;
        beforeAll(() => {
            teamId = new mongoose_1.Types.ObjectId();
            mockGames = [
                {
                    _id: new mongoose_1.Types.ObjectId(),
                    fixture: new mongoose_1.Types.ObjectId(),
                    homeTeam: teamId,
                    awayTeam: new mongoose_1.Types.ObjectId(),
                    result: { homeTeamGoals: 2, awayTeamGoals: 1 },
                    round: 1,
                },
                {
                    _id: new mongoose_1.Types.ObjectId(),
                    fixture: new mongoose_1.Types.ObjectId(),
                    homeTeam: teamId,
                    awayTeam: new mongoose_1.Types.ObjectId(),
                    result: { homeTeamGoals: 1, awayTeamGoals: 1 },
                    round: 2,
                },
                {
                    _id: new mongoose_1.Types.ObjectId(),
                    fixture: new mongoose_1.Types.ObjectId(),
                    homeTeam: teamId,
                    awayTeam: new mongoose_1.Types.ObjectId(),
                    result: { homeTeamGoals: 0, awayTeamGoals: 1 },
                    round: 3,
                },
            ];
            mockTeam = {
                _id: teamId,
                currentSeason: {
                    league: new mongoose_1.Types.ObjectId().toString(),
                    seasonNumber: 1,
                },
            };
            mockGameRepository.getLeagueSeasonTeamGames = jest.fn().mockResolvedValue(mockGames);
            mockTeamService.getTeamEntityById = jest.fn().mockResolvedValue(mockTeam);
        });
        it("should get team games sorted by round asc without limit", () => __awaiter(void 0, void 0, void 0, function* () {
            const gameMapperSpy = jest.spyOn(game_mapper_1.GameMapper, "mapToDtos");
            gameMapperSpy.mockResolvedValue([{}]);
            yield gameService.getCurrentSeasonTeamGames(teamId.toString());
            expect(mockTeamService.getTeamEntityById).toHaveBeenCalledWith(teamId.toString());
            expect(mockGameRepository.getLeagueSeasonTeamGames).toHaveBeenCalledWith(teamId.toString(), mockTeam.currentSeason.league, mockTeam.currentSeason.seasonNumber, undefined);
            expect(gameMapperSpy).toHaveBeenCalledWith(mockGames);
        }));
        it("should get team games sorted by round desc with limit - team last {limit} games", () => __awaiter(void 0, void 0, void 0, function* () {
            const limit = 2;
            const gameMapperSpy = jest.spyOn(game_mapper_1.GameMapper, "mapToDtos");
            gameMapperSpy.mockResolvedValue([{}]);
            yield gameService.getCurrentSeasonTeamGames(teamId.toString(), limit);
            expect(mockTeamService.getTeamEntityById).toHaveBeenCalledWith(teamId.toString());
            expect(mockGameRepository.getLeagueSeasonTeamGames).toHaveBeenCalledWith(teamId.toString(), mockTeam.currentSeason.league, mockTeam.currentSeason.seasonNumber, limit);
            expect(gameMapperSpy).toHaveBeenCalledWith(mockGames);
        }));
        it("should throw an error if the team does not have a current season", () => __awaiter(void 0, void 0, void 0, function* () {
            const invalidTeam = { _id: teamId, currentSeason: null };
            mockTeamService.getTeamEntityById = jest.fn().mockResolvedValue(invalidTeam);
            yield expect(gameService.getCurrentSeasonTeamGames(teamId.toString())).rejects.toThrow(errors_1.BadRequestError);
        }));
    });
});
