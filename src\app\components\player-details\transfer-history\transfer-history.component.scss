/* === BEAUTIFUL TRANSFER HISTORY STYLES === */

.transfer-history-container {
  background: var(--surface-primary);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);
  margin-top: var(--spacing-xl);
}

/* === SECTION HEADER === */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-2xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border-primary);

  .header-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    .header-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, var(--primary-500), var(--primary-400));
      border-radius: var(--radius-full);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--text-lg);
      box-shadow: var(--shadow-md);
    }

    .header-text {
      .section-title {
        font-size: var(--text-xl);
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
      }

      .section-subtitle {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin: 0;
        margin-top: var(--spacing-xs);
      }
    }
  }

  .header-badge {
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-full);
    padding: var(--spacing-sm) var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    .transfer-count {
      font-size: var(--text-lg);
      font-weight: 700;
      color: var(--primary-500);
    }

    .transfer-label {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
}

/* === LOADING STATE === */
.loading-state {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-xl);

  .loading-spinner {
    font-size: var(--text-2xl);
    color: var(--primary-500);
    margin-bottom: var(--spacing-lg);

    i {
      animation: spin 1s linear infinite;
    }
  }

  .loading-text {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin: 0;
  }
}

/* === ERROR STATE === */
.error-state {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-xl);

  .error-icon {
    font-size: var(--text-2xl);
    color: var(--error-500);
    margin-bottom: var(--spacing-lg);
  }

  .error-text {
    color: var(--text-primary);
    font-size: var(--text-base);
    font-weight: 600;
    margin: 0 0 var(--spacing-sm) 0;
  }

  .retry-btn {
    background: var(--primary-500);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    transition: all 0.2s ease;

    &:hover {
      background: var(--primary-600);
      transform: translateY(-1px);
    }
  }
}

/* === EMPTY STATE === */
.empty-state {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-xl);

  .empty-icon {
    font-size: var(--text-3xl);
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-lg);
  }

  .empty-text {
    color: var(--text-primary);
    font-size: var(--text-lg);
    font-weight: 600;
    margin: 0 0 var(--spacing-sm) 0;
  }

  .empty-subtext {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    margin: 0;
  }
}

/* === TRANSFER TIMELINE === */
.transfer-timeline {
  position: relative;
}

.timeline-item {
  position: relative;
  margin-bottom: var(--spacing-2xl);

  &:last-child {
    margin-bottom: 0;
  }

  .timeline-connector {
    position: absolute;
    left: 24px;
    top: 80px;
    bottom: -32px;
    width: 2px;
    z-index: 1;

    .connector-line {
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, var(--border-primary), transparent);
    }
  }
}

/* === TRANSFER CARD === */
.transfer-card {
  background: var(--surface-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-left: var(--spacing-3xl);
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-300);
  }

  &::before {
    content: '';
    position: absolute;
    left: -25px;
    top: 24px;
    width: 16px;
    height: 16px;
    border-radius: var(--radius-full);
    background: var(--surface-primary);
    border: 3px solid var(--primary-500);
    z-index: 2;
  }
}

/* === TRANSFER HEADER === */
.transfer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);

  .transfer-type-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;

    &.badge-signing {
      background: rgba(16, 185, 129, 0.1);
      color: var(--success-600);
      border: 1px solid var(--success-300);
    }

    &.badge-transfer {
      background: rgba(59, 130, 246, 0.1);
      color: var(--info-600);
      border: 1px solid var(--info-300);
    }

    &.badge-release {
      background: rgba(239, 68, 68, 0.1);
      color: var(--error-600);
      border: 1px solid var(--error-300);
    }
  }

  .transfer-date {
    text-align: right;

    .date-primary {
      display: block;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--text-primary);
    }

    .date-secondary {
      display: block;
      font-size: var(--text-xs);
      color: var(--text-secondary);
      margin-top: var(--spacing-xs);
    }
  }
}

/* === TRANSFER FLOW === */
.transfer-flow {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.transfer-entity {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
  min-width: 0;

  &.team-entity {
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;

    &:hover {
      background: var(--surface-primary);
      transform: translateY(-1px);
    }
  }

  .entity-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: 2px solid var(--border-primary);
    flex-shrink: 0;

    .team-logo {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .entity-info {
    min-width: 0;
    flex: 1;

    .entity-name {
      display: block;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--text-primary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .entity-type {
      display: block;
      font-size: var(--text-xs);
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-top: var(--spacing-xs);
    }
  }
}

.transfer-arrow {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-shrink: 0;
  padding: 0 var(--spacing-sm);

  .arrow-line {
    width: 24px;
    height: 2px;
    background: var(--border-secondary);
  }

  .arrow-icon {
    width: 24px;
    height: 24px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xs);
    color: white;
  }

  &.signing-arrow .arrow-icon {
    background: var(--success-500);
  }

  &.transfer-arrow-main .arrow-icon {
    background: var(--info-500);
  }

  &.release-arrow .arrow-icon {
    background: var(--error-500);
  }
}

/* === TRANSFER FOOTER === */
.transfer-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-primary);

  .season-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--text-xs);
    color: var(--text-secondary);

    i {
      color: var(--primary-500);
    }
  }
}

/* === ANIMATIONS === */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .transfer-history-container {
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);

    .header-badge {
      align-self: flex-end;
    }
  }

  .transfer-flow {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .transfer-arrow {
    transform: rotate(90deg);
  }

  .transfer-card {
    margin-left: var(--spacing-lg);
    padding: var(--spacing-lg);
  }

  .timeline-item .timeline-connector {
    left: 12px;
  }

  .transfer-card::before {
    left: -13px;
    width: 12px;
    height: 12px;
  }
}
